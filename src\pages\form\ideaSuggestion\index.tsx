import FORM_JSON from './form.json';
import ReactD<PERSON> from 'react-dom';
import React, { useState, useEffect } from 'react';
import {But<PERSON>, Card, Row, message, Popconfirm, Select, Collapse} from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';

export default function () {
    const { fetchTemporaryIdeaSuggestion, postTemporaryIdeaSuggestion, fetchIdeaSuggestion, postIdeaSuggestion } = api;
    const _types = ['yy_java_ideaSuggestion_high', 'yy_java_ideaSuggestion_low'];
    const _postTypes = ['high', 'low'];
    const _type_texts = ['高收益', '低风险']

    const [option, setOption] = useState('yy_java_ideaSuggestion_high');
    const [type, setType] = useState('high');
    const [init, setInit] = useState(false);
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);

    const getItem = () => {
        fetchTemporaryIdeaSuggestion({
            propName: option
        }).then((res: any) => {
            let _data = FORM_JSON.formData
            try {
                res = JSON.parse(res.data);
                if (res) {
                    _data = res.formData
                }
            } catch (e) {
                console.warn(e)
            }
            setInit(true);
            setData(_data);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }

    const save = () => {
        postTemporaryIdeaSuggestion ({
            propName: option,
            value: JSON.stringify({formData})
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！')
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    const onSubmit = () => {
        let _formData: any = formData;
        if (_formData.items.length < 3 || _formData.items.length > 16) {
            message.error(`请检查个数`);
            return;
        }
        for (let i: number = 0; i < _formData.items.length; i++) {
            if (!_formData.items[i].code || !_formData.items[i].buttonText || _formData.items[i].des.length > 6) {
                message.error(`校验未通过`);
                return;
            }
        }
        postTemporaryIdeaSuggestion ({
            propName: option,
            value: JSON.stringify({formData})
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！');
                    postIdeaSuggestion ({
                        propName: type,
                        value: JSON.stringify({formData})
                    }).then( (res: any) => {
                        try {
                            if (res.code !== '0000') {
                                message.error(res.message);
                            } else {
                                message.success('发布成功！')
                            }
                        } catch (e) {
                            message.error(e.message);
                        }
                    })
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    return (
        <article> 
            <section>
                <span style={{ width: 200 }}> 请选择类型： </span>
                <Select style={{ width: 200 }} onChange={(value:string) => {
                    setOption(value); 
                    setType(_postTypes[_types.indexOf(value)])
                    }}>
                    <Select.Option value={_types[0]}>{_type_texts[0]}</Select.Option>
                    <Select.Option value={_types[1]}>{_type_texts[1]}</Select.Option>
                </Select>
                <Button
                    type="primary" 
                    style={{ marginLeft: '200px', marginBottom: '100px' }}
                    onClick={getItem}
                >查询该配置</Button>
            </section>
            {
                init ?
                <article>
                    <FormRender
                        propsSchema={FORM_JSON.propsSchema}
                        uiSchema={FORM_JSON.uiSchema}
                        onValidate={setValid}
                        formData={formData}
                        onChange={setData}
                        displayType="row"
                        showDescIcon={true}
                        column={2}
                    />
                    <Button
                        type="primary" 
                        onClick={save}
                        style={{marginRight: '300px'}}
                    >
                        保存
                    </Button>


                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么'}
                        onConfirm={onSubmit}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="danger" 
                        >
                            提交修改
                        </Button>
                    </Popconfirm>
                </article>
                :
                ''
            }
        </article>
    )
}