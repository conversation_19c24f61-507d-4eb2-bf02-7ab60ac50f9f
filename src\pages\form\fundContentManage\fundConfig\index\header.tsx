import React, { useState } from 'react';
import { Table, Input, Radio, Switch, message, Modal, DatePicker, Button, Popconfirm } from  'antd';

import api from 'api'

import {
    IS_OPEN_ZX_TAB
} from './../const'

const {
    openZxAll, 
    openZxSome
} = api

export default React.memo(function (props: any) {

    const {
        isOpenZXTab,
        setIsOpenZXTab,
        setIsShowConfigArticle,
        setIsShowZXData,
        setIsShowGoodArticles,
        setIsShowConfiguredArticles,
        setIsShowBlackList,
        setArticleType,
        setArticleValue,
        jumpDetail
    } = props

    const [searchText ,setSearchText] = useState('')

    function clickEditData() {
        // if (isOpenZXTab === IS_OPEN_ZX_TAB.all) return message.info('请选选择部分再编辑数据')
        setIsShowZXData(true)
    }

    function chooseAll() {
        _.fundLoading()
        openZxAll().then((res: any) => {
            _.hideFundLoading()
            if (res.status_code === 0) {
                _.hideFundLoading()
                setIsOpenZXTab(IS_OPEN_ZX_TAB.all)
                message.success('修改为全部成功')
                setTimeout(() => { window.location.reload() }, 1000)
            } else {
                message.error(res.status_msg)
            }
        }).catch((e: any) => {
            _.hideFundLoading()
            console.log(e.message)
            message.error('系统错误,请稍候再试~')
        })
    }

    function chooseSome() {
        _.fundLoading()
        openZxSome().then((res: any) => {
            if (res.status_code === 0) {
                _.hideFundLoading()
                setIsOpenZXTab(IS_OPEN_ZX_TAB.some)
                message.success('修改为部分成功')
                setTimeout(() => { window.location.reload() }, 1000)
            } else {
                message.error(res.status_msg)
            }
        }).catch((e: any) => {
            _.hideFundLoading()
            console.log(e.message)
            message.error('系统错误,请稍候再试~')
        })
    }

    return (
        <div>
            <div className="u-j-middle">
                <div className="u-l-middle">
                    基金代码或基金名称： 
                    <Input style={{width: 400, marginLeft: 30, marginRight: 30}} value={searchText} onChange={((e) => {setSearchText(e.target.value)})} />
                    <Button type="primary" onClick={() => {jumpDetail(searchText)}}>搜索</Button>
                </div>
                <Button 
                type="primary" 
                onClick={() => {
                    setIsShowConfigArticle(true)
                    setArticleValue({})
                    setArticleType('new')
                }}>配置文章</Button>
            </div>
            <div className="u-j-middle g-mt20">
                <div className="u-l-middle">
                    个基开启资讯tab：
                    <Radio.Group value={isOpenZXTab}>
                        <Radio value={IS_OPEN_ZX_TAB.all}>
                            <Popconfirm
                                placement="rightBottom"
                                title={'你确定选择全部么'}
                                onConfirm={chooseAll}
                                okText="确认"
                                cancelText="取消"
                            >
                                <Button>全部 </Button>
                            </Popconfirm>
                        </Radio>
                        <Radio value={IS_OPEN_ZX_TAB.some}>
                            <Popconfirm
                                placement="rightBottom"
                                title={'你确定选择部分么'}
                                onConfirm={chooseSome}
                                okText="确认"
                                cancelText="取消"
                            >
                                <Button>部分 </Button>
                            </Popconfirm>
                        </Radio>
                    </Radio.Group>
                    <Button
                    style={{marginLeft: 20}}
                    type="primary"
                    onClick={clickEditData}
                    >编辑数据</Button>
                </div>
                <div className="u-r-middle">
                    <Button
                    type="primary"
                    onClick={() => {setIsShowGoodArticles(true)}}
                    >查看精选文章</Button>
                    <Button
                    type="primary"
                    onClick={() => {setIsShowConfiguredArticles(true)}}
                    style={{marginLeft: 20, marginRight: 20}}
                    >查看已配置文章</Button>
                    <Button
                    type="primary"
                    onClick={() => {setIsShowBlackList(true)}}
                    >查看黑名单</Button>
                </div>
            </div>
        </div>
    )
})