import React, { useState, useEffect } from 'react';
import { Button, message } from 'antd';
import api from 'api';
import PacketFrom from './packetForm';

const { fetchPacketList, postPacket } = api;


function queryPacketList() {
  const dataToSend = { type: 'query' };
  return new Promise((resolve, reject) => {
    fetchPacketList(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data.data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

function deletePacket(value) {
  if (!value) message.info('缺少必要信息');
  const dataToSend = { type: 'delete', value };
  return new Promise((resolve, reject) => {
    postPacket(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

function addOrEditPacket(value) {
  if (!value) message.info('缺少必要信息');
  const dataToSend = { type: 'insert', value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    postPacket(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

export default function Demo() {
  const [activityList, setActivityList] = useState([]); // 活动列表

  useEffect(() => {
    queryPacketList().then(data => {
      const _activityList = Object.keys(data).map(key => data[key]);
      setActivityList(_activityList);
    });
  }, []);

  function addActivityListItem() {
    const _activityList = JSON.parse(JSON.stringify(activityList));
    _activityList.push({});
    setActivityList(_activityList);
  }

  function submitPacketForm(data) {
    addOrEditPacket(data).then(data => {
      message.info('操作成功');
    });
  }
  return (
    <div style={{ padding: 30 }}>
      {
        activityList.length ? activityList.map((formData, index) => <PacketFrom submit={submitPacketForm} data={formData} key={index}/>) :
          <p className={'f-tc'}>暂无红包活动~</p>
      }
      <Button type={'primary'} onClick={addActivityListItem}>新增</Button>
    </div>
  );
}
