export interface ContentProps {
  backPreSubsceEnd: string,
  backPreSubsceStart: string,
  closePeriod: string,
  createTime: string,
  curOpenNum: string | number,
  custodian: string,
  editTime: string,
  expectIncomeRate: string | number,
  fundAgreementFile: File,
  fundManagerId: string,
  fundManagerName: string,
  fundRaiseFile: File,
  fundRelationFile: File,
  fundRiskPointFile: File,
  incomeDescribe: string,
  investStrategy: string,
  limitNum: string | number,
  limitSale: string | number,
  manageOrganization: string,
  openFreq: string,
  openRule: string,
  operateMethod: string,
  privateFundId: string,
  privateFundName: string,
  publicityEnd: string,
  publicityName: string,
  publicityPictureList: PublicityPicture[],
  publicityStart: string,
  publicityUrl: string,
  publicityVideo: string,
  raiseEnd: string,
  raiseStart: string,
  riskLevel: string,
  setEstabDate: string,
  status: Status,
  subsceOrigin: string,
  tag: string,
  warningLine: string,
}
export type File = {
  name: string;
  url: string
}

export type PublicityPicture = {
  pictureUrl: string;
  title: string
}

export enum Status {
  Up = '1',
  Down = '0'
}

export enum EditContentType {
  Add,
  Edit
}

export type columnsProps = {
  title: string,
  dataIndex?: string,
  key?: string,
  width?: string,
  render?: Function
}