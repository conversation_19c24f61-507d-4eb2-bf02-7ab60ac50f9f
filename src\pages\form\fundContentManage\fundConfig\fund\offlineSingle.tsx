import React, { useState, useEffect } from 'react';
import { Modal, Switch, Button, Popconfirm } from  'antd';

export default React.memo(function({
    isShow,
    newAllArticleOffline,
    setIsShow,
    isAll,
    setIsAll,
    save,
    upload
}: any) {

    return (
        <Modal 
        title="是否将该文章下线？" 
        visible={isShow} 
        width={800}
        onCancel={() =>{setIsShow(false)}}
        footer={
            <div className="u-r-middle">
                <Button type="primary" onClick={() => {setIsShow(false)}}>取消</Button>
                {
                    isAll
                    ?
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么？该操作会对所有基金生效！请谨慎操作！'}
                        onConfirm={upload}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button type="danger" >确认</Button>
                    </Popconfirm>
                    :
                    <Button type="primary" className="g-ml20" onClick={() => {
                        save()
                    }}>确认</Button>
                }
            </div>
        }
        >
            <div className="u-l-middle">
                <Switch 
                checked={isAll}
                onChange={setIsAll}
                className='g-mr20'
                />
                并同时对所有基金下线
            </div>
        </Modal>
    )
})