import React, { useState, useEffect } from 'react';
import 'moment/locale/zh-cn';
import styles from './index.less';
import api from 'api';
import classnames from 'classnames';
import { DraggableArea } from '@/components/DragTags';
import {
  Button,
  message,
  Tag,
  Switch,
  Popconfirm,
} from 'antd';
import moment from 'moment';
import EditDrawer from './edit';

const {
    postEliteHoldConfig,
    getEliteHoldConfig,
} = api;
message.config({
  duration: 3, // 持续时间
  maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
  top: 70, // 到页面顶部距离
});

export default function() {
    // 初始化持仓基金信息
    const initFundDetail = () => {
        return {
            fundCode: '',
            status: 'hold',
            amount: '',
            holdTime: null,
            clearTime: null,
        }
    };
    // 初始化行业信息
    const initIndustryDetail = () => {
        return {
            code: '',
            name: '',
            percent: '',
        }
    };
    // 初始化投资动态信息
    const initInvestmentTrend = () => {
        return {
            id: '', 
            title: '',
            absOfContent: '',
            picUrl: '',
            label: '',
            author: '',
            url: '',
        }
    };
    // 初始化表单信息
    const initFormData = () => {
        return {
            id: '',
            name: '',
            avatar: '',
            tags: '',
            profitRange: 'tMonth',
            holdDetailList: [initFundDetail(),initFundDetail(),initFundDetail()],
            strategyInfo: {
                strategy: 'qsls',
                title: '',
                desc: '',
                toolImg: '',
                toolUrl: '',
                intro: '',
                explain: '',
            },
            holdAnalysisList: [initIndustryDetail(),initIndustryDetail(),initIndustryDetail()],
            investmentTrends: [initInvestmentTrend(), initInvestmentTrend(),initInvestmentTrend()],
            creator: '',
            createTime: '',
            lastEditor: '',
            lastEditTime: '',
            switchStatus: false,
        }
    };

    // 账户列表
    const [accountList, setAccountList] = useState([]);
    // 添加/编辑面板类型。'0'添加，'1'编辑。
    const [drawerType, setDrawerType] = useState('0');
    // 是否显示添加/编辑面板。
    const [showDrawer, setShowDrawer] = useState(false);
    // 当前面板表单信息
    const [formData, setFormData] = useState(initFormData());
    const tableColumns = [
        {
          title: '账户ID',
          dataIndex: 'id',
          key: 'id',
          width: '10%',
        },
        {
          title: '账户名称',
          dataIndex: 'name',
          key: 'name',
          width: '12%',
        },
        {
          title: '账户标签',
          key: 'tags',
          render: (row, index) => (
            <div>
                {row.tags && row.tags?.split(/,|，/)?.filter(Boolean)?.map((tagItem, index2) => {
                    return (
                        <Tag color="red" key={index2}>
                            {tagItem}
                        </Tag>
                    );
                })}
            </div>
          ),
          width: '20%',
        },
        {
          title: '创建人',
          dataIndex: 'creator',
          key: 'creator',
          width: '8%',
        },
        {
          title: '创建时间',
          key: 'createTime',
          render: (row ,index) => (
            <div>{handleFormatTime(row.createTime, 'YYYY-MM-DD HH:mm:ss')}</div>
          ),
          width: '10%',
        },
        {
          title: '最后编辑人',
          dataIndex: 'lastEditor',
          key: 'lastEditor',
          width: '8%',
        },
        {
          title: '最后编辑时间',
          key: 'lastEditTime',
          render: (row ,index) => (
            <div>{handleFormatTime(row.lastEditTime, 'YYYY-MM-DD HH:mm:ss')}</div>
          ),
          width: '10%',
        },
        {
          title: '操作',
          key: 'options',
          render: (row ,index) => {
            return <div className={classnames('f-tl', 'u-l-middle')}>
                <Button type='primary' style={{marginRight: '12px'}} size="small" onClick={() => {handleEdit(row)}}>编辑</Button>
                <Popconfirm
                    title="是否确认删除？"
                    okText="确认"
                    cancelText="取消"
                    onConfirm={() => {
                        handleDelete(index);
                    }}
                >
                    <Button type='danger' style={{marginRight: '12px'}} size="small">删除</Button>
                </Popconfirm>
                <Switch checked={row.switchStatus} onChange={()=>{handleChangeSwicth(row, index)}}/>
            </div>
          },
          width: '20%',
        },
    ];

    // 将时间戳(毫秒)转为指定格式
    const handleFormatTime = (timeNum, formatRule) => {
        return moment(new Date(Number(timeNum))).format(formatRule);
    }
    // 切换账户上线/下线开关
    const handleChangeSwicth = (row, index) => {
        const _accountList = [...accountList];
        _accountList[index].switchStatus = !_accountList[index].switchStatus;
        handlePostEliteHoldConfig(_accountList);
    };
    // 新增账户
    const handleAddAccount = () => {
        setDrawerType('0');
        setShowDrawer(true);
    }
    // 关闭添加/编辑面板
    const closeDrawer = () => {
        setShowDrawer(false);
        setFormData(initFormData());
    }
    // 获取虚拟账户列表
    const handleGetEliteHoldConfig = () => {
        setAccountList([]);
        getEliteHoldConfig().then(res => {
            try {
              const _data = res?.data ? JSON.parse(res?.data) : [];
              setAccountList(_data);
            } catch (error) {
              console.log(error);
            }
          });
    }
    // 编辑
    const handleEdit = (row) => {
        setDrawerType('1');
        setShowDrawer(true);
        setFormData(row);
    }
    // 删除
    const handleDelete = (index) => {
        const _accountList = [...accountList];
        _accountList.splice(index, 1);
        handlePostEliteHoldConfig(_accountList);
    }
    // 保存
    const handlePostEliteHoldConfig = (data) => {
        postEliteHoldConfig({
            value: JSON.stringify(data),
        })
        .then(res => {
            if (res?.code !== '0000') {
                message.error('操作失败');
                console.log(res);
            } else {
                message.success('操作成功！');
                closeDrawer();
                setAccountList(data);
            }
        });
    };

    useEffect(() => {
        handleGetEliteHoldConfig();
    }, [])
    
    return (
        <div className={classnames(styles['elite-hold'])}>
            {/* 页面标题 */}
            <div style={{fontSize: '30px', fontWeight: 'bold', marginBottom: '10px',textAlign: 'center'}}>虚拟账户配置</div>
            {/* 新增账户 */}
            <Button type="primary" size="default" style={{marginBottom: '20px'}} onClick={()=>{handleAddAccount()}}>新增账户</Button>
            {/* 表头 */}
            <div className={classnames('f-tl', 'u-l-middle')} style={{width: 1650, height:50, fontSize: 16, fontWeight:600, background: '#FAFAFA'}}>
                {
                    tableColumns.map((item, index) => {
                        return (
                            <div style={{width: item.width}}>{item.title}</div>
                        )
                    })
                }
            </div>
            {/* 账户列表 */}
            <DraggableArea
                isList
                tags={accountList}
                render={({tag, index}) => {
                    return (
                        <div className={styles['tag']} style={{width: 1650}}>
                            <div className={classnames(styles['m-row'], 'f-tl', 'u-l-middle')} style={{width: '100%', height: 50}}>
                                {
                                    tableColumns.map(item => {
                                        return (
                                            <div key={item.key} style={{width: item.width, overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>
                                                {item.render ? item.render(tag, index) : tag[item.dataIndex]}
                                            </div>
                                        )
                                    })
                                }
                            </div> 
                        </div> 
                    )
                }}
                onChange={(data) => {
                    setAccountList(data);
                    handlePostEliteHoldConfig(data);
                }}
            />
            {/* 新增/编辑面板 */}
            <EditDrawer 
                drawerType={drawerType}
                show={showDrawer}
                closeDrawer={closeDrawer}
                accountList={accountList}
                setAccountList={setAccountList}
                accountInfo={formData}
                initFundDetail={initFundDetail}
                initIndustryDetail={initIndustryDetail}
                initInvestmentTrend={initInvestmentTrend}
                handlePostEliteHoldConfig={handlePostEliteHoldConfig}
            />
        </div>
    );
}
