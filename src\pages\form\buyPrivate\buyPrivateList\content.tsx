import {
    Form,
    Input,
    Row,
    Col,
    Upload,
    message,
    Select,
    Button,
    DatePicker,
    InputNumber,
    Spin,
    Modal
} from 'antd';
import api from 'api';
import styles from './content.less'
import { FormComponentProps } from 'antd/es/form';
import { FILES, STRATEGYS } from './config'
import React, { forwardRef } from 'react';
import UploadImg from '@/pages/frontend/compoment/uploadImg';
import moment from 'moment';
import { dateFormat } from '@/utils/utils';

const { Option } = Select;
const { TextArea } = Input;

interface DrawerFormProps extends FormComponentProps {
    currentData: any;
    noticeInfo: {[index:string]: {name:string;url:string}[]};
    queryAllData: () => void;
    onEditClose: () => void;
    [key: string]: any;
}
const UploadComponent: any = forwardRef<any, any>((props, _ref) => {
    const { uploadImage, imgUrl } = props;
    return (
        <UploadImg
            handleChange={uploadImage}
            imageUrl={imgUrl}
            isEdit={true}
        />
    )
});

class messageForm extends React.Component<DrawerFormProps, any> {
    constructor(props: DrawerFormProps) {
        super(props);
        const privateFundId = props.currentData.privateFundId
        const currentNoticeList = props.noticeInfo[privateFundId] || []
        let initFiles = currentNoticeList.length>0 ? [] : [{
          label: '公告文件',
          val: 'fundNoticeFile1',
          required: false,
          isAdd: true
        }]
        const initNoticeObj = currentNoticeList.reduce((pre,cur,index)=>{
          const val = `fundNoticeFile${index+1}`
          const obj = {
            label: '公告文件',
            val,
            required: false,
            isAdd: true
          }
          pre[val] = cur
          initFiles.push(obj)
          return pre
        },{})
        this.state = {
            formData: {...props.currentData,...initNoticeObj} || {},
            loading: false,
            ifindData: {}, //后端需要额外保存的参数，不和表单挂钩
            newFiles: [...FILES, ...initFiles]
        }
    }

    formItemLayout = {
        labelCol: {
            span: 5
        },
        wrapperCol: {
            span: 19
        },
    };
    handleDate = (time: string) => {
        if (!time) return time;
        return `${time.slice(0, 4)}-${time.slice(4, 6)}-${time.slice(6, 8)}`
    }
    handleSubmit = (e: any) => {
        e.preventDefault();
        this.props.form.validateFields((err, values) => {
            if (!err) {
                const { formData } = this.state;
                const _saveValues = { ...formData, ...values }
                // console.log(_saveValues, '_saveValues')
                const _tags = _saveValues.tag.replace(/；/g, ';').split(';')
                if (_tags.length > 3) {
                    message.error('不能超过三个标签')
                    return
                }
                for (let i in _tags) {
                    if (_tags[i].length > 5) {
                        message.error('请检查标签,每个标签不能超过5个字')
                        return
                    }
                }
                if (_saveValues.publicityPictureList.length === 0) {
                    message.error('至少上传一个宣传图片')
                    return
                }
                for (let i in _saveValues.publicityPictureList) {
                    if (!_saveValues.publicityPictureList[i].pictureUrl) {
                        message.error('请上传宣传图片')
                        return
                    }
                }
                for (let i in this.state.newFiles) {
                    const _key = this.state.newFiles[i].val
                    if (_saveValues[_key]?.url && !_saveValues[_key]?.name || !_saveValues[_key]?.url && _saveValues[_key]?.name) {
                        message.error('请确保文件名称和文件链接都已添加')
                        return
                    }
                }
                if (Date.parse(_saveValues.publicityStart) > Date.parse(_saveValues.publicityEnd)) {
                    message.error('公告开始时间不允许大于公告结束时间')
                    return
                }
                this.handleSave(_saveValues);
            } else {
                message.error('请检查必填项')
            }
        });
    };
    handleSave = (values: any) => {
        const _ajaxMethod: string = this.props.edit === 0 ? 'buyPrivateAdd' : 'buyPrivateEdit';
        const _val = { ...values, ...this.state.ifindData }
        _val.publicityStart = this.handleDate(dateFormat(_val.publicityStart))
        _val.publicityEnd = this.handleDate(dateFormat(_val.publicityEnd))
        _val.setEstabDate = this.handleDate(dateFormat(_val.setEstabDate))
        const noticeList = Object.keys(values).reduce((pre,cur)=>{
          const index = this.state.newFiles.findIndex(item=>item.val === cur)
          if(cur?.includes('fundNoticeFile') && index > -1){
            return pre.concat(values[cur])
          }
          return pre
        },[])
        const key = values.privateFundId
        const query = {
          [key] : noticeList
        }
        const promise1 = api[_ajaxMethod]({ privateOfferFundDtoStr: JSON.stringify(_val) })
        const promise2 = api['postGDLCPrivateFundNoticeConfig']({ value: JSON.stringify({...this.props.noticeInfo,...query}) })
        Promise.all([promise1, promise2]).then(([res,res2])=>{
          if (res?.code !== '0000') {
            Modal.error({ content: res?.message || '保存失败，请稍后重试' });
            return;
          }
          if (res2?.code !== '0000') {
            Modal.error({ content: '公告文件保存失败，请稍后重试' });
            return;
          }
          this.props.queryAllData();
          this.props.onEditClose();
        })
    }

    componentDidMount() {
        if (this.props.edit === 0) {
            this.props.form.resetFields()
            this.setState({
                formData: { ...this.state.formData, ...{ publicityPictureList: [{ pictureUrl: '', title: '' }] } }
            })
        } else if (this.props.edit === 1) {
            this.sameWithIFind()
        }
    }

    changeTitle = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
        e.persist();
        const _publicityPictureList = this.state.formData.publicityPictureList
        _publicityPictureList.forEach((item: any, i: number) => {
            if (index === i) {
                item.title = e?.target?.value
            }
        })
        this.setState({
            formData: { ...this.state.formData, ...{ publicityPictureList: _publicityPictureList } }
        })
    }
    deletePic = (index: number) => {
        const _publicityPictureList = this.state.formData.publicityPictureList
        _publicityPictureList.splice(index, 1)
        this.setState({
            formData: { ...this.state.formData, ...{ publicityPictureList: _publicityPictureList } }
        })
    }
    uploadPic = (val: string, index: number) => {
        const _publicityPictureList = this.state.formData.publicityPictureList
        _publicityPictureList.forEach((item: any, i: number) => {
            if (index === i) {
                item.pictureUrl = val
            }
        })
        this.setState({
            formData: { ...this.state.formData, ...{ publicityPictureList: _publicityPictureList } }
        })
    }
    addPic = () => {
        const _publicityPictureList = this.state.formData.publicityPictureList
        _publicityPictureList.push({ pictureUrl: '', title: '' })
        this.setState({
            formData: { ...this.state.formData, ...{ publicityPictureList: _publicityPictureList } }
        })
    }

    // 同步ifind数据
    sameWithIFind = () => {
        const { getFieldValue, setFieldsValue } = this.props.form
        const _privateFundId = getFieldValue('privateFundId')
        if (!_privateFundId) {
            message.warning('请输入代码');
            return
        }
        this.setState({ loading: true })
        api.buyPrivateGetFromIfind({ privateFundId: _privateFundId }).then((res: any) => {
            if (res?.code !== '0000') {
                message.error(res?.message || '请求错误');
                return;
            }
            const _data = res?.data;
            // console.log(_data, 'ifind')
            this.setState({
                ifindData: {
                    openFreq: _data?.openFreq,
                    subsceOrigin: _data?.subsceOrigin,
                    backPreSubsceStart: _data?.preSubsceStart,
                    backPreSubsceEnd: _data?.preSubsceEnd,
                    productType: _data?.productType
                }
            })
            setFieldsValue({
                riskLevel: _data?.productRiskLevel,
                fundManagerName: _data?.fundManagerName,
                manageOrganization: _data?.managerOrg,
                custodian: _data?.costodyOrg,
                warningLine: _data?.warnLine,
                raiseStart: _data?.subsceStart,
                raiseEnd: _data?.subsceEnd,
                stopLossLine: _data?.stopLossLine,
                preSubsceStart: _data?.preSubsceStart,
                preSubsceEnd: _data?.preSubsceEnd,
                preRedeemEnd: _data?.preRedeemEnd,
                preRedeemStart: _data?.preRedeemStart,
                openPeriod: _data?.openPeriod,
                closePeriod: _data?.closePeriod
            })
        }).finally(() => {
            this.setState({ loading: false })
        })
    }

    // 上传跳转按钮
    uploadButton = () => {
        return <Button type="link" onClick={() => { window.open(`http://${
            window.location.href.indexOf('8080') !== -1 ||
            window.location.href.indexOf('8000') !== -1
              ? 'febs.5ifund.com:8080/yytj/'
              : '172.20.205.130/yytj/index.html'
          }#/form/uploadFile`) }}>上传</Button>
    }

    // 新增公告文件
    handleAddNotice = () => {
      const lastItem = this.state.newFiles.slice(-1)[0]
      let val = ''
      if(lastItem.length === 0){
        val = 'fundNoticeFile1'
      }else{
        val = lastItem.val.slice(0,-1) + (parseInt(lastItem.val.slice(-1)) + 1)
      }
      const obj = {
        label: '公告文件',
        val,
        required: false,
        isAdd: true
      }
      this.state.newFiles.push(obj)
      this.setState({...this.state, newFiles: this.state.newFiles})
    }

    // 删除公告文件
    handleDelNotice = (item) => {
        const index = this.state.newFiles.findIndex(n=>n.val === item.val)
        this.state.newFiles.splice(index,1)
        this.setState({...this.state, newFiles: this.state.newFiles})
    }

    render() {
        const { getFieldDecorator } = this.props.form;
        const { formData } = this.state;
        return (
            <>
                <Form {...this.formItemLayout} onSubmit={this.handleSubmit} className={styles['m-buy-private_detail']}>
                    <>
                        <Spin spinning={this.state.loading}>
                            <Form.Item style={{ textAlign: 'right' }} wrapperCol={{ span: 24 }}>
                                <Button type="primary" onClick={this.sameWithIFind}>同步ifind数据</Button>
                            </Form.Item>
                            <Form.Item label="代码" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('privateFundId', {
                                    initialValue: formData.privateFundId,
                                    rules: [{ required: true, message: '请输入组合名称' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="产品名称" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('privateFundName', {
                                    initialValue: formData.privateFundName,
                                    rules: [
                                        { required: true, message: '请输入产品名称' }
                                    ],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item required label="风险等级" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('riskLevel', {
                                    initialValue: formData.riskLevel,
                                    rules: [{ required: true, message: '请输入风险等级' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item label="投资策略">
                                {getFieldDecorator('investStrategy', {
                                    initialValue: formData.investStrategy,
                                    rules: [{ required: true, message: '请选择投资策略' }],
                                })(
                                    <Select style={{ width: 120 }}>
                                        {
                                            STRATEGYS.map((item: string) => {
                                                return (
                                                    <Option key={item} value={item}>{item}</Option>
                                                )
                                            })
                                        }
                                    </Select>
                                )}
                            </Form.Item>
                            <Form.Item required label="基金经理" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('fundManagerName', {
                                    initialValue: formData.fundManagerName,
                                    rules: [{ required: true, message: '请输入基金经理' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required label="基金管理人" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('manageOrganization', {
                                    initialValue: formData.manageOrganization,
                                    rules: [{ required: true, message: '请输入基金管理人' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item label="公告" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('publicityName', {
                                    initialValue: formData.publicityName
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="链接" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('publicityUrl', {
                                    initialValue: formData.publicityUrl
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="公告开始时间" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('publicityStart', {
                                    initialValue: formData.publicityStart ? moment(formData.publicityStart) : null
                                })(
                                    <DatePicker />
                                )}
                            </Form.Item>
                            <Form.Item label="公告结束时间" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('publicityEnd', {
                                    initialValue: formData.publicityEnd ? moment(formData.publicityEnd) : null
                                })(
                                    <DatePicker />
                                )}
                            </Form.Item>
                            <Form.Item required label="托管机构" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('custodian', {
                                    initialValue: formData.custodian,
                                    rules: [{ required: true, message: '请输入托管机构' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required label="预警线" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('warningLine', {
                                    initialValue: formData.warningLine,
                                    rules: [{ required: true, message: '请输入预警线' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required label="募集开始日期" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('raiseStart', {
                                    initialValue: formData.raiseStart,
                                    rules: [{ required: true, message: '请输入募集开始日期' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required label="募集结束日期" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('raiseEnd', {
                                    initialValue: formData.raiseEnd,
                                    rules: [{ required: true, message: '请输入募集结束日期' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item label="产品成立日" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('setEstabDate', {
                                    initialValue: formData.setEstabDate ? moment(formData.setEstabDate) : null,
                                    rules: [{ required: true, message: '请选择产品成立日' }]
                                })(
                                    <DatePicker />
                                )}
                            </Form.Item>
                            <Form.Item required label="止损线" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('stopLossLine', {
                                    initialValue: formData.stopLossLine,
                                    rules: [{ required: true, message: '请输入止损线' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required className="m-buy-private_label" label="预约申购开始日（最新一期，从ifind自动获取数据）" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('preSubsceStart', {
                                    initialValue: formData.preSubsceStart,
                                    rules: [{ required: true, message: '请输入预约申购开始日' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required className="m-buy-private_label" label="预约申购结束日（最新一期，从ifind自动获取数据）" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('preSubsceEnd', {
                                    initialValue: formData.preSubsceEnd,
                                    rules: [{ required: true, message: '请输入预约申购结束日' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required className="m-buy-private_label" label="开放日（最新一期，从ifind自动获取数据）" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('openPeriod', {
                                    initialValue: formData.openPeriod,
                                    rules: [{ required: true, message: '请输入开放日' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required className="m-buy-private_label" label="预约赎回开始日（最新一期，从ifind自动获取数据）" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('preRedeemStart', {
                                    initialValue: formData.preRedeemStart,
                                    rules: [{ required: true, message: '请输入预约赎回开始日' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required className="m-buy-private_label" label="预约赎回结束日（最新一期，从ifind自动获取数据）" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('preRedeemEnd', {
                                    initialValue: formData.preRedeemEnd,
                                    rules: [{ required: true, message: '请输入预约赎回结束日' }]
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item className="m-buy-private_label" label="标签（;分割,自由填写,最多三个标签,每个不超过5字）" wrapperCol={{ span: 12 }}>
                                {getFieldDecorator('tag', {
                                    initialValue: formData.tag,
                                    rules: [{ required: true, message: '请输入标签' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="开放规则" wrapperCol={{ span: 12 }}>
                                {getFieldDecorator('openRule', {
                                    initialValue: formData.openRule,
                                    rules: [{ required: true, message: '请输入开放规则' }],
                                })(
                                    <TextArea autoSize={{ minRows: 2 }} maxLength={300} />
                                )}
                            </Form.Item>
                            <Form.Item label="收益描述" wrapperCol={{ span: 12 }}>
                                {getFieldDecorator('incomeDescribe', {
                                    initialValue: formData.incomeDescribe,
                                    rules: [{ required: true, message: '请输入收益描述' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="预期收益率" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('expectIncomeRate', {
                                    initialValue: formData.expectIncomeRate,
                                    rules: [{ required: true, message: '请输入预期收益率' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="销售限额" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('limitSale', {
                                    initialValue: formData.limitSale,
                                    rules: [
                                        { required: true, message: '请输入销售限额' },
                                        { pattern: /^([1-9][0-9]*(\.\d{1,2})?)|(0\.\d{1,2})$/, message: '请输入正数' }
                                    ],
                                })(
                                    <InputNumber />
                                )}
                            </Form.Item>
                            <Form.Item label="销售人数限制" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('limitNum', {
                                    initialValue: formData.limitNum,
                                    rules: [
                                        { required: true, message: '请输入销售人数限制' },
                                        { pattern: /^[0-9]*$/, message: '请输入正整数' }
                                    ],
                                })(
                                    <InputNumber />
                                )}
                            </Form.Item>
                            <Form.Item label="运作状态" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('operateMethod', {
                                    initialValue: formData.operateMethod,
                                    rules: [{ required: true, message: '请输入运作状态' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="开放名额" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('curOpenNum', {
                                    initialValue: formData.curOpenNum,
                                    rules: [
                                        { required: true, message: '请输入开放名额' },
                                        { pattern: /^[0-9]*$/, message: '请输入正整数' }
                                    ],
                                })(
                                    <InputNumber />
                                )}
                            </Form.Item>
                            <Form.Item required label="封闭期" wrapperCol={{ span: 8 }}>
                                {getFieldDecorator('closePeriod', {
                                    initialValue: formData.closePeriod,
                                    rules: [{ required: true, message: '请输入封闭期' }],
                                })(
                                    <Input disabled />
                                )}
                            </Form.Item>
                            <Form.Item required label="宣传图片" wrapperCol={{ span: 12 }}>
                                {(!formData.publicityPictureList || formData.publicityPictureList?.length < 3) && <Button type="primary" onClick={this.addPic}>添加</Button>}

                                {
                                    formData.publicityPictureList && formData.publicityPictureList.map((item, index: number) => {
                                        return (
                                            <div key={index} className={styles['m-buy-private_image']}>
                                                <Row>
                                                    <Col span={18}>
                                                        <div className="u-flex m-buy-private_lines">
                                                            <Form.Item label={'宣传图片标题' + (index + 1)}>
                                                                <Input value={formData.publicityPictureList[index].title} onChange={(e) => { this.changeTitle(e, index) }} />
                                                            </Form.Item>
                                                        </div>
                                                    </Col>
                                                    <Col span={6}>
                                                        <Button type="danger" onClick={() => { this.deletePic(index) }}>删除</Button>
                                                    </Col>
                                                </Row>
                                                <Form.Item label={'宣传图片' + (index + 1)}>
                                                    <UploadComponent uploadImage={(val: string) => { this.uploadPic(val, index) }} imgUrl={formData.publicityPictureList[index].pictureUrl} />
                                                </Form.Item>
                                            </div>
                                        )
                                    })
                                }
                            </Form.Item>
                            <Form.Item label='宣传视频链接' wrapperCol={{ span: 8 }}>
                                <div className="u-flex">
                                    {getFieldDecorator('publicityVideo', {
                                        initialValue: formData.publicityVideo,
                                    })(
                                        <Input />
                                    )}
                                    {this.uploadButton()}
                                </div>
                            </Form.Item>
                            {
                                this.state.newFiles.map((item) => {
                                    return (
                                        <Form.Item key={item.val} required={item.required} label={item.label} wrapperCol={{ span: 18 }}>
                                            <div className="u-flex m-buy-private_lines">
                                                <Form.Item label='文件名称' style={{ marginRight: '10px' }}>
                                                    {getFieldDecorator(`${item.val}.name`, {
                                                        initialValue: formData[item.val]?.name,
                                                        rules: [{ required: item.required, message: '请输入文件名' }],
                                                    })(
                                                        <Input />
                                                    )}
                                                </Form.Item>
                                                <Form.Item label='文件链接'>
                                                    {getFieldDecorator(`${item.val}.url`, {
                                                        initialValue: formData[item.val]?.url,
                                                        rules: [{ required: item.required, message: '请输入文件链接' }],
                                                    })(
                                                        <Input />
                                                    )}
                                                </Form.Item>
                                                {this.uploadButton()}
                                                {
                                                  item.isAdd &&
                                                  <>
                                                    <Button type={'primary'} onClick={this.handleAddNotice}>新增</Button>
                                                    <Button type={'danger'} onClick={() => this.handleDelNotice(item)}>删除</Button>
                                                  </>
                                                }
                                            </div>
                                        </Form.Item>
                                    )
                                })
                            }
                            <Form.Item label="产品特征" wrapperCol={{ span: 12 }}>
                                {getFieldDecorator('feature', {
                                    initialValue: formData.feature,
                                    rules: [{ required: false, message: '请输入产品特征' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="推荐理由(橙色)" wrapperCol={{ span: 12 }}>
                                {getFieldDecorator('firstReason', {
                                    initialValue: formData.firstReason,
                                    rules: [{ required: false, message: '请输入推荐理由(橙色)' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="推荐理由(灰色)" wrapperCol={{ span: 12 }}>
                                {getFieldDecorator('secondReason', {
                                    initialValue: formData.secondReason,
                                    rules: [{ required: false, message: '请输入推荐理由(灰色)' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                        </Spin>
                    </>
                    <Form.Item style={{ textAlign: 'right' }} wrapperCol={{ span: 24 }}>
                        <Button type="primary" htmlType="submit">
                            保存
                        </Button>
                    </Form.Item>
                </Form>
            </>

        )
    }
}
const WrappedMessageForm = Form.create<DrawerFormProps>({ name: 'message' })(messageForm);
export default WrappedMessageForm
