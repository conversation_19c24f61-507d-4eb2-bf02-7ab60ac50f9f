{"title": "模型执行", "body": [{"type": "group", "mode": "horizontal", "body": [{"type": "input-text", "name": "userTag", "label": "跑模型次数", "mode": "horizontal", "clearable": true, "size": "sm", "required": true, "options": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}]}, {"type": "button", "className": "mt-2", "label": "批量生成", "level": "primary", "onEvent": {"click": {"actions": [{"actionType": "ajax", "api": {"url": "/aigc/audit/approve", "method": "post", "data": {"approve": "1", "idList": "${GETRENDERERDATA(crud_left, selectedItems)}"}}}]}}}]}, {"type": "grid", "columns": [{"type": "crud", "id": "crud_left", "syncLocation": false, "columns": [{"name": "id", "label": "图片使用词（用户侧）", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "industryLabel", "label": "图片使用词（基金侧）", "type": "text", "placeholder": "-", "className": "text-lg"}], "headerToolbar": [{"type": "bulkActions", "align": "right"}, {"type": "group", "label": "图片长宽（单位px）", "mode": "horizontal", "required": true, "body": [{"type": "input-text", "name": "p<PERSON><PERSON><PERSON><PERSON>", "placeholder": "图片长度"}, {"type": "input-text", "name": "picHeight", "placeholder": "图片宽度"}]}], "bulkActions": [{"label": "", "actionType": "ajax", "className": "width-0", "api": {"url": "/aigc/audit/approve", "method": "post", "data": {"approve": "1", "idList": "${ARRAYMAP(items, item => item.id)}"}}}], "footerToolbar": ["switch-per-page", "pagination", "statistics"], "columnsTogglable": false, "keepItemSelectionOnPageChange": true, "checkOnItemClick": true, "alwaysShowPagination": true, "className": "text-lg", "pageField": "currentPage", "perPageField": "pageSize", "showPerPage": true, "showPageInput": true, "perPageAvailable": [40, 100, 200, 500], "perPage": 40, "api": {"url": "/aigc/audit/page", "method": "post", "data": {"userLabel": "${userTag|default:undefined}", "industryLabel": "${contentWord|default:undefined}", "auditStatus": "${auditStatus|default:undefined}", "urlContent": "${title|default:undefined}", "fileType": "0", "offSet": "${currentPage}", "pageSize": "${pageSize}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.message,\r\n  data: payload.data\r\n}"}}, {"type": "crud", "syncLocation": false, "columns": [{"name": "userLabel", "label": "文本使用词（用户侧）", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "industryLabel", "label": "文本使用词（基金侧）", "type": "text", "placeholder": "-", "className": "text-lg"}], "headerToolbar": [{"type": "bulkActions", "align": "right"}, {"type": "group", "label": "文本长度（字数）", "mode": "horizontal", "required": true, "body": [{"type": "input-text", "name": "contentLengthMin", "placeholder": "最小"}, {"type": "input-text", "name": "contentLengthMax", "placeholder": "最大"}]}], "bulkActions": [{"label": "批量生成", "hidden": true, "actionType": "ajax", "api": {"url": "/aigc/audit/approve", "method": "post", "data": {"approve": "1", "idList": "${ARRAYMAP(items, item => item.id)}"}}}], "footerToolbar": ["switch-per-page", "pagination", "statistics"], "columnsTogglable": false, "keepItemSelectionOnPageChange": true, "checkOnItemClick": true, "alwaysShowPagination": true, "bodyClassName": "", "className": "text-lg", "pageField": "currentPage", "perPageField": "pageSize", "showPerPage": true, "showPageInput": true, "perPageAvailable": [40, 100, 200, 500], "perPage": 40, "api": {"url": "/aigc/audit/page", "method": "post", "data": {"userLabel": "${userTag|default:undefined}", "industryLabel": "${contentWord|default:undefined}", "auditStatus": "${auditStatus|default:undefined}", "urlContent": "${title|default:undefined}", "fileType": "0", "offSet": "${currentPage}", "pageSize": "${pageSize}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.message,\r\n  data: payload.data\r\n}"}}]}]}