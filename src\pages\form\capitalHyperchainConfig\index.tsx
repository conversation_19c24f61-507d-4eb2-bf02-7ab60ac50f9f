import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'form-render/lib/antd';
import api from 'api';
import { Button, message } from 'antd';

const { fetchCapitalHyperchain, postCapitalHyperchain } = api;

let FORM_CONFIG: any = {
  propsSchema: {
    type: 'object',
    displayType: 'row',
    properties: {
      nerThreshold: {
        title: 'Ner分值(数值:-10～10)',
        type: 'number',
        max: 10,
        min: -10,
      },
      linkingThreshold: {
        title: 'Linking分值(数值:-10～10)',
        type: 'number',
        max: 10,
        min: -10,
      },
      wordThreshold: {
        title: '匹配度分值(数值:-10～10)',
        type: 'number',
        max: 10,
        min: -10,
      },
    },
    required: ['wordThreshold', 'linkingThreshold', 'nerThreshold'],
  },
};

//
export default function() {
  const [init, setInit] = useState(false);
  const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  const [formConfig, setFormConfig] = useState({});

  useEffect(() => {
    fetchCapitalHyperchain()
      .then((res: any) => {
        try {
          res = JSON.parse(res.data);
          if (res) {
            FORM_CONFIG.formData = {
              ...res,
            };
          }
        } catch (e) {
          console.warn(e);
        }
        setInit(true);
        setFormConfig(FORM_CONFIG);
        setData(FORM_CONFIG.formData);
      })
      .catch((e: Error) => {
        message.error(e.message);
      });
  }, []);

  const onSubmit = () => {
    if (valid.length > 0) {
      message.error(`校验未通过字段：${valid.toString()}`);
    } else {
      let _postData = {
        ...formData,
      };
      postCapitalHyperchain({
        value: JSON.stringify(_postData),
      }).then((res: any) => {
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success('保存成功！');
          }
        } catch (e) {
          message.error(e.message);
        }
      });
    }
  };

  if (!init) return '加载中';
  return (
    <div style={{ padding: 60 }}>
      <FormRender
        propsSchema={FORM_CONFIG.propsSchema}
        formData={formData}
        showDescIcon={true}
        onChange={setData}
        onValidate={setValid}
      />
      <p>
        <span style={{ color: 'red' }}>* </span>
        匹配度分规则：将算法标出的实体词与基金简称进行匹配，计算两项一致字数占基金简称的占比，规则会对低于配置阈值分的结果进行过滤。该分值用于确保不出现完全无关的结果
      </p>
      <Button type="primary" onClick={onSubmit}>
        保存
      </Button>
    </div>
  );
}
