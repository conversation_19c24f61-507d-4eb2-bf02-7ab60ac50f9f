export const SCHEMA1 = {
    "type": "object",
    "required": [
      "activityId",
      "title",
      "subTitle",
      "tipTitle",
      "recommendCode",
      "marketId",
      "profitDescribe",
      "productTitle",
      "purchaseContext",
      "windowText",
      "cardType",
      "type",
      "activityName",
      "startDate",
      "endDate",
      "status"
    ],
    "properties": {
      "activityId": {
        "title": "活动id",
        "type": "string",
        "pattern": /^\w+$/,
        "description": "需要用英文输入，唯一标识符，不能重复",
        "ui:options": {},
        "ui:disabled": false
      },
      "title": {
        "title": "标题",
        "type": "string",
        "ui:options": {},
        "maxLength": 10
      },
      "subTitle": {
        "title": "副标题",
        "type": "string",
        "ui:options": {}
      },
      "tipTitle": {
        "title": "小知识标题",
        "type": "string",
        "ui:options": {}
      },
      "tipContext": {
        "title": "小知识内容",
        "type": "string",
        "format": "textarea",
        "ui:options": {}
      },
      "cardType": {
        "title": "卡片类型",
        "type": "string",
        "enum": [
          "newFund",
          "continueFund"
        ],
        "enumNames": [
          "新发基金（无收益）",
          "持营基金（有收益）"
        ],
        "ui:widget": "radio"
      },
      "recommendTitle": {
        "title": "推荐标题",
        "type": "string",
        "ui:options": {}
      },
      "recommendCode": {
        "title": "推荐基金代码",
        "type": "string",
        "pattern": /^[A-Za-z0-9]{6}$/,
        "ui:width": "50%",
        "ui:options": {}
      },
      "marketId": {
        "title": "市场号",
        "type": "number",
        "pattern": /^[0-9]+$/,
        "ui:width": "50%",
        "ui:options": {}
      },
      "recommendName": {
        "title": "推荐基金名称",
        "type": "string",
        "ui:width": "50%",
        "ui:options": {}
      },
      "profitDescribe": {
        "title": "收益区间",
        "type": "now",
        "default": "year",
        "ui:hidden": "{{rootValue.cardType === 'newFund'}}",
        "enum": [
          "year",
          "now",
          "week",
          "month",
          "tmonth",
          "hyear",
          "twoyear",
          "tyear",
          "fyear",
          "nowyear"
        ],
        "enumNames": [
          "近一年涨幅-year",
          "成立以来涨幅-now",
          "近一周涨幅-week",
          "近一月涨幅-month",
          "近三月涨幅-tmonth",
          "近半年涨幅-hyear",
          "近两年涨幅-towyear",
          "近三年涨幅-tyear",
          "近五年涨幅-fyear",
          "今年以来涨幅-nowyear"
        ]
      },
      "buyNum": {
        "title": "购买人数",
        "type": "string",
        "ui:options": {}
      },
      "productTitle": {
        "ui:hidden": "{{rootValue.cardType === 'continueFund'}}",
        "title": "产品推荐标题",
        "type": "string",
        "description": "用逗号隔开",
        "ui:options": {}
      },
      "purchaseContext": {
        "title": "购买按钮文案",
        "type": "string",
        "ui:options": {}
      },
      "windowText": {
        "title": "悬浮窗文案",
        "type": "string",
        "ui:options": {}
      },
      "activityRules": {
        "title": "规则",
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "text": {
              "title": "",
              "type": "string",
              "ui:options": {}
            }
          }
        },
        "ui:options": {}
      },
      "type": {
        "title": "中奖类型",
        "type": "string",
        "enum": [
          "level2",
          "cash"
        ],
        "enumNames": [
          "兑换码",
          "现金"
        ],
        "ui:widget": "radio"
      },
      "codeRate": {
        "title": "兑换码中奖概率",
        "type": "number",
        "ui:hidden": "{{rootValue.type === 'cash'}}",
        "ui:options": {
          "addonAfter": "%"
        }
      },
      "cashVoucherList": {
        "title": "现金参数",
        "type": "array",
        "ui:hidden": "{{rootValue.type === 'level2'}}",
        "items": {
          "type": "object",
          "properties": {
            "money": {
              "title": "金额",
              "type": "string",
              "ui:width": "50%"
            },
            "rate": {
              "title": "概率",
              "type": "number",
              "ui:width": "50%"
            }
          }
        },
        "ui:options": {}
      },
      "noRewardDialogTitle": {
        "title": "未中奖弹窗标题",
        "type": "string",
        "ui:width": "50%"
      },
      "noRewardDialogFirstText": {
        "title": "未中奖弹窗第一行内容",
        "type": "string",
        "ui:width": "50%"
      },
      "noRewardDialogSecondText": {
        "title": "未中奖弹窗第二行内容",
        "type": "string",
        "ui:width": "50%"
      },
      "noRewardDialogBtnText": {
        "title": "未中奖弹窗按钮文案",
        "type": "string",
        "ui:width": "50%"
      },
      "guideAttentionUrl": {
        "title": "引导关注跳转链接",
        "type": "string",
        "ui:width": "100%"
      },
      "activityName": {
        "title": "活动名称",
        "type": "string",
        "ui:options": {}
      },
      "startDate": {
        "title": "活动开始时间",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%"
      },
      "endDate": {
        "title": "活动结束时间",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%"
      },
      "status": {
        "title": "状态",
        "type": "string",
        "enum": [
          "1",
          "0"
        ],
        "enumNames": [
          "正常",
          "异常"
        ],
        "ui:widget": "radio"
      },
      "exchangeCodeConfig": {
        "type": "object",
        "required": [
          "imgTitle",
          "imgDescription",
          "popTitle",
          "awardDescription",
          "guideDescription",
        ],
        "ui:hidden": "{{rootValue.type === 'cash'}}",
        "properties": {
          "imgTitle": {
            "title": "获奖图片第一行",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多5个汉字"
            }
          },
          "imgDescription": {
            "title": "获奖图片第二行",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多5个汉字"
            }
          },
          "popTitle": {
            "title": "兑换弹窗标题",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多8个汉字"
            }
          },
          "awardDescription": {
            "title": "弹窗获奖文案",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多14个汉字"
            }
          },
          "guideDescription": {
            "title": "弹窗引导文案",
            "type": "string",
            "ui:options": {
              "placeholder": "最多29个汉字"
            }
          },
          "exchangeOpen": {
            "title": "是否需要兑换码转换页面",
            "type": "string",
            "enum": [
              "1",
              "0"
            ],
            "enumNames": [
              "需要",
              "不需要"
            ],
            "ui:widget": "radio"
          },
          "exchangeChangeConfig": {
            "title": '兑换码转换页面配置(页面地址：https://fund.10jqka.com.cn/ifundapp_web/public/hq/exchangeCode/dist/index.html?activityId={activityId})',
            "type": "object",
            "ui:hidden": "{{rootValue.exchangeOpen === '0'}}",
            "required": [
              "conductDescription",
              "awardDescription",
              "activityRule",
            ],
            "properties": {
              "conductDescription": {
                "title": "宣传文案",
                "type": "string",
                "ui:options": {
                  "placeholder": "最多12个汉字"
                }
              },
              "awardDescription": {
                "title": "获奖文案",
                "type": "string",
                "ui:options": {
                  "placeholder": "最多14个汉字"
                }
              },
              "buttonDescription": {
                "title": "按钮文案",
                "type": "string",
                "ui:options": {
                  "placeholder": "最多12个汉字"
                }
              },
              "buttonType": {
                "title": "按钮跳转类型",
                "type": "string",
                "enum": ['ths', 'h5'],
                "enumNames": ['同花顺', 'h5页面'],
                "ui:width": "30%"
              },
              "buttonUrl": {
                "title": "按钮跳转url",
                "type": "string",
                "ui:width": "70%"
              },
              "activityRule": {
                "title": "活动规则",
                "type": "string",
                "format": "textarea",
              },
            }
          }
        }
      }
    }
  }
  

  export const SCHEMA2 = {
    "type": "object",
    "required": [
      "activityId",
      "title",
      "subTitle",
      "tipTitle",
      "recommendCode",
      "marketId",
      "profitDescribe",
      "productTitle",
      "purchaseContext",
      "windowText",
      "cardType",
      "type",
      "activityName",
      "startDate",
      "endDate",
      "status"
    ],
    "properties": {
      "activityId": {
        "title": "活动id",
        "type": "string",
        "pattern": /^\w+$/,
        "description": "需要用英文输入，唯一标识符，不能重复",
        "ui:options": {},
        "ui:disabled": true
      },
      "title": {
        "title": "标题",
        "type": "string",
        "ui:options": {},
        "maxLength": 10
      },
      "subTitle": {
        "title": "副标题",
        "type": "string",
        "ui:options": {}
      },
      "tipTitle": {
        "title": "小知识标题",
        "type": "string",
        "ui:options": {}
      },
      "tipContext": {
        "title": "小知识内容",
        "type": "string",
        "format": "textarea",
        "ui:options": {}
      },
      "cardType": {
        "title": "卡片类型",
        "type": "string",
        "enum": [
          "newFund",
          "continueFund"
        ],
        "enumNames": [
          "新发基金（无收益）",
          "持营基金（有收益）"
        ],
        "ui:widget": "radio"
      },
      "recommendTitle": {
        "title": "推荐标题",
        "type": "string",
        "ui:options": {}
      },
      "recommendCode": {
        "title": "推荐基金代码",
        "type": "string",
        "pattern": /^[A-Za-z0-9]{6}$/,
        "ui:width": "50%",
        "ui:options": {}
      },
      "marketId": {
        "title": "市场号",
        "type": "number",
        "pattern": /^[0-9]+$/,
        "ui:width": "50%",
        "ui:options": {}
      },
      "recommendName": {
        "title": "推荐基金名称",
        "type": "string",
        "ui:width": "50%",
        "ui:options": {}
      },
      "profitDescribe": {
        "title": "收益区间",
        "type": "now",
        "default": "year",
        "ui:hidden": "{{rootValue.cardType === 'newFund'}}",
        "enum": [
          "year",
          "now",
          "week",
          "month",
          "tmonth",
          "hyear",
          "twoyear",
          "tyear",
          "fyear",
          "nowyear"
        ],
        "enumNames": [
          "近一年涨幅-year",
          "成立以来涨幅-now",
          "近一周涨幅-week",
          "近一月涨幅-month",
          "近三月涨幅-tmonth",
          "近半年涨幅-hyear",
          "近两年涨幅-towyear",
          "近三年涨幅-tyear",
          "近五年涨幅-fyear",
          "今年以来涨幅-nowyear"
        ]
      },
      "buyNum": {
        "ui:hidden": "{{rootValue.cardType === 'newFund'}}",
        "title": "购买人数",
        "type": "string",
        "ui:options": {}
      },
      "productTitle": {
        "ui:hidden": "{{rootValue.cardType === 'continueFund'}}",
        "title": "产品推荐标题",
        "type": "string",
        "description": "用逗号隔开",
        "ui:options": {}
      },
      "purchaseContext": {
        "title": "购买按钮文案",
        "type": "string",
        "ui:options": {}
      },
      "windowText": {
        "title": "悬浮窗文案",
        "type": "string",
        "ui:options": {}
      },
      "activityRules": {
        "title": "规则",
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "text": {
              "title": "",
              "type": "string",
              "ui:options": {}
            }
          }
        },
        "ui:options": {}
      },
      "type": {
        "title": "中奖类型",
        "type": "string",
        "enum": [
          "level2",
          "cash"
        ],
        "enumNames": [
          "兑换码",
          "现金"
        ],
        "ui:widget": "radio"
      },
      "codeRate": {
        "title": "兑换码中奖概率",
        "type": "number",
        "ui:hidden": "{{rootValue.type === 'cash'}}",
        "ui:options": {
          "addonAfter": "%"
        }
      },
      "cashVoucherList": {
        "title": "现金参数",
        "type": "array",
        "ui:hidden": "{{rootValue.type === 'level2'}}",
        "items": {
          "type": "object",
          "properties": {
            "money": {
              "title": "金额",
              "type": "string",
              "ui:width": "50%"
            },
            "rate": {
              "title": "概率",
              "type": "number",
              "ui:width": "50%"
            }
          }
        },
        "ui:options": {}
      },
      "noRewardDialogTitle": {
        "title": "未中奖弹窗标题",
        "type": "string",
        "ui:width": "50%"
      },
      "noRewardDialogFirstText": {
        "title": "未中奖弹窗第一行内容",
        "type": "string",
        "ui:width": "50%"
      },
      "noRewardDialogSecondText": {
        "title": "未中奖弹窗第二行内容",
        "type": "string",
        "ui:width": "50%"
      },
      "noRewardDialogBtnText": {
        "title": "未中奖弹窗按钮文案",
        "type": "string",
        "ui:width": "50%"
      },
      "guideAttentionUrl": {
        "title": "引导关注跳转链接",
        "type": "string",
        "ui:width": "100%"
      },
      "activityName": {
        "title": "活动名称",
        "type": "string",
        "ui:options": {}
      },
      "startDate": {
        "title": "活动开始时间",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%"
      },
      "endDate": {
        "title": "活动结束时间",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%"
      },
      "status": {
        "title": "状态",
        "type": "string",
        "enum": [
          "1",
          "0"
        ],
        "enumNames": [
          "正常",
          "异常"
        ],
        "ui:widget": "radio"
      },
      "exchangeCodeConfig": {
        "type": "object",
        "required": [
          "imgTitle",
          "imgDescription",
          "popTitle",
          "awardDescription",
          "guideDescription",
        ],
        "ui:hidden": "{{rootValue.type === 'cash'}}",
        "properties": {
          "imgTitle": {
            "title": "获奖图片第一行",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多5个汉字"
            }
          },
          "imgDescription": {
            "title": "获奖图片第二行",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多5个汉字"
            }
          },
          "popTitle": {
            "title": "兑换弹窗标题",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多8个汉字"
            }
          },
          "awardDescription": {
            "title": "弹窗获奖文案",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {
              "placeholder": "最多14个汉字"
            }
          },
          "guideDescription": {
            "title": "弹窗引导文案",
            "type": "string",
            "ui:options": {
              "placeholder": "最多29个汉字"
            }
          },
          "exchangeOpen": {
            "title": "是否需要兑换码转换页面",
            "type": "string",
            "enum": [
              "1",
              "0"
            ],
            "enumNames": [
              "需要",
              "不需要"
            ],
            "ui:widget": "radio"
          },
          "exchangeChangeConfig": {
            "title": '兑换码转换页面配置(页面地址：https://fund.10jqka.com.cn/ifundapp_web/public/hq/exchangeCode/dist/index.html?activityId={activityId})',
            "type": "object",
            "ui:hidden": "{{rootValue.exchangeOpen === '0'}}",
            "required": [
              "conductDescription",
              "awardDescription",
              "activityRule",
            ],
            "properties": {
              "conductDescription": {
                "title": "宣传文案",
                "type": "string",
                "ui:options": {
                  "placeholder": "最多12个汉字"
                }
              },
              "awardDescription": {
                "title": "获奖文案",
                "type": "string",
                "ui:options": {
                  "placeholder": "最多14个汉字"
                }
              },
              "buttonDescription": {
                "title": "按钮文案",
                "type": "string",
                "ui:options": {
                  "placeholder": "最多12个汉字"
                }
              },
              "buttonType": {
                "title": "按钮跳转类型",
                "type": "string",
                "enum": ['ths', 'h5'],
                "enumNames": ['同花顺', 'h5页面'],
                "ui:width": "30%"
              },
              "buttonUrl": {
                "title": "按钮跳转url",
                "type": "string",
                "ui:width": "70%"
              },
              "activityRule": {
                "title": "活动规则",
                "type": "string",
                "format": "textarea",
              },
            }
          }
        }
      }
    }
  }
