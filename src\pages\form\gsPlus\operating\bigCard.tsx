import React, { useState, useEffect } from 'react';
import api from 'api';

import { Radio, Input, Select, message, Checkbox, Card } from 'antd';

const { Option } = Select;

const platformOptions = [
    { label: 'App-安卓', value: 'and' },
    { label: 'App-iOS', value: 'ios' },
    { label: 'SDK-安卓', value: 'andsdk' },
    { label: 'SDK-iOS', value: 'iossdk' },
    { label: 'ios至尊', value: 'iossdkvip' },
    { label: '安卓至尊', value: 'andsdkvip' }
]
const userOptions = [
    { label: '默认', value: 'u0' },
    { label: '新手用户', value: 'u1' },
    { label: '次新用户', value: 'u2' },
    { label: '休眠新用户', value: 'u3' },
    { label: '流失新用户', value: 'u4' },
    { label: '成长用户', value: 'u5' },
    { label: '成熟用户', value: 'u6' },
    { label: '高净值用户', value: 'u7' },
    { label: '休眠用户', value: 'u8' },
    { label: '流失用户', value: 'u9' },
    { label: 'F类审核用户', value: 'F' },
]

interface dataProps {
    title?: string;
    onChange?: any;
    baseData: any;
		allProd: any;
}

export default function (props:dataProps) {

    const { fetchFundNameByCode } = api;

    const { title, onChange, baseData, allProd } = props;

    // const [showType, set_showType] = useState("0"); // 0: 开启 1: 关闭 2: 随机推荐
    // const [fundCode, set_fundCode] = useState(""); // 基金代码
    // const [fundName, set_fundName] = useState(""); // 基金名称
    // const [timeRange, set_timeRange] = useState("year"); // 时间区间 year: 近1年 hyear: 近6个月 tmonth: 近3个月 month: 近1个月
    // const [dataIndicator, set_dataIndicator] = useState("rate"); // 数据指标1 rate: 涨跌幅 maxDown: 最大回撤 formerWeekProbability: 过往周胜率
    // const [platform, set_platform] = useState<any[]>([]); // 适用平台
    // const [user, set_user] = useState<any[]>([]); // 用户类型

    // const [tagText, set_tagText] = useState(""); // 标签文案
    // const [buyText, set_buyText] = useState(""); // 上车栏文案
    // const [recommendText, set_recommendText] = useState(""); // 卖点文案
    // const [signText, set_signText] = useState(""); // 角标文案 

    /**
     * 获取基金名称
     */
     const checkFundCode = (fundCode: string) => {
        if (fundCode === "") return;
        if (fundCode.length === 6 && !fundCode.includes(' ')) {
            fetchFundNameByCode({
                fundCode: fundCode
            }).then((data: any) => {
                if (data.code === '0000') {
                    data = data.data;
                    if (data) {
                        changeData('fundName', data.name);
                    } else {
                        message.error('该基金无数据，请核对基金代码')
                    }
                } else {
                    message.error('请求基金详情接口失败，请手动修改基金名称')
                }
            });
        } else {
            message.error('基金代码格式错误！')
        }
    }

    /**
     * 改变数据
     * @param type 
     * @param value 
     */
    const changeData = (type: string, value: any) => {
        let _changeData = {
            showType: baseData.showType,
            fundCode: baseData.fundCode,
            fundName: baseData.fundName,
            signText: baseData.signText,
            recommendText: baseData.recommendText,
            tagText: baseData.tagText,
            buyText: baseData.buyText,
            timeRange: baseData.timeRange,
            dataIndicator: baseData.dataIndicator,
            platform: baseData.platform,
            user: baseData.user,
						jumpUrl: (allProd && allProd[baseData.fundCode]) ? allProd[baseData.fundCode].jumpUrl : ''
        }
        switch (type) {
            case "showType":
                // set_showType(value);
                _changeData.showType = value;
                break;
            case "fundCode":
                // set_fundCode(value);
                _changeData.fundCode = value;
                break;
            case "fundName":
                // set_fundName(value);
                _changeData.fundName = value;
                break;
            case "signText":
                // set_signText(value);
                _changeData.signText = value;
                break;
            case "recommendText":
                // set_recommendText(value);
                _changeData.recommendText = value;
                break;
            case "tagText":
                // set_tagText(value);
                _changeData.tagText = value;
                break;
            case "buyText":
                // set_buyText(value);
                _changeData.buyText = value;
                break;
            case "timeRange":
                // set_timeRange(value);
                _changeData.timeRange = value;
                break;
            case "dataIndicator":
                // set_dataIndicator(value);
                _changeData.dataIndicator = value;
                break;
            case "platform":
                // set_platform(value);
                _changeData.platform = value;
                break;
            case "user":
                // set_user(value);
                _changeData.user = value;
                break;
            default:
                break;
        }
        onChange(_changeData);
    }

    return (
        <Card title={title || ""} style={{marginBottom: 10}}>
            <Radio.Group onChange={(e) => {changeData("showType", e.target.value)}} value={baseData.showType || "0"} style={{marginBottom: 10}}>
                <Radio value={"0"}>开启</Radio>
                <Radio value={"1"}>关闭</Radio>
            </Radio.Group>
            <div className={`${baseData.showType === "0" ? "" : "z-hide"}`}>
                <div>
                    <span><b style={{color: '#fe5d4e'}}>* </b> 基金代码</span>
                    <Input 
                        style={{width: 150, marginLeft: 10}}
                        value={baseData.fundCode}
                        onChange={(e) => {changeData("fundCode", e.target.value)}}
                        onBlur={(e) => {checkFundCode(e.target.value)}}
                    />
                </div>
                <div style={{marginTop: 20}}>
                    <span><b style={{color: '#fe5d4e'}}>* </b>基金名称</span>
                    <Input 
                        style={{width: 300, marginLeft: 10}}
                        value={baseData.fundName}
                        onChange={(e) => {changeData("fundName", e.target.value)}}
                        placeholder="输入基金代码自动显示，可手动编辑"
                    />
                </div>
                <div style={{marginTop: 20}}>
                    <span><b style={{color: '#fe5d4e'}}>* </b>角标文案</span>
                    <Input 
                        style={{width: 150, marginLeft: 10}}
                        value={baseData.signText}
                        onChange={(e) => {changeData("signText", e.target.value)}}
                        placeholder="最多输入四个字"
                        maxLength={4}
                    />
                </div>
                <div style={{marginTop: 20}}>
                    <span><b style={{color: '#fe5d4e'}}>* </b>卖点文案</span>
                    <Input 
                        style={{width: 500, marginLeft: 10}}
                        value={baseData.recommendText}
                        onChange={(e) => {changeData("recommendText", e.target.value)}}
                    />
                </div>
                <div style={{marginTop: 20}}>
                    <span><b style={{color: '#fe5d4e'}}>* </b>标签文案</span>
                    <Input 
                        style={{width: 500, marginLeft: 10}}
                        value={baseData.tagText}
                        onChange={(e) => {changeData("tagText", e.target.value)}}
                        placeholder="用英文逗号分隔，默认显示前三个"
                    />
                </div>
                <div style={{marginTop: 20}}>
                    <span><b style={{color: '#fe5d4e'}}>* </b>上车栏文案</span>
                    <Input 
                        style={{width: 500, marginLeft: 10}}
                        value={baseData.buyText}
                        onChange={(e) => {changeData("buyText", e.target.value)}}
                    />
                </div>
								<div style={{marginTop: 20}}>
                    <span>跳转链接</span>
                    <Input 
                        style={{width: 500, marginLeft: 10}}
												value={(allProd && allProd[baseData.fundCode]) ? allProd[baseData.fundCode].jumpUrl : ''}
                        // value={baseData.buyText}
                        // onChange={(e) => {changeData("buyText", e.target.value)}}
												disabled
                    />
                </div>
                <div style={{marginTop: 20}}>
                    <span><b style={{color: '#fe5d4e'}}>* </b> 时间区间</span>
                    <Select value={baseData.timeRange} onChange={(value: any) => {changeData("timeRange", value)}} style={{width: 120, marginLeft: 10}}>
                        <Option value="year">近1年</Option>
                        <Option value="hYear">近6个月</Option>
                    </Select>
                </div>
                <div style={{marginTop: 20}}>
                    <span><b style={{color: '#fe5d4e'}}>* </b> 数据指标</span>
                    <Radio.Group onChange={(e) => {changeData("dataIndicator", e.target.value)}} value={baseData.dataIndicator} style={{marginLeft: 10}}>
                        <Radio value={"rate"}>涨跌幅</Radio>
                        <Radio value={"maxDown"}>最大回撤</Radio>
                        <Radio value={"formerWeekProbability"}>过往周胜率</Radio>
                    </Radio.Group>
                </div>
                <div style={{marginTop: 20}}>
                    <div><b style={{color: '#fe5d4e'}}>* </b>适用平台</div>
                    <Checkbox.Group 
                        style={{marginTop: 10}}
                        options={platformOptions}
                        value={baseData.platform}
                        onChange={(value) => {changeData('platform', value)}} 
                    />
                </div>
                <div style={{marginTop: 20}}>
                    <div><b style={{color: '#fe5d4e'}}>* </b>用户类型</div>
                    <Checkbox.Group 
                        style={{marginTop: 10}}
                        options={userOptions}
                        value={baseData.user}
                        onChange={(value) => {changeData('user', value)}}
                    />
                </div>
            </div>
            
        </Card>
    )
}