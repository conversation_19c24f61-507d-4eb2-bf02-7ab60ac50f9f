import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ConfigData, HotSpot } from '../../type';
import { Card, DatePicker, Input, Button,Popconfirm } from 'antd';
import InputTitle from '../InputTitle';
import { hotSpotHelper, mapPropToName } from './helper';
import moment from 'moment';

export interface IProps {
  data: ConfigData['hotspots'];
  onChange: (data: ConfigData['hotspots']) => void;
  disabled: boolean;
}
const HotSpot: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleAdd = () => {
    onChange([hotSpotHelper.addArrayData(), ...data]);
  };
  const handleChange = (index, name, value) => {
    const newData = [...data];
    newData[index][name] = value;
    onChange(newData);
  };
  const handleDel=(index)=>{
    const newData = [...data];
    newData.splice(index,1)
    onChange(newData)
  }
  return (
    <div>
      <h3>重磅热点</h3>
      {data?.map((item, index) => {
        return (
          <Card style={{ width: '350px',display:"inline-block" }}>
            <Input
              value={item.title}
              onChange={e => handleChange(index, 'title', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['title']} />}
            />
            <div style={{ marginBottom: 10, width: 300 }}>
              <InputTitle title={mapPropToName['time']} />
              <DatePicker
                disabled={disabled}
                placeholder={'请选择日期'}
                value={item.time?moment(item.time):''}
                onChange={(date, dateString) => handleChange(index, 'time', dateString)}
              />
            </div>
            <Input
              value={item.link}
              onChange={e => handleChange(index, 'link', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['link']} />}
            />
            
            <div>
            <InputTitle  title={mapPropToName['content']}  isRequired={false}/>
            <Input.TextArea     value={item.content}
              onChange={e => handleChange(index, 'content', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              ></Input.TextArea>
            
            </div>
            {/* <Input
              value={item.content}
              onChange={e => handleChange(index, 'content', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['content']} />}
            /> */}
            <Input
              value={item.tags}
              onChange={e => handleChange(index, 'tags', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['tags']} isRequired={false} />}
            />

            <div>
            <InputTitle title={mapPropToName['analyze']} isRequired={false} />
            <Input.TextArea  value={item.analyze}
              onChange={e => handleChange(index, 'analyze', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              ></Input.TextArea>
            
            </div>
            {/* <Input
              value={item.analyze}
              onChange={e => handleChange(index, 'analyze', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['analyze']} isRequired={false} />}
            /> */}
            
            <Popconfirm disabled={disabled} title={'请确认是否删除'} okText={'确认'} cancelText={'取消'}
            onConfirm={()=>handleDel(index)}
            >
              <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                删除
              </Button>
          </Popconfirm>
          </Card>
        );
      })}

      <div>
      <Button disabled={disabled} onClick={handleAdd} style={{ marginTop: '4px' }} size="small">
        新增重磅热点
      </Button>
      </div>
    </div>
  );
};
export default HotSpot;
