/*
 * 机构客户数据维护
 * @Author: <PERSON><PERSON><PERSON><PERSON>@myhexin.com
 * @Date: 2023-02-16 15:56:07
 * @Last Modified by: z<PERSON><PERSON><PERSON>@myhexin.com
 * @Last Modified time: 2023-03-16 17:03:37
 */
import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import {
  Button,
  Col,
  Collapse,
  DatePicker,
  message,
  Modal,
  Row,
  Select,
  Spin,
  Table,
  Card,
  Form,
} from 'antd';
const { Panel } = Collapse;
const { Option } = Select;
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import api from 'api';
import ModifyIndicators from './modifyIndicators';
import moment from 'moment';
import { SummaryFilter } from '../components/filter';
import { fofProductProps, Result, showFiltersMap } from '../type';
import { safeValueShow } from '../../smsPlatform/fn';
import styles from './index.less';
import { generateManagerFeeInfoListInTable, generateSaleInfoListInTable } from '../util';
import { ExportButton, ExportType } from './exportButton';
const { postHash, fetchHashAll, postHashDel, getTopicFunds, fetchFofQueryList } = api;

interface IProps {
  nowEditInfo: any;
  onClose: () => void;
  modalAllShow: boolean;
}

const AllTable: FC<IProps> = ({ nowEditInfo, onClose, modalAllShow }) => {
  useEffect(() => {
    console.log('now', nowEditInfo);
  }, [nowEditInfo]);
  return (
    <div>
      <div>
        <Modal
          title={`投资者全景信息`}
          visible={modalAllShow}
          onCancel={() => {
            onClose();
          }}
        >
          <ExportButton
            exportType={ExportType.CLIENT_DATA_SINGLE_TYPE}
            data={nowEditInfo?.fofId}
          ></ExportButton>
          <Row justify="space-between" type="flex">
            <Col span={12}>
              {' '}
              <Card title={'账户信息'} className={styles['a-table']}>
                <table>
                  <thead>
                    <tr>
                      <th>指标</th>
                      <th>内容</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>投资账户名称</td>
                      <td>{nowEditInfo.accountName}</td>
                    </tr>
                    <tr>
                      <td>投管人名称</td>
                      <td>{nowEditInfo.investorName}</td>
                    </tr>
                    <tr>
                      <td>投资账户名称</td>
                      <td>{nowEditInfo.accountName}</td>
                    </tr>
                    <tr>
                      <td>开户类型</td>
                      <td>{nowEditInfo.accountTypeName}</td>
                    </tr>
                    <tr>
                      <td>机构类型</td>
                      <td>{nowEditInfo.institutionTypeName}</td>
                    </tr>
                    <tr>
                      <td>托管人名称</td>
                      <td>{nowEditInfo.bankName}</td>
                    </tr>
                    <tr>
                      <td>客户号</td>
                      <td>{nowEditInfo.custId}</td>
                    </tr>
                    <tr>
                      <td>交易号</td>
                      <td>{nowEditInfo.transactionAccountId}</td>
                    </tr>
                    <tr>
                      <td>开户时间</td>
                      <td>{nowEditInfo.openAccountDate}</td>
                    </tr>
                  </tbody>
                </table>
              </Card>
            </Col>
            <Col span={12}>
              {' '}
              <Card title={'交易信息'} className={styles['a-table']}>
                <table>
                  <thead>
                    <tr>
                      <th>指标</th>
                      <th>内容</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>投资状态</td>
                      <td>{nowEditInfo.investment}</td>
                    </tr>
                    <tr>
                      <td>交易通道</td>
                      <td>{nowEditInfo.tradeChannelName}</td>
                    </tr>
                    <tr>
                      <td>保有量</td>
                      <td>{nowEditInfo.holdings}</td>
                    </tr>
                    <tr>
                      <td>货币保有量</td>
                      <td>{nowEditInfo.moneyHoldings}</td>
                    </tr>
                    <tr>
                      <td>债券保有量</td>
                      <td>{nowEditInfo.bondFundHoldings}</td>
                    </tr>
                    <tr>
                      <td>股票保有量</td>
                      <td>{nowEditInfo.stockFundHoldings}</td>
                    </tr>
                    <tr>
                      <td>理财产品保有量</td>
                      <td>{nowEditInfo.financeHoldings}</td>
                    </tr>
                  </tbody>
                </table>
              </Card>
            </Col>
          </Row>

          <Row justify="space-between" type="flex">
            <Col span={12}>
              {' '}
              <Card title={'参数信息'} className={styles['a-table']}>
                <table>
                  <thead>
                    <tr>
                      <th>指标</th>
                      <th>内容</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>货基赎回交割日</td>
                      <td>{nowEditInfo.moneySetDayTypeName}</td>
                    </tr>
                    <tr>
                      <td>非货赎回交割日</td>
                      <td>{nowEditInfo.stockSetDayTypeName}</td>
                    </tr>
                    <tr>
                      <td>保有量</td>
                      <td>{nowEditInfo.holdings}</td>
                    </tr>
                    <tr>
                      <td>超转转换</td>
                      <td>{nowEditInfo.scFlag}</td>
                    </tr>
                  </tbody>
                </table>
              </Card>{' '}
            </Col>
            <Col span={12}>
              {' '}
              <Card title={'销售信息'} className={styles['a-table']}>
                {' '}
                <table>
                  <thead>
                    <tr>
                      <th>指标</th>
                      <th>内容</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>策略</td>
                      <td>{nowEditInfo.strategyName}</td>
                    </tr>
                    <tr>
                      <td>销售人员</td>
                      <td>{generateSaleInfoListInTable(nowEditInfo.saleInfoList)}</td>
                    </tr>
                    <tr>
                      <td>返点比例</td>
                      <td>{nowEditInfo.returnRatio}</td>
                    </tr>
                    <tr>
                      <td>管理费分成登记</td>
                      <td>{generateManagerFeeInfoListInTable(nowEditInfo.managerFeeInfoList)}</td>
                    </tr>
                  </tbody>
                </table>
              </Card>
            </Col>
          </Row>
        </Modal>
      </div>
    </div>
  );
};
export default AllTable;
