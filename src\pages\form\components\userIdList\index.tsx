import React, { useState, useEffect } from 'react';
import { Button, Radio, Upload, message } from 'antd';
import styles from '@/pages/frontend/frontend.less';
import ConfigSelect from './../selectModel';
import TagModel from './../tagModel';
import api from 'api';
import { RadioChangeEvent } from 'antd/lib/radio';

const {
  uploadUserList,
  downloadUserList,
  updateUserType,
  getUserType,
  fetchCBAS,
  fetchOlasTags,
} = api;

interface Props {
  filterId: string;
  setFilterId: (key: string) => void;
  isAdd: boolean;
  isEdit: boolean;
  setUserTypeData?: Function;
  isCanEdit: boolean;
  setIsCanEdit: Function;
}

interface UserType {
  updateTime: string;
  platform: string[];
  targetType: string;
  kycLogic: string;
  kycs: {
    lable: string;
    logic: string;
    value: string;
  }[];
  olasId: string;
  olasType: USERTYPE;
  id?: string;
  userType: USERTYPE;
  blackUserId: string;
  whiteUserId: string;
  utype: string[];
}

interface OlastagItem {
  description: string;
  groupid: string;
  types: USERTYPE;
}

interface KeyTagItem {
  app_info: string | null;
  conver_num: string | null;
  converage: string | null;
  data_type: string;
  enum_value: string;
  is_enum: string;
  label_code: string;
  label_desc: string;
  label_name: string;
  segment_info: string | null;
  support_search: string;
  unit: string;
}

interface ConfigData {
  platform?: string[];
  utype?: string[];
}

interface RelationData {
  targetType: string;
  kycLogic: string;
  kycs: {
    lable: string;
    logic: string;
    value: string;
  }[];
  olasId: string;
}

type USERTYPE = '1' | '3';

enum USER_TYPE {
  user = '1',
  cust = '3',
}

interface tableObj {
  '1': string;
  '3': string;
}

const handle0Time = (number: number) => {
  return number < 10 ? '0' + number : number;
};

const handleTime = (date: Date) => {
  let _year = date.getFullYear(),
    _month = handle0Time(date.getMonth() + 1),
    _date = handle0Time(date.getDate()),
    _hour = handle0Time(date.getHours()),
    _minute = handle0Time(date.getMinutes()),
    _second = handle0Time(date.getSeconds());
  return `${_year}-${_month}-${_date} ${_hour}:${_minute}:${_second}`;
};

export default function({
  filterId,
  setFilterId,
  isEdit,
  isAdd,
  setUserTypeData,
  isCanEdit,
  setIsCanEdit,
}: Props) {
  const [kycTag, setKycTag] = useState<KeyTagItem[]>([]); //kyc tag
  const [olasTag, setOlasTag] = useState<OlastagItem[]>([]); //olas tag
  const [olasTypeObj, setOlasTypeObj] = useState({
    [USER_TYPE['user']]: [],
    [USER_TYPE['cust']]: [],
  }); //手动指定用户名单类型对象

  const [configData, setConfig] = useState<ConfigData>({}); //用户信息
  const [relationData, setRelation] = useState<RelationData>({
    targetType: 'kyc',
    kycLogic: 'and',
    kycs: [],
    olasId: '',
  }); //标签逻辑信息
  const [olasUserType, setOlasUserType] = useState<USERTYPE>(USER_TYPE['user']); //手动指定用户名单类型
  const [userType, setUserType] = useState<USERTYPE>(USER_TYPE['user']); //手动指定用户名单类型
  const [blackFile, setBlackFile] = useState<tableObj>({
    [USER_TYPE['user']]: '',
    [USER_TYPE['cust']]: '',
  }); //黑名单
  const [whiteFile, setWhiteFile] = useState<tableObj>({
    [USER_TYPE['user']]: '',
    [USER_TYPE['cust']]: '',
  }); //白名单

  useEffect(() => {
    if (!filterId) return;
    fetchUserType(filterId);
  }, [filterId]);

  useEffect(() => {
    if (_?.kycTag) return setKycTag(_.kycTag);
    fetchCBAS().then((res: any) => {
      if (res.code === '0000' && res.data) {
        _.kycTag = res.data;
        setKycTag(res.data);
      }
    });
  }, []);

  useEffect(() => {
    if (_.olasTag) return setOlasTypeObj(_.olasTag);
    fetchOlasTags({
      // userType: olasUserType,
      userType: 'all',
    }).then((res: any) => {
      if (res.code === '0000' && res.data) {
        let _userOlasTag = [],
          _custOlasTag = [];
        let data = res.data;
        for (let prop in data) {
          const item = { groupid: data[prop].groupIDs, description: data[prop].description };
          if (data[prop].types === USER_TYPE['user']) {
            _userOlasTag.push(item);
          } else if (data[prop].types === USER_TYPE['cust']) {
            _custOlasTag.push(item);
          }
          // _olasTag.push({ groupid: data[prop].groupIDs, description: data[prop].description });
        }
        let _olasTagObj = _.olasTag || {};
        _olasTagObj[USER_TYPE['user']] = _userOlasTag;
        _olasTagObj[USER_TYPE['cust']] = _custOlasTag;
        setOlasTypeObj(_olasTagObj);
        _.olasTag = _olasTagObj;
      }
    });
  }, []);

  useEffect(() => {
    setOlasTag(olasTypeObj[olasUserType]);
  }, [olasUserType, olasTypeObj]);

  useEffect(() => {
    if (setUserTypeData)
      setUserTypeData({
        ...relationData,
        ...configData,
        updateTime: handleTime(new Date()),
        olasType: olasUserType,
        userType,
        blackUserId: blackFile[userType],
        whiteUserId: whiteFile[userType],
        id: isAdd ? '' : filterId, //id  新增不填
      });
  }, [blackFile, whiteFile, olasUserType, userType, relationData, configData]);

  /**
   * 上传黑名单
   * @param param0
   */
  const uploadBlackList = ({ file }: any): void => {
    console.log(file);
    uploadList('black', file);
  };

  /**
   * 上传白名单
   * @param param0
   */
  const uploadWhiteList = ({ file }: any): void => {
    console.log(file);
    uploadList('white', file);
  };

  /**
   * 上传名单方法
   * @param type
   * @param file
   */
  const uploadList = (type: 'black' | 'white', file: File) => {
    let _data = new FormData();
    _data.append('multipartFile', file);
    _data.append('userType', userType);
    _data.append('listType', type);
    console.log(file, _data);
    uploadUserList(_data)
      .then((res: Record<string, string>) => {
        if (res.code !== '0000') return message.error(res.message);
        message.success('上传成功～');
        switch (type) {
          case 'black':
            setBlackFile({
              ...blackFile,
              [userType]: res.data,
            });
            break;
          case 'white':
            setWhiteFile({
              ...whiteFile,
              [userType]: res.data,
            });
            break;
        }
      })
      .catch((e: Error) => {
        message.error(e.message || '系统错误');
      });
  };

  /**
   * 下载名单
   * @param type black黑名单  white白名单
   */
  const downLoadList = (type: 'black' | 'white') => {
    if (!(type === 'black' ? blackFile[userType] : whiteFile[userType]))
      return message.error('该文件不存在');
    downloadUserList(
      {},
      `randomKey=${
        type === 'black' ? blackFile[userType] : whiteFile[userType]
      }&listType=${type}&userType=${userType}`,
      null,
      {
        responseType: 'blob',
      },
    )
      .then((data: Record<string, any>) => {
        if (data.data && data.data.size === 0 && data.message !== 'OK' && data.message)
          return message.error('下载失败');
        if (data.code !== '0000' && data.message !== 'OK' && data.message)
          return message.error(data.message);
      })
      .catch((e: Error) => {
        message.error('下载失败');
      });
  };

  const addFile = (File: File, fileList: File[]) => {
    console.log(fileList.length);
    if (fileList.length > 1) {
      message.error('只能上传一个文件，请先删除原先文件');
      return false;
    }
    return true;
  };

  /**
   * 上传用户类型
   */
  const uploadUserType = () => {
    let _data = {
      ...relationData,
      ...configData,
      updateTime: handleTime(new Date()),
      olasType: olasUserType,
      userType,
      blackUserId: blackFile[userType],
      whiteUserId: whiteFile[userType],
      id: isAdd ? '' : filterId, //id  新增不填
    };

    if (!_data.targetType) {
      return message.error('请指定kyc和olas');
    } else if (!_data.userType) {
      return message.error('请指定黑白名单用户类型');
    }

    updateUserType(_data)
      .then((res: any) => {
        if (res.code !== '0000') return message.error(res.message);
        setFilterId(res.data);
        setIsCanEdit(false);
        message.success('提交用户类型成功');
      })
      .catch((e: Error) => {
        message.error(e.message || '系统错误');
      });
  };

  /**
   * 获取用户类型信息
   * @param filterId
   */
  const fetchUserType = (filterId: string) => {
    getUserType({
      filterId,
    })
      .then((res: any) => {
        if (res.code !== '0000') return message.error(res.message);
        const _data: UserType = res.data[0];
        setConfig({
          platform: _data.platform,
          utype: _data.utype,
        });
        setRelation({
          targetType: _data.targetType,
          kycLogic: _data.kycLogic,
          kycs: _data.kycs,
          olasId: _data.olasId,
        });
        setOlasUserType(_data.olasType);
        setUserType(_data.userType);
        setBlackFile({
          ...blackFile,
          [_data.userType]: _data.blackUserId,
        });
        setWhiteFile({
          ...whiteFile,
          [_data.userType]: _data.whiteUserId,
        });
      })
      .catch((e: Error) => {
        message.error(e.message || '系统错误');
      });
  };

  const handleIdType = (e: RadioChangeEvent) => {
    setOlasUserType(e.target.value);
  };

  const handleConfig = (value: React.SetStateAction<ConfigData>) => {
    setConfig(value);
  };

  const handleRelation = (value: React.SetStateAction<RelationData>) => {
    setRelation(value);
  };

  const handleUserType = (e: RadioChangeEvent) => {
    setUserType(e.target.value);
  };

  const downloadBlack = () => {
    downLoadList('black');
  };

  const downloadWhite = () => {
    downLoadList('white');
  };

  const startEdit = () => {
    setIsCanEdit(true);
    setFilterId('');
  };

  const userTypeText = userType === USER_TYPE['user'] ? 'user_id' : 'cust_id';

  return (
    <>
      <div className="u-j-middle">
        <h2>指定用户</h2>
        <div>
          {isCanEdit ? (
            <Button disabled={!isEdit} type="primary" onClick={uploadUserType}>
              提交用户类型
            </Button>
          ) : (
            <Button disabled={!isEdit} type="primary" onClick={startEdit}>
              编辑用户类型
            </Button>
          )}
        </div>
      </div>
      <ConfigSelect
        handleChange={handleConfig}
        isHead={false}
        isEdit={isCanEdit}
        data={configData}
      />
      <TagModel
        olasUserType={olasUserType}
        handleIdType={handleIdType}
        handleChange={handleRelation}
        data={relationData}
        kycTag={kycTag}
        olasTag={olasTag}
        isEdit={isCanEdit}
      />

      <div className="u-l-middle" style={{ marginBottom: 20, color: 'black' }}>
        <p className={styles['m-card-label']} style={{ marginBottom: 0 }}>
          手动指定用户名单类型:
        </p>
        <Radio.Group onChange={handleUserType} value={userType} disabled={!isCanEdit}>
          <Radio value={USER_TYPE['user']}>同花顺账号user_id</Radio>
          <Radio value={USER_TYPE['cust']}>基金客户号cust_id</Radio>
        </Radio.Group>
      </div>
      <div className="u-l-middle" style={{ marginBottom: 20, color: 'black' }}>
        <p className={styles['m-card-label']} style={{ marginBottom: 0 }}>
          黑名单（{userTypeText}）:
        </p>
        <a
          style={{
            display: blackFile[userType] ? '' : 'none',
            marginRight: 20,
          }}
          onClick={() => {
            downloadBlack();
          }}
        >
          用户黑名单文件
        </a>
        <Upload beforeUpload={addFile} customRequest={uploadBlackList} showUploadList={false}>
          <Button type="primary" disabled={!isCanEdit}>
            上传文件
          </Button>
        </Upload>
        <span style={{ color: 'red', marginLeft: 20 }}>
          请上传xlsx文档，每行填入一个同花顺账号（{userTypeText}），第一行不要填数据，填写“
          {userTypeText}”
        </span>
      </div>
      <div className="u-l-middle" style={{ marginBottom: 20, color: 'black' }}>
        <p className={styles['m-card-label']} style={{ marginBottom: 0 }}>
          手动上传用户名单（{userTypeText}）:
        </p>
        <a
          style={{
            display: whiteFile[userType] ? '' : 'none',
            marginRight: 20,
          }}
          onClick={() => {
            downloadWhite();
          }}
        >
          用户白名单文件
        </a>
        <Upload beforeUpload={addFile} customRequest={uploadWhiteList} showUploadList={false}>
          <Button type="primary" disabled={!isCanEdit}>
            上传文件
          </Button>
        </Upload>
        <span style={{ color: 'red', marginLeft: 20 }}>
          请上传xlsx文档，每行填入一个同花顺账号（{userTypeText}），第一行不要填数据，填写“
          {userTypeText}”
        </span>
      </div>
    </>
  );
}
