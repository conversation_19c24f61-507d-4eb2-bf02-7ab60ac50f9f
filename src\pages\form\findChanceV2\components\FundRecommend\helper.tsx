import { FundRecommend, ChanceLiftType } from '../../type';
import { checkLengthAndThrow, checkRequiredAndThrow } from '../../utils';
export const mapPropToName = {
  recommendWords: '一句话推荐',
  fundCode: '基金代码',
  fundName: '基金名称',
  profitArea: '收益区间',
  heavyPositionStock: '重仓股票',
  articleContent: '文章内容',
  buttonText: '按钮内容',
  link: '跳转链接',
  linkVersion: '跳转链接客户端版本号',
  articleLink: '文章链接',
  articleLinkVersion: '版本控制',
  articleCreator: '文章作者',
  articleCreatorAvatar: '头像',
  showType: '一级页展示',
  fundDetailUrl: '详情跳转链接',
  fundDetailVersion: '详情跳转链接客户端版本号',
};
export const fundRecommendHelper = {
  addArrayData: function(): FundRecommend {
    return {
      recommendWords: '',
      fundCode: '',
      fundName: '',
      profitArea: '',
      heavyPositionStock: '',
      buttonText: '',
      link: '',
      fundDetailUrl: '',
      fundDetailVersion: '',
      articleContent: '',
      $type: ChanceLiftType.FUND,
      articleLink: '',

      articleCreator: '',
      articleCreatorAvatar: '',
      showType: '',
    };
  },
  addInit: function(): FundRecommend[] {
    return [];
  },
  checkSingleData: function(item: FundRecommend) {
    checkRequiredAndThrow(item.recommendWords, mapPropToName['recommendWords']);
    checkLengthAndThrow(item.recommendWords, mapPropToName['recommendWords'], 20);
    checkLengthAndThrow(item.buttonText, mapPropToName['buttonText'], 4);
    checkRequiredAndThrow(item.fundCode, mapPropToName['fundCode']);
    checkRequiredAndThrow(item.fundName, mapPropToName['fundName']);
    checkRequiredAndThrow(item.profitArea, mapPropToName['profitArea']);
    checkRequiredAndThrow(item.heavyPositionStock, mapPropToName['heavyPositionStock']);
    checkRequiredAndThrow(item.articleCreatorAvatar, mapPropToName['articleCreatorAvatar']);
    checkRequiredAndThrow(item.heavyPositionStock, mapPropToName['heavyPositionStock']);
    checkRequiredAndThrow(item.showType, mapPropToName['showType']);
    return true;
  },
  checkData: function(data: FundRecommend[]) {
    data.forEach(item => {
      checkRequiredAndThrow(item.recommendWords, mapPropToName['recommendWords']);
      checkRequiredAndThrow(item.fundCode, mapPropToName['fundCode']);
      checkRequiredAndThrow(item.fundName, mapPropToName['fundName']);
      checkRequiredAndThrow(item.profitArea, mapPropToName['profitArea']);
      checkRequiredAndThrow(item.heavyPositionStock, mapPropToName['heavyPositionStock']);
    });
    return true;
  },
};
