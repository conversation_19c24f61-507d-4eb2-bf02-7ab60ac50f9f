/**
 * 黄金宝 - 基础配置 - 天天领黄金
 */
import React, { useEffect } from 'react';
import styles from './index.less';
import { Typography, Form, Select, Input, Button } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import RecommendedFundForm from './RecommendedFundForm';
import BlockWithToolBar from '../BlockWithToolBar';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

type Props = {
  formRef: { current: { activity: FormComponentProps['form'] } };
  data: { [k: string]: unknown };
  fund: { [k: string]: unknown }[];
  onAppendFund: (n: number) => void;
  onRemoveFund: (n: number) => void;
};

//
const EverydayLeadingGold: React.FunctionComponent<Props & FormComponentProps> = ({
  form,
  formRef,
  data,
  fund,
  onAppendFund,
  onRemoveFund,
}) => {
  //
  useEffect(() => {
    formRef.current.activity = form;

    return () => {
      formRef.current.activity = null;
    };
  }, [/* form, */ data]);

  // prettier-ignore
  return (
    <section>
      <Typography.Title level={4}>天天领黄金</Typography.Title>
      <Form
        { ...formItemLayout }
        layout="horizontal"
      >
        <Form.Item label="活动状态">
          {
            form.getFieldDecorator('status', {
              initialValue: data.status || 'offline',
              rules: [{ required: true }],
            })(
              <Select>
                <Select.Option value="online">上线</Select.Option>
                <Select.Option value="offline">下线</Select.Option>
              </Select>
            )
          }
        </Form.Item>
        <Form.Item label="黄金克数有效时间(小时)">
          {
            form.getFieldDecorator('gold_valid_time', {
              initialValue: data.gold_valid_time,
              rules: [{ required: true, message: '请输入黄金克数有效时间' }, { validator: (_, value) => value > 0 && parseInt(value) == parseFloat(value), message: '必须大于 0 且为整数' }],
            })(
              <Input
                type="number"
                placeholder="请输入黄金克数有效时间"
              />,
            )
          }
        </Form.Item>
        <Form.Item label="黄金豆提取门槛">
          {
            form.getFieldDecorator('golden_bean_extraction_threshold', {
              initialValue: data.golden_bean_extraction_threshold,
              rules: [{ required: true, message: '请输入黄金豆提取门槛' }, { validator: (_, value) => value > 0 && parseInt(value) == parseFloat(value), message: '必须大于 0 且为整数' }],
            })(
              <Input
                type="number"
                placeholder="请输入黄金豆提取门槛"
              />,
            )
          }
        </Form.Item>
        <Form.Item label="黄金豆提取规则">
          {
            form.getFieldDecorator('golden_bean_extraction_rule', {
              initialValue: data.golden_bean_extraction_rule,
              rules: [{ required: true, message: '请输入黄金豆提取规则' }],
            })(
              <Input.TextArea
                rows={ 5 }
                placeholder="请输入黄金豆提取规则"
              />,
            )
          }
        </Form.Item>
        <Form.Item label="活动规则">
          {
            form.getFieldDecorator('rule', {
              initialValue: data.rule,
              rules: [{ required: true, message: '请输入活动规则' }],
            })(
              <Input.TextArea
                rows={ 5 }
                placeholder="请输入活动规则"
              />,
            )
          }
        </Form.Item>
      </Form>
      {
        fund.length === 0 && onAppendFund &&
        <Button type="primary" size="large" onClick={ () => onAppendFund(0) }>添加推荐基金</Button>
      }
      {
        fund.map((data, index) =>
          <BlockWithToolBar key={ data._id } number={ index + 1 } onAppend={ onAppendFund } onRemove={ fund.length === 1 ? undefined : onRemoveFund }>
            <RecommendedFundForm formRef={ formRef } data={ data } />
          </BlockWithToolBar>
        )
      }
    </section>
  )
};

export default Form.create({ name: '' })(EverydayLeadingGold);
