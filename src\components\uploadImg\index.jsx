import React from 'react';
import api from 'api';
import { Upload, message, But<PERSON>, Modal } from 'antd';
import { toast } from 'utils/message';
import './index.less';


const { uploadImg } = api;

/**
 * imageUrl: string 生成的图片链接地址
 * handleChange: Function  上传图片后的回调函数
 * limit?: number(M)  最大上传体积
 * size?: Array<string>    example string= 800*600  最大上传体积
 * isEdit: boolean 是否可操作状态
 * title: string  表单标题名
 * isOnlyPng: boolean 是否只支持png文件
 */

class UploadImg extends React.Component {
  state = {
    visible: false,
    imgData: null,
  };
  getBase64 = (img, callback) => {
    const reader = new FileReader();
    reader.addEventListener('load', e => {
      let tag = 0;
      const image = new Image();
      image.src = e.target.result;
      
      image.onload = () => {
        for (let item of this.props.size || []) {
          let width = item.split('*')[0];
          let height = item.split('*')[1];
         
          if (
            `${image.width}` != width ||
            (`${image.height}` != height && !this.props.isNoCheckHeight)
          ) {
            tag = 1;
          } else {
            tag = 0;
            callback(reader.result);
            return;
          }
        }
        if (tag === 0) {
          callback(reader.result);
        }
        if (tag === 1) {
          message.error(`请上传${this.getSizeStr()}大小的图片`);
        }
      };
    });
    reader.readAsDataURL(img);
  };
  getSizeStr = () => {
    let str = '';
    for (let item of this.props.size || []) {
      let width = item.split('*')[0];
      let height = item.split('*')[1];
      str += `${width}px*${height}px,`;
    }
    return str.slice(0, str.length - 1);
  };
  beforeUpload = file => {
    const isJpgOrPng = this.props.isOnlyPng
      ? file.type === 'image/png'
      : file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif';
    if (!isJpgOrPng) {
      message.error(this.props.isOnlyPng ? '请上传PNG格式的图片' : '请上传JPG/PNG/GIF格式的图片');
    }
    //设置图片大小，默认2MB
    const limit = this.props.limit ? this.props.limit : 2;

    const isLt2M = file.size / 1024 / 1024 < limit;
    if (!isLt2M) {
      message.error(`图片大小不能超过${limit}MB!`);
    }
    return isJpgOrPng && isLt2M;
  };
  customRequest = file => {
    const imgFile = file.file;
    this.getBase64(imgFile, imageUrl => {
      uploadImg({
        base64str: imageUrl,
      }).then(data => {
        if (data && data.code === '0000') {
          this.setState({
            imageUrl,
          });
          let _img = data.data.url;
          if (_img.includes('http') && !_img.includes('https')) {
            _img = _img.replace('http', 'https');
          }
          this.props.handleChange(_img);
        } else {
          toast.error('图片上传失败，请重新上传');
        }
      });
    });
  };
  render() {
    const { imageUrl } = this.state;

    return (
      <section>
        <div style={{ marginBottom: '10px' }} className="u-l-middle">
          <div>
            {this.props.title ? (
              <p >
                {this.props.isRequired && <span style={{color:"red"}} >*</span>}
                {this.props.title}:
              </p>
            ) : null}
          </div>
          <div className="u-c-middle">
            {imageUrl || this.props.imageUrl ? (
              <div>
                <img
                  src={imageUrl || this.props.imageUrl}
                  alt="avatar"
                  style={{ width: '128px', height: '128px', border: '1px dashed #d9d9d9' }}
                  onClick={() => {
                    this.setState({ visible: true, imgData: imageUrl });
                  }}
                />
                <span style={{ marginRight: '20px' }}></span>
              </div>
            ) : null}

            <Upload
              listType="picture-card"
              className="avatar-uploader"
              showUploadList={false}
              customRequest={this.customRequest}
              beforeUpload={this.beforeUpload}
              disabled={!this.props.isEdit}
            >
              <div style={{ textAlign: 'left' }}>
                <Button type="primary" disabled={!this.props.isEdit}>
                  选择文件
                </Button>
                <span style={{ color: 'red', marginLeft: '10px' }}>
                  请上传{this.props.size ? this.getSizeStr() : '展示的'}的图片
                </span>
              </div>
            </Upload>
          </div>
        </div>
        <Modal
          visible={this.state.visible}
          onCancel={() => {
            this.setState({ visible: false });
          }}
          footer={null}
          bodyStyle={{ height: '0', padding: '0' }}
          width={'1080px'}
          closable={false}
        >
          <img src={imageUrl || this.props.imageUrl} alt="avatar" />
        </Modal>
      </section>
    );
  }
}

export default UploadImg;
