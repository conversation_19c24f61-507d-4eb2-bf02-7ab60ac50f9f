.m-card {
    .m-header {
        padding-right: 50px;
        margin-bottom: 20px;
        height: 40px;
        background:rgb(149, 186, 221);
        color: #ffffff;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .m-button {
            margin-left: 20px;
        }
    }
    .m-floor {
        padding-right: 50px;
        margin-bottom: 20px;
        height: 40px;
        background:rgb(149, 186, 221);
        color: #ffffff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .m-button {
            margin-left: 20px;
        }
    }
    .m-card-required {
      position: relative;
      &::before {
        display: inline-block;
        margin-right: 4px;
        color: #f5222d;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
    }
}

.m-panel-header {
    width: calc(100% - 130px);
    display: inline-flex;
}