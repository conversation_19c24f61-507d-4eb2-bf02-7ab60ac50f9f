import {
    Form,
    Input,
    message,
    Select,
    But<PERSON>,
    DatePicker,
    InputN<PERSON>ber,
    Popconfirm,
    Modal
  } from 'antd';
import api from 'api';
import { FormComponentProps } from 'antd/es/form';
import React, { forwardRef } from 'react';
import UploadImg from '../uploadImg'; 
import styles from './message.less';
import moment from 'moment';
import { dateFormat } from '@/utils/utils';
import StockModal from '../stockModal/stockModal';

const { Option } = Select;
const { TextArea } = Input;
const { composeInvestment, getTradeDay } = api;

interface MessageFormProps extends FormComponentProps {
    currentData: any
    queryAllData: any
    [key: string]: any
}

interface iQuestion {
    question: string,
    answer: string
}
interface iCommentList {
    title: string,
    content: string
}
interface iWXArcicle {
    title: string,
    articleUrl: string,
    imgUrl: string,
    publishTime: string
}
const UploadComponent:any = forwardRef<any, any>((props, _ref) => {
    const { uploadImage, imgUrl } = props;
    return (
        <UploadImg onChange={uploadImage} imgUrl={imgUrl}></UploadImg>
    )
});

class messageForm extends React.Component<MessageFormProps, any> {
    constructor(props: MessageFormProps) {
        super(props);
        const { commentList, groupId, questionList, imgUrl, wxArticleList } = props.currentData;
        this.state = {
            imgUrl: imgUrl,
            formData: props.currentData || {},
            questionList: (questionList && JSON.parse(JSON.stringify(questionList))) || [],
            commentList: (commentList && JSON.parse(JSON.stringify(commentList))) || [],
            groupId,
            visible: false,
            stockInfo: [],
            wxArticleList: (wxArticleList && JSON.parse(JSON.stringify(wxArticleList))) || [],
            investFlag: false,
            valueObj: {},
            chooseType: props.chooseType,
            isEdit: false, // 交易日可编辑
        }
    }

    formItemLayout = {
        labelCol: {
            span: 6
        },
        wrapperCol: {
            span: 18
        },
    };
    handleSubmit = (e: any) => { 
        e.preventDefault();
        this.props.form.setFieldsValue({
            imgUrl: this.state.imgUrl
        })
        this.props.form.validateFields((err, values) => {
            if (!err) {
                const { formData } = this.state;
                const { followStatus } = values;
                const { edit } = this.props;
                if (edit === 2 && formData.followStatus === '1' && followStatus === '0') {
                    this.setState({
                        investFlag: true,
                        valueObj: {...values}
                    })
                    return;
                } else {
                    this.handleSave(values);
                }
            }
        });
    };
    handleSave = (values: any, flag = 0) => {
        const { detail, detailUrl, followStatus, groupName, label, maxAmountpershare, minAmountpershare, operateInfo, operateUrl, imgUrl,
            name, tshId, wxId, riskLevel, startDate, targetRate, totalShares, dvCustid, dvAccountid, recommendFundList, groupInitFundList } = values;
        const { groupId, questionList, commentList, formData, wxArticleList } = this.state;
        const { edit, onEditClose, queryAllData, chooseType } = this.props;

        let obj = {
            ...formData,
            ...values,
        }
        let handler = {
            tshId: tshId ?? formData?.tshId,
            wxId: wxId ?? formData?.wxId,
            name: name ?? formData?.name,
            imgUrl: imgUrl ?? formData?.imgUrl,
        }
        let jsonDto: any = {
            groupName: groupName ?? formData?.groupName,
            targetRate: targetRate ?? formData?.targetRate,
            handler,
            detail: detail ?? formData?.detail,
            detailUrl: detailUrl ?? formData?.detailUrl,
            riskLevel: riskLevel ?? formData?.riskLevel,
            label: label ?? formData?.label,
            startDate: dateFormat(startDate) ?? formData.startDate,
            totalShares: totalShares ?? formData?.totalShares,
            minAmountpershare: minAmountpershare ?? formData?.minAmountpershare,
            maxAmountpershare: maxAmountpershare ?? formData?.maxAmountpershare,
            followStatus: followStatus ?? formData?.followStatus,
            operateInfo: operateInfo ?? formData?.operateInfo,
            operateUrl: operateUrl ?? formData?.operateUrl,
            dvCustid: dvCustid ?? formData?.dvCustid,
            dvAccountid: dvAccountid ?? formData?.dvAccountid,
            onSaleDate: formData?.onSaleDate,
            offSaleDate: formData?.offSaleDate,
            endDate: formData?.endDate ?? null,
            remainShares: formData?.remainShares ?? null,
            questionList: formData.questionList || [],
            commentList: formData.commentList || [],
            wxArticleList: formData.wxArticleList || [],
            
        };
        let str = recommendFundList ?? formData?.recommendFundList;
        let str1 = groupInitFundList ?? formData?.groupInitFundList;
        jsonDto.recommendFundList = this.handleData(str);
        jsonDto.groupInitFundList = this.handleData(str1);
        if (chooseType === 2) {
            if (wxArticleList?.length === 0) {
                message.error('请输入主理人微信文章');
                return;
            } else {
                for (let i = 0, len = wxArticleList.length; i < len; i++) {
                    const { articleUrl, imgUrl, publishTime, title } = wxArticleList[i];
                    if ( title === null || title === undefined || title === '') {
                        message.error('请输入主理人微信文章的标题');
                        return;     
                    }
                    if ( articleUrl === null || articleUrl === undefined || articleUrl === '') {
                        message.error('请输入主理人微信文章的链接');
                        return;     
                    }
                    if ( publishTime === null || publishTime === undefined || publishTime === '') {
                        message.error('请输入主理人微信文章的发布日期');
                        return;     
                    }
                    if ( imgUrl === null || imgUrl === undefined || imgUrl === '') {
                        message.error('请输入主理人微信文章的图片');
                        return;     
                    }
                }

                jsonDto.wxArticleList = (wxArticleList && JSON.parse(JSON.stringify(wxArticleList))) || []
            }
        } else if (chooseType === 3) {
            if (questionList?.length === 0) {
                message.error('请输入主理人答疑');
                return;
            } else {
                for (let i = 0, len = questionList.length; i < len; i++) {
                    const { question, answer } = questionList[i]
                    if ( question === null || question === undefined || question === '') {
                        message.error('请输入主理人答疑的问题');
                        return;     
                    }
                    if ( answer === null || answer === undefined || answer === '') {
                        message.error('请输入主理人答疑的答案');
                        return;     
                    }
                }
                jsonDto.questionList = (questionList && JSON.parse(JSON.stringify(questionList))) || []
            }
            
        } else if (chooseType === 4) {
            if (commentList?.length === 0) {
                message.error('请输入用户精评');
                return;
            } else {
                for (let i = 0, len = commentList.length; i < len; i++) {
                    const { title, content } = commentList[i]
                    if ( title === null || title === undefined || title === '') {
                        message.error('请输入用户精评的标题');
                        return;     
                    }
                    if ( content === null || content === undefined || content === '') {
                        message.error('请输入用户精评的内容');
                        return;     
                    }
                }
                jsonDto.commentList = (commentList && JSON.parse(JSON.stringify(commentList))) || []
            }
            
        }
        
        if (groupId) {
            jsonDto.groupId = groupId;
        }
        jsonDto.status = formData?.status;
        console.log(jsonDto)
        composeInvestment({ type: edit === 1 ? 'add' : 'update', jsonDto: JSON.stringify(jsonDto) }).then((res: any) => {
            const { status_code, data, status_msg } = res;
            if (status_code === 0) {
                if (edit === 1) {
                    message.success('产品信息保存成功');
                    onEditClose();
                    queryAllData();
                    
                } else {
                    if (flag === 1) {
                        this.setState({
                            investFlag: false
                        })
                        message.success('当前产品已关闭跟投');
                    } else {
                        message.success('产品信息保存成功');
                    }
                    let { handler, recommendFundList, groupInitFundList, wxArticleList, ...rest } = data;
                    let str = '', str1 = '';
                    recommendFundList.forEach((item: any) => {
                        str += `${item.code},${item.share};`
                    })
                    groupInitFundList.forEach((item: any) => {
                        str1 += `${item.code},${item.share};`
                    })
                    wxArticleList?.forEach((item: any) => {
                        let time = item?.publishTime?.replace(/-/g, '/');
                        item.realTime = new Date(time)?.getTime();
                    })
                    wxArticleList.sort((a: any, b: any) => {
                        return b.realTime - a.realTime;
                    })
                    this.setState({
                        wxArticleList,
                        formData: { ...obj, ...handler, ...rest, recommendFundList: str, groupInitFundList: str1, wxArticleList }
                    })
                    console.log({ ...obj, ...handler, ...rest, recommendFundList: str, groupInitFundList: str1, wxArticleList });
                    queryAllData();
                }
                
            } else if (status_code === 20625) {
                message.error(status_msg);
            } else {
                if (flag === 1) {
                    message.error(status_msg ?? '产品跟投状态变更失败，请重试')
                } else {
                    message.error(status_msg ?? '产品信息保存失败，请重试');
                }
                
            }
        }).catch((e: any) => {
            message.error(flag === 1 ? '产品跟投状态变更失败，请重试' : '产品信息保存失败，请重试');
        })
    }
    handleData = (str: string) => {
        let len = str.length;
        if (str.charAt(len - 1) === ';') {
            str = str.slice(0, len - 1);
        }
        let arr = str.split(';'), stockList: any = [];
        arr.forEach((item: any) => {
            let arr1 = item.split(',');
            stockList.push({
                "code": arr1[0],
                "share": arr1[1]
            })
        }) 
        return [...stockList];
    }
    add0 = (m:number) => {
        return m < 10 ? '0' + m : m 
    };
    uploadImage = (val: string) => {
        const { setFieldsValue } = this.props.form;
        this.setState({
            imgUrl: val
        })
        setFieldsValue({
            imgUrl: val
        })
    };

    addQuestion = (type: number) => {
        const { questionList, commentList } = this.state;
        if (type === 1) {
            let obj = {
                question: '',
                answer: ''
            }
            questionList.push(obj);
            this.setState({
                questionList: [...questionList]
            })
        } else {
            if (commentList?.length >= 10) {
                message.info('用户精评最多配置10个');
                return;
            }
            let obj = {
                title: '',
                content: ''
            }
            commentList.push(obj);
            this.setState({
                commentList: [...commentList]
            })
        }
        

        
    }
    deleteQuestion = (type: number, index: number) => {
        const { questionList, commentList } = this.state;
        if (type === 1) {
            questionList.splice(index, 1);
            this.setState({
                questionList: [...questionList]
            })
        } else {
            commentList.splice(index, 1);
            this.setState({
                commentList: [...commentList]
            })
        }
        
    }
    handleQuestion = (event: any, index: number, type: number) => {
        const { questionList, commentList } = this.state;
        let list = type === 0 ? [...questionList] : [...commentList];
        let value = event?.target?.value;
        if (value !== undefined && value !== null && list[index]) {
            list[index][`${type === 0 ? 'question' : 'title'}`] = value;
        }
        if (type === 0) {
            this.setState({
                questionList: [...list]
            })
        } else {
            this.setState({
                commentList: [...list]
            })
        }

    }
    handleAnswer = (event: any, index: number, type: number) => {
        const { questionList, commentList } = this.state;
        let list = type === 0 ? [...questionList] : [...commentList];
        let value = event?.target?.value;
        if (value !== undefined && value !== null && list[index]) {
            list[index][`${type === 0 ? 'answer' : 'content'}`] = value;
        }
        if (type === 0) {
            this.setState({
                questionList: [...list]
            })
        } else {
            this.setState({
                commentList: [...list]
            })
        }
    };

    handleWXTitle = (event: any, index: number) => {
        const { wxArticleList } = this.state;
        wxArticleList[index].title = event?.target?.value;
        this.setState({
            wxArticleList
        })
    };
    handleWXUrl = (event: any, index: number) => {
        const { wxArticleList } = this.state;
        wxArticleList[index].articleUrl = event?.target?.value;
        this.setState({
            wxArticleList
        })
    };
    handleWXImageUrl = (val: string, index: number) => {
        const { wxArticleList } = this.state;
        wxArticleList[index].imgUrl = val;
        this.setState({
            wxArticleList
        })
    };
    handleWXDate = (str: string, index: number) => {
        console.log(str);
        const { wxArticleList } = this.state;
        wxArticleList[index].publishTime = str;
        this.setState({
            wxArticleList
        })
    };
    addWXArticle = () => {
        const { wxArticleList } = this.state;
        wxArticleList.push({
            title: '',
            articleUrl: '',
            imgUrl: '',
            publishTime: ''
        })
        this.setState({
            wxArticleList
        }) 
    }
    deleteWXArticle = (index: number) => {
        const { wxArticleList } = this.state;
        wxArticleList.splice(index, 1);
        this.setState({
            wxArticleList
        })
    }

    getStockList = () => {
        const { groupId } = this.state;
        composeInvestment({ type: 'history', groupId}).then((res: any) => {
            const { status_code, status_msg, data } = res;
            if (status_code === 0) { 
              this.handleModal(true);
              let stockInfo:any = [];
              data.forEach((item: any) => {
                let { confDate, recommendFundList  } = item;
                let obj = {
                    confDate,
                    stockList: recommendFundList
                }
                stockInfo.push(obj);
              })
              
              this.setState({
                stockInfo
              })
            } else {
              message.error(status_msg || '查询调仓记录失败');
            }
        }).catch((e: any) => {
            message.error(e?.message || '查询调仓记录失败');
        })
    }
    handleModal = (flag = false) => {
        this.setState({
            visible: flag
        })
    }

    handleOk = () => {
        const { valueObj } = this.state;
        this.handleSave(valueObj, 1)
    }

    handleCancel = () => {
        this.setState({
            investFlag: false
        })
    }
    handleTradeDay = () => {
        getTradeDay().then((res: any) => {
            const { status_code, status_msg, data } = res;
            if (status_code === 0) { 
                let nowTime = Date.now();
                let str = dateFormat(nowTime);
                if (str === data) {
                    str = `${str.slice(0,4)}/${str.slice(4,6)}/${str.slice(6,8)}`
                    let baseTime = new Date(str)?.getTime()
                    let startTime = baseTime + 13 * 3600 * 1000;
                    let endTime = baseTime + 15 * 3600 * 1000;
                    if (nowTime >= startTime && nowTime <= endTime) {
                        this.setState({
                            isEdit: true
                        })
                    }
                }
                
            } else {
                message.error(status_msg ?? '网络错误，请重试');
            }
        }).catch((e: any) => {
            message.error('网络错误，请重试')
        })
    }
    componentDidMount() {
        const { chooseType, currentData } = this.props;
        if (chooseType === 0 && currentData?.imgUrl) {
            this.uploadImage(currentData.imgUrl)
        }
        this.handleTradeDay();
    }
    static getDerivedStateFromProps(nextProps: any, prevState: any) {
        const { imgUrl } = prevState.formData;
        if (nextProps.chooseType !== prevState.chooseType) {
            if (prevState.chooseType === 1) {
                return {
                    chooseType: nextProps.chooseType,
                    imgUrl
                };
            } else if (prevState.chooseType === 2) {
                const { wxArticleList } = prevState.formData;
                return {
                    wxArticleList: (wxArticleList && JSON.parse(JSON.stringify(wxArticleList))) || [],
                    chooseType: nextProps.chooseType,
                    imgUrl
                };
            } else if (prevState.chooseType === 3) {
                const { questionList } = prevState.formData;
                return {
                    questionList: (questionList && JSON.parse(JSON.stringify(questionList))) || [],
                    chooseType: nextProps.chooseType,
                    imgUrl
                };
            } else if (prevState.chooseType === 4) {
                const { commentList } = prevState.formData;
                return {
                    commentList: (commentList && JSON.parse(JSON.stringify(commentList))) || [],
                    chooseType: nextProps.chooseType,
                    imgUrl
                };
            } else {
                return {
                    chooseType: nextProps.chooseType,
                    imgUrl
                };
            }
            
        } else if (nextProps.remainShares !== prevState.formData.remainShares) {
            const { formData } = prevState;
            return {
                formData: {...formData, remainShares: nextProps.remainShares},
                imgUrl
            };
        }
        return null;
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const { formData, imgUrl, questionList, commentList, visible, stockInfo, wxArticleList, investFlag, isEdit } = this.state;
        const { chooseType, edit } = this.props;
        let startDate = formData.startDate;
        console.log(this.state);
        return (
            <>
                <Form {...this.formItemLayout} onSubmit={this.handleSubmit}>
                    { chooseType === 0 && (
                        <>
                            <Form.Item label="组合名称" wrapperCol={{span: 6}}>
                                {getFieldDecorator('groupName', {
                                    initialValue: formData.groupName,
                                    rules: [{ required: true, message: '请输入组合名称' }],
                                })(
                                    <Input maxLength={15}/>
                                )}
                            </Form.Item>
                            <Form.Item label="目标收益率（%）" wrapperCol={{span: 3}}>
                                {getFieldDecorator('targetRate', {
                                    initialValue: formData.targetRate,
                                    rules: [
                                        { required: true, message: '请输入目标收益率' },
                                        { pattern: /^[1-9][0-9]*$/, message: '请输入正整数'}        
                                    ],
                                })(
                                    <InputNumber />
                                )}
                            </Form.Item>
                            <Form.Item label="主理人名称" wrapperCol={{span: 3}}>
                                {getFieldDecorator('name', {
                                    initialValue: formData.name,
                                    rules: [{ required: true, message: '请输入主理人名称' }],
                                })(
                                    <Input maxLength={10}/>
                                )}
                            </Form.Item>
                            <Form.Item label="主理人头像" wrapperCol={{span: 12}}>
                                {getFieldDecorator('imgUrl', {
                                    rules: [{ required: true, message: '请上传主理人头像' }],
                                }
                                )(
                                    <UploadComponent uploadImage={this.uploadImage} imgUrl={imgUrl}/>
                                )}
                            </Form.Item>
                            <Form.Item label="主理人同顺号ID" wrapperCol={{span: 3}}>
                                {getFieldDecorator('tshId', {
                                    initialValue: formData.tshId,
                                    rules: [{ required: true, message: '请输入主理人同顺号ID' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="主理人客户号" wrapperCol={{span: 3}}>
                                {getFieldDecorator('dvCustid', {
                                    initialValue: formData.dvCustid,
                                    rules: [{ required: true, message: '请输入主理人客户号' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="主理人交易账号" wrapperCol={{span: 3}}>
                                {getFieldDecorator('dvAccountid', {
                                    initialValue: formData.dvAccountid,
                                    rules: [{ required: true, message: '请输入主理人交易账号' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="产品说明">
                                {getFieldDecorator('detail', {
                                    initialValue: formData.detail,
                                    rules: [{ required: true, message: '请输入产品说明' }],
                                })(
                                    <TextArea maxLength={100} />
                                )}
                            </Form.Item>
                            <Form.Item label="产品介绍页链接">
                                {getFieldDecorator('detailUrl', {
                                    initialValue: formData.detailUrl,
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item label="风险等级">
                                {getFieldDecorator('riskLevel', {
                                    initialValue: formData.riskLevel,
                                    rules: [{ required: true, message: '请选择风险等级' }],
                                })(
                                    <Select style={{ width: 120 }}>
                                        <Option value={1}>低</Option>
                                        <Option value={2}>中低</Option>
                                        <Option value={3}>中</Option>
                                        <Option value={4}>中高</Option>
                                        <Option value={5}>高</Option>
                                    </Select>
                                )}
                            </Form.Item>
                            <Form.Item label="运作期限" wrapperCol={{span: 3}}>
                                {getFieldDecorator('label', {
                                    initialValue: formData.label,
                                    rules: [{ required: true, message: '请输入运作期限' }],
                                })(
                                    <Input/>
                                )}
                                <span className={'u-block'}>例如：90-270天</span>
                            </Form.Item>
                            
                            <Form.Item label="组合开始时间">
                                {getFieldDecorator('startDate', {
                                    initialValue: startDate ? moment(startDate) : null,
                                    rules: [{ required: true, message: '请输入组合开始时间' }],
                                })(
                                    <DatePicker />
                                )}
                            </Form.Item>
                            <Form.Item label="组合总份数" wrapperCol={{span: 4}}>
                                {getFieldDecorator('totalShares', {
                                    initialValue: formData.totalShares,
                                    rules: [
                                        { required: true, message: '请输入组合总份数' },
                                        { pattern: /^[1-9][0-9]*$/, message: '请输入正整数'}
                                    ],
                                })(
                                    <InputNumber disabled={!!formData.totalShares}/>
                                )}
                                { edit === 2 && <span className={'u-block'}>剩余可投份数：{formData.remainShares ?? '--'}</span>}
                                
                            </Form.Item>
                            
                            <Form.Item label="每份最低起购金额" wrapperCol={{span: 4}}>
                                {getFieldDecorator('minAmountpershare', {
                                    initialValue: formData.minAmountpershare,
                                    rules: [
                                        { required: true, message: '请输入每份最低起购金额' },
                                        { pattern: /^([1-9][0-9]*(\.\d{1,2})?)|(0\.\d{1,2})$/, message: '请输入正数'}
                                    ],
                                })(
                                    <InputNumber />
                                )}
                            </Form.Item>
                            <Form.Item label="每份最高起购金额" wrapperCol={{span: 4}}>
                                {getFieldDecorator('maxAmountpershare', {
                                    initialValue: formData.maxAmountpershare,
                                    rules: [
                                        { required: true, message: '请输入每份最高起购金额' },
                                        { pattern: /^([1-9][0-9]*(\.\d{1,2})?)|(0\.\d{1,2})$/, message: '请输入正数'}
                                    ],
                                })(
                                    <InputNumber />
                                )}
                            </Form.Item>
                            <Form.Item label="是否开放跟投">
                                {getFieldDecorator('followStatus', {
                                    initialValue: formData.followStatus || '1', // 默认是
                                    rules: [{ required: true, message: '请选择是否开放跟投' }],
                                })(
                                    <Select style={{ width: 120 }} disabled={edit === 1}>
                                        <Option value="1">是</Option>
                                        <Option value="0">否</Option>
                                    </Select>
                                )}
                            </Form.Item>
                            <Form.Item label="当前推荐建仓成分基金及份数">
                                {getFieldDecorator('recommendFundList', {
                                    initialValue: formData.recommendFundList,
                                    rules: [{ required: true, message: '请输入成分基金及份数' }],
                                })(
                                    <Input disabled={(edit === 1 && false) || (edit === 2 && !isEdit)}/>
                                )}
                                <span className={'u-block'}>填写示例：基金代码1,份数;基金代码2,份数;...(注：交易日15：00前，基金和份数调整完成并保存后立即生效，调仓记录将在下方进行展示。上述填写内容需要使用英文逗号和英文分号)</span>
                                <span style={{color: '#1890ff', cursor: 'pointer'}} onClick={this.getStockList}>历史记录</span>
                            </Form.Item>
                            <Form.Item label="组合成分基金">
                                {getFieldDecorator('groupInitFundList', {
                                    initialValue: formData.groupInitFundList,
                                    rules: [{ required: true, message: '请输入组合成分基金' }],
                                })(
                                    <Input disabled={!!formData.groupInitFundList}/>
                                )}
                                <span className={'u-block'}>填写示例：基金代码1,0;基金代码2,0;...</span>
                            </Form.Item>
                        </>
                    )}
                    { chooseType === 1 && (
                        <>
                            <Form.Item label="运营入口说明">
                                {getFieldDecorator('operateInfo', {
                                    initialValue: formData.operateInfo,
                                })(
                                    <TextArea />
                                )}
                            </Form.Item>
                            <Form.Item label="运营入口链接">
                                {getFieldDecorator('operateUrl', {
                                    initialValue: formData.operateUrl,
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                        </>
                    )}
                    { chooseType === 2 && (
                        <>
                            <Form.Item label="主理人微信ID" wrapperCol={{span: 3}}>
                                {getFieldDecorator('wxId', {
                                    initialValue: formData.wxId,
                                    rules: [{ required: true, message: '请输入主理人微信ID' }],
                                })(
                                    <Input />
                                )}
                            </Form.Item>
                            <Form.Item className='m-wx-wrapper' label="主理人微信文章" labelCol={{span: 3}} wrapperCol={{span: 21}}>
                                <div className={styles['m-wx-article']}>
                                    <Button style={{marginTop: '10px', marginRight: '20px'}} type="primary" onClick={this.addWXArticle}>添加</Button>
                                    <ul>
                                        {
                                            wxArticleList?.length > 0 && wxArticleList.map((item: iWXArcicle, index: number) => {
                                                return (
                                                    <li key={index}>
                                                        <div className={styles['m-article']}>
                                                            <div className={styles['m-article-content']}>
                                                                <span>文章标题</span>
                                                                <Input value={item.title} onChange={(e) => this.handleWXTitle(e, index)}/>
                                                            </div>
                                                            <div className={styles['m-article-content']}>
                                                                <span>文章链接</span>
                                                                <TextArea value={item.articleUrl} onChange={(e) => this.handleWXUrl(e, index)}/>
                                                            </div>
                                                            <div className={styles['m-article-content']}>
                                                                <span>文章发布日期</span>
                                                                <DatePicker showTime onChange={(val, str) => this.handleWXDate(str, index)} value={item.publishTime ? moment(item.publishTime, 'YYYY-MM-DD hh:mm:ss') : null}/>
                                                            </div>
                                                        </div>
                                                        <div className={styles['m-article']}>
                                                            <UploadComponent uploadImage={(val: string) => this.handleWXImageUrl(val, index)} imgUrl={item.imgUrl}/>

                                                            <Popconfirm placement="left" title="确定要删除吗?" onConfirm={() => { this.deleteWXArticle(index) }}>
                                                                <Button type="primary">删除</Button>
                                                            </Popconfirm>
                                                        </div>
                                                    </li>
                                                )
                                            })
                                        }
                                    </ul>
                                </div>
                            </Form.Item>
                        </>
                    )}
                    
                    { chooseType === 3 && (
                        <Form.Item className='m-wx-wrapper' label="主理人答疑" labelCol={{span: 3}} wrapperCol={{span: 21}}>
                            <div className={styles['m-user-question']}>
                                <Button style={{marginTop: '10px', marginRight: '20px'}} type="primary" onClick={() => this.addQuestion(1)}>添加</Button>
                                <ul>
                                    {
                                        questionList?.length > 0 && questionList.map((item: iQuestion, index: number) => {
                                            return (
                                                <li key={index}>
                                                    <div>
                                                        <span>问题</span>
                                                        <Input value={item.question} onChange={(e) => this.handleQuestion(e, index, 0)}/>
                                                    </div>
                                                    <div>
                                                        <span>答案</span>
                                                        <TextArea value={item.answer} onChange={(e) => this.handleAnswer(e, index, 0)}/>
                                                    </div>
                                                    <Popconfirm placement="left" title="确定要删除吗?" onConfirm={() => { this.deleteQuestion(1, index) }}>
                                                        <Button type="primary">删除</Button>
                                                    </Popconfirm>
                                                </li>
                                            )
                                        })
                                    }
                                </ul>
                            </div>
                        </Form.Item>
                    )}
                    { chooseType === 4 && (
                        <Form.Item className='m-wx-wrapper' label="用户精评" labelCol={{span: 3}} wrapperCol={{span: 21}}>
                            <div className={styles['m-user-question']}>
                                <Button style={{marginTop: '10px', marginRight: '20px'}} type="primary" onClick={() => this.addQuestion(2)}>添加</Button>
                                <ul>
                                    {
                                    commentList?.length > 0 && commentList.map((item: iCommentList, index: number) => {
                                            return (
                                                <li key={index}> 
                                                    <div>
                                                        <span>标题</span>
                                                        <Input value={item.title} onChange={(e) => this.handleQuestion(e, index, 1)}/>
                                                    </div>
                                                    <div>
                                                        <span>内容</span>
                                                        <TextArea value={item.content} onChange={(e) => this.handleAnswer(e, index, 1)}/>
                                                    </div>
                                                    <Popconfirm placement="left" title="确定要删除吗?" onConfirm={() => { this.deleteQuestion(2, index) }}>
                                                        <Button type="primary">删除</Button>
                                                    </Popconfirm>
                                                </li>
                                            )
                                    })
                                }
                                </ul>
                            </div>
                        </Form.Item>
                    )}
                    <Form.Item style={{textAlign: 'right'}} wrapperCol={{span: 24}}>
                        <Button type="primary" htmlType="submit">
                            保存
                        </Button>
                    </Form.Item>
                </Form>
                <StockModal title="历史记录" visible={visible} handleModal={this.handleModal} stockInfo={stockInfo}/>
                <Modal 
                    width={420} 
                    visible={investFlag} 
                    onOk={this.handleOk} 
                    onCancel={this.handleCancel}
                    okText="确定"
                    cancelText="取消">
                    <p>产品将由开放状态调整为关闭跟投，确定要进行变更吗？</p>
                </Modal>
            </>
                
        )
    }
}
const WrappedMessageForm = Form.create<MessageFormProps>({ name: 'message' })(messageForm);
export default WrappedMessageForm