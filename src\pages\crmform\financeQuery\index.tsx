import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Row, DatePicker, message } from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
// import FormRender from 'form-render/lib/antd';
// import schema from './form.json';

const { fetchFinance } = api;

class financeQuery extends React.Component {
    constructor(props: any) {
        super(props)
        this.state = {
            financeQueryStartTime: '',
            financeQueryEndTime: '',
        }
    }

    onchangeFinanceQueryDownLoadTime = (value: any) => {
        function getTime(Date: Date) {
            let _year: any = Date.getFullYear();
            let _month: any = Date.getMonth() + 1;
                _month = _month < 10 ? '0' + _month : _month;
            let _day: any = Date.getDate();
                _day = _day < 10 ? '0' + _day : _day;
            return `${_year}${_month}${_day}`;
        }
        let _startTime = getTime(value[0]._d);
        
        let _endTime = getTime(value[1]._d);
        console.log(_startTime, _endTime)
        this.setState({
            financeQueryStartTime: _startTime,
            financeQueryEndTime: _endTime
        }, () => {console.log(this.state)})
    }

    downloadHigherFinanceQuery = () => {
        if (!this.state.financeQueryStartTime || !this.state.financeQueryEndTime) {
            message.error('请填写时间');
            return;
        }
        fetchFinance({
            startDate: this.state.financeQueryStartTime,
            endDate: this.state.financeQueryEndTime,
            responseType: 'blob',
        }).then((res: any) => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '高端理财私募基金' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    render() {
        return (
            <section className="financeQuery">
                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-16461'}
                    extra={'上线时间：2020/11/05'}
                >
                    <Row>
                    <header>
                    <span
                        style={{fontSize: '18px'}}>高端理财专区 私募基金列表</span>
                        </header>
                        <article style={{marginTop: '20px'}}>
                        <DatePicker.RangePicker
                            showTime
                            // onChang={ (value: any, dateString: any) => {console.log(value, dateString)}}
                            onOk={(value) => {this.onchangeFinanceQueryDownLoadTime(value)}}/>
                        </article>
                        
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadHigherFinanceQuery}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>
            </section>
        )
    }
}

export default financeQuery