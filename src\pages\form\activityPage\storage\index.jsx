/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React, { Fragment } from 'react';
import {But<PERSON>, Card, Row, message, Popconfirm, Collapse, DatePicker, Select, Input} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';

const { fetchStorage } = api;



@autobind
class storage extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            weChatNumber: 0,
            tyjNumber: 0,
        };
    }

    componentDidMount () {
        fetchStorage({
            type: 'query'
        }).then((res) => {
            this.setState({
                weChatNumber: res.wechatNum,
                tyjNumber: res.tyjNumber
            })
        })
    }

    render () {
        const {
            weChatNumber,
            tyjNumber
        } = this.state

        return (
            <section className="codeInterface">
                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-17633'}
                    extra={'818赛车活动'}
                >
                    <Row>
                        <p className="u-j-middle">
                            <span>微信红包剩余数量</span>
                            <span>{weChatNumber}</span>
                        </p>
                        <p className="u-j-middle">
                            <span>体验金剩余金额</span>
                            <span>{tyjNumber}</span>
                        </p>
                    </Row>
                </Card>
            </section>
        )
    }
}

export default storage;