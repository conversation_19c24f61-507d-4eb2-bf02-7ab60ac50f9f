import React, { useEffect, useState } from 'react';
import { Modal, Table, message, Popconfirm, Button } from  'antd';

import api from 'api'
const {
    fetchConfiguredArticles,
    deleteArticle
} = api

export default React.memo(function({
    isShow,
    setIsShow,
    refresh
}: any) {
    const [pagination, setPagination] = useState<Pagination>({current: 1, pageSize: 10})     
    const [loading, setLoading] = useState(false) //表格加载
    const [articles, setArticles] = useState<article[]>([]) //文章
    const [cancelArticles, setCancelArticles] = useState<string[]>([]) //需要取消配置的文章

    useEffect(() => {
        if (!isShow) return
        handleTableChange(pagination)
    }, [isShow])

    function handleTableChange(pagination: Pagination) {
        setLoading(true)
        fetchConfiguredArticles({
            offer: pagination.current,
            limit: pagination.pageSize
        }).then((data: any) => {
            setLoading(false)
            if (data.status_code === 0) {
                setArticles(data?.data?.configedInfoList || [])
                let _pagination: Pagination = {...pagination}
                _pagination.total = data.data.totalNum
                setPagination(_pagination)
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            setLoading(false)
            console.log(e.message)
            message.error('系统错误请稍候再试～')
        })
    }

    /**
     * 复制
     * @param data 
     */
    function clipboard(data: string) {
        try {
            let input = document.createElement('input');
            input.value = data;
            input.setAttribute('readonly', 'readonly');
            let dialog = document.createElement('div');
            dialog.style.position = 'fixed';
            dialog.style.top = '0';
            dialog.style.left = '0';
            dialog.style.bottom = '0';
            dialog.style.right = '0';
            document.body.appendChild(dialog);
            dialog.appendChild(input);
            input.focus();
            input.setSelectionRange(0, input.value.length);
            let copyResult = document.execCommand('copy');
            if (copyResult) {
                message.success('复制成功')
            } else {
                message.error('复制失败')
            }
            document.body.removeChild(dialog);
        } catch (e) {
            console.error('copy to clipboard fail' + e)
        }
    };

    /**
     * 操作配置
     * @param itemId 
     */
    function handleConfig(itemId: string) {
        if (cancelArticles.indexOf(itemId) > -1) {
            let _cancelArticles: string[] = [...cancelArticles]
            _cancelArticles.splice(cancelArticles.indexOf(itemId), 1)
            setCancelArticles(_cancelArticles)
        } else {
            let _cancelArticles: string[] = [...cancelArticles]
            _cancelArticles.push(itemId)
            setCancelArticles(_cancelArticles)
        }
    }

    /**
     * 保存
     */
    function save() {
        deleteArticle({
            data: cancelArticles.join(',')
        }).then((data: any) => {
            if (data.status_code === 0) {
                setIsShow(false)
                setCancelArticles([])
                refresh && refresh()
                message.success('操作成功')
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            console.log(e.message)
            message.error('系统错误请稍候再试～')
        })
    }

    return (
        <Modal 
        title="已配置文章" 
        visible={isShow} 
        width={1000}
        onCancel={() =>{setIsShow(false)}}
        footer={
            <div className="u-r-middle">
                <Button type="primary" onClick={() =>{setIsShow(false)}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={save}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button type="danger" >保存</Button>
                </Popconfirm>
            </div>
        }
        >
            <Table 
            dataSource={articles}
            // rowKey={(item: article) => item.itemId}
            pagination={pagination}
            loading={loading}
            onChange={handleTableChange}
            >
                <Table.Column title="文章id" dataIndex={'itemId'} key="itemId"></Table.Column>
                <Table.Column title="文章标题" dataIndex={'title'} key="title"></Table.Column>
                <Table.Column title="配置个基" dataIndex={'batchInfo'} key="batchInfo" render={(batchInfo) => {
                    let _text: string = batchInfo.funds?.join(',')
                    return (
                        <div className="u-j-middle" style={{width: 150}}>
                            <p className="u-w100 f-ellipsis">{_text}</p>  
                            <a style={{color: 'blue'}} onClick={() => {clipboard(_text)}}>复制</a>
                        </div>
                    )
                }}></Table.Column>
                <Table.Column title="操作时间" dataIndex={'batchInfo'} key="batchInfo" render={(batchInfo) => {
                    return batchInfo.opTime
                }}
                ></Table.Column>
                <Table.Column title="操作" key="action" render={(text, record: article, index) => {
                    let _itemId = `${record.itemId}_${record.index}`
                    return (
                        <a key={index} style={{color: 'blue'}} onClick={() => {
                            handleConfig(_itemId)
                        }}>
                            {
                                cancelArticles.indexOf(_itemId) > -1
                                ?
                                '恢复配置'
                                :
                                '取消配置'
                            }
                        </a>
                    )
                }}></Table.Column>
            </Table>
        </Modal>
    )

})