:global {
    .strategy-header {
        width: 100%;
        display: flex;
        flex-flow: column nowrap;
        margin-bottom: 10px;
        .strategy-type-title {
            font-weight: bold;
            font-size: 25px;
            color: #000000;
        }
        button {
            width: 100px;
            align-self: flex-end;
        }
    }
    .strategy-footer {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
        button {
            width: 100px;
        }
    }
    .strategy-addType { 
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        margin-bottom: 20px;
        .warning {
            color: red;
            margin-left: 10px;
        }
    }
    .strategy-picture {
    }
    .home-strategy-type-name {
        display: flex;
    }
}