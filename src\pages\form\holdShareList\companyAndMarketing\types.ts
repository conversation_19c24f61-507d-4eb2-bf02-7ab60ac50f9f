export interface ContentProps {
  /** 展示标签 */
  label: string;
  /** 标题 */
  title: string;
  /** 跳转链接 */
  url: string;
  /** 起始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
  /** 推送类型 0：基金 1：财富有伴 */
  type: EnumType;
  /** 推送的基金 */
  dst: string;
  /** 是否 push 0：否 1：是 */
  push: string;
  /** update 时, id 为空新增，id 不为空修改，其余 id 不可为空 */
  id?: string,
  /** push 时间 */
  pushTime?: string;
  /** 审核状态 0：未审核 1：已审核 */
  status: string;
  /** 编辑人 */
  editor: string;
  /** 审核人 */
  aeditor?: string;
}
export enum EnumType {
  Fund = '0',
  Wealth = '1'
}
export enum EnumPush {
  No = '0',
  Yes = '1'
}
export interface FormDataProps {
  companyData: ContentProps
}
export enum EditContentType {
  Add,
  Edit,
  Review
}
