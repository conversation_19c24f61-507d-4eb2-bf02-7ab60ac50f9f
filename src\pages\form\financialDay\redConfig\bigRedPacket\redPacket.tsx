import React, { useState } from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Drawer } from 'antd';
import WrappedInvestDrawer from './investDrawer';

export interface iBigRed{
  name: string,
  activityId: string,
  redPaperId: string,
  threshold: string,
  minusMoney: string,
  status: string,
  key?: string;
}

interface iProps {
  handleData: (data: iBigRed[]) => void;
  cardData: iBigRed[];
  isEdit: boolean;
}
function RedPacket({handleData, cardData, isEdit}: iProps) {
    const [edit, setEdit] = useState(0); // 1 新增 2 编辑
    const [currentData, setCurrentData] = useState<iBigRed>({
      name: '',
      activityId: '',
      redPaperId: '',
      threshold: '',
      minusMoney: '',
      status: '0'
    });

    const handleEdit = (val: iBigRed) => {
      setCurrentData({...val});
      setEdit(2);
    }
    const handleDelete = (num: number) => {
      let arr = cardData && JSON.parse(JSON.stringify(cardData));
      arr.splice(Number(num), 1);
      arr?.forEach((item: iBigRed, index: number) => {
        item.key = index.toString();
      })
      handleData(arr);
    }
    const modifyPlanData = (data: iBigRed) => {
      let arr = cardData && JSON.parse(JSON.stringify(cardData));
      if (data?.key) {
        let index: any = data.key;
        arr[index] = data;
      } else {
        arr.push(data);
      }
      arr?.forEach((item: iBigRed, index: number) => {
        item.key = index.toString();
      })
      handleData(arr);
    }
    const onEditClose = () => {
      setEdit(0);
    }
    const newAdd = () => {
      setCurrentData({
        activityId: '',
        threshold: '',
        redPaperId: '',
        minusMoney: '',
        name: '',
        status: '0'
      });
      setEdit(1);
    }
    return (
      <div style={{marginTop: 40}}>
        
        <ul>
          { cardData?.map((item, index) => {
            return (
              <div className="u-j-middle" key={index} >
                <span style={{width: '15%'}}>{item.name}</span>
                <span style={{width: '15%'}}>{item.activityId}</span>
                <span style={{width: '15%'}}>{item.redPaperId}</span>
                <span style={{width: '15%'}}>{item.threshold}</span>
                <span style={{width: '10%'}}>{item.minusMoney}</span>
                <span style={{width: '10%'}}>{item.status === '2' ? '已抢完' : item.status === '0' ? '可领取' : '--'}</span>
                <div style={{width: '20%'}}>
                  <Button type="primary" style={{ marginRight: '20px' }} onClick={() => handleEdit(item)} disabled={!isEdit}>编辑</Button>
                  <Popconfirm title="确定要删除吗?" onConfirm={() => handleDelete(index)} disabled={!isEdit}>
                    <Button type="danger" disabled={!isEdit}>删除</Button>
                  </Popconfirm>
                </div>
              </div>
            )
          }) }
        </ul>
        <div style={{marginTop: 20}}>
          <Button type="primary" style={{marginRight: 20}} onClick={newAdd} disabled={!isEdit}>新增</Button>
        </div>
        <Drawer
          width={1000}
          title="新增红包配置"
          placement="right"
          closable
          onClose={onEditClose}
          visible={Boolean(edit)}
          >
          { Boolean(edit) && <WrappedInvestDrawer currentData={currentData} onEditClose={onEditClose} handleData={modifyPlanData}></WrappedInvestDrawer> }
        </Drawer>
      </div>
    )
}

export default React.memo(RedPacket);