/*
 * 机构客户数据维护
 * @Author: <PERSON><PERSON><PERSON><PERSON>@myhexin.com
 * @Date: 2023-02-16 15:56:07
 * @Last Modified by: z<PERSON><PERSON><PERSON>@myhexin.com
 * @Last Modified time: 2023-02-16 20:36:57
 */
import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';

import { Button, Col, Collapse, DatePicker, message, Modal, Row, Select, Spin, Table, Card, Input, ConfigProvider, Pagination } from 'antd';
const { Panel } = Collapse;
const { Option } = Select;
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { List, operateEnum, operateMap, salesManFilterQueryProps, strategyKeyMap } from '../type'
import api from 'api';
import { strategyMap } from '../type';
import record from '../../redMoney/record';
import zhCN from 'antd/es/locale/zh_CN';
import styles from '../index.less'
import { ExportButton, ExportType } from '../components/exportButton';
const { fetchSalesManList, fetchSalseManFilter, postEditUser } = api;

interface IProps { }
const salseMan: FC<IProps> = () => {
  const [isListLoading, setIsListLoading] = useState<boolean>(false); // 列表加载loading
  const [dataSource, setDataSource] = useState<any[]>();
  const [companyOption, setCompanyOption] = useState<any>();
  const [companyKeyOption, setCompanyKeyOption] = useState<any>();
  const [modalShow, setModalShow] = useState(false);
  const [nowEditInfo, setNowEditInfo] = useState<List>({});
  const [disabled, setDisabled] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [operateType, setOperateType] = useState('-1');
  const [searchItem, setSearchItem] = useState<any>({
    strategyCode: '',
    companyCode: ''
  });
  const [pageNum, setPageNum] = useState(1);
  const [tableTotal, setTableTotal] = useState(0);
  const updateInfo = record => {
    setOperateType(operateEnum.UPDATE)
  };
  const deleteInfo = record => {
    setOperateType(operateEnum.DELETE)
  };
  // 页容量 控制器
  const handlePageSize = (pageNum: number, pageSize: number) => {
    console.log('页容量控制器', pageNum, pageSize);
    setPageSize(pageSize);
  };
  const addInfo = () => {
    setOperateType(operateEnum.ADD)
  }
  useEffect(() => {
    const pageObj = { ...searchItem, pageNum: pageNum, pageSize: pageSize }
    pageNum !== 1 && pageSize !== 10 && fetchSalesManList({}, getUrlParmFromObject(pageObj))
      .then((res: any) => {
        if (res.status_code === 0) {
          setDataSource(res.data.saleInfoList)
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
  }, [pageSize, pageNum])
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '姓名',
      dataIndex: 'saleName',
      key: 'saleName',
    },
    {
      title: '策略',
      dataIndex: 'strategyName',
      key: 'strategyName',
    },
    {
      title: '联系方式',
      dataIndex: 'telephoneNo',
      key: 'telephoneNo',
    },
    {
      title: '公司',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: '工号',
      dataIndex: 'workNo',
      key: 'workNo',
    },
    {
      title: '操作',
      dataIndex: 'options',
      render: (text: unknown, record: any) => {
        return (
          <section>
            <Button
              onClick={() => {
                setModalShow(true);
                // updateInfo(record);
                setOperateType(operateEnum.UPDATE)
                console.log('nowedit', record)
                setNowEditInfo(record);
              }}
              type="primary"
            >
              修改
            </Button>
            <Button
              onClick={() => {
                setModalShow(true);
                setNowEditInfo(record);
                setOperateType(operateEnum.DELETE)
              }}
              type="danger"
            >
              删除
            </Button>
          </section>
        );
      },
    },
  ];
  const getUrlParmFromObject = obj => {
    let url = '?';
    Object.keys(obj || {})?.forEach((key, index) => {
      if (obj[key]) {
        url += key + '=' + obj[key] + (index !== Object.keys(obj || {}).length - 1 ? '&' : '');
      }
    });
    return url;
  };
  useEffect(() => {
    fetchSalesManList({}, '').then(res => {
      console.log(res);
      if (res.status_code === 0) {
        setDataSource(res.data.saleInfoList);
        setTableTotal(res.data.count)
      } else {
        message.warn(res.message);
      }
    }).catch((res) => {
    })
    fetchSalseManFilter().then(res => {
      const tcompanyOption = {}
      const kcompanyOption = {}
      if (res.status_code === 0) {
        (res.data as salesManFilterQueryProps).companyList.map((company) => {
          tcompanyOption[company.companyCode] = company.companyName
          kcompanyOption[company.companyName] = company.companyCode
        })
        setCompanyOption(tcompanyOption)
        setCompanyKeyOption(kcompanyOption)
        console.log(tcompanyOption)
      } else {
        message.warn(res.message);
      }

    }).catch(err => {

    })
  }, []);
  useEffect(() => {
    const pageObj = { ...searchItem, pageNum: pageNum, pageSize: pageSize }
    fetchSalesManList({}, getUrlParmFromObject(pageObj))
      .then((res: any) => {
        if (res.status_code === 0) {
          setDataSource(res.data.saleInfoList)
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
  }, [pageSize, pageNum])
  // 用户名称修改事件
  const handlePageNum = (current: number) => {
    setPageNum(current)
  }
  const handleOk = () => {
    let reqBody = {};
    if (operateType === operateEnum.ADD) {

      reqBody = { ...nowEditInfo, isValid: '1' }
    } else if (operateType === operateEnum.DELETE) {
      reqBody = { ...nowEditInfo, isValid: '0' }
    } else if (operateType === operateEnum.UPDATE) {
      reqBody = { ...nowEditInfo, isValid: '1', companyName: companyOption[nowEditInfo?.companyCode] }
    }
    postEditUser(reqBody).then((res: any) => {
      if (res.status_code === 0) {
        message.success('录入成功');
        setTimeout(() => { location.reload(); }, 500)
      } else {
        message.warn(res.message);
      }
    })
      .catch((err: unknown) => {
        console.log(err);
        message.warn('系统错误');
      });
  }
  return (
    <div>
      <Row>
        <Col style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <span>策略</span>
            <Select
              showSearch={true}
              style={{ width: 200, marginRight: 20 }}
              placeholder="策略"
              onChange={(val) => {
                setSearchItem(prev => ({ ...prev, strategyCode: strategyKeyMap[val as string] }))
              }}
              mode="multiple"
            >
              {Object.keys(strategyMap)?.map((nam, index) => {
                return <Option value={strategyMap[nam]} key={nam}>{strategyMap[nam]}</Option>
              })}
            </Select>
            <span>公司</span>
            <Select
              showSearch={true}
              style={{ width: 200 }}
              placeholder="公司"
              onChange={(val) => {
                console.log('val', val)
                setSearchItem(prev => ({ ...prev, companyCode: val?.map(v => (companyKeyOption[v])).join(',') }))
              }}
              mode="multiple"
            >
              {Object.keys(companyOption || {})?.map((nam, index) => {
                return <Option value={companyOption[nam]} key={nam}>{companyOption[nam]}</Option>
              })}
            </Select>
          </div>
          <div>
            <Button onClick={() => {
              fetchSalesManList({}, getUrlParmFromObject(searchItem))
                .then((res: any) => {
                  if (res.status_code === 0) {
                    setDataSource(res.data.saleInfoList)
                    setTableTotal(res?.data?.count)
                  } else {
                    message.warn(res.status_msg);
                  }
                })
                .catch((err: unknown) => {
                  console.log(err);
                });
            }}>查询</Button>
            <Button onClick={() => {
              setOperateType(operateEnum.ADD)
              setNowEditInfo({
                saleName: '',
                workNo: '',
                companyCode: '',
                telephoneNo: '',
                strategyCode: ''
              })
              setModalShow(true)
            }}>新增</Button>
            <ExportButton exportType={ExportType.SALE_MAN_TYPE} data={searchItem} />
          </div>
        </Col>
      </Row>
      <ConfigProvider locale={zhCN}>
        <Table columns={columns}
          style={{ marginTop: 50 }}
          dataSource={dataSource}
          rowKey={record => record.index}
        >
        </Table>
      </ConfigProvider>
      <div className={classnames(styles['m-table-pagination'], styles['m-top'])}>
        <p>
          当前第 <span>{pageNum}</span>/{Math.ceil(tableTotal / 10)} 页
        </p>
        <ConfigProvider locale={zhCN}>
          <Pagination
            className={classnames(styles['m-pagination'])}
            current={pageNum}
            total={tableTotal}
            showSizeChanger
            onChange={handlePageNum}
            pageSizeOptions={['10', '20', '30', '50']}
            onShowSizeChange={handlePageSize}
            hideOnSinglePage={tableTotal < 10}
          />
        </ConfigProvider>
      </div>
      <Modal
        title={`${operateMap[operateType]}销售人员`}
        visible={modalShow}
        onOk={handleOk}
        onCancel={() => { setModalShow(false) }}
      >
        <Input
          disabled={operateType === operateEnum.DELETE}
          value={nowEditInfo.saleName}
          onChange={e => {
            e.persist()
            setNowEditInfo((prev) => {
              return {
                ...prev, saleName: e.target.value,
              }
            })
          }}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<span>姓名：</span>}
        />
        <Input
          disabled={operateType === operateEnum.DELETE}
          value={nowEditInfo.workNo}
          onChange={e => {
            e.persist()
            setNowEditInfo((prev) => {
              return {
                ...prev, workNo: e.target.value,

              }
            })
          }}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<span>工号：</span>}
        />
        <Col>
          <span>策略：</span>
          <Select
            disabled={operateType === operateEnum.DELETE}
            showSearch={true}
            placeholder="策略"
            value={strategyMap?.[nowEditInfo.strategyCode]}
            style={{ marginBottom: 10, width: 300 }}
            onChange={val => {
              setNowEditInfo((prev) => {
                return {
                  ...prev, strategyCode: strategyKeyMap[val as string],
                }
              })
            }}
          >
            {Object.keys(strategyMap || {}).map((sKey, index) => {
              return <Option value={strategyMap[sKey]} key={`optionStar-${sKey}`}>{strategyMap[sKey]}</Option>
            })}
          </Select>
        </Col>

        <Input
          value={nowEditInfo.telephoneNo}
          onChange={e => {
            e.persist()
            setNowEditInfo((prev) => {
              return {
                ...prev, telephoneNo: e.target.value,

              }
            })
          }}
          disabled={operateType === operateEnum.DELETE}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<span>联系方式：</span>}
        />
        <Col>
          <span>公司：</span>
          <Select
            showSearch={true}
            value={companyOption?.[nowEditInfo?.companyCode]}
            style={{ marginBottom: 10, width: 300 }}
            placeholder="公司"
            onChange={val => {
              setNowEditInfo((prev) => {
                return {
                  ...prev, companyCode: companyKeyOption[val as string],
                }
              })
            }}
            disabled={operateType === operateEnum.DELETE}
          >
            {Object.keys(companyOption || {})?.map((nam, index) => {
              return <Option value={companyOption[nam]} key={`option-${nam}`}>{companyOption[nam]}</Option>
            })}
          </Select>
        </Col>
      </Modal>


    </div>
  );
};
export default salseMan;
