import React from 'react';
import { Form, Input, <PERSON><PERSON>, Card } from 'antd';
import UploadForm from '../Upload';

const DynamicCommentForm = props => {
  const { getFieldDecorator, commentNum, setCommentNum } = props;
  const commentList = [];
  for (let i = 0; i < commentNum; i++) {
    const commentItem = (
      <Card key={i}>
        <Form.Item label="用户昵称">
          {getFieldDecorator(`comments.commentList[${i}].nickname`, {
            rules: [{ required: true, message: '请填写用户昵称' }],
          })(<Input />)}
        </Form.Item>
        <UploadForm
          getFieldDecorator={getFieldDecorator}
          label="用户头像"
          fieldLocation={`comments.commentList[${i}].avatar`}
          required={true}
        />
        <Form.Item label="初始点赞数">
          {getFieldDecorator(`comments.commentList[${i}].likeNumber`, {
            rules: [{ required: true, message: '请输入初始点赞数' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="精评内容">
          {getFieldDecorator(`comments.commentList[${i}].commentContent`, {
            rules: [{ required: true, message: '请输入精评内容' }],
          })(<Input />)}
        </Form.Item>
      </Card>
    );
    commentList.push(commentItem);
  }
  const handleAddComment = () => {
    setCommentNum(commentNum + 1);
  };
  const handleDeleteComment = () => {
    setCommentNum(commentNum - 1);
  };
  return (
    <>
      {commentList}
      <Button onClick={handleAddComment}>添加评论</Button>
      <Button onClick={handleDeleteComment} disabled={commentNum === 0}>
        删除评论
      </Button>
    </>
  );
};

export default DynamicCommentForm;
