{"type": "object", "properties": {"functionName": {"title": "功能名称", "type": "string", "ui:labelWidth": 110, "ui:width": "55%"}, "functionId": {"title": "功能id", "type": "string", "ui:labelWidth": 94, "ui:width": "55%", "pattern": "^[\\x20-\\x7E]+$", "message": {"pattern": "请使用英文字符输入，不要含空格和换行"}}, "notes1": {"type": "string", "ui:widget": "html", "default": "找对应任务的前端开发提供,使用英文字符输入", "ui:width": "45%"}, "applicationType": {"title": "应用类型", "type": "array", "ui:labelWidth": 110, "items": {"type": "string"}, "enum": [1, 2, 3], "enumNames": ["APP", "SDK-普通版", "SDK-至尊版"]}, "systemSelection": {"title": "系统选择", "type": "string", "enum": ["ios", "android"], "enumNames": "{{['iOS端', 'Android端']}}", "ui:width": "30%", "ui:widget": "radio"}, "notes2": {"type": "string", "ui:width": "70%", "ui:widget": "html", "default": "版本号填区间，左小右大，全留空则代表默认应用到全部版本，一边留空则是>=（或<=）"}, "iosAppLeft": {"title": "iOS端APP版本号", "type": "string", "ui:hidden": "{{formData.systemSelection === 'android'}}", "ui:width": "278px", "ui:labelWidth": 156, "ui:widget": "customInputLeft", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "iosAppRight": {"type": "string", "ui:hidden": "{{formData.systemSelection === 'android'}}", "ui:width": "220px", "ui:widget": "customInputRight", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "iosSystemLeft": {"title": "iOS端系统版本号", "type": "string", "ui:hidden": "{{formData.systemSelection === 'android'}}", "ui:width": "278px", "ui:labelWidth": 156, "ui:widget": "customInputLeft", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "iosSystemRight": {"type": "string", "ui:hidden": "{{formData.systemSelection === 'android'}}", "ui:width": "120px", "ui:widget": "customInputRight", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "notes6": {"type": "string", "ui:widget": "html", "ui:hidden": "{{formData.systemSelection === 'android'}}", "default": "APP版本号格式为XX.XX.XX，使用英文字符，例如6.23.11", "ui:width": "45%"}, "notes7": {"type": "string", "ui:widget": "html", "ui:hidden": "{{formData.systemSelection === 'android'}}", "default": "系统版本号格式为XX.XX.XX，使用英文字符，例如14.6写作14.6.0", "ui:width": "45%"}, "androidAppLeft": {"title": "Android端APP版本号", "type": "string", "ui:hidden": "{{formData.systemSelection === 'ios'}}", "ui:width": "306px", "ui:labelWidth": 185, "ui:widget": "customInputLeft", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "androidAppRight": {"type": "string", "ui:hidden": "{{formData.systemSelection === 'ios'}}", "ui:width": "196px", "ui:widget": "customInputRight", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "androidSystemLeft": {"title": "Android端系统版本号", "type": "string", "ui:hidden": "{{formData.systemSelection === 'ios'}}", "ui:width": "306px", "ui:labelWidth": 185, "ui:widget": "customInputLeft", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "androidSystemRight": {"type": "string", "ui:hidden": "{{formData.systemSelection === 'ios'}}", "ui:width": "120px", "ui:widget": "customInputRight", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "notes8": {"type": "string", "ui:widget": "html", "ui:hidden": "{{formData.systemSelection === 'ios'}}", "default": "APP版本号格式为XX.XX.XX，使用英文字符，例如6.23.11", "ui:width": "45%"}, "notes9": {"type": "string", "ui:widget": "html", "ui:hidden": "{{formData.systemSelection === 'ios'}}", "default": "系统版本号格式为XX.XX.XX，使用英文字符，例如4.4写作4.4.0", "ui:width": "45%"}, "applicationMarket": {"type": "array", "items": {"type": "string"}, "ui:hidden": "{{formData.systemSelection === 'ios'}}", "ui:labelWidth": 110, "ui:widget": "selectMode"}, "userIdType": {"title": "用户id类型", "type": "string", "enum": ["1", "2", "3", "4"], "enumNames": ["IID(设备识别码)", "User-id(手抄用户号)", "Cust-id(基金客户号)", "DeviceCode(设备指纹)"], "ui:widget": "select", "ui:width": "30%", "ui:labelWidth": 122, "ui:disabled": "{{formData.distributionMode === '2' }}"}, "notes4": {"type": "string", "ui:widget": "html", "default": "1、IID选择数字0-9或者小写字母a-z或者大写字母A-Z；2、Userid选择数字0-9；3、Custid选择数字0-9；4，DeviceCode选择数字0-9或者大写字母A-Z", "ui:width": "70%"}, "distributionMode": {"title": "分发模式", "type": "string", "enum": ["1", "2"], "enumNames": "{{['按规则筛选', '手动导入文件']}}", "ui:widget": "radio", "ui:disabled": "{{formData.userIdType === '1' || formData.userIdType === '4' }}"}, "contentRules": {"title": "规则内容", "type": "string", "ui:hidden": "{{formData.distributionMode === '2'}}", "ui:width": "50%", "pattern": "^[A-Za-z0-9]+([,][A-Za-z0-9]+)*$", "message": {"pattern": "请使用英文字符输入，多个以“,”分隔"}}, "notes5": {"type": "string", "ui:widget": "html", "default": "使用英文字符输入，多个以“,”分隔", "ui:hidden": "{{formData.distributionMode === '2'}}", "ui:width": "50%"}, "userIdList": {"title": "导入客户号", "type": "string", "ui:hidden": "{{formData.distributionMode === '1'}}", "ui:widget": "fileImport", "ui:labelWidth": 126}, "mode": {"title": "开关", "type": "string", "enum": ["1", "2", "0"], "enumNames": ["全量开", "匹配规则", "全量关"], "ui:widget": "select", "ui:width": "15%", "ui:labelWidth": 82}}, "required": ["functionName", "functionId", "applicationType", "systemSelection", "userIdType", "distributionMode", "contentRules", "userIdList", "mode"]}