import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ConfigData, HotSpot } from '../../type';
import { Card, DatePicker, Input, Button,Popconfirm } from 'antd';
import InputTitle from '../InputTitle';
import { streamCardHelper, mapPropToName } from './helper';
import UploadImg from '../UploadImg';

export interface IProps {
  data: ConfigData['streamCard'];
  onChange: (data: ConfigData['streamCard']) => void;
  disabled: boolean;
}
const StreamCard: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleAdd = () => {
    onChange([streamCardHelper.addArrayData(), ...data]);
  };
  const handleChange = (index, name, value) => {
    const newData = [...data];
    newData[index][name] = value;
    onChange(newData);
  };
  const handleDel = index => {
    const newData = [...data];
    newData.splice(index, 1);
    onChange(newData);
  };
  return (
    <div>
      <h3>直播卡片</h3>
      
      {
        data?.map((item,index)=>{

          return <Card key={index} style={{ width: '400px',display:"inline-block" }}>
          <Input
          value={item.recommendWords}
          onChange={e => handleChange(index, 'recommendWords', e.target.value)}
            disabled={disabled}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<InputTitle title={mapPropToName['recommendWords']} />}
          />
          <Input
          value={item.title}
          onChange={e => handleChange(index, 'title', e.target.value)}
            disabled={disabled}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<InputTitle title={mapPropToName['title']} />}
          />
          <Input
          value={item.organization}
          onChange={e => handleChange(index, 'organization', e.target.value)}
            disabled={disabled}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<InputTitle title={mapPropToName['organization']} />}
          />
          <Input
           value={item.link}
           onChange={e => handleChange(index, 'link', e.target.value)}
            disabled={disabled}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<InputTitle title={mapPropToName['link']} />}
          />
  
          <UploadImg
          value={item.cover}
          disabled={disabled}
          onChange={v => handleChange(index, 'cover', v)}
          title={mapPropToName['cover']} isRequired size={['60*60']}></UploadImg>
          <Popconfirm
              title={'请确认是否删除'}
              disabled={disabled}
              okText={'确认'}
              cancelText={'取消'}
              onConfirm={() => handleDel(index)}
            >
              <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                删除
              </Button>
            </Popconfirm>
        </Card>
        })
      }
     
      

<div>
        <Button disabled={disabled} onClick={handleAdd} style={{ marginTop: '4px' }} size="small">
          新增直播卡片
        </Button>
      </div>
    </div>
  );
};
export default StreamCard;
