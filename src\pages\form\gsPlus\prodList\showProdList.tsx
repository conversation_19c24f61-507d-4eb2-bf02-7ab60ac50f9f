import React, { useState, useEffect } from 'react';
import classnames from 'classnames';
import styles from './index.less';
import { <PERSON>lapse, Popconfirm, But<PERSON>, Tooltip, Tag, Drawer, message } from 'antd';
import TabList from './tabList';

import ProdCard from './prodCard';

const { Panel } = Collapse;

interface dataProps {
  prodShowBox: any[];
  saveData: any;
  prodList: any[];
}

export default function(props: dataProps) {
  const { prodShowBox, saveData, prodList } = props;

  const [tabData, set_tabData] = useState<any>({});
  const [showProd1, set_showProd1] = useState<any[]>([]);
  const [showProd2, set_showProd2] = useState<any[]>([]);

  const [showDrawer, set_showDrawer] = useState(false);
  const [editData, set_editData] = useState(null);
  const [editIndex, set_editIndex] = useState(-1);
  const [editType, set_editType] = useState('');

  const [showDrawer2, set_showDrawer2] = useState(false);
  const [addType, set_addType] = useState('');

  /**
   * 修改系列描述、名称
   * @param data
   */
  const changeTab = (data: any) => {
    uniteData(data, showProd1, showProd2);
  };

  /**
   * 整理数据
   * @param tab
   * @param prod1
   * @param prod2
   */
  const uniteData = (tab: any, prod1: any[], prod2: any[]) => {
    let _data = [
      {
        tabName: tab.tab1Name,
        tabDesc: tab.tab1Desc,
        tabKey: '001',
        prod: prod1,
      },
      {
        tabName: tab.tab2Name,
        tabDesc: tab.tab2Desc,
        tabKey: '002',
        prod: prod2,
      },
    ];
    saveData(_data);
  };

  /**
   * 映射数据指标
   * @param key
   * @returns
   */
  const getDataIndicator = (key: string) => {
    switch (key) {
      case 'rate':
        return '涨跌幅';
      case 'maxDown':
        return '最大回撤';
      case 'formerWeekProbability':
        return '过往周胜率';
      case 'profitProbability':
        return '持有盈利概率';
      default:
        return '--';
    }
  };

  /**
   * 删除产品
   * @param index
   * @param type
   */
  const deleteProd = (index: number, type: string) => {
    if (type === '001') {
      let _showProd1 = [...showProd1];
      _showProd1.splice(index, 1);
      uniteData(tabData, _showProd1, showProd2);
    } else if (type === '002') {
      let _showProd2 = [...showProd2];
      _showProd2.splice(index, 1);
      uniteData(tabData, showProd1, _showProd2);
    }
  };

  /**
   * 编辑产品
   * @param item
   * @param index
   * @param type
   */
  const editProd = (item: any, index: number, type: string) => {
    set_showDrawer(true);
    set_editData(item);
    set_editIndex(index);
    set_editType(type);
  };

  /**
   * 操作数据
   * @param data
   * @param index
   */
  const handleProdData = (data: any, index: number) => {
    if (editType === '001') {
      let _showProd1 = [...showProd1];
      _showProd1[index] = data;
      uniteData(tabData, _showProd1, showProd2);
    } else if (editType === '002') {
      let _showProd2 = [...showProd2];
      _showProd2[index] = data;
      uniteData(tabData, showProd1, _showProd2);
    }
    set_showDrawer(false);
    set_editIndex(-1);
    set_editData(null);
    set_editType('');
  };

  /**
   * 置顶
   * @param index
   * @param type
   */
  const stickTop = (index: number, type: string) => {
    if (type === '001') {
      let _showProd1 = [...showProd1],
        item = _showProd1.splice(index, 1);
      _showProd1.unshift(item[0]);
      uniteData(tabData, _showProd1, showProd2);
    } else if (type === '002') {
      let _showProd2 = [...showProd2],
        item = _showProd2.splice(index, 1);
      _showProd2.unshift(item[0]);
      uniteData(tabData, showProd1, _showProd2);
    }
  };

  /**
   * 添加产品
   * @param type
   */
  const addProd = (data: any, index: number) => {
    if (addType === '001') {
      let _showProd1 = [...showProd1];
			for (let i = 0; i < _showProd1.length; i++) {
				if (_showProd1[i].fundCode === data.fundCode) {
					message.error('基金代码重复，添加失败');
					return;
				}
			}
      _showProd1.push(data);
      uniteData(tabData, _showProd1, showProd2);
    } else if (addType === '002') {
      let _showProd2 = [...showProd2];
			for (let i = 0; i < _showProd2.length; i++) {
				if (_showProd2[i].fundCode === data.fundCode) {
					message.error('基金代码重复，添加失败');
					return;
				}
			}
      _showProd2.push(data);
      uniteData(tabData, showProd1, _showProd2);
    }
    set_showDrawer2(false);
    set_addType('');
  };

  /**
   * 打开添加产品抽屉
   * @param type
   */
  const openAddProdDrawer = (type: string) => {
    set_addType(type);
    set_showDrawer2(true);
  };

  useEffect(() => {
    if (prodShowBox && prodShowBox.length > 0) {
      let _tabData: any = {};
      for (let i = 0; i < prodShowBox.length; i++) {
        if (i === 0) {
          _tabData.tab1Name = prodShowBox[i].tabName;
          _tabData.tab1Desc = prodShowBox[i].tabDesc;
          let _prod = prodShowBox[i].prod;
          set_showProd1(_prod);
        } else if (i === 1) {
          _tabData.tab2Name = prodShowBox[i].tabName;
          _tabData.tab2Desc = prodShowBox[i].tabDesc;
          let _prod = prodShowBox[i].prod;
          set_showProd2(_prod);
        }
      }
      set_tabData(_tabData);
    }
  }, [prodShowBox]);

  useEffect(() => {
    // 当prodList变化时，需要去更新产品的推荐文案和跳转链接
    let _prodObj: any = {};
    for (let i = 0; i < prodList.length; i++) {
      _prodObj[`${prodList[i].fundCode}`] = prodList[i];
    }
    let _showProd1 = [...showProd1],
      _showProd2 = [...showProd2];
    for (let i = 0; i < _showProd1.length; i++) {
      let _code = _showProd1[i].fundCode;
      _showProd1[i].jumpUrl = _prodObj[`${_code}`] && _prodObj[`${_code}`].jumpUrl;
      _showProd1[i].recommendText = _prodObj[`${_code}`] && _prodObj[`${_code}`].recommendText;
    }
    for (let i = 0; i < _showProd2.length; i++) {
      let _code = _showProd2[i].fundCode;
      _showProd2[i].jumpUrl = _prodObj[`${_code}`] && _prodObj[`${_code}`].jumpUrl;
      _showProd2[i].recommendText = _prodObj[`${_code}`] && _prodObj[`${_code}`].recommendText;
    }
    uniteData(tabData, _showProd1, _showProd2);
  }, [prodList]);

  return (
    <article>
      {/* <TabList onChange={changeTab} tabData={tabData} /> */}
      <Collapse style={{ marginTop: 10 }} defaultActiveKey={['showProdList1', 'showProdList2']}>
        <Panel header="系列1配置" key="showProdList1">
          <div className={styles['m-head']} style={{ width: 1450 }}>
            <div
              className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')}
              style={{ width: 1450 }}
            >
              <p style={{ width: 80 }}>列表序号</p>
              <p style={{ width: 120 }}>基金代码</p>
              <p style={{ width: 200 }}>基金名称</p>
              <p style={{ width: 300 }}>推荐文案</p>
              <p style={{ width: 120 }}>数据指标1</p>
              <p style={{ width: 120 }}>数据指标2</p>
              <p style={{ width: 120 }}>时间区间</p>
              <p style={{ width: 80 }}>状态</p>
              <p style={{ width: 310 }}>操作</p>
            </div>
          </div>
          <div>
            {showProd1.map((tag: any, index: any) => (
              <div key={tag.fundCode} className={styles['tag']} style={{ width: 1450 }}>
                <div
                  className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')}
                  style={{ width: 1450 }}
                >
                  <p style={{ width: 80 }}>{index + 1}</p>
                  <p style={{ width: 120 }}>{tag.fundCode}</p>
                  <p style={{ width: 200 }} className={classnames('f-ellipsis')}>
                    {tag.fundName}
                  </p>
                  <Tooltip title={tag.recommendText}>
                    <p style={{ width: 300 }} className={classnames('f-ellipsis')}>
                      {tag.recommendText}
                    </p>
                  </Tooltip>
                  <p style={{ width: 120 }}>{getDataIndicator(tag.dataIndicator1)}</p>
                  <p style={{ width: 120 }}>{getDataIndicator(tag.dataIndicator2)}</p>
                  <p style={{ width: 120 }}>
                    {tag.timeRange === 'year'
                      ? '近1年'
                      : tag.timeRange === 'hYear'
                      ? '近6个月'
                      : '--'}
                  </p>
                  <p style={{ width: 80 }}>
                    <Tag
                      color={
                        tag.showType === '0' ? '#fe5d4e' : tag.showType === '1' ? '#ccc' : '#7eb2f3'
                      }
                    >
                      {tag.showType === '0' ? '开启' : tag.showType === '1' ? '关闭' : '随机推荐'}
                    </Tag>
                  </p>
                  <div style={{ width: 310 }}>
                    <Button
                      type="primary"
                      onClick={() => {
                        editProd(tag, index, '001');
                      }}
                    >
                      编辑
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        stickTop(index, '001');
                      }}
                      style={{ marginLeft: 5 }}
                    >
                      置顶
                    </Button>
                    <Popconfirm
                      title="确定删除吗？"
                      onConfirm={() => {
                        deleteProd(index, '001');
                      }}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button type="danger" style={{ marginLeft: 5 }}>
                        删除
                      </Button>
                    </Popconfirm>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <Button
            type="primary"
            onClick={() => {
              openAddProdDrawer('001');
            }}
            style={{ marginTop: 10 }}
          >
            新增
          </Button>
        </Panel>
        <Panel header="系列2配置" key="showProdList2">
          <div className={styles['m-head']} style={{ width: 1450 }}>
            <div
              className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')}
              style={{ width: 1450 }}
            >
              <p style={{ width: 80 }}>列表序号</p>
              <p style={{ width: 120 }}>基金代码</p>
              <p style={{ width: 200 }}>基金名称</p>
              <p style={{ width: 300 }}>推荐文案</p>
              <p style={{ width: 120 }}>数据指标1</p>
              <p style={{ width: 120 }}>数据指标2</p>
              <p style={{ width: 120 }}>时间区间</p>
              <p style={{ width: 80 }}>状态</p>
              <p style={{ width: 310 }}>操作</p>
            </div>
          </div>
          <div>
            {showProd2.map((tag: any, index: number) => (
              <div key={tag.fundCode} className={styles['tag']} style={{ width: 1450 }}>
                <div
                  className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')}
                  style={{ width: 1450 }}
                >
                  <p style={{ width: 80 }}>{index + 1}</p>
                  <p style={{ width: 120 }}>{tag.fundCode}</p>
                  <p style={{ width: 200 }} className={classnames('f-ellipsis')}>
                    {tag.fundName}
                  </p>
                  <Tooltip title={tag.recommendText}>
                    <p style={{ width: 300 }} className={classnames('f-ellipsis')}>
                      {tag.recommendText}
                    </p>
                  </Tooltip>
                  <p style={{ width: 120 }}>{getDataIndicator(tag.dataIndicator1)}</p>
                  <p style={{ width: 120 }}>{getDataIndicator(tag.dataIndicator2)}</p>
                  <p style={{ width: 120 }}>
                    {tag.timeRange === 'year'
                      ? '近1年'
                      : tag.timeRange === 'hYear'
                      ? '近6个月'
                      : '--'}
                  </p>
                  <p style={{ width: 80 }}>
                    <Tag
                      color={
                        tag.showType === '0' ? '#fe5d4e' : tag.showType === '1' ? '#ccc' : '#7eb2f3'
                      }
                    >
                      {tag.showType === '0' ? '开启' : tag.showType === '1' ? '关闭' : '随机推荐'}
                    </Tag>
                  </p>
                  <div style={{ width: 310 }}>
                    <Button
                      type="primary"
                      onClick={() => {
                        editProd(tag, index, '002');
                      }}
                    >
                      编辑
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        stickTop(index, '002');
                      }}
                      style={{ marginLeft: 5 }}
                    >
                      置顶
                    </Button>
                    <Popconfirm
                      title="确定删除吗？"
                      onConfirm={() => {
                        deleteProd(index, '002');
                      }}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button type="danger" style={{ marginLeft: 5 }}>
                        删除
                      </Button>
                    </Popconfirm>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <Button
            type="primary"
            onClick={() => {
              openAddProdDrawer('002');
            }}
            style={{ marginTop: 10 }}
          >
            新增
          </Button>
        </Panel>
      </Collapse>
      <Drawer
        title="编辑产品"
        placement="right"
        visible={showDrawer}
        onClose={() => {
          set_showDrawer(false);
        }}
        width={800}
        destroyOnClose={true}
      >
        <ProdCard
          baseData={editData}
          baseIndex={editIndex}
          prodList={prodList}
          handleProdData={handleProdData}
          type="showProd"
					ways="edit"
        />
      </Drawer>
      <Drawer
        title="新增产品"
        placement="right"
        visible={showDrawer2}
        onClose={() => {
          set_showDrawer2(false);
        }}
        width={800}
        destroyOnClose={true}
      >
        <ProdCard
          baseData={{}}
          baseIndex={-1}
          prodList={prodList}
          handleProdData={addProd}
          type="showProd"
        />
      </Drawer>
    </article>
  );
}
