import {
    Form,
    Input,
    But<PERSON>,
    Popconfirm,
    Select
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';

const { Option } = Select;
interface baseDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: any;
    handleData: (data: any) => void;
    consultTacticsList: any;
}

class pageDrawer extends React.Component<baseDrawerProps, any> {
    constructor(props: baseDrawerProps) {
        super(props);
    }

    formItemLayout = {
        labelCol: {
            span: 4
        },
        wrapperCol: {
            span: 19
        },
    };
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { currentData, consultTacticsList } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                const { strategy } = values;
                values = { 
                    ...currentData,
                    ...values,
                    ictRisk: '',
                    inHoldDay: ''
                };
                for (let i = 0, len = consultTacticsList?.length; i < len; i++) {
                    if (strategy === consultTacticsList[i]?.consultTacticsId) {
                        values = { 
                            ...values,
                            strategyName: consultTacticsList[i]?.investConsultTacticsName,
                            ictRisk: consultTacticsList[i]?.ictRisk,
                            inHoldDay: consultTacticsList[i]?.inHoldDay,
                        };
                        console.log(values);
                        break;
                    }
                }
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;
        const { onEditClose, consultTacticsList } = this.props;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="TA机构名称：" wrapperCol={{span: 6}}>
                        <span>{this.props.currentData?.taName}</span>
                    </Form.Item>
                    <Form.Item label="卡片名称：" wrapperCol={{span: 6}}>
                        {getFieldDecorator('cardName', {
                            initialValue: this.props.currentData?.cardName,
                            rules: [{ required: true, message: '请输入卡片名称' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="投顾策略：" wrapperCol={{span: 6}}>
                        {getFieldDecorator('strategy', {
                            initialValue: this.props.currentData?.strategy,
                            rules: [{ required: true, message: '请选择投顾策略' }],
                        })(
                            <Select style={{width:'300px',marginRight: '20px'}}>
                                { consultTacticsList?.map((item: any) => (
                                    <Option key={item.consultTacticsId} value={item.consultTacticsId}>{item.investConsultTacticsName}</Option>
                                )) }
                            </Select>
                        )}
                    </Form.Item>
                    <Form.Item label="卡片主文案：" wrapperCol={{span: 6}}>
                        {getFieldDecorator('cardMasterTitle', {
                            initialValue: this.props.currentData?.cardMasterTitle,
                            rules: [{ required: true, message: '请输入卡片主文案：' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="卡片副文案：" wrapperCol={{span: 6}}>
                        {getFieldDecorator('cardSubTitle', {
                            initialValue: this.props.currentData?.cardSubTitle,
                            rules: [{ required: true, message: '请输入卡片副文案' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="卡片说明：">
                        {getFieldDecorator('cardDescription', {
                            initialValue: this.props.currentData?.cardDescription,
                            rules: [{ required: true, message: '请输入卡片说明' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                            <Button type="primary">
                                提交
                            </Button>
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedPageDrawer = Form.create<baseDrawerProps>({ name: 'pageDrawer' })(pageDrawer);
export default WrappedPageDrawer