export interface IResponse {
  code: string;
  data: string | null;
  message: string;
}

export interface ITableItem {
  key: string;
  online: '0' | '1';
  name: string;
  sortType: string;
  sort: 'ASC' | 'DESC';
  initialField: string;
  filterList: { 
    filterField: string; 
    filterSymbol: 'GREATER' | 'LESS' | 'BETWEEN' | 'IN';
    filterValue: string 
  }[];
  financialFloor: string;
  recommend: '0' | '1';
  operation: '0' | '1';
  operationText: string;
  operationLink: string;
  emptyData: '0' | '1' | null;
  lastEditor: string;
  editorTime: string;
}

export interface IRequestData {
  zeroTag: '0' | '1';
  rankList: ITableItem[];
}

export interface IFormData {
  online: boolean;
  name: string;
  sortType: string;
  sort: 'ASC' | 'DESC';
  initialField: string;
  [key: `filterField${number}`]: string;
  [key: `filterSymbol${number}`]: string;
  [key: `filterValue${number}`]: string;
  fundTypeValue: string[];
  yearsSymbol: 'GREATER' | 'BETWEEN';
  yearsStartValue: string;
  yearsEndValue: string;
  financialFloor: string;
  recommend: boolean;
  operation: boolean;
  operationText: string;
  operationLink: string;
}

export interface IRankFilterReq {
  filterList: IRankFilterItem[]
}

export interface IRankFilterItem {
  key: string;
  strategyKey: string;
  strategyTitle: string;
  strategyText: string;
  sentence: string;
  lastEditor: string;
  editorTime: string;
}

export interface IRankFilterForm {
  strategyKey: string;
  strategyTitle: string;
  strategyText: string;
  sentence: string;
}
