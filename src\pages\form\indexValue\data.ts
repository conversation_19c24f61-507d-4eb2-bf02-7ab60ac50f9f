let data = [{ "flag": 1620629155609, "fundCode": "000827.SH", "fundName": "中证环保产业指数", "name": "环保", "type": "行业", "introduce": "中证环保产业指数反映上海和深圳市场环保产业公司表现。将符合资源管理、清洁技术和产品、污染管理的公司纳入环保产业主题，例如：宁德时代、隆基股分等。", "innerFund": "516070", "innerName": "低碳ETF", "innerText": "把握碳中和时代机遇", "outterFund": "002984", "outterName": "广发环保C", "outterText": "把握碳中和时代机遇", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155610, "fundCode": "930651.CSI", "fundName": "中证计算机主题指数", "name": "计算机", "type": "行业", "introduce": "中证计算机主题指数反映计算机类相关上市公司整体表现，选取涉及信息技术服务、应用系统软件、电脑硬件等业务的公司股票作为成份股，例如：海康威视、恒生电子等。", "innerFund": "159998", "innerName": "计算机", "innerText": "科技兴国 5G应用龙头", "outterFund": "001630", "outterName": "天弘中证计算机ETF联接C", "outterText": "科技兴国 5G应用龙头", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155611, "fundCode": "000949.CSI", "fundName": "中证农业主题指数", "name": "农业", "type": "行业", "introduce": "中证农业主题指数反映农业上市公司股票的整体表现，选取涉及农用机械、化肥与农药、农产品、肉类乳制品等领域的公司股票作为指数样本股。例如：通威股份、牧原股份等。", "innerFund": "159825", "innerName": "农业ETF", "innerText": "一键布局农业板块", "outterFund": "010770", "outterName": "天弘中证农业主题C", "outterText": "国之根本 投资行业龙头", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155612, "fundCode": "399997.SZ", "fundName": "中证白酒指数", "name": "白酒", "type": "行业", "introduce": "中证白酒指数代表了白酒生产业务相关的上市公司整体情况。以中证全指为样本空间，选取白酒生产业务相关上市公司股票组成。例如：贵州茅台、五粮液等。", "innerFund": "512690", "innerName": "酒ETF", "innerText": "国民指数 专注白酒", "outterFund": "161725", "outterName": "招商中证白酒指数", "outterText": "国民指数 专注白酒", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155613, "fundCode": "399976.SZ", "fundName": "中证新能源汽车指数", "name": "新能车", "type": "行业", "introduce": "新能源汽车指数代表了新能源汽车相关上市公司的整体情况。由涉及锂电池、充电桩、新能源整车等业务的上市公司股票组成。例如：宁德时代、亿纬锂能等。", "innerFund": "515700", "innerName": "新能车", "innerText": "电动化浪潮 国产加速", "outterFund": "009068", "outterName": "国泰中证新能源汽车ETF联接C", "outterText": "电动化浪潮 国产加速", "applyArea": "PB", "pe": "", "pb": "" }, { "flag": 1620629155614, "fundCode": "931087.CSI", "fundName": "中证科技龙头指数", "name": "科技龙头", "type": "行业", "introduce": "科技龙头指数由电子、计算机、通信、生物科技等科技领域里规模大、市占率高、成长能力强、研发投入高的50只龙头公司股票组成。例如：立讯精密、海康威视等。", "innerFund": "515000", "innerName": "科技ETF", "innerText": "精选赛道 布局科技浪潮", "outterFund": "007345", "outterName": "富国科技创新", "outterText": "精选赛道 布局科技浪潮", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155616, "fundCode": "930697.CSI", "fundName": "中证全指家用电器指数", "name": "家用电器", "type": "行业", "introduce": "中证全指家用电器指数是中证全指的四级行业主题，选择中证全指中家用电器行业的股票组成指数样本。例如：海尔智家、美的集团等。", "innerFund": "159996", "innerName": "家电ETF", "innerText": "消费升级 布局新兴龙头", "outterFund": "008714", "outterName": "国泰中证全指家用电器ETF联接C", "outterText": "消费升级 布局新兴龙头", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155617, "fundCode": "000815.CSI", "fundName": "中证食品饮料指数", "name": "食品饮料", "type": "行业", "introduce": "中证食品饮料指数反映食品饮料类相关上市公司整体表现，选取归属于饮料、包装食品与肉类两个行业的上市公司股票作为成份股。例如：贵州茅台、海天味业等。", "innerFund": "515170", "innerName": "食品饮料", "innerText": "长牛赛道 14亿人刚需", "outterFund": "001632", "outterName": "天弘中证食品饮料C", "outterText": "长牛赛道 14亿人刚需", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155618, "fundCode": "399998.SZ", "fundName": "中证煤炭指数", "name": "煤炭", "type": "行业", "introduce": "中证煤炭指数反映煤炭行业上市公司整体表现,从煤与消费用燃料行业选取相关上市公司作为成份股。例如：陕西煤业、中国神华等。", "innerFund": "515220", "innerName": "煤炭ETF", "innerText": "供需紧俏 受益碳中和", "outterFund": "008280", "outterName": "国泰中证煤炭ETF联接C", "outterText": "供需紧俏 受益碳中和", "applyArea": "PB", "pe": "", "pb": "" }, { "flag": 1620629155619, "fundCode": "930606.CSI", "fundName": "中证钢铁指数", "name": "钢铁", "type": "行业", "introduce": "中证钢铁指数反映钢铁行业上市公司整体表现，从钢铁行业中选取相关上市公司作为样本股。例如：宝钢股份、包钢股份等。", "innerFund": "515210", "innerName": "钢铁ETF", "innerText": "碳中和首要投资标的", "outterFund": "008190", "outterName": "国泰中证钢铁ETF联接C", "outterText": "碳中和首要投资标的", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155620, "fundCode": "399995.SZ", "fundName": "中证基建工程指数", "name": "基建工程", "type": "行业", "introduce": "中证基建工程指数选取涉及建筑与工程业务的上市公司股票作为成份股，以反映基建工程领域相关上市公司的整体表现。例如：中国中铁、中国建筑等。", "innerFund": "", "innerName": "", "innerText": "", "outterFund": "005224", "outterName": "广发中证基建工程指数C ", "outterText": "稳定经济的强心剂", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155621, "fundCode": "H30165.CSI", "fundName": "中证全指房地产指数", "name": "房地产开发", "type": "行业", "introduce": "中证全指房地产指数选取中证全指样本股中的房地产行业股票组成，以反映该行业股票的整体表现。例如：万科A、保利地产等。", "innerFund": "515060", "innerName": "华夏地产", "innerText": "紧跟房地产开发机遇", "outterFund": "008089", "outterName": "华夏中证全指房地产ETF联接C", "outterText": "房地产开发经营", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155622, "fundCode": "000922.CSI", "fundName": "中证红利指数", "name": "红利", "type": "主题", "introduce": "中证红利指数以沪深A股中现金股息率高、分红比较稳定、具有一定规模及流动性的100只股票为成分股。例如：柳钢股份、兖州煤业等。", "innerFund": "515180", "innerName": "100红利", "innerText": "网罗高分红高股息股票", "outterFund": "100032", "outterName": "富国中证红利", "outterText": "网罗高分红高股息股票", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155623, "fundCode": "399986.SZ", "fundName": "中证银行指数", "name": "银行", "type": "行业", "introduce": "中证银行指数选取中证全指样本股中的银行行业股票组成，以反映该行业股票的整体表现。例如：招商银行、兴业银行等。", "innerFund": "515290", "innerName": "银行", "innerText": "低估高息 顺周期王者", "outterFund": "001595", "outterName": "天弘中证银行ETF联接C", "outterText": "低估高息 顺周期王者", "applyArea": "PB", "pe": "", "pb": "" }, { "flag": 1620629155624, "fundCode": "399967.SZ", "fundName": "中证军工指数", "name": "军工", "type": "行业", "introduce": "中证军工指数由十大军工集团控股的且主营业务与军工行业相关的上市公司作为指数样本。例如：航发动力、中航沈飞等。", "innerFund": "512710", "innerName": "军工龙头", "innerText": "景气延续 一键投军工", "outterFund": "161024", "outterName": "富国中证军工", "outterText": "景气延续 一键投军工", "applyArea": "PB", "pe": "", "pb": "" }, { "flag": 1620629155625, "fundCode": "399971.SZ", "fundName": "中证传媒指数", "name": "传媒", "type": "行业", "introduce": "中证传媒指数从有线电视、出版、营销广告、电影娱乐、移动互联网信息服务等行业中，选取总市值较大的50只公司股票作为指数样本股，例如：分众传媒 、芒果超媒等。", "innerFund": "159805", "innerName": "传媒ETF", "innerText": "一键投资文娱游戏", "outterFund": "004753", "outterName": "广发中证传媒ETF联接C", "outterText": "宅经济来袭 投文娱游戏", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155626, "fundCode": "000932.SH", "fundName": "中证主要消费指数", "name": "消费", "type": "行业", "introduce": "中证主要消费指数由中证800指数样本股中的主要消费行业股票组成，以反映该行业公司股票的整体表现。例如：贵州茅台、五粮液等。", "innerFund": "510630", "innerName": "消费行业", "innerText": "把握消费升级机会", "outterFund": "002697", "outterName": "中欧消费主题", "outterText": "顶尖消费专家带你淘金", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155627, "fundCode": "399975.SZ", "fundName": "中证全指证券公司指数", "name": "证券", "type": "行业", "introduce": "中证全指证券公司指数选取中证全指样本股中的证券公司行业股票组成，以反映该行业股票的整体表现。例如：东方财富、中信证券等。", "innerFund": "159841", "innerName": "证券ETF", "innerText": "牛市风向标 低位布局", "outterFund": "004070", "outterName": "南方全指证券联接C", "outterText": "牛市风向标 低位布局", "applyArea": "PB", "pe": "", "pb": "" }, { "flag": 1620629155628, "fundCode": "980017.SZ", "fundName": "国证半导体芯片指数", "name": "芯片", "type": "行业", "introduce": "为反映A股市场芯片产业相关上市公司的市场表现，丰富指数化投资工具，编制国证半导体芯片指数。例如：韦尔股份、兆易创新等。", "innerFund": "512760", "innerName": "芯片ETF", "innerText": "半导体国产化", "outterFund": "008888", "outterName": "华夏国证半导体芯片ETF联接C", "outterText": "半导体国产化", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155629, "fundCode": "000933.SH", "fundName": "中证医药卫生指数", "name": "医药", "type": "行业", "introduce": "中证医药卫生指数由中证800指数样本股中的医药卫生行业股票组成，以反映该行业公司股票的整体表现。例如：恒瑞医药、药明康德等。", "innerFund": "159929", "innerName": "医药ETF", "innerText": "一键布局医药板块", "outterFund": "007077", "outterName": "添富中证医药ETF联接C", "outterText": "一键布局医药板块", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155630, "fundCode": "931079.CSI", "fundName": "中证5G通信主题指数", "name": "5G", "type": "行业", "introduce": "中证5G通信主题指数选取电信服务、通信设备、计算机及电子设备和计算机运用等细分行业，旨在反映相关领域的A股上市公司整体表现。例如：立讯精密、中兴通讯等。", "innerFund": "515050", "innerName": "5GETF", "innerText": "一键布局5G产业链", "outterFund": "008087", "outterName": "华夏中证5G通信主题ETF联接C", "outterText": "一键布局5G产业链", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155631, "fundCode": "930652.CSI", "fundName": "中证电子指数", "name": "电子", "type": "行业", "introduce": "中证电子指数反映电子类相关上市公司整体表现，选取涉及半导体产品与设备生产、电子制造服务和消费电子生产等业务的公司股票作为成份股。例如：立讯精密、海康威视等。", "innerFund": "159997", "innerName": "电子ETF", "innerText": "高景气度 苹果产业链", "outterFund": "001618", "outterName": "天弘中证电子ETF联接C", "outterText": "高景气度 苹果产业链", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155632, "fundCode": "000813.CSI", "fundName": "中证细分化工产业主题指数", "name": "化工", "type": "行业", "introduce": "中证细分化工产业主题指数从化学制品等细分产业中挑选规模较大、流动性较好的公司股票组成样本股。例如：万华化学、恒力石化等。", "innerFund": "159870", "innerName": "化工ETF", "innerText": "一键布局化工龙头", "outterFund": "001644", "outterName": "易方达供给改革混合", "outterText": "把握改革投资机会", "applyArea": "PB", "pe": "", "pb": "" }, { "flag": 1620629155633, "fundCode": "HSI.GI", "fundName": "恒生指数", "name": "恒生指数", "type": "宽基", "introduce": "恒生指数代表了香港股市价幅动趋势最有影响的一种股价指数。由香港股票市场中有代表性的上市股票组成。例如：腾讯控股、阿里巴巴等。", "innerFund": "510900", "innerName": "H股ETF", "innerText": "港股蓝筹精选", "outterFund": "000071", "outterName": "华夏恒生ETF联接A", "outterText": "港股蓝筹精选", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155634, "fundCode": "000903.SH", "fundName": "中证100指数", "name": "中证100", "type": "宽基", "introduce": "中证100指数代表了A股市场最具市场影响力的一批大市值公司的整体情况。是从沪深300指数样本股中挑选规模最大的100只股票组成。例如：工商银行、贵州茅台等。", "innerFund": "512910", "innerName": "100ETF", "innerText": "一键布局中国核心资产", "outterFund": "007136", "outterName": "广发中证100ETF联接C", "outterText": "一键布局中国核心资产", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155635, "fundCode": "399005.SZ", "fundName": "中小100指数", "name": "中小100", "type": "宽基", "introduce": "中小板指数代表了中小上市企业的整体情况。由深交所中小企业板上市交易的A股中选取的，具有代表性的股票。例如：海康威视、顺丰控股等。", "innerFund": "159902", "innerName": "中小100", "innerText": "中小企业百强集合", "outterFund": "006247", "outterName": "华夏中小企业100ETF联接C", "outterText": "中小企业百强集合", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155636, "fundCode": "SPX.GI", "fundName": "标准普尔500指数", "name": "标普500", "type": "宽基", "introduce": "标准普尔500指数是记录美国500家上市公司的一个股票指数，其成份股由工业、运输业、公用事业和金融业股票组成。例如：苹果、美国航空等。", "innerFund": "161125", "innerName": "标普500ETF ", "innerText": "一基便捷投资美国", "outterFund": "006075", "outterName": "博时标普500联接C", "outterText": "一基便捷投资美国", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155637, "fundCode": "399001.SZ", "fundName": "深证成指", "name": "深证成指", "type": "宽基", "introduce": "深证成指反映的是深圳市场的结构性特点，按一定标准选出500家有代表性的上市公司作为样本股。例如：五粮液、美的集团等。", "innerFund": "159903", "innerName": "深成ETF", "innerText": "一键布局新经济公司", "outterFund": "004345", "outterName": "南方深成C", "outterText": "一键布局新经济公司", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155638, "fundCode": "000905.SH", "fundName": "中证500指数", "name": "中证500", "type": "宽基", "introduce": "中证500指数由全部A股中剔除沪深300指数成份股及总市值排名前300名的股票后，总市值排名靠前的500只股票组成，综合反映中小市值公司的股票价格表现。", "innerFund": "515530", "innerName": "泰康500", "innerText": "配置均衡 汇聚细分龙头", "outterFund": "005929", "outterName": "天弘中证500ETF联接C", "outterText": "配置均衡 汇聚细分龙头", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155639, "fundCode": "000016.SH", "fundName": "上证50指数", "name": "上证50", "type": "宽基", "introduce": "上证50指数由沪市A股中规模大、流动性好的最具代表性的50只股票组成，反映上海证券市场最具影响力的龙头公司的股票价格表现。例如：中国平安、贵州茅台等。", "innerFund": "510100", "innerName": "SZ50ETF", "innerText": "一键布局上证50指数", "outterFund": "005733", "outterName": "华夏上证50ETF联接C", "outterText": "优选全市场50强", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155640, "fundCode": "000001.SH", "fundName": "上证综合指数", "name": "上证指数", "type": "宽基", "introduce": "上证综合指数由在上海证券交易所上市的符合条件的股票与存托凭证组成样本，反映上海证券交易所上市公司的整体表现。", "innerFund": "510760", "innerName": "上证ETF", "innerText": "紧密跟踪大盘指数", "outterFund": "100053", "outterName": "富国上证指数ETF联接", "outterText": "紧密跟踪大盘指数", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155641, "fundCode": "000300.SH", "fundName": "沪深300指数", "name": "沪深300", "type": "宽基", "introduce": "沪深300指数由上海和深圳证券市场中市值大、流动性好的300只股票组成，综合反映中国A股市场上市股票价格的整体表现。", "innerFund": "515380", "innerName": "泰康300", "innerText": "沪深两市300家最强公司", "outterFund": "000051", "outterName": "华夏沪深300etf联接", "outterText": "A股走势“晴雨表”", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155642, "fundCode": "399330.SZ", "fundName": "深证100", "name": "深证100", "type": "宽基", "introduce": "深证100由深圳证券市场中市值大、流动性好的100只股票组成，定位旗舰型指数，表征创新型、成长型龙头企业。例如：五粮液、美的集团等。", "innerFund": "159901", "innerName": "深100ETF", "innerText": "紧密跟踪深100指数", "outterFund": "004742", "outterName": "易方达深证100ETF联接C", "outterText": "紧密跟踪深100指数", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155643, "fundCode": "399006.SZ", "fundName": "创业板指", "name": "创业板", "type": "宽基", "introduce": "创业板指由创业板中市值大、流动性好的100只股票组成，反映创业板市场的运行情况。例如：宁德时代、东方财富等。", "innerFund": "159977", "innerName": "创业板TH", "innerText": "聚焦创新 引领中国经济", "outterFund": "001593", "outterName": "天弘创业板ETF联接C", "outterText": "聚焦创新 引领中国经济", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155644, "fundCode": "NDX.GI", "fundName": "纳斯达克100指数", "name": "纳斯达克100", "type": "宽基", "introduce": "纳斯达克100指数，旨在衡量100家最大的纳斯达克上市非金融公司的表现，可作为投资者观察除金融行业以外的美国市场表现的参考指标。例如：苹果、微软等。", "innerFund": "513300", "innerName": "纳斯达克", "innerText": "一键配置全球科技巨头", "outterFund": "160213", "outterName": "国泰纳斯达克100指数(QDII)", "outterText": "一键配置全球科技巨头", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155645, "fundCode": "399550.SZ", "fundName": "央视财经50指数", "name": "央视50", "type": "宽基", "introduce": "央视财经50指数从“成长、创新、回报、公司治理、社会责任”5个维度对上市公司进行评价，每个维度选出10家、合计50家A股公司构成样本股。", "innerFund": "159965", "innerName": "央视50", "innerText": "一键投资50家优质公司", "outterFund": "217027", "outterName": "招商央视50", "outterText": "一键投资50家优质公司", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155646, "fundCode": "000688.SH", "fundName": "上证科创板50成份指数", "name": "科创50", "type": "宽基", "introduce": "上证科创板50成份指数由上海证券交易所科创板中市值大、流动性好的50只证券组成，反映最具市场代表性的一批科创企业的整体表现。", "innerFund": "588080", "innerName": "科创板50", "innerText": "科创引领、创新驱动", "outterFund": "011609", "outterName": "易方达上证科创板50成份ETF联接C", "outterText": "科创引领、创新驱动", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155647, "fundCode": "930721.CSI", "fundName": "中证智能汽车主题指数", "name": "智能汽车", "type": "主题", "introduce": "中证智能汽车主题指数选取为智能汽车提供终端感知、平台应用的公司，以及其他受益于智能汽车的代表性沪深A股作为样本股。例如：中科创达、长城汽车等。", "innerFund": "515250", "innerName": "智能汽车", "innerText": "智能驾驶 特斯拉产业链", "outterFund": "010956", "outterName": "天弘中证智能汽车C", "outterText": "智能驾驶 特斯拉产业链", "applyArea": "PE", "pe": "", "pb": "" }, { "flag": 1620629155648, "fundCode": "930708.CSI", "fundName": "中证有色金属指数", "name": "有色", "type": "行业", "introduce": "中证有色金属指数选取涉及有色金属采选、有色金属冶炼与加工业务的上市公司股票作为成份股。例如：紫金矿业、赣锋锂业等。", "innerFund": "512400", "innerName": "有色ETF", "innerText": "一键捕捉顺周期风口", "outterFund": "004433", "outterName": "南方有色金属联接C", "outterText": "一键捕捉顺周期风口", "applyArea": "PB", "pe": "", "pb": "" }]

export default data