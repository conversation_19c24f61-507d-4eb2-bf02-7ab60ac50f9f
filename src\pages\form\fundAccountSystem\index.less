.m-config {
    margin-bottom:20px;
    .m-title {
      display: inline-block;
      padding-left: 40px;
    }
}
.m-card {
    .m-header {
        padding-right: 50px;
        margin-bottom: 20px;
        height: 40px;
        background:rgb(149, 186, 221);
        color: #ffffff;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .m-button {
            margin-left: 20px;
        }
    }
    .m-floor {
        padding-right: 50px;
        margin-bottom: 20px;
        height: 40px;
        background:rgb(149, 186, 221);
        color: #ffffff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .m-button {
            margin-left: 20px;
        }
    }
    .m-card-required {
      position: relative;
      &::before {
        display: inline-block;
        margin-right: 4px;
        color: #f5222d;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
    }
}
  .m-drag {
    height: 100%;
    width: 1100px;
    overflow: hidden;
  }
  .tag {
    margin: 3px;
    font-size: 13px;
    border: 1px dashed #cccccc;
    border-radius: 4px;
    // padding: 0 8px;
    line-height: 30px;
    color: #666666;
    background: rgba(255, 255, 255, 0.7);
    
  }
  .tag-content {
    padding-right: 10px;
    word-break: break-all;
    display: -webkit-box;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    /*! autoprefixer: on */
    -webkit-line-clamp: 6;
    overflow: hidden;
  }
  .m-head{
    width:1100px;
    // height: 30px;
    padding-top: 5px;
    text-justify: auto;
    background: rgb(59, 102, 194);
    color: #ffffff;
    }
.m-row{
    padding-left: 5px;
     width: 1100px;
}
.m-row-top{
    padding-left: 5px;
    width: 1100px;
   background-color: rgba(235, 235, 235, 0.884);
}
.m-card-label{
  width:110px;
  color: #333;
  text-align: right;
  padding-right: 12px;
  display: inline-block;
  margin-bottom: 0;

}


.m-tagModel-button{
  margin-left: 20px;
}
.m-tagModel-span{
  margin-right: 10px;
}
.m-tagModel-row{
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.m-collpase-button {
  margin-top: -5px;
}

.m-required {
    margin: 1px 4px 0 0;
    color: #f5222d;
    font-size: 14px;
    font-family: SimSun, sans-serif;
}