import React, { useEffect, useState } from 'react';
import api from 'api';
import Activity from './activity';
import { Button, message, Card, Collapse, Modal, DatePicker, Input, Popconfirm } from 'antd';
import './type';

const { postActivityCentre } = api;

const DEFAULT_OBJECT: uploadItem = {
  name: '',
  imgUrl: '',
  jumpUrl: '',
  startTime: '',
  endTime: '',
  mutex: '0',
  mutexList: [],
  filterId: '',
};

export default function() {
  const [dataList, setDataList] = useState<uploadItem[]>([]); //数据
  const [isInit, setIsInit] = useState<boolean>(false); //接口是否加载完
  const [isCanUpload, setIsCanUpload] = useState<boolean>(false); //是否可以上传

  useEffect(() => {
    postActivityCentre(null, 'query')
      .then((res: any) => {
        if (res.code !== '0000') return message.error(res.message);
        let _list: uploadItem[] = res?.data?.confList ?? [];
        setDataList(_list);
        setIsInit(true);
      })
      .catch((e: Error) => {
        message.error(e.message || '系统错误');
      });
  }, []);

  const addActivity = () => {
    const activities: uploadItem[] = [...dataList];
    activities.push(DEFAULT_OBJECT);
    setDataList(activities);
    setIsCanUpload(false);
  };

  /**
   * 上移
   * @param index 序号
   */
  const upActivity = (index: number) => {
    if (index <= 0) return;
    let _list = [...dataList],
      _temp = dataList[index];
    _list[index] = _list[index - 1];
    _list[index - 1] = _temp;
    setDataList(_list);
    setIsCanUpload(true);
  };

  /**
   * 下移
   * @param index 序号
   */
  const downActivity = (index: number) => {
    if (index >= dataList.length - 1) return;
    let _list = [...dataList],
      _temp = dataList[index];
    _list[index] = _list[index + 1];
    _list[index + 1] = _temp;
    setDataList(_list);
    setIsCanUpload(true);
  };

  /**
   * 删除
   * @param index 序号
   */
  const deleteActivity = (index: number) => {
    let _list = [...dataList];
    _list.splice(index, 1);
    setDataList(_list);
    setIsCanUpload(true);
  };

  const upload = () => {
    let _flag = true;
    dataList.forEach((item: uploadItem) => {
      if (!item.name) {
        _flag = false;
        return message.error('请先写活动名称');
      } else if (!item.imgUrl) {
        _flag = false;
        return message.error('请完成活动图片');
      } else if (!item.jumpUrl) {
        _flag = false;
        return message.error('请填写跳转链接');
      } else if (!item.filterId) {
        _flag = false;
        return message.error('请先上传用户类型');
      }
    });
    if (!_flag) return;

    postActivityCentre({ confList: dataList }, 'update')
      .then((res: any) => {
        if (res.code !== '0000') return message.error(res.message);
        message.success('提交成功');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      })
      .catch((e: Error) => {
        message.error(e.message || '系统错误');
      });
  };

  return (
    isInit && (
      <div>
        <div style={{ marginBottom: 20 }}>
          <Popconfirm title="确定保存并发布?" onConfirm={upload} okText="是" cancelText="否">
            <Button type="danger" disabled={!isCanUpload}>
              保存并发布
            </Button>
          </Popconfirm>
        </div>
        {dataList.map((item, index) => (
          <div key={index}>
            <Activity
              dataList={dataList}
              setDataList={setDataList}
              index={index}
              upActivity={upActivity}
              downActivity={downActivity}
              deleteActivity={deleteActivity}
              setIsCanUpload={setIsCanUpload}
            />
          </div>
        ))}
        <Button type="primary" className="g-mt20" onClick={addActivity}>
          添加
        </Button>
      </div>
    )
  );
}
