import React, {useEffect, useState} from 'react';
import { Select, Button, message, Popconfirm, Input } from 'antd';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../index.less';
import ConfigSelect from '../component/selectModel';
import TagModel from '../component/tagModel';
import UploadImg from './uploadImg'; 
import UploadFile from '../component/uploadFile';
import api from 'api';

const { Option } = Select;
const { fetchAccountUpload, fetchAccountDownLoad, fetchAccountTemplate } = api;
interface dataProps {
    data: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;
    kycTag: Array<string>;
    olasTag: Array<string>;
}

export default function (props: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);

    const [formConfig, setFormConfig] = useState(FORM_CONFIG.schema);
    const [formData, setFormState] = useState<any>({});
    const [configData, setConfig] = useState<any>({});
    const [relationData, setRelation] = useState({});
    const [fileExist, setFileExist] = useState({
        blackListFile: false,
        whiteListFile: false
    });
    const [fileList, setFileList] = useState<any>([]); // 文件列表
    const [moduleType, setModuleType] = useState('button');
    const [btnText, setBtnText] = useState('');
    const [jumpAction, setJumpAction] = useState('');
    const [imgUrl, setImgUrl] = useState('');
    const [bgColor, setBgColor] = useState('');

    useEffect(() => {
        console.log('formData=',props)
        setFormState(props.data?.formData)
        setConfig(props.data?.configData)
        setRelation(props.data?.relationData)
        setFileExist(props.data?.fileExist ?? {
            blackListFile: false,
            whiteListFile: false
        })
        if (props.data?.initData) {
            const { type, name, imageUrl, backgroundColor, jumpAction } = props.data.initData;
            setModuleType(type);
            setBtnText(name);
            setImgUrl(imageUrl);
            setBgColor(backgroundColor);
            setJumpAction(jumpAction);
        }
        if (props.data?.isNew) {
            setEdit(true);
            const { isNew, ...other } = props.data;
            props.handleUpdate(other, props.position, '', true);
        }
    }, [props.data])
    
    function onValidate(valid: React.SetStateAction<never[]>) {
        setValid(valid);
    }
    async function handleChange(){
        if(isEdit){
            const fileFormData = new FormData();
            const { whiteListFile, blackListFile } = fileList;
            let _relationData: any = {...relationData};
            if ( whiteListFile || blackListFile ) {
                fileFormData.append('type', 'my');
                fileFormData.append('configIndex', `${props.position}`);
                if (whiteListFile && _relationData?.targetType === 'file') {
                    fileFormData.append('whiteListFile', whiteListFile);
                }
                if (blackListFile) {
                    fileFormData.append('blackListFile', blackListFile);
                }
            }
            
            if (_relationData?.targetType === 'kyc') {
                _relationData.olasId = [];
                _relationData.whiteList = null;
            } else if (_relationData?.targetType === 'olas') {
                _relationData.kycLogic = '';
                _relationData.kycs = [];
                _relationData.whiteList = null;
            } else {
                if (_relationData?.targetType !== 'file') {
                    _relationData.targetType = '';
                    _relationData.whiteList = null;
                }
                _relationData.kycLogic = '';
                _relationData.kycs = [];
                _relationData.olasId = [];
            }
            let _data: any = {
                initData: {
                    type: moduleType,
                    name: btnText,
                    jumpAction: '',
                    imageUrl: '',
                    backgroundColor: ''
                },
                formData,
                configData,
                relationData: _relationData,
                other: props.data.other,
                fileExist: props.data.fileExist
            }
            if (moduleType === 'button') {
                if (!btnText) {
                    message.error(`请填写按钮名称`)
                    return;
                }
                if (!jumpAction) {
                    message.error('请填写跳转链接')
                    return;
                }
                _data.initData.jumpAction = jumpAction;
            } else if (moduleType === 'image') {
                if (!btnText) {
                    message.error(`请填写宣传图名称`)
                    return;
                }
                if (!imgUrl) {
                    message.error(`请填写宣传图图片`)
                    return;
                }
                _data.initData.imageUrl = imgUrl;
                _data.initData.backgroundColor = bgColor;
            }
            if (!moduleType) {
                message.error('请填写类型')
            } else if (configData?.platform?.length === 0) {
                message.error('请选择适用平台')
            } else if (configData?.utype?.length === 0) {
                message.error('请选择用户类型')
            } else {
                props.handleUpdate(_data, props.position, {[props.position]: fileFormData})
                setEdit(!isEdit)
            }
            
        } else {
            setEdit(!isEdit)
        }
        
    }
    function handleSelect(data: any){
        console.log('data',data)
        setConfig(data)
    }
    /**
     * kyc指定用户修改
     * @param data 修改的kyc标签
     */
    const handleRelationChange = (data: any) => {
        if(data.targetType === 'kyc' && !data.kycLogic) {
            data.kycLogic = 'and'
        }
        data = {
            ...relationData,
            ...data
        }
        setRelation(data)
    }
    /**
     * 下载黑白名单文件
     */
    const downloadFile = (type: string) => {
        fetchAccountDownLoad({}, `?type=my&filename=${type}&configIndex=${props.position}`, '', { responseType: 'blob' }).then((res: any) => {
            if (!res.success) message.error(res.message)
        })
    }
    /**
     * 下载黑白名单模板
     */
    const downloadModel = (type: string) => {
        fetchAccountTemplate({}, `?type=my&filename=${type}`, '', { responseType: 'blob' }).then((res: any) => {
            if (!res.success) message.error(res.message)
        })
    }
    /**
     * 保存指定黑白名单文件
     */
    const saveFile = (file: any, type: string) => {
        let _fileList = {...fileList};
        _fileList[type] = file;
        setFileList(_fileList);
    }
    const handleModuleType = (val: string) => {
        setModuleType(val);
    }
    const handleJumpAction = (e: any) => {
        const { value } = e.target;
        setJumpAction(value);
    }
    const handleBtnText = (e: any) => {
        const { value } = e.target;
        setBtnText(value);
    }
    const handleBgColor = (e: any) => {
        const { value } = e.target;
        setBgColor(value);
    }
    const uploadImage = (val: string) => {
        setImgUrl(val);
    };
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {props.handleDelete(props.position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <section>
                    <div className={styles['m-tagModel-row']}>
                        <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>类型:</p>
                        <Select value={moduleType} style={{ width: 180 }} onChange={handleModuleType} disabled={!isEdit}>
                            <Option value="button">头部按钮</Option>
                            <Option value="image">未开户引导开户宣传图</Option>
                        </Select>
                    </div>
                    { moduleType === 'button' ? (
                        <>
                            <div className={styles['m-tagModel-row']}>
                                <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>按钮名称:</p>
                                <Input style={{width: 514}} value={btnText} onChange={handleBtnText} disabled={!isEdit}></Input>
                            </div>
                            <div className={styles['m-tagModel-row']}>
                                <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>跳转链接:</p>
                                <Input style={{width: 514}} value={jumpAction} onChange={handleJumpAction} disabled={!isEdit}></Input>
                            </div>
                        </>
                    ) : (
                        <>
                        <div className={styles['m-tagModel-row']}>
                            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>宣传图名称:</p>
                            <Input style={{width: 514}} value={btnText} onChange={handleBtnText} disabled={!isEdit}></Input>
                        </div>
                        <div className={styles['m-tagModel-row']}>
                            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>宣传图图片:</p>
                            <UploadImg onChange={uploadImage} imgUrl={imgUrl} disabled={!isEdit}></UploadImg>
                        </div>
                        <div className={styles['m-tagModel-row']}>
                            <p className={styles['m-card-label']}>背景图片:</p>
                            <Input style={{width: 140}} value={bgColor} onChange={handleBgColor} disabled={!isEdit}></Input>
                        </div>
                        </>
                    ) }
                </section>
                <FormRender
                    propsSchema={formConfig}
                    displayType='row'
                    formData={formData}
                    onValidate={onValidate}
                    onChange={setFormState}
                    readOnly={!isEdit}
                />
                <ConfigSelect
                    handleChange={handleSelect}
                    isHead={false}
                    isEdit={isEdit}
                    data={configData}  
                    type="my"
                />
                <TagModel
                    handleChange={(data: any) => {handleRelationChange(data)}}
                    kycTag={props.kycTag}
                    olasTag={props.olasTag}
                    isEdit={isEdit}
                    data={relationData}
                    fileType={'whiteListFile'}
                    isExist={fileExist['whiteListFile']}
                    downloadFile={() => downloadFile('whiteListFile')}
                    downloadModel={() => downloadModel('whiteListFile')}
                    saveFile={saveFile}
                />
                <UploadFile
                    name={'黑名单'}
                    position={props.position}
                    pageType={'my'}
                    fileType={'blackListFile'}
                    saveFile={saveFile}
                    isEdit={isEdit}
                    describe={'请上传xlsx文档，每行填入一个基金客户号（cust_id），第一行不要填数据'}
                    isExist={fileExist['blackListFile']}
                />
            </div>

}