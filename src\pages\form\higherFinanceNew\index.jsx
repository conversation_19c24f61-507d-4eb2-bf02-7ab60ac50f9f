import React, { useState, useEffect } from 'react';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import 'moment/locale/zh-cn';
import styles from './index.less';
import api from 'api';
import {
  Row,
  Col,
  Input,
  Upload,
  Button,
  DatePicker,
  Divider,
  ConfigProvider,
  message,
  Drawer,
  Table,
  Modal,
  Spin,
  Select,
} from 'antd';
import moment from 'moment';

const { confirm } = Modal;
const { Option  } = Select;
const {
  saveHigherFinanceNew,
  getHigherFinanceAllNew,
  commonUpload,
  publishHigherFinanceAllNew,
} = api;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
message.config({
  duration: 3, // 持续时间
  maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
  top: 70, // 到页面顶部距离
});

export default function() {
  const [isloading, setIsloading] = useState(false); // 页面加载状态
  const [isUploading, setIsUploading] = useState(false); // 文件上传状态
  const [allData, setAllData] = useState([]); // 所有数据
  const [listData, setListData] = useState([]); // 所有列表
  const [addOrRevise, setAddOrRevise] = useState(0); // 添加：0；修改：1。
  const [isShowAddDrawer, setIsShowAddDrawer] = useState(false); // 是否显示抽屉
  const [formData, setFormData] = useState({
    fundCode: '', // 产品代码
    fundName: '', // 基金名称
    feature: '', // 产品特征
    firstReason: '', // 推荐理由(橙色)
    secondReason: '', // 推荐理由(灰色)
    closedPeriod: '', // 产品锁定期(范围)
    speClosedPeriod: '', // 产品锁定期(具体)
    productInterpretation: {
      title: '', // 链接标题
      content: '', // 产品文案
      url: '', // 链接地址
    }, // 产品解读
    notice: {
      title: '', // 公告标签
      content: '', //　公告标题
      url: '', //　链接地址
      startTime: '', // 公告开始时间
      endTime: '', // 公告结束时间
    }, // 公告栏
    tradeContent: '', // 交易须知文案
    noticeList: [], // 公告文件
    highLightUrl: '', // 投资亮点
    videoUrl: '', // 视频url
    videoPage: '', // 视频封面
  }); // 表单数据
  const tableColumns = [
    {
      title: '序号',
      key: 'index',
      render: (row, record, index) => <div>{index + 1}</div>,
    },
    {
      title: '产品代码',
      dataIndex: 'fundCode',
      key: 'fundCode',
    },
    {
      title: '产品名称',
      dataIndex: 'fundName',
      key: 'fundName',
    },
    {
      title: '操作',
      key: 'action',
      render: (row, record, index) => (
        <div>
          <span
            style={{ color: '#1890FF', cursor: 'pointer', marginRight: '10px' }}
            onClick={() => {
              getProductData(row, record, index);
            }}
          >
            编辑
          </span>
          <span
            style={{ color: '#1890FF', cursor: 'pointer' }}
            onClick={() => {
              deleteListItem(row, record, index);
            }}
          >
            删除
          </span>
        </div>
      ),
    },
  ];
  const closedPeriodList = [
    {key: '0', value: '无锁定期'},
    {key: '1', value: '1个月以下'},
    {key: '2', value: '1-3个月'},
    {key: '3', value: '3-6个月'},
    {key: '4', value: '6-12个月'},
    {key: '5', value: '12个月以上'},
  ];
  useEffect(() => {
    getForm();
    initFormData();
  }, []);
  // 删除列表项
  const deleteListItem = (row, record, index) => {
    confirm({
      title: '提示',
      content: '确定删除吗',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        setIsloading(true);
        saveHigherFinanceNew(
          {
            value: '',
          },
          record.fundCode,
        )
          .then(res => {
            if (res.code !== '0000') {
              message.error(res.msg);
            } else {
              message.success('删除成功！');
              getForm();
            }
            setIsloading(false);
          })
          .catch(e => {
            message.error(e.message);
            setIsloading(false);
          });
      },
    });
  };
  // 初始化表单数据
  const initFormData = () => {
    setFormData({
      fundCode: '', // 产品代码
      fundName: '', // 基金名称
      feature: '', // 产品特征
      firstReason: '', // 推荐理由(橙色)
      secondReason: '', // 推荐理由(灰色)
      closedPeriod: '', // 产品锁定期(范围)
      speClosedPeriod: '', // 产品锁定期(具体)
      productInterpretation: {
        title: '', // 链接标题
        content: '', // 产品文案
        url: '', // 链接地址
      }, // 产品解读
      notice: {
        title: '', // 公告标签
        content: '', //　公告标题
        url: '', //　链接地址
        startTime: '', // 公告开始时间
        endTime: '', // 公告结束时间
      }, // 公告栏
      tradeContent: '', // 交易须知文案
      noticeList: [], // 公告文件
      highLightUrl: '', // 投资亮点
      videoUrl: '', // 视频url
      videoPage: '', // 视频封面
    });
  };
  // 修改表单项
  const changeFormItem = (type, value, index) => {
    const newFormData = { ...formData };
    switch (true) {
      case type === 'productInterpretationTitle':
        newFormData.productInterpretation.title = value;
        break;
      case type === 'productInterpretationContent':
        newFormData.productInterpretation.content = value;
        break;
      case type === 'productInterpretationUrl':
        newFormData.productInterpretation.url = value;
        break;
      case type === 'noticeTitle':
        newFormData.notice.title = value;
        break;
      case type === 'noticeContent':
        newFormData.notice.content = value;
        break;
      case type === 'noticeUrl':
        newFormData.notice.url = value;
        break;
      case type === 'noticeTime':
        newFormData.notice.startTime = value[0];
        newFormData.notice.endTime = value[1];
        break;
      case type === 'noticeListUrl':
        newFormData.noticeList[index].url = value;
        break;
      case type === 'noticeListName':
        newFormData.noticeList[index].name = value;
        break;
      case type === 'noticeListCreateTime':
        newFormData.noticeList[index].createTime = value;
        break;
      default:
        newFormData[type] = value;
        break;
    }
    setFormData(newFormData);
  };
  // 添加表单项
  const addFormItem = type => {
    const newFormData = { ...formData };
    switch (true) {
      case type === 'noticeList':
        newFormData[type].push({
          url: '',
          name: '',
          createTime: '',
        });
        break;
      default:
        break;
    }
    setFormData(newFormData);
  };
  // 删除表单项
  const deleteFormItem = (type, index) => {
    const newFormData = { ...formData };
    newFormData[type].splice(index, 1);
    setFormData(newFormData);
  };
  // 获取所有列表
  const getForm = () => {
    setIsloading(true);
    getHigherFinanceAllNew()
      .then(res => {
        let { code, data } = res;
        if (code === '0000' && data) {
          setAllData(data);
          const temp = data.filter((item, index) => {
            return item.value !== 'null' && item.value !== '';
          });
          const result = temp.map((item, index) => {
            return JSON.parse(item.value);
          });
          setListData(result);
        } else {
          message.error(res.msg);
        }
        setIsloading(false);
      })
      .catch(e => {
        message.error(e.message);
        setIsloading(false);
      });
  };
  // 点击编辑获取数据
  const getProductData = (row, record, index) => {
    let _formData = deepCopy(record);
    _formData.notice.startTime =
      _formData.notice.startTime === ''
        ? ''
        : moment(_formData.notice.startTime, 'YYYY-MM-DD HH:mm:ss');
    _formData.notice.endTime =
      _formData.notice.endTime === ''
        ? ''
        : moment(_formData.notice.endTime, 'YYYY-MM-DD HH:mm:ss');
    _formData.noticeList = _formData.noticeList.map((item, index) => {
      item.createTime =
        item.createTime === '' ? '' : moment(item.createTime, 'YYYY-MM-DD HH:mm:ss');
      return item;
    });
    setAddOrRevise(1);
    setFormData(_formData);
    setIsShowAddDrawer(true);
  };
  // 上传文件
  const customRequest = (options, type, index) => {
    setIsUploading(true);
    message.info('文件上传中');
    let params = new FormData();
    params.append('file', options.file);
    commonUpload(params)
      .then(res => {
        if (res.status_code === 0) {
          let _formData = { ...formData };
          switch (type) {
            case 'notice':
              _formData.notice.url = insertStr(res.data, 4, 's');
              break;
            case 'highLightUrl':
              _formData.highLightUrl = insertStr(res.data, 4, 's');
              break;
            case 'videoPage':
              _formData.videoPage = insertStr(res.data, 4, 's');
              break;
            case 'noticeList':
              _formData.noticeList[index].url = insertStr(res.data, 4, 's');
              _formData.noticeList[index].name = options.file.name.substring(
                0,
                options.file.name.lastIndexOf('.'),
              );
              break;
            default:
              break;
          }
          setFormData(_formData);
          setIsUploading(false);
          message.success('上传成功');
        } else {
          message.error('上传服务器失败');
          setIsUploading(false);
        }
      })
      .catch(() => {
        message.error('上传服务器失败');
        setIsUploading(false);
      });
  };
  // 关闭抽屉
  const closeDrawer = () => {
    initFormData();
    setIsShowAddDrawer(false);
  };
  // 保存时表单校验
  const checkForm = () => {
    let isPass = false;
    switch (true) {
      case formData.fundCode === '':
        message.error('请填写基金代码');
        break;
      case formData.fundCode.length !== 6 || formData.fundCode.includes(' '):
        message.error('基金代码格式不正确');
        break;
      case addOrRevise === 0 &&
        allData.some((item, index) => {
          return formData.fundCode === item.key && item.value !== 'null' && item.value !== '';
        }):
        message.error('该产品已存在');
        break;
      case formData.fundName === '':
        message.error('请填写基金名称');
        break;
      case formData.notice.url.toLowerCase().indexOf('.png') !== -1 ||
        formData.notice.url.toLowerCase().indexOf('.jpg') !== -1:
        message.error('公告链接地址不能为图片');
        break;
      case formData.noticeList.some((item, index) => {
        return item.url === '' || item.createTime === '' || item.createTime === null;
      }):
        message.error('已添加的公告文件不能为空');
        break;
      default:
        isPass = true;
        break;
    }
    return isPass;
  };
  // 保存
  const saveForm = () => {
    if (checkForm()) {
      let _formData = { ...formData };
      _formData.notice.startTime =
        _formData.notice.startTime === ''
          ? ''
          : _formData.notice.startTime.format('YYYY-MM-DD HH:mm:ss');
      _formData.notice.endTime =
        _formData.notice.endTime === ''
          ? ''
          : _formData.notice.endTime.format('YYYY-MM-DD HH:mm:ss');
      _formData.noticeList = _formData.noticeList.map((item, index) => {
        item.createTime = item.createTime === '' ? '' : item.createTime.format('YYYY-MM-DD');
        return item;
      });
      saveHigherFinanceNew(
        {
          value: JSON.stringify(_formData),
        },
        _formData.fundCode,
      )
        .then(res => {
          if (res.code !== '0000') {
            message.error(res.msg);
          } else {
            message.success('保存成功！');
            closeDrawer();
            getForm();
          }
        })
        .catch(e => {
          message.error(e.message);
        });
    }
  };
  // 发布
  const onPublish = () => {
    setIsloading(true);
    publishHigherFinanceAllNew()
      .then(data => {
        if (data.status_code === 0) {
          message.success('发布成功');
        } else {
          message.error(data.message);
        }
        setIsloading(false);
      })
      .catch(() => {
        setIsloading(false);
      });
  };
  // 深拷贝
  const deepCopy = (obj, cache = new WeakMap()) => {
    if (!obj instanceof Object) {
      return obj;
    }
    // 防止循环引用
    if (cache.get(obj)) {
      return cache.get(obj);
    }
    // 支持函数
    if (obj instanceof Function) {
      return function() {
        obj.apply(this, arguments);
      };
    }
    // 支持日期
    if (obj instanceof Date) {
      return new Date(obj);
    }
    // 支持正则对象
    if (obj instanceof RegExp) {
      return new RegExp(obj.source, obj.flags);
    }
    // 还可以增加其他对象,比如:map, set 等,根据情况判断增加极客

    // 数组是 key 为数字的特殊对象
    const res = Array.isArray(obj) ? [] : {};

    // 缓存 copy 的对象,用于处理循环引用的情况
    cache.set(obj, res);

    Object.keys(obj).forEach(key => {
      if (obj[key] instanceof Object) {
        res[key] = deepCopy(obj[key], cache);
      } else {
        res[key] = obj[key];
      }
    });
    return res;
  };
  // 字符串指定位置插入字符
  const insertStr = (soure, start, newStr) => {
    return soure.slice(0, start) + newStr + soure.slice(start);
  };
  return (
    <Spin spinning={isloading}>
      <div className={styles['higher-finance-new']}>
        <Button
          type="danger "
          style={{ marginBottom: '20px', marginRight: '20px' }}
          onClick={() => {
            onPublish();
          }}
        >
          发布
        </Button>
        <Button
          type="primary"
          style={{ marginBottom: '20px' }}
          onClick={() => {
            setIsShowAddDrawer(true);
            setAddOrRevise(0);
          }}
        >
          添加
        </Button>
        <Table
          columns={tableColumns}
          dataSource={listData}
          pagination={false}
          rowKey={record => record.fundCode}
        />
        <Drawer
          className="higher-finance-new-drawer"
          title={addOrRevise === 0 ? '添加' : '编辑'}
          placement="right"
          width="1500"
          maskClosable={false}
          destroyOnClose={true}
          onClose={closeDrawer}
          visible={isShowAddDrawer}
        >
          <ConfigProvider locale={zh_CN}>
            {/* 产品代码、产品名称、产品特征、推荐理由、产品锁定期 */}
            <Row gutter={[40, 14]}>
              <Col span={8}>
                <div>产品代码：</div>
                <Input
                  placeholder="请输入产品代码"
                  disabled={addOrRevise === 1}
                  value={formData.fundCode}
                  onChange={e => {
                    changeFormItem('fundCode', e.target.value);
                  }}
                />
              </Col>
              <Col span={8}>
                <div>产品名称：</div>
                <Input
                  placeholder="请输入产品名称"
                  value={formData.fundName}
                  onChange={e => {
                    changeFormItem('fundName', e.target.value);
                  }}
                />
              </Col>
            </Row>
            <Row gutter={[40, 14]}>
              <Col span={8}>
                <div>产品特征：</div>
                <Input
                  placeholder="请输入产品特征"
                  value={formData.feature}
                  onChange={e => {
                    changeFormItem('feature', e.target.value);
                  }}
                />
              </Col>
              <Col span={8}>
                <div>推荐理由(橙色)：</div>
                <Input
                  placeholder="请输入推荐理由(橙色)"
                  value={formData.firstReason}
                  onChange={e => {
                    changeFormItem('firstReason', e.target.value);
                  }}
                />
              </Col>
              <Col span={8}>
                <div>推荐理由(灰色)：</div>
                <Input
                  placeholder="请输入推荐理由(灰色)"
                  value={formData.secondReason}
                  onChange={e => {
                    changeFormItem('secondReason', e.target.value);
                  }}
                />
              </Col>
            </Row>
            <Row gutter={[40, 14]}>
              <Col span={8}>
                <div>产品锁定期：</div>
                <Select value={formData.closedPeriod} onChange={(e) => {changeFormItem('closedPeriod',e)}} style={{width:'100%'}}>
                  {
                    closedPeriodList && closedPeriodList.map((item, index) => {
                        return (
                            <Option value={item.key} key={index}>{item.value}</Option>
                        );
                    })
                  }
                </Select>
              </Col>
              <Col span={8}>
                <div>产品锁定期文案：</div>
                <Input
                  placeholder="请输入产品锁定期文案"
                  value={formData.speClosedPeriod}
                  onChange={e => {
                    changeFormItem('speClosedPeriod', e.target.value);
                  }}
                />
              </Col>
            </Row>
            <Divider />
            {/* 产品解读 */}
            <div>
              <Row gutter={[40, 14]}>
                <Col span={8}>
                  <div style={{ fontWeight: '600' }}>产品解读</div>
                </Col>
              </Row>
              <Row gutter={[40, 14]}>
                <Col span={8}>
                  <div>链接标题：</div>
                  <Input
                    placeholder="链接标题"
                    value={formData.productInterpretation.title}
                    onChange={e => {
                      changeFormItem('productInterpretationTitle', e.target.value);
                    }}
                  />
                </Col>
                <Col span={8}>
                  <div>链接地址：</div>
                  <Input
                    placeholder="请填入链接地址"
                    value={formData.productInterpretation.url}
                    onChange={e => {
                      changeFormItem('productInterpretationUrl', e.target.value);
                    }}
                  />
                </Col>
              </Row>
              <Row gutter={[40, 14]}>
                <Col span={24}>
                  <div>产品文案：</div>
                  <TextArea
                    placeholder="请输入产品文案"
                    value={formData.productInterpretation.content}
                    onChange={e => {
                      changeFormItem('productInterpretationContent', e.target.value);
                    }}
                  />
                </Col>
              </Row>
            </div>
            {/* 公告栏 */}
            <div>
              <Row gutter={[40, 14]}>
                <Col span={8}>
                  <div style={{ fontWeight: '600' }}>公告栏</div>
                </Col>
              </Row>
              <Row gutter={[40, 14]}>
                <Col span={8}>
                  <div>公告标签：</div>
                  <Input
                    placeholder="请输入公告标签"
                    maxLength={2}
                    value={formData.notice.title}
                    onChange={e => {
                      changeFormItem('noticeTitle', e.target.value);
                    }}
                  />
                </Col>
                <Col span={8}>
                  <div>公告标题：</div>
                  <Input
                    placeholder="请输入公告标题"
                    maxLength={30}
                    value={formData.notice.content}
                    onChange={e => {
                      changeFormItem('noticeContent', e.target.value);
                    }}
                  />
                </Col>
                <Col span={8}>
                  <div>公告开始至结束时间：</div>
                  <RangePicker
                    showToday={false}
                    style={{ width: '100%' }}
                    showTime
                    value={[formData.notice.startTime, formData.notice.endTime]}
                    onChange={e => {
                      changeFormItem('noticeTime', e);
                    }}
                  />
                </Col>
              </Row>
              <Row gutter={[40, 14]}>
                <Col span={16}>
                  <div>链接地址：</div>
                  <Input
                    placeholder="请填入链接地址"
                    value={formData.notice.url}
                    onChange={e => {
                      changeFormItem('noticeUrl', e.target.value);
                    }}
                  />
                  <Upload
                    customRequest={options => {
                      customRequest(options, 'notice');
                    }}
                    showUploadList={false}
                  >
                    <Button style={{ marginLeft: '4px' }} disabled={isUploading}>
                      选择文件
                    </Button>
                  </Upload>
                </Col>
              </Row>
            </div>
            <Divider />
            {/* 投资亮点、视频链接、视频封面 */}
            <Row gutter={[40, 14]}>
              <Col span={16}>
                <div>投资亮点：</div>
                <Input
                  placeholder="请填入链接地址"
                  value={formData.highLightUrl}
                  onChange={e => {
                    changeFormItem('highLightUrl', e.target.value);
                  }}
                />
                <Upload
                  customRequest={options => {
                    customRequest(options, 'highLightUrl');
                  }}
                  showUploadList={false}
                >
                  <Button style={{ marginLeft: '4px' }} disabled={isUploading}>
                    选择文件
                  </Button>
                </Upload>
              </Col>
            </Row>
            <Row gutter={[40, 14]}>
              <Col span={16}>
                <div>视频封面：</div>
                <Input
                  placeholder="请填入链接地址"
                  value={formData.videoPage}
                  onChange={e => {
                    changeFormItem('videoPage', e.target.value);
                  }}
                />
                <Upload
                  customRequest={options => {
                    customRequest(options, 'videoPage');
                  }}
                  showUploadList={false}
                >
                  <Button style={{ marginLeft: '4px' }} disabled={isUploading}>
                    选择文件
                  </Button>
                </Upload>
              </Col>
            </Row>
            <Row gutter={[40, 14]}>
              <Col span={16}>
                <div>视频链接：</div>
                <Input
                  placeholder="请填入链接地址"
                  value={formData.videoUrl}
                  onChange={e => {
                    changeFormItem('videoUrl', e.target.value);
                  }}
                />
              </Col>
            </Row>
            {/* 交易须知文案 */}
            <Row gutter={[40, 14]}>
              <Col span={24}>
                <div>交易须知文案：</div>
                <TextArea
                  placeholder="请输入交易须知文案"
                  value={formData.tradeContent}
                  onChange={e => {
                    changeFormItem('tradeContent', e.target.value);
                  }}
                />
              </Col>
            </Row>
            {/* 公告文件 */}
            <Row gutter={[40, 14]}>
              <Col span={8}>
                <div>公告文件：</div>
                <div
                  style={{ color: '#1890ff', cursor: 'pointer', marginRight: '10px' }}
                  onClick={() => {
                    window.open(
                      `http://${
                        window.location.href.indexOf('8080') !== -1 ||
                        window.location.href.indexOf('8000') !== -1
                          ? 'febs.5ifund.com:8080/yytj/'
                          : '172.20.205.130/yytj/index.html'
                      }#/form/uploadFile`,
                    );
                  }}
                >
                  上传
                </div>
                <div
                  style={{ color: '#1890ff', cursor: 'pointer' }}
                  onClick={() => {
                    addFormItem('noticeList');
                  }}
                >
                  添加
                </div>
              </Col>
            </Row>
            <Row gutter={[40, 14]}>
              {formData.noticeList.map((item, index) => {
                return (
                  <div key={index}>
                    <Col span={8}>
                      <Input
                        placeholder="公告文件链接地址"
                        value={formData.noticeList[index].url}
                        onChange={e => {
                          changeFormItem('noticeListUrl', e.target.value, index);
                        }}
                      />
                      <Upload
                        customRequest={options => {
                          customRequest(options, 'noticeList', index);
                        }}
                        showUploadList={false}
                      >
                        <Button style={{ marginLeft: '4px' }} disabled={isUploading}>
                          选择文件
                        </Button>
                      </Upload>
                    </Col>
                    <Col span={8}>
                      <Input
                        placeholder="请填入文件名称"
                        value={formData.noticeList[index].name}
                        onChange={e => {
                          changeFormItem('noticeListName', e.target.value, index);
                        }}
                      />
                    </Col>
                    <Col span={7}>
                      <DatePicker
                        style={{ width: '100%' }}
                        placeholder="公告文件日期"
                        value={formData.noticeList[index].createTime}
                        onChange={e => {
                          changeFormItem('noticeListCreateTime', e, index);
                        }}
                      />
                    </Col>
                    <Col span={1}>
                      <div
                        style={{ color: '#ff4d4f', cursor: 'pointer' }}
                        onClick={() => {
                          deleteFormItem('noticeList', index);
                        }}
                      >
                        删除
                      </div>
                    </Col>
                  </div>
                );
              })}
            </Row>
            {/* 保存 */}
            <Row gutter={[40, 14]}>
              <Col span={8}>
                <Button
                  type="primary"
                  onClick={() => {
                    saveForm();
                  }}
                >
                  保存
                </Button>
              </Col>
            </Row>
          </ConfigProvider>
        </Drawer>
      </div>
    </Spin>
  );
}
