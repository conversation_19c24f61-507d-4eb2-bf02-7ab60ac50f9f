import * as React from 'react';
import classNames from 'classnames';
import { <PERSON><PERSON>, Card, Spin, message } from 'antd';
import api from 'api';

import FormRender from 'form-render/lib/antd';
// @ts-ignore
import FROM_JSON from './form';

const { useState, useEffect } = React;
const { fetchChenXing2022Data, postChenXing2022Data } = api;

const VALID_FUND = [
  ['005233', '040035', '519704', '163409', '001018', '163807'],
  ['450001', '519697'],
  ['202105', '002351'],
  ['100018', '164808'],
  ['000402', '003547'],
];

const PRIZE_TITLE = [
  '积极配置型基金提名',
  '混合型基金提名',
  '积极债券型基金提名',
  '普通债券型基金提名',
  '纯债型基金提名',
];

export default function() {
  const [formData, setFormData]: any = useState([{}, {}, {}, {}, {}]);
  const [valid, setValid] = useState([]);
  const [init, setInit] = useState(false);

  const submit = () => {
    for (let i = 0; i < formData.length; i++) {
      let list = formData[i].list || [];
      if (list.length) {
        for (let j = 0; j < list.length; j++) {
          if (!list[j].code) {
            message.error('基金代码不能为空');
            return;
          }
          if (VALID_FUND[i].indexOf(list[j].code) === -1) {
            message.error(`${list[j].code}不属于${PRIZE_TITLE[i]}`);
            return;
          }
          if (!list[j].desc) {
            message.error('介绍文案不能为空');
            return;
          }
          if (list[j].desc.length > 51) {
            message.error('介绍文案长度不能超过51个字');
            return;
          }
        }
      } else {
        message.error(PRIZE_TITLE[i] + '未配置基金');
        return;
      }
    }
    postChenXing2022Data({
      value: JSON.stringify({ list: formData }),
    })
      .then((data: any) => {
        if (data.code === '0000') {
          message.success('保存成功');
          setTimeout(() => {
            location.reload();
          }, 400);
        } else {
          message.error(data.message || '网络错误，请稍后再试');
        }
      })
      .catch(() => {
        message.error('网络错误，请稍后再试');
      });
  };

  const getData = () => {
    fetchChenXing2022Data()
      .then((data: any) => {
        console.log(data);
        if (data.code === '0000') {
          setInit(true);
          if (data.data) {
            data = JSON.parse(data.data);
            setFormData(data.list);
          }
        } else {
          message.error(data.message || '网络错误，请稍后再试');
        }
      })
      .catch(() => {
        message.error('网络错误，请稍后再试');
      });
  };

  useEffect(() => {
    getData();
  }, []);

  return (
    <Spin spinning={!init} tip="加载中...">
      {[0, 1, 2, 3, 4].map(index => (
        <Card style={{ width: 700, marginBottom: 5 }} title={PRIZE_TITLE[index]} key={index}>
          <FormRender
            propsSchema={FROM_JSON}
            formData={formData[index]}
            onChange={data => {
              let _formData = [...formData];
              _formData[index] = data;
              setFormData(_formData);
            }}
            onValidate={setValid}
          />
        </Card>
      ))}
      <Button onClick={submit} type="primary" style={{ marginTop: 30 }}>
        提交
      </Button>
    </Spin>
  );
}
