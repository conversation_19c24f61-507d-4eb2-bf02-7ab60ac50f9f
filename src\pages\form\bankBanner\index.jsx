 /*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React, { Fragment } from 'react';
import {Button, Select, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import Form<PERSON><PERSON> from 'form-render/lib/antd';
import schema from './form.json';

const {  fetchBankBanner, postBankBanner, fetchBankBannerTemp, postBankBannerTemp} = api;
const bank = [{
    text: '兴业银行',
    value: 'xyb'
}]

@autobind
class BankBanner extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            formData: {},
            bank: ''
        };
    }

    onChange = formData => {
        this.setState({ formData }, () => {console.log(this.state)});
    }
    
    dataInit () {
        fetchBankBannerTemp(null, 'bankBanner' + this.state.bank).then(data => {
            if (data.data) {
                data = JSON.parse(data.data);
                this.setState({formData: data});
            } else {
                this.setState({formData: schema.formData});
            }
        })
    }
    
    saveForm () {
        let _formData = this.state.formData;
        console.log(_formData)
        postBankBannerTemp ({
            value: JSON.stringify(_formData)
        }, 'bankBanner' + this.state.bank, null ).then(data => {
            if (data) {
                message.success('保存成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
  }

    updateForm () {
        let _formData = this.state.formData;
        postBankBannerTemp ({
            value: JSON.stringify(_formData)
        }, 'bankBanner' + this.state.bank, null).then(data => {
            if (data) {
                message.success('保存成功');
                postBankBanner ({
                    value: JSON.stringify(_formData)
                }, this.state.bank, null ).then(data => {
                    if (data) {
                        message.success('修改成功');
                        window.location.reload();
                    } else {
                        message.error(data.message);
                    }
                })
            } else {
                message.error(data.message);
            }
        })
    }

    handleSelect(value) {
        this.setState({bank: value})
    }


    componentDidMount () {
        // this.dataInit();
        // this.newBoyDataInit();
    }

    render () {
        return (
            <section className="codeInterface" style={{ width: '800px' }}>
                <div className="u-j-middle">
                    <Select onChange={this.handleSelect} style={{ width: '100px' }}>
                        {
                            bank.map((item, index) => {
                                return (
                                    <Select.Option key={index} value={item.value}>{item.text}</Select.Option>
                                )
                            })
                        }
                    </Select>
                    <Button type="primary" onClick={this.dataInit}>查询</Button>
                </div>

                <FormRender
                    propsSchema={schema.propsSchema}
                    formData={this.state.formData}
                    onChange={this.onChange}
                    displayType="row"
                    showDescIcon={true}
                />

                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={this.updateForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button type="danger">
                        提交
                    </Button>
                </Popconfirm>

                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={this.saveForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="primary" 
                        style={{ marginLeft: '600px' }}
                    >
                        保存
                    </Button>
                </Popconfirm>

            </section>
        )
    }
}

export default BankBanner;