import React from 'react';
import api from 'api';
import moment from 'moment';
import { Button, Input, Table, message, Modal, Popconfirm, Select, Form, DatePicker, Pagination } from 'antd';
import { autobind } from 'core-decorators';
import styles from './index.less';
import { outputXlsxFile, formatDate } from 'utils/utils';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { getRiskMonitor } = api;

@autobind
class ComplianceRisk extends React.Component<any, any> {
    constructor(props: any) {
        super(props);
        this.state = {
            init: false,
            visible: false,
            dataFund: [],
            currentPage: 1,
            pageSize: 100,
            total: void 0,
            pages: 0,
            editFund: {},
            constantEditFund: {},
            allData: [],
            download: true
        }
    }

    async componentDidMount () {
        let params: any = {}, riskQueryDto: any = {};
        let temp = +new Date();
        riskQueryDto = {
            riskUpdateTimeStart: formatDate(new Date(temp - 7 * 24 * 3600 * 1000), 'YYYYMMdd'),
            riskUpdateTimeEnd: formatDate(new Date(), 'YYYYMMdd'),
            limit: this.state.pageSize,
            offset: (this.state.currentPage - 1) * this.state.pageSize,
        };
        params.riskQueryDto = JSON.stringify(riskQueryDto);
        this.setState({
            constantEditFund: {...riskQueryDto},
        }, async () => {
            await this.getRiskMonitor(params);
            await this.getAllData();
        });
    }

    async getRiskMonitor(query: any, flag: number = 0) { // flag 0 初始化&筛选 1 默默请求数据 2 分页请求数据
        try {
            let { code, data, message, recordSum } = await getRiskMonitor(query);
            if (code === '0000') {
                data && data.forEach((item: any, index: number) => {
                    item.key = index;
                    item.buyTime = this.handleTime(item.buyTime);
                    item.investigateTime = this.handleTime(item.investigateTime);
                    item.riskReason = item.riskReason === '2' ? '高端理财风险>用户风险等级' : '公募产品风险>用户风险等级（1级）';
                    item.riskDescription = item.riskDescription ? `存在${item.riskDescription}个风险`: '';
                })
                if (flag === 0) {
                    let pages = recordSum !== undefined && Math.ceil(recordSum / this.state.pageSize);
                    this.setState({
                        init: true,
                        dataFund: [...data],
                        total: recordSum,
                        pages,
                        download: false
                    })
                } else if (flag === 1) {
                    this.setState((prevState: any) => ({
                        allData: [...prevState.allData, ...data]
                    }))
                } else {
                    this.setState({
                        dataFund: [...data],
                    })
                }
                
            } else {
                message.error(message || '查询数据失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    // 高级筛选
    handleFilter() {
        this.setState({
            visible: true,
        });
    }

    handleOk() {
        let editFund: any = {}, params: any = {};
        const { pageSize } = this.state;
        this.props.form.validateFields((err: any, values: any) => {
            if (!err) {
                const { testTime, buyTime, ...other } = values;
                editFund.custId = other.custId ? other.custId : '';
                editFund.fundType = other.fundType ? other.fundType : '';
                editFund.custRiskLevel = other.custRiskLevel ? other.custRiskLevel : '';
                editFund.fundCode = other.fundCode ? other.fundCode : '';
                editFund.fundName = other.fundName ? other.fundName : '';
                editFund.fundRiskLevel = other.fundRiskLevel ? other.fundRiskLevel : '';
                editFund.riskReason = other.riskReason ? other.riskReason : '';
                editFund.riskUpdateTimeStart = (testTime && testTime.length > 0) ? testTime[0].format('YYYYMMDD') : '';
                editFund.riskUpdateTimeEnd = (testTime && testTime.length > 0) ? testTime[1].format('YYYYMMDD') : '';
                editFund.buyTimeStart = buyTime ? buyTime[0].format('YYYYMMDD') : '';
                editFund.buyTimeEnd = buyTime ? buyTime[1].format('YYYYMMDD') : '';
                if (other.riskDescription === "1") {
                    editFund.riskNumStart = 1;
                    editFund.riskNumEnd = 3;
                } else if (other.riskDescription === "2"){
                    editFund.riskNumStart = 4;
                }
                editFund.limit = pageSize;
                editFund.offset =  0;
                params.riskQueryDto = JSON.stringify(editFund);
                
                this.setState({
                    visible: false,
                    editFund,
                    currentPage: 1,
                    constantEditFund: {...editFund},
                    allData: [],
                }, async () => {
                    await this.getRiskMonitor(params);
                    await this.getAllData();
                });
            }
        });
    }
    handleCancel() {
        this.setState({
            visible: false
        });
    }
    handleTime(str: string) {
        let result: string = '';
        if (!str) { return str }
        result = str.slice(0, 4) + '.' + str.slice(4, 6) + '.' + str.slice(6, 8);
        return result;
    }
    exportExcel() {
        if (!this.state.download) {
            message.error("正在请求文件，请稍后点击导出Excel文件");
            return;
        }
        let data = [
            ['用户ID', '类型', '最新风险等级', 'null', '描述', '存在风险的理财产品', 'null', 'null', 'null', '风险原因'],
            ['null', 'null', '测评时间', '级别', 'null', '购买时间', '产品名称', '代码', '风险等级', 'null'],
        ]
        const { allData } = this.state;
        allData.forEach((item: any) => {
            let arr = [
                item.custId,
                item.fundType,
                item.investigateTime,
                item.custRiskLevel,
                item.riskDescription,
                item.buyTime,
                item.fundName,
                item.fundCode,
                item.fundRiskLevel,
                item.riskReason
            ];
            data.push(arr);
        })
        let merge = [
            { s: {r:0, c:0}, e: {r:1, c:0} },
            { s: {r:0, c:1}, e: {r:1, c:1} },
            { s: {r:0, c:2}, e: {r:0, c:3} },
            { s: {r:0, c:4}, e: {r:1, c:4} },
            { s: {r:0, c:5}, e: {r:0, c:8} },
            { s: {r:0, c:9}, e: {r:1, c:9} },
        ]
        outputXlsxFile(data, merge, '基金用户风险等级查询');
    }
    pageChange(page: number) {
        let params: any = {}, editFund = { ...this.state.editFund };
        editFund.offset = (page - 1) * this.state.pageSize;
        params.riskQueryDto = JSON.stringify(editFund);
        this.getRiskMonitor(params, 2);
        this.setState({
            currentPage: page,
            editFund
        })
    }
    sizeChange(page: number, size: number) {
        let params: any = {}, editFund = { ...this.state.editFund };
        editFund.offset = (page - 1) * this.state.pageSize;
        editFund.limit = size;
        params.riskQueryDto = JSON.stringify(editFund);
        this.getRiskMonitor(params, 2);
        this.setState({
            currentPage: page,
            pageSize: size,
            editFund
        })
    }
    async getAllData() {
        let { pages, constantEditFund, pageSize } = this.state;
        for(let i = 1; i <= pages; i++){
            let params: any = {};
            constantEditFund.offset = (i - 1) * pageSize;
            params.riskQueryDto = JSON.stringify(constantEditFund);
            await this.getRiskMonitor(params, 1);
        }
        this.setState({
            download: true
        })
    }
    render() {
        const _this = this;
        const columns = [
            {
                title: '用户ID',
                dataIndex: 'custId',
                key: 'custId',
            },
            {
                title: '类型',
                dataIndex: 'fundType',
                key: 'fundType',
            },
            {
                title: '最新风险等级',
                children: [
                    {
                        title: '测评时间',
                        dataIndex: 'investigateTime',
                        key: 'investigateTime',
                    },
                    {
                        title: '级别',
                        dataIndex: 'custRiskLevel',
                        key: 'custRiskLevel',
                    }
                ],
            },
            {
                title: '描述',
                dataIndex: 'riskDescription',
                key: 'riskDescription',
            },
            {
                title: '存在风险的理财产品',
                children: [
                    {
                        title: '购买时间',
                        dataIndex: 'buyTime',
                        key: 'buyTime',
                    },
                    {
                        title: '产品名称',
                        dataIndex: 'fundName',
                        key: 'fundName',
                    },
                    {
                        title: '代码',
                        dataIndex: 'fundCode',
                        key: 'fundCode',
                    },
                    {
                        title: '风险等级',
                        dataIndex: 'fundRiskLevel',
                        key: 'fundRiskLevel',
                    }
                ],
            },
            {
                title: '风险原因',
                dataIndex: 'riskReason',
                key: 'riskReason',
            },
        ];
        const formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
        };
        const { getFieldDecorator } = this.props.form;
        const { visible, dataFund, total, currentPage, download } = this.state;
        let temp = +new Date();
        const paginationProps: any = {
            current: currentPage,
            showTotal: (total: any) => `共 ${total} 条`,
            defaultPageSize: 100,
            showQuickJumper: true,
            showSizeChanger: true,
            total,
            pageSizeOptions: [
                '100', '200', '300', '400'
            ],
            onChange: _this.pageChange,
            onShowSizeChange: _this.sizeChange
        };
        if (!this.state.init) return '加载中';
        return (
            <div className={styles['compliance-risk']}>
                <header style={{textAlign: 'right', marginBottom: 20}}>
                    <Button type="primary" style={{marginRight: 10}} onClick={this.handleFilter}>高级筛选</Button>
                    <Button type="primary" disabled={!download} onClick={this.exportExcel}>导出Excel</Button>
                </header>
                <Table 
                    dataSource={dataFund} 
                    columns={columns} 
                    bordered
                    pagination={paginationProps}/>
                <Modal
                    title="高级筛选"
                    visible={visible}
                    footer={null}
                    onCancel={this.handleCancel}
                    wrapClassName="compliance-wrapper"
                    destroyOnClose
                    width={720}
                    >
                    <Form layout="vertical" {...formItemLayout}>
                        <h3 style={{marginBottom: 20, fontWeight: 'bold'}}>通用筛选</h3>
                        <Form.Item label="用户ID">
                            {getFieldDecorator("custId")(<Input placeholder="请输入用户名称" allowClear/>)}
                        </Form.Item>
                        <Form.Item label="类型" wrapperCol={{ span: 10 }}>
                            {getFieldDecorator("fundType")(<Select placeholder="类型" allowClear>
                                <Option value="高端理财">高端理财</Option>
                                <Option value="公募基金">公募基金</Option>
                            </Select>)}
                        </Form.Item>
                        <Form.Item label="风险原因" wrapperCol={{ span: 18 }}>
                            {getFieldDecorator("riskReason")(<Select placeholder="风险原因" allowClear>
                                <Option value="2">{'高端理财风险>用户风险等级'}</Option>
                                <Option value="1">{'公募产品风险>用户风险等级（1级）'}</Option>
                            </Select>)}
                        </Form.Item>
                        <h3 style={{marginBottom: 20, fontWeight: 'bold'}}>最新风险测评筛选</h3>
                        <Form.Item label="测评日期">
                            {getFieldDecorator("testTime", {
                                initialValue: [moment(formatDate(new Date(temp - 7 * 24 * 3600 * 1000), 'YYYY-MM-dd')), moment(formatDate(new Date(), 'YYYY-MM-dd'))],
                                rules:[{
                                    type: 'array', required: true, message: '请选择测评日期'
                                }]
                            })(<RangePicker />)}
                        </Form.Item>
                        <Form.Item label="级别" wrapperCol={{ span: 6 }}>
                            {getFieldDecorator("custRiskLevel")(<Select placeholder="级别" allowClear>
                                <Option value="1">1</Option>
                                <Option value="2">2</Option>
                                <Option value="3">3</Option>
                                <Option value="4">4</Option>
                                <Option value="5">5</Option>
                            </Select>)}
                        </Form.Item>
                        <Form.Item label="描述" wrapperCol={{ span: 11 }}>
                            {getFieldDecorator("riskDescription")(<Select placeholder="描述" allowClear>
                                <Option value="1">存在1-3个风险</Option>
                                <Option value="2">存在3个以上风险</Option>
                            </Select>)}
                        </Form.Item>
                        <h3 style={{marginBottom: 20, fontWeight: 'bold'}}>风险产品筛选</h3>
                        <Form.Item label="购买日期">
                            {getFieldDecorator("buyTime")(<RangePicker />)}
                        </Form.Item>
                        <Form.Item label="产品名称">
                            {getFieldDecorator("fundName")(<Input placeholder="请输入产品名称" allowClear/>)}
                        </Form.Item>
                        <Form.Item label="代码">
                            {getFieldDecorator("fundCode")(<Input placeholder="请输入产品代码" allowClear/>)}
                        </Form.Item>
                        <Form.Item label="风险等级" wrapperCol={{ span: 8 }}>
                            {getFieldDecorator("fundRiskLevel")(<Select placeholder="风险等级" allowClear>
                                <Option value="1">1</Option>
                                <Option value="2">2</Option>
                                <Option value="3">3</Option>
                                <Option value="4">4</Option>
                                <Option value="5">5</Option>
                            </Select>)}
                        </Form.Item>
                        
                        <Form.Item wrapperCol={{span: 24}} style={{textAlign: 'center', marginTop: 20}}>
                            <Button onClick={this.handleCancel} style={{marginRight: 20}}>取消</Button>
                            <Popconfirm title={'是否保存该筛选？'} onConfirm={() => this.handleOk()}>
                                <Button type="primary">确定</Button>
                            </Popconfirm>
                        </Form.Item>
                    </Form>
                </Modal>
            </div>
        )
    }
}

export default Form.create({ name: 'complianceRisk' })(ComplianceRisk)