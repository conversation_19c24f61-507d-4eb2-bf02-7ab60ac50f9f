import React from 'react';
import { Form, Input, Select } from 'antd';
import { INCOME_RANGE_MAP } from '../../const';

const { Option } = Select;

const NoFundTrendForm = props => {
  const { getFieldDecorator } = props;
  return (
    <>
      <Form.Item label="基金代码">
        {getFieldDecorator('fundRecommend.mainFund.fundCode', {
          rules: [{ required: true, message: '请输入基金代码' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="收益区间选择">
        {getFieldDecorator('fundRecommend.mainFund.optional.isOldFund.incomeRange', {
          rules: [{ required: true, message: '请选择收益区间' }],
        })(
          <Select>
            {Array.from(INCOME_RANGE_MAP.entries()).map(([key, value]) => {
              return (
                <Option value={key} key={key}>
                  {value}
                </Option>
              );
            })}
          </Select>,
        )}
      </Form.Item>
      <Form.Item label="突出亮点1-主">
        {getFieldDecorator('fundRecommend.mainFund.mainHighLight1', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="突出亮点1-副">
        {getFieldDecorator('fundRecommend.mainFund.subHighLight1', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="突出亮点2-主">
        {getFieldDecorator('fundRecommend.mainFund.optional.noShowFundTrend.mainHighLight2', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="突出亮点2-副">
        {getFieldDecorator('fundRecommend.mainFund.optional.noShowFundTrend.subHighLight2', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
    </>
  );
};

export default NoFundTrendForm;
