export default {
	"type": "object",
	"properties": {
		"list": {
			"title": "",
			"type": "array",
			"items": {
				"type": "object",
				"required": ["code", "desc"],
				"properties": {
					"code": {
						"title": "基金代码",
						"type": "string",
						"ui:options": {}
					},
					"syDesc": {
						"title": "收益率",
						"type": "string",
						"enum": [
							"year",
							"hyear",
							"tmonth"
						],
						"enumNames": [
							"近一年",
							"近六月",
							"近三月"
						],
						"default": "year"
					},
					"desc": {
						"title": "介绍文案",
						"type": "string",
						"format": "textarea",
						"ui:options": {},
						"maxLength": 51
					}
				}
			}
		}
	}
};
