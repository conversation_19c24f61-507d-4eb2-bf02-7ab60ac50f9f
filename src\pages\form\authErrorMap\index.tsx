import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';;
import { message } from 'antd';
import api from 'api'
const { postHash, fetchHashAll, fetchBlockDetail } = api;
export default function() {
  let amisScoped: any;
  const init = async () => {
    let roles = localStorage.getItem('roles');
    let amis = amisRequire('amis/embed');
    let authDictOption = [];
    let authDictMap = {};
    let equityMap = {};
    const sceneMap = [
      {
        label: '开户',
        value: 'openAccountScene',
      },
      {
        label: '交易',
        value: 'tradeScene',
      },
      {
        label: '签约（含渠道升级、绑卡、修改手机号）',
        value: 'signScene',
      },
      {
        label: '定投',
        value: 'dtScene',
      },
    ];
    const sceneShowMap = {
      openAccountScene: '开户',
      tradeScene: '交易',
      signScene: '签约（含渠道升级、绑卡、修改手机号）',
    };

    let body = {
      key: 'AUTH_ERROR_DICT',
    };
    await fetchHashAll(body)
      .then((res: { code: string; message: string; data: string }) => {
        if (res.code === '0000') {
          if (!res.data) {
            return;
          }
          const formData = res.data;
          authDictOption = Object.keys(formData).map(keyItem => {
            // authDictOption.push({ label: keyItem, value: keyItem });
            authDictMap[keyItem] = JSON.parse(formData[keyItem]).value;
            return {
              label: keyItem,
              value: keyItem,
            };
          });
          console.log('console.log', authDictMap, authDictOption);
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        message.error('网络请求错误，请稍后重试');
      });
    amisScoped = amis.embed(
      '#authErrorMap',
      {
        ...amisJSON,
        data: {
          authDictMap,
          authDictOption,
          sceneMap,
          sceneShowMap
        },
      },
      {},
      amisEnv(),
    );
  };
  useEffect(() => {
    init();
    return () => {
      amisScoped.unmount();
    };
  }, []);

  return <div id="authErrorMap"></div>;
}
