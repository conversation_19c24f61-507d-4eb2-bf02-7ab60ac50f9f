import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';

import { Button, Drawer, message, Popconfirm } from 'antd';
import {  STRATEGYTable, TATable } from '../../utiles';
import FormRender from 'form-render/lib/antd';
export interface IProps {
  visible: boolean;
  data: STRATEGYTable;
  onSubmit: (data: object) => void;
  onClose: () => void;
}
const TGStrategyAlterConfig: FC<IProps> = ({ visible, data, onSubmit, onClose }) => {
    const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  useEffect(()=>{
    if(visible){
      setData({
        protoName:data?.strategy?.fundAlterPool?.name||'基金备选库',
        protoLink:data?.strategy?.fundAlterPool?.link
      })
    }
  },[visible,data])
  const schema = {
    type: 'object',
    // required:['protoName','protoLink'],
    properties:{
      protoName: {
          title: '基金备选库名称',
          type: 'string',
          "ui:hidden":true
        },
        protoLink: {
          title: '基金备选库链接',
          type: 'string',
          pattern: "^http(|s)?://[^\n ，]*$"
        },
    }
   
  };
  const handleSave=()=>{
      console.log(formData)
    if (valid.length > 0) {
    message.error(`输入校验未通过，请检查`)
    return ;
    }
    onSubmit(formData);
  }
  return (
    <Drawer title={<>{`投顾机构：${data?.taName||'--'}`}<span style={{margin:'0 12px'}}></span>{`策略名称：${data?.investConsultTacticsName||'--'}`}</>} width={1000} visible={visible} onClose={onClose} >
      <div style={{ paddingTop: '40px' }}>
        <FormRender formData={formData} 
         onChange={setData}
         onValidate={setValid}
        labelWidth={160} displayType={'row'} propsSchema={schema} />
        <div style={{display:'flex',justifyContent:'flex-end'}}>
        <Button style={{marginRight:'12px'}} onClick={onClose}>取消</Button>
        <Popconfirm title={'确定要提交吗？'} cancelText={'取消'} okText={'确定'} onConfirm={handleSave}>
        <Button type='primary' >提交</Button>
        </Popconfirm>
        </div>
      </div>
    </Drawer>
  );
};
export default TGStrategyAlterConfig;
