import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ConfigData, HotSpot } from '../../type';
import { Card, DatePicker, Select, Input, Button, Popconfirm, message } from 'antd';
import InputTitle from '../InputTitle';
import { fundRecommendHelper, mapPropToName } from './helper';
import UploadImg from '../UploadImg';
import api from 'api';
import useDebounce from '@/hooks/useDebounce';
const { fetchFundNameByCode } = api;
console.log('mapPropToName,', mapPropToName);
export interface IProps {
  data: ConfigData['hotspots'];
  onChange: (data: ConfigData['hotspots']) => void;
  disabled: boolean;
}
const FundRecommend: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleAdd = () => {
    onChange([fundRecommendHelper.addArrayData(), ...data]);
  };
  const handleChange = (index, name, value) => {
    const newData = [...data];
    newData[index][name] = value;
    onChange(newData);
  };
  const handleDel = index => {
    const newData = [...data];
    newData.splice(index, 1);
    onChange(newData);
  };
  /**
   * 获取基金名称
   */
  const getFundName = (code: string, key: string, index?: number) => {
    handleChange(index, key, '');
    if (code === '') return;
    code = code.trim();
    fetchFundNameByCode({
      fundCode: code,
    })
      .then((data: any) => {
        if (data.code === '0000') {
          if (data.data) {
            let name = data.data.name;
            handleChange(index, key, name);
          } else {
            message.error('该基金不存在，请重新输入');
          }
        } else {
          message.error('调用基金详情接口失败，请稍后再试');
        }
      })
      .catch(() => {
        message.error('调用基金详情接口失败，请稍后再试');
      });
  };
  return (
    <div>
      <h3>基金推荐</h3>
      {data.map((item, index) => {
        return (
          <Card key={index} style={{ width: '400px', display: 'inline-block' }}>
            <Input
              value={item.recommendWords}
              onChange={e => handleChange(index, 'recommendWords', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['recommendWords']} />}
            />
            <Input
              value={item.fundCode}
              onBlur={e => {
                getFundName(e.target.value, 'fundName', index);
              }}
              onChange={e => {
                handleChange(index, 'fundCode', e.target.value);
              }}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['fundCode']} />}
            />
            <Input
              value={item.fundName}
              onChange={e => handleChange(index, 'fundName', e.target.value)}
              disabled={true}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['fundName']} />}
            />
            <div>
              <InputTitle title={mapPropToName['profitArea']} />
              <Select
                value={item.profitArea}
                onChange={v => handleChange(index, 'profitArea', v)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
              >
                <Option value="month">近1月</Option>
                <Option value="tmonth">近3月</Option>
                <Option value="hyear">近6月</Option>
                <Option value="year">近1年</Option>
                <Option value="tyear">近3年</Option>
                <Option value="fyear">近5年</Option>
                <Option value="nowyear">今年来</Option>
                <Option value="now">成立以来</Option>
              </Select>
            </div>
            <Input
              value={item.heavyPositionStock}
              onChange={e => handleChange(index, 'heavyPositionStock', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['heavyPositionStock']} />}
            />
            <Input
              value={item.buttonText}
              onChange={e => handleChange(index, 'buttonText', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['buttonText']} isRequired={false} />}
            />
            <Input
              value={item.link}
              onChange={e => handleChange(index, 'link', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['link']} isRequired={false} />}
            />
            <Input
              value={item.articleContent}
              onChange={e => handleChange(index, 'articleContent', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={
                <InputTitle title={mapPropToName['articleContent']} isRequired={false} />
              }
            />
            <Input
              value={item.articleLink}
              onChange={e => handleChange(index, 'articleLink', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={<InputTitle title={mapPropToName['articleLink']} isRequired={false} />}
            />

            <Input
              value={item.articleCreator}
              onChange={e => handleChange(index, 'articleCreator', e.target.value)}
              disabled={disabled}
              style={{ marginBottom: 10, width: 300 }}
              addonBefore={
                <InputTitle title={mapPropToName['articleCreator']} isRequired={false} />
              }
            />

            <UploadImg
              disabled={disabled}
              value={item.articleCreatorAvatar}
              onChange={v => handleChange(index, 'articleCreatorAvatar', v)}
              title={mapPropToName['articleCreatorAvatar']}
              isRequired
              size={['18*18']}
            ></UploadImg>
            <Popconfirm
              title={'请确认是否删除'}
              okText={'确认'}
              disabled={disabled}
              cancelText={'取消'}
              onConfirm={() => handleDel(index)}
            >
              <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                删除
              </Button>
            </Popconfirm>
          </Card>
        );
      })}

      <div>
        <Button disabled={disabled} onClick={handleAdd} style={{ marginTop: '4px' }} size="small">
          新增相关基金
        </Button>
      </div>
    </div>
  );
};
export default FundRecommend;
