/*
* 南方投顾
* <AUTHOR>
* @time 2020.03
*/

import React from 'react';
import { Row, Input, Button, Icon, Select, message, Collapse, Popconfirm } from 'antd';
import Answer from './answer';
import api from 'api';
import { autobind } from 'core-decorators';
import {get} from './../../../../functions/request'
const { fetchLazyCatProd, postLazyCatProd, fetchLazyCatQueKey, postLazyCatQueKey, pushLasyCatQueKey } = api;
const {Panel} = Collapse;
const {Option} = Select;
const list = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

@autobind 
class southQuestions extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            questions: [{
                key: "",//题目标签
                title: "",//题目
                answerList: [{
                    key: "",//答案标签
                    title: "",//答案文案
                    jumpChosen: 'question', //默认跳转
                    loginNextKey: "",//登录了跳转标签
                    noLoginNextKey: "",//未登录跳转标签
                    productList: [ //项目
                        {
                            vc_tacode: "",
                            vc_consulttacticsid: "",
                            company: "", //公司
                            idea: ""//投资理念
                        }
                    ]
                }]
            }],
            choiceList: [['A']],
            lazyCatQueKeys: [''],
        }
    }

    /**
     * 添加一个题目
     */
    addQuestion () {
        const { questions, choiceList } = this.state;
        questions.push(
            {
                key: "",//题目标签
                title: "",//题目
                answerList: [{
                    key: "A",//答案标签
                    title: "",//答案文案
                    jumpChosen: 'question', //默认跳转
                    loginNextKey: "",//登录了跳转标签
                    noLoginNextKey: "",//未登录跳转标签
                    productList: [ //项目
                        {
                            vc_tacode: "",
                            vc_consulttacticsid: "",
                            company: "", //公司
                            idea: ""//投资理念
                        }
                    ]
                }]
            }
        );
        
        choiceList.push(['A']);
        this.setState( {questions, choiceList} );
    }

    /**
     * 删除一个题目
     * @param {题目索引} questionIndex 
     */
    deleteQuestion (questionIndex) {
        const { questions, choiceList } = this.state;
        choiceList.splice(questionIndex, 1)
        questions.splice(questionIndex, 1);
        this.setState( {questions, choiceList} );
    }

    /**
     * 给对应题目添加一个答案
     * @param {题目索引} questionIndex 
     */
    addAnswer (questionIndex) {
        const { questions, choiceList } = this.state;
        let _length = questions[questionIndex].answerList.length;
        questions[questionIndex].answerList.push(
            {
                key: list[_length],//答案标签
                title: "",//答案文案
                jumpChosen: 'question', //默认跳转
                loginNextKey: "",//登录了跳转标签
                noLoginNextKey: "",//未登录跳转标签
                productList: [ //项目
                    {
                        vc_tacode: "",
                        vc_consulttacticsid: "",
                        company: "", //公司
                        idea: ""//投资理念
                    }
                ]
            }
        );
        choiceList[questionIndex] = list.slice(0, questions[questionIndex].answerList.length);
        this.setState( {questions, choiceList} );
    }

    /**
     * 删除对应题目的对应答案
     * @param {题目索引} questionIndex 
     * @param {回答索引} answerIndex 
     */
    deleteAnswer (questionIndex, answerIndex) {
        console.log(questionIndex, answerIndex)
        const { questions, choiceList } = this.state;
        questions[questionIndex].answerList.splice(answerIndex, 1);
        for (let i = 0; i < questions[questionIndex].answerList.length; i++) {
            questions[questionIndex].answerList[i].key = list[i];
        }
        choiceList[questionIndex] = list.slice(0, questions[questionIndex].answerList.length);
        this.setState( {questions, choiceList} );
    }

    /**
     * 改变key值
     */
    handleKey (value, questionIndex) {
        const {questions} = this.state;
        questions[questionIndex].key = value;
        this.setState({questions})
    }

    /**
     * 改变title值
     */
    handleTitle (e, questionIndex) {
        const {value} = e.target;
        const {questions} = this.state;
        questions[questionIndex].title = value;
        this.setState({questions}, console.log(this.state))
    }

    /**
     * 改变对应题目答案标签
     * @param {} questionIndex 
     */
    handleAnswerKey (value, questionIndex, answerIndex) {
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].key = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目答案文案
     */
    handleAnswerTitle (e, questionIndex, answerIndex) {
        const {value} = e.target;
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].title = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目答案跳转目标
     */
    handleAnswerJumpChosen (value, questionIndex, answerIndex) {
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].jumpChosen = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目答案登录了跳转
     */
    handleAnswerLoginNextKey (value, questionIndex, answerIndex) {
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].loginNextKey = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目答案未登录了跳转
     */
    handleAnswerNoLoginNextKey (value, questionIndex, answerIndex) {
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].noLoginNextKey = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目答案项目TaCode
     */
    handleAnswerTacode (e, questionIndex, answerIndex) {
        const {value} = e.target;
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].productList[0].vc_tacode = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目答案项目投顾id
     */
    handleAnswerConsulttacticsid (e, questionIndex, answerIndex) {
        const {value} = e.target;
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].productList[0].vc_consulttacticsid = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目公司
     */
    handleAnswerCompany (e, questionIndex, answerIndex) {
        const {value} = e.target;
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].productList[0].company = value;
        this.setState({questions})
    }

    /**
     * 改变对应题目投资理念
     */
    handleAnswerIdea (e, questionIndex, answerIndex) {
        const {value} = e.target;
        const {questions} = this.state;
        questions[questionIndex].answerList[answerIndex].productList[0].idea = value;
        this.setState({questions})
    }

    /**
     * 添加题目key
     */
    addLazyCatQueKey () {
        let {lazyCatQueKeys} = this.state;
        lazyCatQueKeys.push('');
        this.setState({lazyCatQueKeys});
    }

    /**
     * 删除key
     */
    deleteLazyCatQueKey (index) {
        const { lazyCatQueKeys } = this.state;
        lazyCatQueKeys.splice(index, 1);
        this.setState( {lazyCatQueKeys} );
    }

    /**
     * 修改key
     */
    handleLazyCatQueKey (e, index) {
        let data = e.target.value;
        const { lazyCatQueKeys } = this.state;
        lazyCatQueKeys[index] = data;
        this.setState( {lazyCatQueKeys} );
    }

    /**
     * 提交
     */
    upload () {
        let data = this.state.questions;
        for (let i = 0; i< data.length ;i++) {
            let _q = data[i];
            if (!_q.key) {
                message.error(`第${i+1}个题目标签没有填`);
                return;
            }
            if (!_q.title) {
                message.error(`第${i+1}个题目没有填`);
                return;
            }
            let _a = _q.answerList;
            for (let j = 0; j < _a.length; j++) {
                let answer = _a[j];
                if (!answer.key) {
                    message.error(`第${i+1}个题目第${j+1}个答案题目标签没有填`);
                    return;
                }
                if (!answer.title) {
                    message.error(`第${i+1}个题目第${j+1}个答案题目没有填`);
                    return;
                }
                if (answer.jumpChosen === 'question') {
                    if (!answer.loginNextKey) {
                        message.error(`第${i+1}个题目第${j+1}个答案登录跳转标签没有填`);
                        return;
                    }
                    if (!answer.noLoginNextKey) {
                        message.error(`第${i+1}个题目第${j+1}个答案未登录跳转标签没有填`);
                        return;
                    }
                    data[i].answerList[j].productList[0].vc_tacode = '';
                    data[i].answerList[j].productList[0].vc_consulttacticsid = '';
                    data[i].answerList[j].productList[0].company = '';
                    data[i].answerList[j].productList[0].idea = '';
                } else if (answer.jumpChosen === 'product') {
                    if (!answer.productList[0].vc_tacode) {
                        message.error(`第${i+1}个题目第${j+1}个答案TaCode没有填`);
                        return;
                    }
                    if (!answer.productList[0].vc_consulttacticsid) {
                        message.error(`第${i+1}个题目第${j+1}个答案投顾id没有填`);
                        return;
                    }
                    if (!answer.productList[0].company) {
                        message.error(`第${i+1}个题目第${j+1}个答案公司没有填`);
                        return;
                    }
                    if (!answer.productList[0].idea) {
                        message.error(`第${i+1}个题目第${j+1}个答案投资理念没有填`);
                        return;
                    }
                    data[i].answerList[j].loginNextKey = '';
                    data[i].answerList[j].noLoginNextKey = '';
                }
            }
        }
        postLazyCatProd (
            {value: JSON.stringify(data)}
        ).then ( data => {
            if (data) {
                this.getJsonp();
                window.jsonp = function (data) {
                    console.log(data);
                }
                message.success('保存成功');
            } else {
                message.error("修改失败");
            }
        })
    }

    /**
     * 跨域jsonp
     */
    getJsonp() {
        console.log("getJsonp");
        let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://testfund.10jqka.com.cn/interface/Rabbitmq/newyypushlish?key=updateLazyCat&time=${new Date().getTime()}&return=jsonp&jsonp=jsonp`;
        document.body.appendChild(_script);
    }

    /**
     * 初始化
     */
    init () {
        fetchLazyCatProd ().then ( data => {
            if (data && data.data) {
                data = JSON.parse(data.data);
                let _choiceList = [];
                for(let i = 0; i< data.length ; i++) {
                    _choiceList.push(list.slice(0, data[i].answerList.length));
                }
                this.setState({
                    questions: data,
                    choiceList: _choiceList
                }, () => {console.log(this.state)});
            }
        })
    }

    /**
     * 获取key值表
     */
    fetchKeys () {
        fetchLazyCatQueKey ().then ( data => {
            if (data && data.data){
                data = JSON.parse(data.data)
                this.setState({lazyCatQueKeys: data})
            }
        })
    }

    /**
     * 提交key表
     */
    uploadKeys () {
        let data = this.state.lazyCatQueKeys;
        for (let i = 0; i < data.length; i++ ) {
            data[i] = data[i].trim();
            if (!data[i]) {
                message.error(`第${i+1}个key为空`)
                return;
            }
        }
        for (let i = 0; i < data.length; i++ ) {
            if (data.indexOf(data[i]) !== data.lastIndexOf(data[i])) {
                message.error(`key有重复`)
                return;
            }
        }
        postLazyCatQueKey (
            {value: JSON.stringify(data)}
        ).then ( data => {
            if (data) {
                message.success('修改成功');
                // window.location.reload();
            }else {
                message.error("失败");
            }
        })
    }

    componentDidMount () {
        this.fetchKeys();
        this.init();
    }

    render () {
        return (
            <article style={{width: 1100}}>
                <section style={{marginBottom: '100px'}}>
                    <section>
                        题目key值
                    </section>
                    {
                        this.state.lazyCatQueKeys.map( (key, index) => {
                            return (
                                <section style={{marginTop: '20px'}} key={index}>
                                    <Input 
                                    style={{width: '300px'}} 
                                    addonBefore="key值:" 
                                    onChange={(e) => {this.handleLazyCatQueKey(e, index)}}
                                    value={key}></Input>

                                    <Popconfirm
                                    placement="rightBottom"
                                    title={`你确定要删除${key}么`}
                                    onConfirm={() => {this.deleteLazyCatQueKey(index)}}
                                    okText="确认"
                                    cancelText="取消"
                                    >
                                        <Button 
                                        type="danger"
                                        style={{marginLeft:'100px'}}
                                        >删除</Button>
                                    </Popconfirm>
                                </section>
                            )
                        })
                    }
                    <section style={{marginTop: '30px'}}>
                        <Button 
                        type="primary"
                        onClick={this.addLazyCatQueKey}
                        >增加key值</Button>

                        <Popconfirm
                        placement="rightBottom"
                        title={`你确定要提交key值么`}
                        onConfirm={() => {this.uploadKeys()}}
                        okText="确认"
                        cancelText="取消"
                        >
                            <Button 
                            type="danger"
                            style={{marginLeft:'120px'}}
                            >提交</Button>
                        </Popconfirm>
                    </section>
                </section>

                <Collapse style={{width: 1100, marginBottom: '20px'}}>
                    {
                        this.state.questions.map( (item, index) => {
                            return (
                                <Panel
                                 header={'题目'+(index+1) + ': ' + item.key + '    ' + item.title} key={index} extra={
                                    <Popconfirm
                                    placement="rightBottom"
                                    title={`你确定要删除题目${item.title}么`}
                                    onConfirm={() => {this.deleteQuestion(index)}}
                                    okText="确认"
                                    cancelText="取消"
                                    >
                                        <Button type="danger" >删除题目</Button>
                                    </Popconfirm>
                                }>
                                    <Row style={{ marginBottom: '20px'}}>
                                        <Input 
                                        style={{width: '500px', marginRight: '100px'}} 
                                        addonBefore="题目:" 
                                        onChange={(e) => {this.handleTitle(e, index)}}
                                        value={item.title}></Input>
                                        <span>题目标签key：</span>
                                        <Select
                                            style={{width: '200px'}}
                                            onChange={(value) => {this.handleKey(value, index)}}
                                            value={item.key}
                                        >
                                            {
                                                this.state.lazyCatQueKeys.map((key, index) => {
                                                    return (
                                                        <Option value={key} key={index}>{key}</Option>
                                                    )
                                                })
                                            }
                                        </Select>
                                    </Row>
                                    <Answer 
                                    lazyCatQueKeys={this.state.lazyCatQueKeys}
                                    choiceList={this.state.choiceList[index]}
                                    answerList={item.answerList}
                                    questionIndex={index} //题目索引
                                    addAnswer={this.addAnswer}
                                    deleteAnswer={this.deleteAnswer}
                                    handleAnswerKey={this.handleAnswerKey}
                                    handleAnswerTitle={this.handleAnswerTitle}
                                    handleAnswerJumpChosen={this.handleAnswerJumpChosen}
                                    handleAnswerLoginNextKey={this.handleAnswerLoginNextKey}
                                    handleAnswerNoLoginNextKey={this.handleAnswerNoLoginNextKey}
                                    handleAnswerTacode={this.handleAnswerTacode}
                                    handleAnswerConsulttacticsid={this.handleAnswerConsulttacticsid}
                                    handleAnswerCompany={this.handleAnswerCompany}
                                    handleAnswerIdea={this.handleAnswerIdea}
                                    ></Answer>
                                </Panel>
                            )
                        })
                    }
                </Collapse>
                <Button type="primary" className="g-center" onClick={this.addQuestion}>添加一个题目</Button>
                <Popconfirm
                placement="rightBottom"
                title={`你确定要提交么`}
                onConfirm={() => {this.upload()}}
                okText="确认"
                cancelText="取消"
                >
                    <Button 
                    type="danger"
                    style={{marginLeft:'500px'}}
                     >提交题目</Button>
                </Popconfirm>
            </article>
        )
    }
}

export default southQuestions;
