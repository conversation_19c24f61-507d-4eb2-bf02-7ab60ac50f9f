/**
 * @see 工具权限管理-权益管理
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023-08-08
 */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Input,
  Button,
  Table,
  message,
  Select,
  Spin,
  PageHeader,
  Popconfirm,
  Modal,
  Form,
} from 'antd';
import api from 'api';
import styles from './index.less';
import classnames from 'classnames';

import { TOOLTITLE } from '../const';
const { searchEquityList, saveEquty, deleteEquty } = api;
const { Search } = Input;
const FormItem = Form.Item;
const { Option } = Select;

export default function(props) {
  const [init, setInit] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [spinToggle, setSpinToggle] = useState(false);
  // 当前页码
  const [oneLevelKeysArr, setOneLevelKeysArr] = useState([]); //当前页面中一级权益的所有key数组（用来校验编辑/新增的唯一性）
  // 新增modal
  const [visible, setVisible] = useState(false);
  const [isAdd, setIsAdd] = useState(true);
  const [secentArr, setSecentArr] = useState([]);
  const [oneLevel, setOneLevel] = useState({});
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 400,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 200,
    },
    {
      title: '权益id',
      dataIndex: 'key',
      key: 'key',
      width: 400,
    },
    {
      title: '操作',
      key: 'operator',
      render: (row, record, index) => (
        <div>
          {!row.id ? (
            <div>
              <Button
                type="primary"
                onClick={() => {
                  handleEditTenBillion(row);
                }}
              >
                编辑
              </Button>
              <Popconfirm
                title="是否删除该权益以及下面的所有二级权益"
                okText="删除"
                cancelText="取消"
                onConfirm={() => {
                  deleteRole(row);
                }}
              >
                <Button style={{ marginLeft: '20px' }} type="danger">
                  删除
                </Button>
              </Popconfirm>
            </div>
          ) : null}
        </div>
      ),
    },
  ];

  /**
   * @see获取数据
   * @param {String} suffix
   */
  const fetchData = () => {
    setSpinToggle(true);
    searchEquityList({
      offSet: 1,
      pageSize: 1000,
    }).then(data => {
      if (data.statusCode === 200) {
        const { configMap } = data.data;
        const newData = [];
        const myOneLevelKeysArr = [];
        for (const key in configMap) {
          if (Object.hasOwnProperty.call(configMap, key)) {
            const keyElement = configMap[key];
            myOneLevelKeysArr.push(keyElement[0].rightsTypeId);
            const obj = {
              type: '一级权益',
              key: keyElement[0].rightsTypeId,
              name: keyElement[0].rightsType,
              id: '',
            };
            if (keyElement && keyElement.length > 0) {
              obj.children = [];
              keyElement.map(item => {
                const { rightsId, rightsName, id } = item;
                const childrenObj = {
                  key: rightsId,
                  id: id,
                  name: rightsName,
                  type: '二级权益',
                };
                obj.children.push(childrenObj);
              });
            }
            newData.push(obj);
          }
        }
        setOneLevelKeysArr(myOneLevelKeysArr); //一级权益所有的ey
        setDataSource(newData);
        setSpinToggle(false);
      }
    });
  };

  /**
   * @see 新增
   */
  const add = () => {
    setVisible(true);
    setIsAdd(true);
    setOneLevel({
      rightsType: '',
      rightsTypeId: '',
    });
    const arr = [{ rightsId: '', rightsName: '' }];
    setSecentArr(arr);
  };

  const onCancel = () => {
    setVisible(false);
    setOneLevel({});
  };

  const oneLevelChange = (e, key) => {
    const obj = JSON.parse(JSON.stringify(oneLevel));
    obj[key] = e.target.value;
    setOneLevel(obj);
  };

  /**
   * @see 保存
   */
  const onFinish = () => {
    // 去掉一级权益和二级权益表单项中首尾的空格
    const _oneLevel = {
      rightsType: oneLevel.rightsType?.trim(),
      rightsTypeId: oneLevel.rightsTypeId?.trim(),
    };
    const _secentArr = secentArr?.map(item => {
      return {
        ...item,
        rightsId: item.rightsId?.trim(),
        rightsName: item.rightsName?.trim(),
      }
    })
    //校验一级权益
    if (!_oneLevel.rightsType || !_oneLevel.rightsTypeId) {
      message.error('请完善一级权益信息');
      return;
    }
    if (_secentArr && _secentArr.length === 0) {
      message.error('请增加二级权益信息');
      return;
    }
    //校验一级权益的key
    if (isAdd && oneLevelKeysArr.includes(_oneLevel.rightsTypeId)) {
      message.error('一级权益id已存在，请重新填写');
      return;
    }
    // 校验二级权益
    let errorNum = 0;
    const secondKesArr = [];
    _secentArr &&
    _secentArr.forEach(item => {
        secondKesArr.push(item.rightsId);
        if (!item.rightsId || !item.rightsName) {
          errorNum++;
        }
      });
    if (errorNum > 0) {
      message.error('请完善二级权益信息');
      return;
    }
    //校验二级权益key是否唯一
    const oldLength = JSON.parse(JSON.stringify(secondKesArr)).length;
    const newArrLength = [...new Set(secondKesArr)].length;
    if (oldLength !== newArrLength) {
      message.error('请填写唯一的二级权益id');
      return;
    }
    let seArr = JSON.parse(JSON.stringify(_secentArr));
    const param = seArr.map(item => {
      item.rightsType = _oneLevel.rightsType;
      item.rightsTypeId = _oneLevel.rightsTypeId;
      return item;
    });
    saveEquty(param).then(res => {
      if (res.statusCode === 200) {
        message.success('保存成功');
        fetchData();
      } else {
        message.success('服务器异常');
      }
    });
    setVisible(false);
  };

  /**
   * @see 删除角色
   * @param {*} row
   */
  const deleteRole = row => {
    const ids = row.children.map(item => item.id);
    deleteEquty({ idList: ids }).then(res => {
      if (res?.status_code === 0) {
        message.success('删除成功');
        fetchData();
      } else {
        message.success('服务器异常');
      }
    });
  };

  /**
   * @see 编辑按钮
   */
  const handleEditTenBillion = row => {
    const { children, name, key } = row;
    setOneLevel({
      rightsType: name,
      rightsTypeId: key,
    });
    if (children?.length) {
      const two = children.map(item => {
        return {
          rightsId: item.key,
          rightsName: item.name,
          id: item.id,
        };
      });
      setSecentArr(two);
    }
    setIsAdd(false);
    setVisible(true);
  };

  /**
   * @see 增加二级权益
   */
  const addSecentButton = () => {
    const arr = JSON.parse(JSON.stringify(secentArr));
    arr.push({ rightsId: '', rightsName: '' });
    setSecentArr(arr);
  };

  /**
   * @see modal二级权益input事件
   * @param {*} e
   * @param {*} index
   * @param {*} key
   */
  const secondChange = (e, index, key) => {
    const value = e.target.value;
    const arr = JSON.parse(JSON.stringify(secentArr));
    arr[index][key] = value;
    setSecentArr(arr);
  };

  /**
   * @see 删除二级权益item
   * @param {*} index
   */
  const deleteSecondItem = index => {
    const arr = JSON.parse(JSON.stringify(secentArr));
    if (arr?.length === 1) {
      message.info('删除失败，应至少保存一条二级权益');
      return;
    }
    arr.splice(index, 1);
    setSecentArr(arr);
  };
  useEffect(() => {
    fetchData();
    setInit(true);
  }, []);

  if (!init) return '加载中';
  return (
    <article className="salaryManagerBackgroundColor authEquityManage" style={{ width: '1500px' }}>
      <section>
        <PageHeader
          title={TOOLTITLE.EQUITY}
          extra={
            <Button type="primary" onClick={add}>
              添加
            </Button>
          }
        ></PageHeader>
      </section>
      <center>
        <div style={{ padding: 10 }}>
          <Spin spinning={spinToggle} tip="加载中...">
            <Table style={{ marginTop: 30 }} columns={columns} dataSource={dataSource} />
          </Spin>
        </div>
      </center>
      <Modal
        title={'新增'}
        okText="保存"
        cancelText="取消"
        visible={visible}
        width={1000}
        className="authEquityModal"
        onOk={onFinish}
        onCancel={onCancel}
      >
        <div className={classnames(styles['modal-body-auth-e'])}>
          <div className={classnames(styles['top-content'])}>
            <span className={classnames(styles['red-xi'])}>一级权益名称：</span>
            <Input
              className={classnames(styles['m-b-top-input'])}
              value={oneLevel.rightsType}
              onChange={e => {
                oneLevelChange(e, 'rightsType');
              }}
            />
          </div>
          <div className={classnames(styles['top-content'])}>
            <span className={classnames(styles['red-xi'])}>一级权益id：</span>
            <Input
              className={classnames(styles['m-b-top-input'])}
              value={oneLevel.rightsTypeId}
              onChange={e => {
                oneLevelChange(e, 'rightsTypeId');
              }}
            />
          </div>
        </div>
        <div className={classnames(styles['m-b-content-content'])}>
          <div className={secentArr.length > 0 ? classnames(styles['m-b-content-mid']) : ''}>
            {secentArr.length > 0 &&
              secentArr.map((item, index) => {
                return (
                  <div key={'item' + index} className={classnames(styles['modal-body-auth-e'])}>
                    <div className={classnames(styles['top-content'])}>
                      <span className={classnames(styles['red-xi'])}>二级权益名称：</span>
                      <Input
                        className={classnames(styles['m-b-top-input'])}
                        onChange={e => {
                          secondChange(e, index, 'rightsName');
                        }}
                        value={item.rightsName}
                      />
                    </div>
                    <div className={classnames(styles['top-content'])}>
                      <span className={classnames(styles['red-xi'])}>二级权益id：</span>
                      <Input
                        className={classnames(styles['m-b-top-input'])}
                        onChange={e => {
                          secondChange(e, index, 'rightsId');
                        }}
                        value={item.rightsId}
                      />
                    </div>
                    <Popconfirm
                      title="是否删除该二级权益？"
                      okText="删除"
                      cancelText="取消"
                      onConfirm={() => {
                        deleteSecondItem(index);
                      }}
                    >
                      <Button type="danger">删除</Button>
                    </Popconfirm>
                  </div>
                );
              })}
          </div>

          <Button
            type="primary"
            onClick={() => {
              addSecentButton();
            }}
          >
            新增二级权益
          </Button>
        </div>
      </Modal>
    </article>
  );
}
