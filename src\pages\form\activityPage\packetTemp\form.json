﻿{
  "type": "object",
  "properties": {
    "activityId": {
      "type": "string",
      "title": "页面ID",
      "description": "使用JIRA任务号 端外微信活动需要以WX_开头",
      "ui:width": "30%"
    },
    "title": {
      "type": "string",
      "title": "主标题",
      "maxLength": 9,
      "ui:width": "30%"
    },
    "subTitle": {
      "type": "string",
      "title": "副标题",
      "maxLength": 15,
      "ui:width": "30%"
    },
    "startDate": {
      "title": "活动开始日期（必填）",
      "type": "string",
      "format": "dateTime",
      "ui:width": "30%"
    },
    "endDate": {
      "title": "活动结束日期（必填）",
      "type": "string",
      "format": "dateTime",
      "ui:width": "30%"
    },
    "learnContent": {
      "type": "string",
      "title": "理财知识学习内容",
      "ui:width": "30%"
    },
    "activityState": {
      "title": "活动状态 （必填）",
      "type": "string",
      "description": "0-正常 1-结束 3-提前结束（活动总资金达到上限） 注意activityState在0、3的状态下是会正常发放已经使用的用户收益的；当为1时，加息终止，此状态是为特殊情况预留。",
      "ui:width": "100%"
    },
    "defaultPacketId": {
      "type": "string",
      "title": "默认红包ID",
      "ui:width": "50%"
    },
    "defaultPacketDuration": {
      "type": "number",
      "title": "有效期(小时)",
      "description": "红包领取之后有效期，不填则取默认配置时间",
      "ui:width": "50%"
    },
    "shareTitle": {
      "type": "string",
      "title": "分享标题",
      "ui:width": "30%"
    },
    "shareDes": {
      "type": "string",
      "title": "分享描述",
      "ui:width": "30%"
    },
    "otherCouponUrl": {
      "type": "string",
      "title": "转换奖励跳转链接",
      "ui:width": "30%"
    },
    "otherCouponType": {
      "type": "string",
      "title": "转换奖励id",
      "description": "1-保险",
      "ui:width": "30%"
    },
    "otherCouponId": {
      "type": "string",
      "title": "转换奖励类型",
      "description": "1-保险",
      "ui:width": "30%"
    },
    "templateSupport": {
      "title": "是否支持模板活动",
      "type": "boolean",
      "ui:width": "100%",
      "ui:widget": "switch",
      "default": true
    },
    "packets": {
      "type": "array",
      "title": "红包列表",
      "description": "用户标签对应红包列表",
      "items": {
        "type": "object",
        "properties": {
          "tag": {
            "type": "string",
            "title": "用户标签",
            "ui:width": "25%"
          },
          "packetId": {
            "type": "string",
            "title": "红包ID",
            "ui:width": "25%"
          },
          "duration": {
            "type": "number",
            "title": "有效期(小时)",
            "description": "红包领取之后有效期，不填则取默认配置时间",
            "ui:width": "50%"
          }
        },
        "required": [
          "tag",
          "packetId"
        ]
      }
    },
    "funds": {
      "type": "array",
      "title": "基金列表",
      "maxItems": 3,
      "items": {
        "type": "object",
        "properties": {
          "tags": {
            "type": "array",
            "title": "基金标签",
            "items": {
              "type": "string"
            }
          },
          "fundCode": {
            "type": "string",
            "title": "基金代码",
            "ui:width": "30%"
          },
          "fundName": {
            "type": "string",
            "title": "基金名称",
            "ui:width": "30%"
          },
          "profitReplaceValue": {
            "type": "string",
            "title": "代替收益显示",
            "ui:width": "30%"
          },
          "profits": {
            "type": "string",
            "title": "收益区间",
            "enum": [
              "rate",
              "week",
              "month",
              "tmonth",
              "hyear",
              "year",
              "tyear",
              "now"
            ],
            "enumNames": [
              "昨日涨幅",
              "近一周涨幅",
              "近一月涨幅",
              "近三月涨幅",
              "近六月涨幅",
              "近一年涨幅",
              "近三年涨幅",
              "成立以来"
            ],
            "ui:width": "30%"
          },
          "des": {
            "type": "string",
            "title": "基金描述",
            "ui:width": "50%"
          }
        },
        "required": [
          "fundCode",
          "fundName",
          "des",
          "profits"
        ]
      }
    },
    "imageUrl": {
      "type": "string",
      "description": "上传图片链接",
      "title": "产品宣传切图",
      "ui:width": "50%"
    }
  },
  "required": [
    "defaultPacketId",
    "activityId",
    "startDate",
    "endDate",
    "title",
    "subTitle",
    "activityState",
    "learnContent"
  ]
}
