import { message } from 'antd';
import { checkUrl, generateId, getRandomId, setRandomId } from '../../script/utils';
export const modelName = 'title';
export function checkForm(formData, index) {
  try {
    if (!formData.title) {
      throw new Error(`请输入标题x`);
    }
    if (
      !formData.mainCondition ||
      !formData.mainCondition.normal ||
      !formData.mainCondition.parsed
    ) {
      throw new Error(`请输入核心条件设置`);
    }
    if (!formData.condition || !formData.condition.normal || !formData.condition.parsed) {
      throw new Error('请输入条件设置');
    }
    if (!formData.vipInfo) {
      throw new Error('请输入牛人信息');
    }
    if (!formData.vipInfo.avatar) {
      throw new Error('请输入牛人信息-头像');
    }
    if (!formData.vipInfo.name) {
      throw new Error('请输入牛人信息-名称');
    }
    if (formData.vipInfo.name.length > 10) {
      throw new Error('请输入牛人信息-名称不超过10个字符');
    }
    if (!formData.vipInfo.background) {
      throw new Error('请输入牛人信息-背景');
    }
    if (formData.vipInfo.name.background > 25) {
      throw new Error('请输入牛人信息-背景不超过25个字符');
    }
    if (!formData.vipInfo.mind) {
      throw new Error('请输入牛人信息-理念');
    }
    if (formData.vipInfo.mind.length > 50) {
      throw new Error('请输入牛人信息-理念不超过50个字符');
    }
    if (!formData.vipInfo.jumpUrl) {
      throw new Error('请输入牛人信息-跳转链接');
    }
    const urlResult = checkUrl(formData.vipInfo.jumpUrl);
    if (urlResult.isError) {
      throw new Error(urlResult.msg);
    }
  } catch (e) {
    message.error(`${e.message} (第${index + 1}项)`);
    return false;
  }

  return true;
}

export const fetchData = (item: any) => {
  setRandomId(item.id);
  return {
    id: item.id,
    title: item.title,
    mainCondition: {
      normal: item.mainConditionStr,
      parsed: item.mainConditionParsed,
    },
    condition: {
      normal: item.conditionStr,
      parsed: item.conditionParsed,
    },
    vipInfo: item.vipInfo,
  };
};
export const parseData = item => {
  return {
    id: item.id || generateId('vip-'),
    title: item.title,
    mainConditionStr: item.mainCondition.normal,
    mainConditionParsed: item.mainCondition.parsed,
    conditionStr: item.condition.normal,
    conditionParsed: item.condition.parsed,
    vipInfo: item.vipInfo,
  };
};
export const addData = () => {
  return {
    title: '',
    mainCondition: {
      normal: '',
      parsed: [],
    },
    condition: {
      normal: '',
      parsed: [],
    },
    vipInfo: {
      avatar: '',
      name: '',
      background: '',
      mind: '',
      jumpUrl: '',
    },
  };
};
