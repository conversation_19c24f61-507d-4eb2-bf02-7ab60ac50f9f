import React, { Component } from 'react'
import {Table} from 'antd'

interface TableProps {
    columns?: any[];
    dataSource?: any[];
    children?: any;
    scrollX?: number;
    [props: string]: any;
}


const TableNew: React.FC = (props: TableProps) => {
    const  {
        columns,
        dataSource,
        scrollX = 1000,
    } = props;
    const _getTableRowClass = (recoed: any, index: number) => index % 2 === 0 ? 'u-tr_odd' : '';
    
    return (
        <Table
            rowClassName={_getTableRowClass}
            scroll={{
                x: scrollX
            }}
            columns={columns} 
            dataSource={dataSource} 
            pagination={{
                pageSize: 10,
                pageSizeOptions: ['10', '30', '50', '100'],
                showSizeChanger: true,
                showQuickJumper: true,
            }}
        />
    )
};

export default TableNew;
