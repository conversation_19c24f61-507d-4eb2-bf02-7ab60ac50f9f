export const mockFilterData = {
  code: '0000',
  message: '请求成功',
  singleData: {
    strategyInfoList: [
      {
        strategyCode: '1',
        strategyName: 'toB',
      },
      {
        strategyCode: '2',
        strategyName: '销售',
      },
      {
        strategyCode: '3',
        strategyName: '销售',
      },
      {
        strategyCode: '4',
        strategyName: '合作机构',
      },
    ],
    saleInfoList: [
      {
        saleName: '张三',
        saleId: 1,
      },
    ],
    investorInfoList: [],
    accountInfoList: [
      {
        custId: '************',
        accountName: '工商银行',
      },
      {
        custId: '************',
        accountName: '托管账号1',
      },
      {
        custId: '************',
        accountName: '*************',
      },
      {
        custId: '************',
        accountName: '工商银行',
      },
      {
        custId: '************',
        accountName: '前端测试',
      },
      {
        custId: '************',
        accountName: '工商银行',
      },
      {
        custId: '************',
        accountName: '测试上银基金',
      },
      {
        custId: '************',
        accountName: '万家基金',
      },
      {
        custId: '************',
        accountName: '测试上银2',
      },
      {
        custId: '************',
        accountName: '工商银行',
      },
      {
        custId: '************',
        accountName: '工商银行',
      },
      {
        custId: '123456',
        accountName: 'test',
      },
      {
        custId: '************',
        accountName: '北京开金远景信息科技有限公司',
      },
      {
        custId: '2222',
        accountName: '2222',
      },
      {
        custId: '3333',
        accountName: '3333',
      },
      {
        custId: '************',
        accountName: '测试产品PA',
      },
      {
        custId: '************',
        accountName: '测试产品PA1',
      },
      {
        custId: '************',
        accountName: '测试产品CJHX',
      },
      {
        custId: '************',
        accountName: '测试产品WJ',
      },
      {
        custId: '************',
        accountName: '测试产品DJZG',
      },
      {
        custId: '************',
        accountName: '11111',
      },
      {
        custId: '************',
        accountName: '**********',
      },
      {
        custId: '************',
        accountName: '演示测试公司',
      },
      {
        custId: '************',
        accountName: '测试产品GHZG',
      },
      {
        custId: '1234',
        accountName: '1',
      },
      {
        custId: '*********',
        accountName: '1',
      },
      {
        custId: '**********',
        accountName: '托管户名称托管户名称托管户名称托管户名称',
      },
      {
        custId: '***********',
        accountName: '张三',
      },
      {
        custId: '789',
        accountName: '12fdsdfsf3424',
      },
      {
        custId: '987',
        accountName: 'fdgdfh',
      },
      {
        custId: '*********',
        accountName: '托管户名称1托管户名称1托管户名称1',
      },
      {
        custId: '456',
        accountName: '托管户名称托管户名称',
      },
      {
        custId: '6',
        accountName: '车程妮账号的托管户',
      },
      {
        custId: '0',
        accountName: '测试产品HXCF',
      },
      {
        custId: '************',
        accountName: '测试产品HXCF',
      },
      {
        custId: '************',
        accountName: '测试产品JSCC',
      },
      {
        custId: '************',
        accountName: '平安基金喜乐2号集合资产管理计划',
      },
      {
        custId: '************',
        accountName: '测试产品DJZG4',
      },
      {
        custId: '************',
        accountName: '测试数据银行账户名',
      },
      {
        custId: '************',
        accountName: '测试产品DJZG5',
      },
      {
        custId: '********',
        accountName: '农业银行',
      },
      {
        custId: '************',
        accountName: '平安基金喜乐3号集合资产管理计划',
      },
      {
        custId: '************',
        accountName: '同花顺演示测试产品002号集合资产管理计划',
      },
      {
        custId: '************',
        accountName: '同花顺演示测试产品003号集合资产管理计划',
      },
      {
        custId: '************',
        accountName: '深圳市尚佐慈善基金会',
      },
      {
        custId: '************',
        accountName: '中国工商银行广州天河工业园支行',
      },
      {
        custId: '************',
        accountName: '创金合信景气行业3个月持有期股票型发起式基金中基金（FOF）',
      },
      {
        custId: '************',
        accountName: '国泰君安申万利稳双周盈1号集合资产管理计划',
      },
      {
        custId: '************',
        accountName: '华夏基金&同花顺',
      },
      {
        custId: '************',
        accountName: '同花顺-华夏基金',
      },
      {
        custId: '************',
        accountName: '同花顺1',
      },
      {
        custId: '************',
        accountName: '同花顺2',
      },
      {
        custId: '************',
        accountName: '上银恒享平衡养老目标三年持有期混合型发起式基金中基金（FOF）',
      },
      {
        custId: '************',
        accountName: '国泰基金管理有限公司',
      },
      {
        custId: '************',
        accountName: '蓝筹精选5号',
      },
      {
        custId: '************',
        accountName: '大家人寿传统',
      },
      {
        custId: '************',
        accountName: '国泰君安申万利稳双周盈1号集合资产管理计划',
      },
      {
        custId: '**********',
        accountName: '同花顺测试银行',
      },
      {
        custId: '************',
        accountName: '中国基金管理有限公司',
      },
      {
        custId: '************',
        accountName: '下银恒享平衡养老目标三年持有期混合型发起式基金中基金（FOF）',
      },
      {
        custId: '************',
        accountName: '中国基金管理有限公司2号',
      },
      {
        custId: '************',
        accountName: '中国黄金管理有限公司2号',
      },
      {
        custId: '************',
        accountName: '大家人寿传统建行户',
      },
      {
        custId: '************',
        accountName: '测试产品PYAS集合资产管理计划',
      },
      {
        custId: '************',
        accountName: '测试测试',
      },
      {
        custId: '************',
        accountName: '同花顺3',
      },
      {
        custId: '************',
        accountName: '浙江杭州市余杭鼎顺问财科技股份技术有限公司',
      },
      {
        custId: '************',
        accountName: '浙江东方财富白手起家发家致富科技网络信息',
      },
      {
        custId: '************',
        accountName: '小财神小财神小财神小财神小财神小财神小财神',
      },
      {
        custId: '************',
        accountName: '同花顺同花顺同花顺同花顺同花顺同花顺同花顺',
      },
      {
        custId: '************',
        accountName: '爱基金爱基金爱基金爱基金爱基金爱基金爱基金爱基金爱基金',
      },
      {
        custId: '************',
        accountName: '账户名称账户名称账户名称账户名称账户名称账户名称账户名称账户',
      },
      {
        custId: '************',
        accountName: '名称名称名称名称名称名称名称名称名称名称名称名称名称名称账户',
      },
      {
        custId: '************',
        accountName: '浙江同花顺爱基金金融股份有限公司',
      },
      {
        custId: '************',
        accountName: '浙江核心同花顺有限公司',
      },
      {
        custId: '************',
        accountName: '杭州11发杭州广发杭州广发杭州广发星星银行',
      },
      {
        custId: '123',
        accountName: '中国台湾手抓饼有限公司',
      },
      {
        custId: '************',
        accountName: '中国台湾爱玛电动车股份有限公司',
      },
      {
        custId: '************',
        accountName: '中国台湾优乐美奶茶股份有限公司',
      },
      {
        custId: '************',
        accountName: '中国台湾香飘飘奶茶股份有限公司',
      },
      {
        custId: '************',
        accountName: '中国旺旺集团股份有限公司',
      },
      {
        custId: 'dsads',
        accountName: 'ewqew',
      },
      {
        custId: '************',
        accountName: '中国黄金管理有限公司5号',
      },
      {
        custId: '************',
        accountName: '中国黄金管理有限公司5号',
      },
      {
        custId: '************',
        accountName: '中国黄金管理有限公司6号',
      },
      {
        custId: '************',
        accountName: '中国菠萝手机信息股份有限公司',
      },
      {
        custId: '************',
        accountName: '中国苹果手机信息股份有限公司',
      },
      {
        custId: '************',
        accountName: '哇唧唧哇娱乐（天津）有限公司',
      },
      {
        custId: '************',
        accountName: '恒生平衡养老目标三年持有期混合型发起式基金中基金（FOF)',
      },
      {
        custId: '************',
        accountName: '鼎盛控股集团有限公司',
      },
      {
        custId: '************',
        accountName: '同花顺香港股份有限分公司',
      },
      {
        custId: '************',
        accountName: '杭州宏达网络技术开发有限公司',
      },
      {
        custId: '************',
        accountName: '浙江邻汇网络科技有限公司',
      },
      {
        custId: '************',
        accountName: '同花顺问财技术股份有限公司',
      },
      {
        custId: '************',
        accountName: '同花顺智富软件有限公司',
      },
      {
        custId: '************',
        accountName: '网易（杭州）网络有限公司',
      },
      {
        custId: '************',
        accountName: '天天基金网络信息有限公司',
      },
      {
        custId: '************',
        accountName: '浙江省杭州市余杭区鼎才基数有限公司',
      },
      {
        custId: '************',
        accountName: '北京生物技术有限公司',
      },
      {
        custId: '************',
        accountName: '同花顺（广东）国际有限公司',
      },
      {
        custId: '************',
        accountName: '杭州辰青和业科技有限公司',
      },
      {
        custId: '************',
        accountName: '杭州芯云半导体技术有限公司',
      },
      {
        custId: '************',
        accountName: '浙江康柏榆生物技术有限公司',
      },
      {
        custId: '************',
        accountName: 'bug验证测试',
      },
      {
        custId: '************',
        accountName: '杭州勤耀广告有限公司',
      },
      {
        custId: '************',
        accountName: '长城基金管理有限公司',
      },
      {
        custId: '************',
        accountName: '中融基金管理有限公司',
      },
      {
        custId: '************',
        accountName: '杭州易智达信科技有限公司',
      },
      {
        custId: '************',
        accountName: '超级转换测试',
      },
      {
        custId: '************',
        accountName: '易联通测试',
      },
      {
        custId: '************',
        accountName: '开户测试',
      },
      {
        custId: '************',
        accountName: '华硕电脑公司',
      },
      {
        custId: '************',
        accountName: '开户行名称',
      },
      {
        custId: '************',
        accountName: '杭州高达软件系统股份有限公司',
      },
      {
        custId: '************',
        accountName: '核心同花顺网络信息技术股份有限公司\t',
      },
      {
        custId: '************',
        accountName: '杭州同花顺云软件股份公司',
      },
      {
        custId: '************',
        accountName: '南方基金管理计划',
      },
      {
        custId: '************',
        accountName: '浙江省杭州市余杭区智富软件技术有限集团\t',
      },
      {
        custId: '************',
        accountName: '南方基金1号管理计划',
      },
      {
        custId: '************',
        accountName: '南方基金理财计划2号\t',
      },
      {
        custId: '************',
        accountName: '中国农业银行\t',
      },
      {
        custId: '************',
        accountName: '1111',
      },
      {
        custId: '************',
        accountName: '平安养老目标日期2035三年持有期混合型基金中基金（FOF）',
      },
      {
        custId: '************',
        accountName: '南方小集合',
      },
      {
        custId: '************',
        accountName: '1',
      },
      {
        custId: '************',
        accountName: '机构户自测银行账户',
      },
      {
        custId: '************',
        accountName: '招商基金1号集合资产管理计划',
      },
      {
        custId: '************',
        accountName: '中国邮政储蓄银行有限责任公司邮政储蓄银行有限蔡家坡凤凰路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国邮政储蓄银行有限责任公司邮政储蓄银行有限蔡家坡凤凰路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行 ',
      },
      {
        custId: '************',
        accountName: '国泰民安基金中基金',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京四季青支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京地坛支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京鹿港支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国农业银行北京天通东苑支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京四季青支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京地坛支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京鹿港支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京四季青支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京地坛支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京鹿港支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京鹿港支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京地坛支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京四季青支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京鹿港支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京地坛支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京四季青支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京复兴门支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京鹿港支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京地坛支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京四季青支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京十八里店南桥支行 ',
      },
      {
        custId: '************',
        accountName: '嘉实养老32号混合型基金中基金（FOF)',
      },
      {
        custId: '************',
        accountName: '天天养老1号混合型基金中基金（FOF）',
      },
      {
        custId: '************',
        accountName: '嘉实1号混合型基金中基金２０３３（ｆｏｆ）',
      },
      {
        custId: '************',
        accountName: '民生养老1号混合型基金中基金（FOF)',
      },
      {
        custId: '************',
        accountName: '和谐万家2023兔年基金中基金',
      },
      {
        custId: '************',
        accountName: '和谐万家2023兔年基金中基金1号',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京经济技术开发区支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京经济技术开发区支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京小营西路支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京小营西路支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京小营西路支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京小营西路支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京小营西路支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行 ',
      },
      {
        custId: '************',
        accountName: '交银养老1号混合型基金中基金（FOF)',
      },
      {
        custId: '************',
        accountName: '太平基金中基金',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行 ',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '华夏灵活032机构基金',
      },
      {
        custId: '************',
        accountName: '嘉实灵活1032产品基金',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行',
      },
      {
        custId: '************',
        accountName: '大家资产产品',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京四季青支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京地坛支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京鹿港支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京南站支行 ',
      },
      {
        custId: '************',
        accountName: '华宝美国消费创业板',
      },
      {
        custId: '************',
        accountName: '嘉实灵活1032产品基金',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
      {
        custId: '************',
        accountName: '中国工商银行北京大屯路支行',
      },
    ],
    tradeChannelList: [
      {
        code: '1',
        name: 'O32',
      },
      {
        code: '2',
        name: '基顺通线下',
      },
      {
        code: '3',
        name: '基顺通线上',
      },
    ],
    accountTypeList: [
      {
        code: '0',
        name: '机构',
      },
      {
        code: '1',
        name: '个人',
      },
      {
        code: '2',
        name: '产品',
      },
    ],
  },
};
export const mockSearchService = {
  code: '0000',
  message: '请求成功',
  singleData: {
    productInfoList: [
      {
        fofId: '********',
        custId: '************',
        transactionAccountId: '************',
        accountName: '工商银行',
        investorName: '招商基金管理有限公司',
        accountType: '1',
        accountTypeName: '个人',
        bankName: '',
        holdings: 0,
        moneyHoldings: 0,
        stockHoldings: 0,
        bondFundHoldings: 0,
        hybridFundHoldings: 0,
        stockFundHoldings: 0,
        financeHoldings: 0,
        institutionType: null,
        institutionTypeName: null,
        investment: true,
        tradeChannelCode: '3',
        tradeChannelName: '基顺通线上',
        strategyCode: '1',
        strategyName: 'toB',
        saleInfoList: [
          {
            saleId: 1,
            saleName: '张三',
            divideRatio: 1.0,
          },
        ],
        returnRatio: null,
        returnRatioExpression: null,
        managerFeeInfoList: [],
        moneySetDayType: null,
        moneySetDayTypeName: null,
        stockSetDayType: null,
        stockSetDayTypeName: null,
        superConversion: null,
        superConversionName: null,
        scFlag: null,
        scFlagName: null,
        openAccountDate: '********',
      },
      {
        fofId: '********',
        custId: '************',
        transactionAccountId: '************',
        accountName: '中国工商银行北京大屯路支行',
        investorName: '富国基金管理有限公司',
        accountType: '2',
        accountTypeName: '产品',
        bankName: '富国中证体育产业5号基金中基金',
        holdings: 0,
        moneyHoldings: 0,
        stockHoldings: 0,
        bondFundHoldings: 0,
        hybridFundHoldings: 0,
        stockFundHoldings: 0,
        financeHoldings: 0,
        institutionType: null,
        institutionTypeName: null,
        investment: false,
        tradeChannelCode: '2',
        tradeChannelName: '基顺通线下',
        strategyCode: '1',
        strategyName: 'toB',
        saleInfoList: [
          {
            saleId: 2,
            saleName: '李二狗',
            divideRatio: 0.7,
          },
        ],
        returnRatio: 0.7,
        returnRatioExpression: '(客户维护费+销售服务费）*70%',
        managerFeeInfoList: [
          {
            fundCode: '013885',
            fundName: null,
            ratio: 0.5,
          },
        ],
        moneySetDayType: null,
        moneySetDayTypeName: null,
        stockSetDayType: null,
        stockSetDayTypeName: null,
        superConversion: null,
        superConversionName: null,
        scFlag: null,
        scFlagName: null,
        openAccountDate: '********',
      },
    ],
    result: {
      investorCount: 2,
      accountCount: 2,
      holdings: 0,
      stockHoldings: 0,
      moneyHoldings: 0,
      financeHoldings: 0,
    },
    minId: 913,
  },
};
