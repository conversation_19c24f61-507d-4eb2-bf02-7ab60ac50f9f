interface temporaryData {
    addBlackThsList: string[]
    delBlackThsList: string[]
    addLinkedThsList: string[]
    delLinkedThsList: string[]
    addOutlineList: string[]
    topContentList: string[]
    delTopContentList: string[]
    delOutlineList: string[]
}

interface TSH {
    id: string
    name: string
    isBlack: boolean
}

interface batchInfo {
    funds: string[]
    opTime: string
}

interface article {
    itemId: string
    contentType: number
    title: string
    index?: number
    time?: string
    funds?: string
    startTime?: string
    endTime?: string
    label?: string
    sendSelectedHold?: boolean | string
    batchInfo?: batchInfo
    fundInfo?: articleFundInfo[]
}

interface articleFundInfo {
    funds: string[]
    startTime: string
    endTime: string
    label: string
    sendSelectedHold: string
}

// 表格页码
interface Pagination {
    current: number
    pageSize: number
    total?: number
}

interface FundItem {
    blackThs: TSH[]
    configFundNews: string[]
    configFundThsArticle: string[]
    fundCode: string
    fundName: string
    fundNews: string[]
    fundThsArticle: string[]
    linkedThs: TSH[]
    linkedThsMap: object
    newsCount: number
    newsSwitch: number
    outLineNewsContent: object[]
    outLineThsContent: object[]
    thsCount: number
    thsSwitch: number
    topContentList: object[]
}
