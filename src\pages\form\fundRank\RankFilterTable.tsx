import React, { useEffect, useState } from 'react';
import { Popconfirm, Table, message, Modal, Form, Input, Button } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import moment from 'moment';
import api from 'api';
import store from 'store';

import { IRankFilterReq, IRankFilterItem, IResponse, IRankFilterForm } from './type';
import { SUCCESS_CODE } from './const';
import styles from './index.less';

const { fetchFundRankFilter, postFundRankFilter } = api;
const { TextArea } = Input;

const RankFilterTable = (props: FormComponentProps) => {
  const { form } = props;
  const { getFieldDecorator, setFieldsValue, resetFields } = form;

  const [dataSource, setDataSource] = useState<IRankFilterItem[]>([]);
  const [editingKey, setEditingKey] = useState('');
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchFundRankFilter().then((res: IResponse) => {
      if(res.code !== SUCCESS_CODE){
        message.error(res.message)
      } else {
        const data: IRankFilterReq = JSON.parse(res.data || '{}');
        setDataSource(data?.filterList || []);
      }
    });
  }, []);

  const columns = [
    {
      title: '策略key',
      key: 'strategyKey',
      dataIndex: 'strategyKey',
    },
    {
      title: '策略标题',
      key: 'strategyTitle',
      dataIndex: 'strategyTitle',
    },
    {
      title: '移动',
      key: 'move',
      render: (_1: IRankFilterItem, _2: IRankFilterItem, index: number) => {
        if (dataSource.length === 1) {
          return '默认位置';
        } else if (index === 0) {
          return <a onClick={() => handleDown(index)}>下移</a>;
        } else if (index === dataSource.length - 1) {
          return <a onClick={() => handleUp(index)}>上移</a>;
        }
        return (
          <>
            <a onClick={() => handleUp(index)}>上移</a>
            &nbsp;&nbsp;&nbsp;
            <a onClick={() => handleDown(index)}>下移</a>
          </>
        );
      },
    },
    {
      title: '策略文案',
      key: 'strategyText',
      dataIndex: 'strategyText',
    },
    {
      title: '问财查询问句',
      key: 'sentence',
      dataIndex: 'sentence',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: IRankFilterItem, record: IRankFilterItem, index: number) => {
        return (
          <>
            <a onClick={() => handleEdit(record.strategyKey)}>编辑</a>
            &nbsp;&nbsp;&nbsp;
            {
              index === 0 ? null : <Popconfirm
              title="确定要删除这行数据吗？"
              okText="是"
              cancelText="否"
              onConfirm={() => handleDelete(record.strategyKey)}
            >
              <a>删除</a>
            </Popconfirm>
            }
          </>
        );
      },
    },
    {
      title: '最后编辑人/时间',
      key: 'editInfo',
      render: (dataItem: IRankFilterItem) => {
        return (
          <>
            <div>{dataItem.lastEditor}</div>
            <div>{dataItem.editorTime}</div>
          </>
        );
      },
    },
  ];

  const handleEdit = (key: string) => {
    setEditingKey(key);
    setShowModal(true);
    const editData = dataSource.find(item => item.strategyKey === key);
    if (editData) {
      const { key, lastEditor, editorTime, ...formData } = editData
      setFieldsValue(formData);
    } else {
      message.error('未知错误');
    }
  };
  const handleDelete = (key: string) => {
    const newData = dataSource.filter(item => item.strategyKey !== key);
    if (newData.length === dataSource.length - 1) {
      postFundRankFilter({
        value: JSON.stringify({ filterList: newData }),
      }).then((res: IResponse) => {
        if (res.code === SUCCESS_CODE) {
          setDataSource(newData);
          message.success('删除成功');
        } else {
          message.error(res.message)
        }
      });
    } else {
      message.error('未知错误');
    }
  };
  // 上移和下移功能
  const handleUp = (index: number) => {
    const newDataSource = [
      ...dataSource.slice(0, index - 1),
      dataSource[index],
      dataSource[index - 1],
      ...dataSource.slice(index + 1),
    ];
    const reqData: IRankFilterReq = {
      filterList: newDataSource,
    };
    postFundRankFilter({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
      if (res.code === SUCCESS_CODE) {
        setDataSource(newDataSource);
      } else {
        message.error(res.message);
      }
    });
  };
  const handleDown = (index: number) => {
    const newDataSource = [
      ...dataSource.slice(0, index),
      dataSource[index + 1],
      dataSource[index],
      ...dataSource.slice(index + 2),
    ];
    const reqData: IRankFilterReq = {
      filterList: newDataSource,
    };
    postFundRankFilter({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
      if (res.code === SUCCESS_CODE) {
        setDataSource(newDataSource);
      } else {
        message.error(res.message);
      }
    });
  };
  const handleAdd = () => {
    setEditingKey('');
    setShowModal(true);
    resetFields();
  };
  // 提交表单
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const editor = store.get('name');
    const editTime = moment().format('YYYY-MM-DD HH:mm:ss');
    const targetIndex = dataSource.findIndex(item => item.strategyKey === editingKey);
    form.validateFields((err, value: IRankFilterForm) => {
      if (!err) {
        const reqItem: IRankFilterItem = {
          ...value,
          key: value.strategyKey,
          strategyKey: value.strategyKey,
          lastEditor: editor,
          editorTime: editTime,
        };
        let newData: IRankFilterItem[];
        // 是编辑还是新增
        if (targetIndex !== -1) {
          newData = dataSource.map((item, index) => {
            if (index === targetIndex) {
              return reqItem;
            }
            return item;
          });
        } else {
          if (dataSource.find(item => item.strategyKey === value.strategyKey)) {
            message.error('已经存在这个策略key');
            return;
          } else {
            newData = [...dataSource, reqItem];
          }
        }
        postFundRankFilter({ value: JSON.stringify({ filterList: newData }) }).then(
          (res: IResponse) => {
            if (res.code === SUCCESS_CODE) {
              setDataSource(newData);
              setShowModal(false);
              message.success('修改成功');
            } else {
              message.error('修改数据错误');
            }
          },
        );
      } else {
        message.error('请输入完整数据');
      }
    });
  };
  return (
    <div>
      <div className={styles['table-title']}>
        一键筛选配置
        <Button className={styles['add-button']} onClick={handleAdd} type="primary">
          新增策略
        </Button>
      </div>
      <Table dataSource={dataSource} columns={columns} bordered pagination={false}></Table>
      <div className={styles.empty}></div>
      <Modal visible={showModal} onCancel={() => setShowModal(false)} footer={null}>
        <Form onSubmit={handleSubmit}>
          <Form.Item label="策略key">
            {getFieldDecorator('strategyKey', {
              rules: [{ required: true, message: '请输入策略key' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="策略标题">{getFieldDecorator('strategyTitle')(<Input />)}</Form.Item>
          <Form.Item label="策略文案">{getFieldDecorator('strategyText')(<TextArea />)}</Form.Item>
          <Form.Item label="问财查询问句">{getFieldDecorator('sentence')(<TextArea />)}</Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

const WrappedRankListTable = Form.create({ name: 'rankFilter' })(RankFilterTable);

export default WrappedRankListTable;
