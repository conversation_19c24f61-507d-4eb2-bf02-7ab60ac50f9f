import FORM_JSON from './form.json';
import React, { useState, useEffect } from 'react';
import {But<PERSON>, Popconfirm, message} from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';

import PushForm from './form';

export default function () {
    const { fetchBookingPushAllData, postBookingPushData, fetchBookingPushData } = api;

    const [init, setInit] = useState(false);
    const [pushList, setPushList] = useState([]);

    function addPushListItem() {
        const _pushList = JSON.parse(JSON.stringify(pushList));
        _pushList.push({new: true});
        setPushList(_pushList);
    }

    function deletePushForm (data: any, ifNew: boolean) {
        let _pushId = data.pushId;
        if (ifNew) {
            fetchPushList();
        } else {
            if (!data || !data.pushId) return message.info('缺少必要信息');
            postBookingPushData({value: ''}, _pushId).then((data: any) => {
                console.log(data);
                if (data.code === '0000') {
                    fetchPushList();
                } else {
                    message.info(data.message);
                }
            }).catch((e: any) => {
                message.info(e.message);
            })
        }
    }

    function submitPushForm (data: any, ifNew = false) {
        if (!data || !data.pushId) return message.info('缺少必要信息');
        let _pushId = data.pushId;
        /**
         * 1. 时间关系 startDate < endDate < pushDate
         * 2. push链接 https 开头 client 开头
         */
        let _startDate = new Date(data.startDate),
            _endDate = new Date(data.endDate),
            _pushDate = new Date(data.pushDate),
            _pushUrl: string = data.pushUrl;
        if (!(_startDate < _endDate && _endDate < _pushDate)) {
            return message.error('时间顺序错误');
        }
        if (!(_pushUrl.indexOf('https') === 0 || _pushUrl.indexOf('client') === 0)) {
            return message.error('push链接格式错误');
        }
        if (ifNew) {
            fetchBookingPushData({}, _pushId).then((_data: any) => {
                if (_data.code === '0000') {
                    if (_data.data) {
                        message.info(_pushId + '已存在，请前往修改配置');
                    } else {
                        postBookingPushData({value: JSON.stringify(data)}, _pushId).then((__data: any) => {
                            console.log(__data);
                            if (__data.code === '0000') {
                                message.info('编辑成功');
                                setTimeout(() => {
                                    location.reload();
                                }, 500)
                            } else {
                                message.info(__data.message);
                            }
                        }).catch((e: any) => {
                            message.info(e.message);
                        })
                    }
                } else {
                    message.info(_data.message);
                }
            }).catch((e: any) => {
                message.info(e.message);
            })
        } else {
            postBookingPushData({value: JSON.stringify(data)}, _pushId).then((data: any) => {
                if (data.code === '0000') {
                    message.info('编辑成功');
                } else {
                    message.info(data.message);
                }
            }).catch((e: any) => {
                message.info(e.message);
            })
        }
    }

    /**
     * 获取所有数据
     */
    function fetchPushList() {
        fetchBookingPushAllData().then((data: any) => {
            if (data.code === '0000') {
                setInit(true)
                data = data.data;
                let _pushList = [];
                for (let i = 0; i < data.length; i++) {
                    if (data[i].value) {
                        _pushList.push(JSON.parse(data[i].value));
                    }
                }
                setPushList(_pushList);
            } else {
                message.info(data.message);
            }
        }).catch((e: any) => {
            message.info(e.message);
        })
    }

    useEffect(() => {
        fetchPushList();
    }, [])

    

    return (
        <article> 
            {
                init ? 
                <div className={'g-mb20'}>
                    {
                        pushList.length ? pushList.map((formData, index) =>
                            <PushForm 
                                className={'g-mb20'} 
                                doDelete={deletePushForm} 
                                submit={submitPushForm} 
                                data={formData} 
                                key={formData.pushId}
                            />
                        ) : <p className={'f-tc'}>暂无预约push~</p>
                    }
                </div> : null
            }
            <Button type={'primary'} onClick={addPushListItem}>新增</Button>
        </article>
    )
}