import React, { useState, useEffect, useCallback } from 'react';
import { Table, Button, Popconfirm, message } from  'antd';
import api from 'api';

import TshBlackList from './tshBlackList'
import AddBlackList from './addBlackList'
import ConnectTsh from './connectTsh'
import OfflineArticles from './offlineArticles'
import GoodArticle from './../component/goodArticle'
import OfflineSingle from './offlineSingle'
import Article from './../component/article'

import './../types'

const CONTENT_TYPE: any = {
    '1': '资讯',
    '2': '同顺号文章'
}

const {
    fetchZxFundConfig, //page2-查询单个基金的内容配置
    fetchTempZxFundConfig, //page2-查询临时基金内容配置
    resetTempConfig, //page2-重置临时基金内容配置
    postTshAllBlackList, //page2-对所有基金拉黑同顺号列表
    postArticleAllOffline, //page2-对所有基金下线文章列表
    postZxFundConfig, //page2-更新单个基金的内容配置
    fetchTshName, //查询同顺号名称
} = api

export default function(props: any) {

    const [isOpenZx, setIsOpenZx] = useState(false) //资讯是否开启
    const [isOpenTsh, setIsOpenTsh] = useState(false) //同顺号是否开启
    const [code ,setCode] = useState(props?.location?.query?.code) //基金代码
    const [name, setName] = useState(props?.location?.query?.name) //基金名称

    const [modifiedData, setModifiedData] = useState<temporaryData>({
        addBlackThsList: [],
        delBlackThsList: [],
        addLinkedThsList: [],
        delLinkedThsList: [],
        addOutlineList: [],
        delOutlineList: [],
        topContentList: [],
        delTopContentList: [],
    }) //修改了的数据，用于查询临时信息
    const [newAllTshBlackList, setNewAllTshBlackList] = useState<string[]>([]) //新增全基金同顺号黑名单列表
    const [newAllArticleOffline, setNewAllArticleOffline] = useState<string[]>([]) //新增全基金文章下架列表

    const [connectTsh, setConnectTsh] = useState([]) //关联同顺号名单
    const [connectTshText, setConnectTshText] = useState('') //关联同顺号文案
    const [blackTsh, setBlackTsh] = useState<TSH[]>([]) //同顺号黑名单
    const [goodArticles, setGoodArticles] = useState([]) //精选文章
    const [offlineTshArticles, setOfflineTshArticles] = useState([]) //同顺号已下线文章
    const [offlineZxArticles, setOfflineZxArticles] = useState([]) //资讯已下线文章
    const [topContentList, setTopContentList] = useState<string[]>([]) //置顶列表

    const [isTemp, setIsTemp] = useState(true) //是否拉取临时信息
    const [articleValue, setArticleValue] = useState({}) //文章信息
    const [showData, setShowData] = useState<article[]>([]) //展示的文章
    const [pagination, setPagination] = useState<Pagination>({current: 1, pageSize: 10}) 
    const [loading, setLoading] = useState(false) //图表是否在加载

    const [isShowTshBlackList, setIsShowThsBlackList] = useState(false) //是否展示同顺号黑名单
    const [isShowAddBlackList, setIsShowAddBlackList] = useState(false) //是否展示新增黑名单 
    const [isShowConnect, setIsShowConnect] = useState(false) //是否展示关联同顺号
    const [isShowGood, setIsShowGood] = useState(false) //是否展示精选文章
    const [isShowOffline, setIsShowOffline] = useState(false) //是否展示已下线文章
    const [isShowOfflineSingle, setIsShowOfflineSingle] = useState(false) //是否展示单独下线文章窗口
    const [isShowConfigArticle, setIsShowConfigArticle] = useState(false) //是否展示文章配置窗口

    const [isArticleAll, setIsArticleAll] = useState(false) //文章是否选择全部
    const [chosenArticle, setChosenArticle] = useState<article>({
        itemId: '',
        contentType: 0,
        title: '',
    }) //选中的文章

    const columns: any = [
        {
            title: '文章id',
            dataIndex: 'itemId',
            width: '20%',
        },
        {
            title: '文章标题',
            dataIndex: 'title',
            width: '20%',
        },
        {
            title: '来源',
            dataIndex: 'contentType',
            render: (contentType: number, record: any) => (<p>
                {CONTENT_TYPE[String(contentType)]}<br></br>
                {contentType === 2 ? (record?.authorInfo?.subId || '') : ''}
            </p>),
            width: '20%',
        },
        {
          title: '发布时间',
          dataIndex: 'time',
          width: '20%',
          render: (time: number) => {
              let date: Date = new Date(time * 1000),
                _year: number = date.getFullYear(),
                _month: number = date.getMonth() + 1,
                _date: number = date.getDate(),
                _hour: number = date.getHours(),
                _minute: number = date.getMinutes()
            return `${_year}${_month >= 10 ? _month : '0' + _month}${_date >= 10 ? _date : '0' + _date} ${_hour >= 10 ? _hour : '0' + _hour}:${_minute >= 10 ? _minute : '0' + _minute}`
          }
        },
        {
            title: '操作',
            key: 'action',
            render: (record: any, data: any, index: number) => {
                let _isTop = topContentList.indexOf(record.itemId) > -1
                return (
                    <div className="f-tl" style={{color: 'blue'}}>
                        <a onClick={() => {
                            setChosenArticle(record)
                            setIsShowOfflineSingle(true)
                            setIsArticleAll(Boolean(~newAllArticleOffline.indexOf(record.itemId)))
                        }} style={{}}>下线</a>
                        <a className="g-ml20" onClick={() => {
                            if (_isTop) {
                                cancelTop(record.itemId)
                            } else {
                                topArticle(record.itemId)
                            }
                        }}>
                            {
                                _isTop
                                ?
                                '取消置顶'
                                :
                                '置顶'
                            }
                        </a>
                    </div>
                )
            }
        }
    ];

    useEffect(() => {
        // fetchTemporaryFundData(pagination)
        // fetchFundData(pagination)
        reset(() => {
            fetchFundData(pagination)
            setIsTemp(false)
        })
    }, [])

    useEffect(() => {
        if (!isShowOfflineSingle) setIsArticleAll(false)
    }, [isShowOfflineSingle])

    useEffect(() => {
        if (loading) _.fundLoading()
        else _.hideFundLoading()
    }, [loading])

    /**
     * 获取信息
     * @param pageNumber 页码
     * @param number 每一页数量
     */
    function fetchFundData(pagination: Pagination) {
        const {
            current,
            pageSize
        } = pagination
        setLoading(true)
        _.fundLoading()
        fetchZxFundConfig({
            fund: code || name,
            offer: current,
            limit: pageSize
        }, new Date().getTime()).then((res: any) => {
            _.hideFundLoading()
            setIsTemp(false) //设置为不拉取临时数据
            setLoading(false)
            if (res.status_code === 0) {
                handleData(res.data, pagination, false)
            } else {
                message.error(res.status_msg)
            }
        }).catch((e: any) => {
            _.hideFundLoading()
            setLoading(false)
            console.log(e.message)
            message.error('系统错误,请稍候再试~')
        })
    }

    /**
     * 获取临时信息
     * @param pageNumber 页码
     * @param number 每一页数量 
     */
    function fetchTemporaryFundData(pagination: Pagination, _modifiedData: temporaryData = modifiedData) {
        const {
            current,
            pageSize
        } = pagination
        let {
            addBlackThsList,
            delBlackThsList,
            addLinkedThsList,
            delLinkedThsList,
            addOutlineList,
            delOutlineList,
            topContentList,
            delTopContentList,
        } = _modifiedData
        _.fundLoading()
        setLoading(true)
        fetchTempZxFundConfig({
            fundCode: code || name,
            offer: current,
            limit: pageSize,
            addBlackThsList: addBlackThsList.join(',') || '',
            delBlackThsList: delBlackThsList.join(',') || '',
            addLinkedThsList: addLinkedThsList.join(',') || '',
            delLinkedThsList: delLinkedThsList.join(',') || '',
            addOutlineList: addOutlineList.join(',') || '',
            topContentList: topContentList.join(',') || '',
            delTopContentList: delTopContentList.join(',') || '',
            delOutlineList: delOutlineList.join(',') || '',
        }, new Date().getTime()).then((res: any) => {
            setLoading(false)
            setIsTemp(true) //设置为拉取临时数据
            _.hideFundLoading()
            if (res.status_code === 0) {
                handleData(res.data, pagination, true)
            } else {
                message.error(res.status_msg)
            }
        }).catch((e: any) => {
            setLoading(false)
            _.hideFundLoading()
            console.log(e.message)
            message.error('系统错误,请稍候再试~')
        })
    }

    /**
     * 处理数据
     * @param data 
     * @param pagination 
     * @param isTemp 是否为临时用来判断是否要更新开关
     */
    function handleData(data: any, pagination: Pagination, isTemp: boolean) {
        setModifiedData({
            ...modifiedData,
            addBlackThsList: []
        })
        const {
            totalNum,
            contentList,
            fundConfigContentDto
        } = data
        let _pagination: Pagination = { ...pagination }
        _pagination.current = pagination.current
        _pagination.pageSize = pagination.pageSize
        _pagination.total = totalNum
        setPagination(_pagination)
        setShowData(contentList)
        const {
            fundCode,
            fundName,
            newsSwitch,
            thsSwitch,
            blackThs,
            linkedThs,
            outLineNewsContent,
            outLineThsContent,
            topContentList,
        } = fundConfigContentDto
        setConnectTsh(linkedThs)
        setConnectTshText((linkedThs.reduce((_text: string, item: TSH, index: number) => _text + item.name + '(' + item.id + ')、'
        , '')))
        let _blackTsh: any = [...blackThs]
        _blackTsh.forEach((item: TSH, index: number) => _blackTsh[index].isBlack = true)
        setBlackTsh(_blackTsh)
        setTopContentList(topContentList)
        setOfflineTshArticles(outLineThsContent)
        setOfflineZxArticles(outLineNewsContent)
        setCode(fundCode)
        setName(fundName)
        // 如果是临时数据不对开关进行更新
        if (!isTemp) {
            setIsOpenZx(newsSwitch === 1 ? true : false)
            setIsOpenTsh(thsSwitch === 2 ? true : false)
        }
    }

    /**
     * 操作表格变换
     * @param pagination 
     * @param filters 
     * @param sorter 
     */
    function handleTableChange(pagination: Pagination) {
        setLoading(true)
        if (isTemp) {
            fetchTemporaryFundData(pagination)
        } else {
            fetchFundData(pagination)
        }
    }

    /**
     * 重置
     */
    function reset(fn: () => void) {
        resetTempConfig({
            fundCode: code
        }).then((res: any) => {
            if (res.status_code === 0) {
                fn && fn()
            } else {
                message.error(res.status_msg)
            }
        }).catch((e: any) => {
            console.log(e.message)
            message.error('系统错误,请稍候再试~')
        })
    }

    /**
     * 点击重置按钮
     */
    function clickReset() {
        reset(() => {
            message.info('信息重置成功')
            let _setTimeout = setTimeout(() => {
                clearTimeout(_setTimeout)
                window.location.reload()
            }, 1000)
        })
    }

    /** 
     * 取消 回到主页面
     */
    function cancel() {
        props.history.push(`/form/fundContentManage/fundConfig`)
    }

    /**
     * 提交
     */
    function upload() {
        let _promises = [ postZxFundConfig({
            fundCode: code,
            newsSwitch: isOpenZx ? 1 : 0,
            thsSwitch: isOpenTsh ? 2 : 0,
            addOutlineList: [],
            topContentList: modifiedData.topContentList,
            delTopContentList: modifiedData.delTopContentList
        })]
        if (newAllArticleOffline.length) {
            _promises.push(postArticleAllOffline({
                data: newAllArticleOffline.join(','),
                fundSelf: code
            }, new Date().getTime()))
        } else {
            _promises.push(Promise.resolve({}))
        }
        if (newAllTshBlackList.length) {
            _promises.push(postTshAllBlackList({
                data: newAllTshBlackList.join(','),
                fundSelf: code
            }, new Date().getTime()))
        } else {
            _promises.push(Promise.resolve({}))
        }
        Promise.all(_promises).then((res: any) => {
            console.log(res)
            if (res[0]['status_code'] === 0) {
                message.success('提交成功')
            } else {
                message.error(res[0].status_msg)
            }
            if (res[1]['status_code'] === 0) {
                setNewAllArticleOffline([])
                message.success('提交全局下线文章成功')
            } else if (res[1]['status_code']) {
                message.error(res[1].status_msg)
            }
            if (res[2]['status_code'] === 0) {
                setNewAllTshBlackList([])
                message.success('提交全局同顺号黑名单成功')
            } else if (res[2]['status_code']) {
                message.error(res[2].status_msg)
            }

            let _setTimeout = setTimeout(() => {
                clearTimeout(_setTimeout)
                cancel()
            }, 1000)
        }).catch((e: any) => {
            console.log(e.message)
            message.error('系统错误,请稍候再试~')
        })
    }

    /**
     * 置顶文章
     * @param id 文章id
     */
    function topArticle(id: string) {
        let _modifiedData = {...modifiedData},
            _delTopContentList = _modifiedData.delTopContentList
        //如果删除列表里有
        if (_delTopContentList.indexOf(id) > -1) {
            _modifiedData.delTopContentList.splice(_delTopContentList.indexOf(id), 1)
        } 
        if (topContentList.length === 3) {
            let _topContentList = [...topContentList]
            _modifiedData.delTopContentList.push(topContentList[0])
            _topContentList.splice(0, 1)
            _topContentList.push(id)
            _modifiedData.topContentList = _topContentList
        } else {
            _modifiedData.topContentList = [...topContentList]
            _modifiedData.topContentList.push(id)
        }
        fetchTemporary (_modifiedData)
    }

    /**
     * 取消文章置顶
     */
    function cancelTop(id: string) {
        let _modifiedData = {...modifiedData},
            _delTopContentList = _modifiedData.delTopContentList,
            _topContentList = [...topContentList]
        if (_topContentList.indexOf(id) > -1) _topContentList.splice(_topContentList.indexOf(id), 1)
        _delTopContentList.push(id)
        _modifiedData.topContentList = _topContentList
        _modifiedData.delTopContentList = _delTopContentList
        fetchTemporary(_modifiedData)
    }

    /**
     * 文章下线
     */
    function offlineArticle() {
        let id = chosenArticle.itemId,
            _modifiedData = { ...modifiedData }
            _modifiedData.addOutlineList = [id]
            _modifiedData.delOutlineList = []
        console.log(newAllArticleOffline, id)
        if (~newAllArticleOffline.indexOf(id)) return message.info('这个文章已经全局下线（提交后），若想取消全局下线可以刷新页面。')
        setIsShowOfflineSingle(false)
        fetchTemporary(_modifiedData)
    }

    function fetchTemporary (_modifiedData: temporaryData) {
        setModifiedData(_modifiedData)
        fetchTemporaryFundData({current: 1, pageSize: 10}, _modifiedData)
        setPagination({current: 1, pageSize: 10})
    }

    /**
     * 添加全部下线文章
     */
    function addAllArticle() {
        setNewAllArticleOffline([...newAllArticleOffline, chosenArticle.itemId])
        setIsShowOfflineSingle(false)
    } 

    /**
     * 添加全部拉黑的同顺号
     * @param tshId 
     */
    function addAllTsh(tshId: string) {
        setNewAllTshBlackList([...newAllTshBlackList, tshId])
        setIsShowAddBlackList(false)
    }

    return (
        <div>
            <header>
                <div className="u-l-middle">
                    基金名称:&nbsp;&nbsp;&nbsp;&nbsp;
                    <span>{name}</span>
                    <span>({code})</span>
                </div>
                <div className="u-l-middle g-mt40">
                    <p style={{marginRight: 20, width: 50}}>资讯</p>
                    <Button 
                    size="small"
                    type={isOpenZx ? 'danger' : 'primary'}
                    onClick={() => {setIsOpenZx(!isOpenZx)}}
                    >
                        {isOpenZx ? '关闭' : '开启'}
                    </Button>
                </div>
                <div className="u-l-middle g-mb20">
                    <p style={{marginRight: 20, width: 50}}>同顺号</p>
                    <Button 
                    type={isOpenTsh ? 'danger' : 'primary'}
                    size="small"
                    onClick={() => {setIsOpenTsh(!isOpenTsh)}}
                    >{isOpenTsh ? '关闭' : '开启'}</Button>     
                    <Button 
                    type="primary" 
                    className="g-ml40"
                    onClick={() => {setIsShowThsBlackList(true)}}
                    >查看黑名单</Button>           
                </div>
                <div>
                    <span>已关联同顺号</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span>{connectTshText}</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a style={{color: 'blue'}} onClick={() => {setIsShowConnect(true)}}>编辑</a>
                </div>
                <div className="u-r-middle">
                    <Button 
                    type="primary" 
                    onClick={() => {setIsShowGood(true)}}
                    >查看精选文章</Button>
                    <Button 
                    type="primary"
                    className="g-ml20"
                    onClick={() => {setIsShowOffline(true)}}
                    >查看已下线文章</Button>
                </div>
            </header>

            <div>
                <Table
                    columns={columns}
                    // rowKey={(item: article) => item.itemId}
                    dataSource={showData}
                    pagination={pagination}
                    loading={loading}
                    onChange={handleTableChange}
                    className="g-mt20 g-mb20"
                />

                <div className="u-r-middle">
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么'}
                        onConfirm={upload}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button type="danger" >提交</Button>
                    </Popconfirm>
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要退回一级页面么么'}
                        onConfirm={cancel}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button type="primary" style={{marginLeft: 20, marginRight: 20}}>取消</Button>
                    </Popconfirm>
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要重置保存内容么'}
                        onConfirm={clickReset}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button type="primary"> 重置 </Button>
                    </Popconfirm>
                </div>
            </div>

            <TshBlackList 
            isShow={isShowTshBlackList}
            setIsShow={useCallback(setIsShowThsBlackList, [])}
            tsh={blackTsh}
            setTsh={useCallback(setBlackTsh, [])}
            setIsShowAdd={useCallback(setIsShowAddBlackList, [])}
            modifiedData={modifiedData}
            setModifiedData={useCallback(setModifiedData, [])}
            save={useCallback(fetchTemporary, [modifiedData])}
            />
            <AddBlackList 
            isShow={isShowAddBlackList}
            setIsShow={useCallback(setIsShowAddBlackList, [])}
            tsh={blackTsh}
            setTsh={useCallback(setBlackTsh, [])}
            modifiedData={modifiedData}
            setModifiedData={useCallback(setModifiedData, [])}
            blackAllTsh={useCallback(addAllTsh, [])}
            />
            <ConnectTsh 
            isShow={isShowConnect}
            setIsShow={useCallback(setIsShowConnect, [])}
            connectTsh={connectTsh}
            modifiedData={modifiedData}
            save={useCallback(fetchTemporary, [])}
            />
            <OfflineArticles 
            isShow={isShowOffline}
            setIsShow={useCallback(setIsShowOffline, [])}
            offlineTshArticles={offlineTshArticles}
            offlineZxArticles={offlineZxArticles}
            modifiedData={modifiedData}
            save={useCallback(fetchTemporary, [])}
            />
            <GoodArticle 
            isShowArticle={isShowConfigArticle}
            isShow={isShowGood}
            setIsShow={useCallback(setIsShowGood, [])}
            articles={goodArticles}
            setArticles={useCallback(setGoodArticles, [])}
            setIsShowConfigArticle={useCallback(setIsShowConfigArticle, [])}
            setArticleValue={useCallback(setArticleValue, [])}
            modifiedData={modifiedData}
            code={code}
            type="second"
            />
            <Article
            isShow={isShowConfigArticle}
            setIsShow={useCallback(setIsShowConfigArticle, [])}
            articleValue={articleValue}
            setArticleValue={useCallback(setArticleValue, [])}
            type="edit"
            ></Article>
            <OfflineSingle 
            isShow={isShowOfflineSingle}
            newAllArticleOffline={newAllArticleOffline}
            setIsShow={useCallback(setIsShowOfflineSingle, [])}
            isAll={isArticleAll}
            setIsAll={useCallback(setIsArticleAll, [])}
            save={useCallback(offlineArticle, [chosenArticle, newAllArticleOffline])}
            upload={addAllArticle}
            />
        </div>
    )
}