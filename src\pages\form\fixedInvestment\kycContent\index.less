.m-card {
    .m-header {
        padding-right: 50px;
        margin-bottom: 20px;
        height: 40px;
        background:rgb(149, 186, 221);
        color: #ffffff;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .m-button {
            margin-left: 20px;
        }
    }
    .m-floor {
        padding-right: 50px;
        margin-bottom: 20px;
        height: 40px;
        background:rgb(149, 186, 221);
        color: #ffffff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .m-button {
            margin-left: 20px;
        }
    }
    .m-card-required {
      position: relative;
      &::before {
        display: inline-block;
        margin-right: 4px;
        color: #f5222d;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
    }
}
.m-panel-header {
    width: calc(100% - 130px);
    display: inline-flex;
}
.m-panel-header1 {
  width: calc(100% - 40px);
  display: inline-flex;
}
.m-card-label{
    width:110px;
    color: #333;
    text-align: right;
    padding-right: 12px;
    display: inline-block;
    margin-bottom: 0;
  
  }
  
  .m-tagModel-button{
    margin-left: 20px;
  }
  .m-tagModel-span{
    margin-right: 10px;
  }
  .m-tagModel-row{
    display: flex;
    align-items: center;
    margin: 10px 0;
  }
  .m-collpase-button {
    margin-top: -5px;
  }
  
  .m-required {
      margin: 1px 4px 0 0;
      color: #f5222d;
      font-size: 14px;
      font-family: SimSun, sans-serif;
  }
  :global {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
      -webkit-box-shadow: 0 0 0px 1000px white inset;
    }
  }