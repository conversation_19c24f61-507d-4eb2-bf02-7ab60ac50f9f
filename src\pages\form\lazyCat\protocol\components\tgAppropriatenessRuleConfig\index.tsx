import React, { useState, useEffect, FC } from 'react';

import { Button, Drawer, message, Popconfirm } from 'antd';
import { TATable } from '../../utiles';
import FormRender from 'form-render/lib/antd';
export interface IProps {
  visible: boolean;
  data: TATable;
  onSubmit: (data: object) => void;
  onClose: () => void;
}
const TgInfoConfig: FC<IProps> = ({ visible, data, onSubmit, onClose }) => {
    const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  const schema = {
    type: 'object',
    required: ["appropriatenessRule"],
    properties: {
      appropriatenessRule: {
        title: '适当性匹配规则',
        type: 'string',
        enum: [
          'strong',
          'weak'
        ],
        enumNames: [
          '强匹配',
          '弱匹配'
        ]
      },
    },
   
  };
  useEffect(()=>{
    if(visible){
      setData({
        appropriatenessRule: data.appropriatenessRule || ''
      })
    }
  },[visible,data])
  const handleSave=()=>{
    if (valid.length > 0) {
      message.error(`输入校验未通过，请检查`)
      return ;
    }
    onSubmit(formData);
  }
  return (
    <Drawer title={`投顾机构：${data?.taName||'--'}`} width={1000} visible={visible} onClose={onClose} >
      <div style={{ paddingTop: '40px' }}>
        <FormRender formData={formData} 
         onChange={setData}
         onValidate={setValid}
        labelWidth={180} displayType={'row'} propsSchema={schema} />
        <div style={{display:'flex',justifyContent:'flex-end'}}>
        <Button style={{marginRight:'12px'}} onClick={onClose}>取消</Button>
        <Popconfirm title={'确定要提交吗？'} cancelText={'取消'} okText={'确定'} onConfirm={handleSave}>
        <Button type='primary' >提交</Button>
        </Popconfirm>
        </div>
      </div>
    </Drawer>
  );
};
export default TgInfoConfig;
