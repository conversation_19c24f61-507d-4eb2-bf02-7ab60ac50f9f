import React, { useState, useEffect, useCallback } from 'react';
import { Select, Button, Popconfirm, Table, message } from  'antd';
import api from 'api';

import Header from './index/header'
import ZXData from './component/ZXData'
import Article from './component/article'
import GoodArticle from './component/goodArticle'
import ConfiguredArticle from './component/configuredArticle'
import BlackList from './component/blackList'

import {
    IS_OPEN_ZX_TAB
} from './const'

const {
    fetchAllFunds
} = api

export default function (props: any) {

    const [fundList, setFundList] = useState<FundItem[]>([]) //所有基金列表
    const [pagination, setPagination] = useState<Pagination>({current: 1, pageSize: 10})     
    const [loading, setLoading] = useState(false) //表格加载
    
    const [isOpenZXTab, setIsOpenZXTab] = useState(IS_OPEN_ZX_TAB.some) //个基开启资讯tab
    const [fundType, setFundType] = useState('0') //基金类型

    const [articleValue, setArticleValue] = useState({}) //文章信息
    const [goodArticles, setGoodArticles] = useState([]) //精选文章
    const [blackList, setBlackList] = useState([]) //黑名单
    const [articleType, setArticleType] = useState('new') //文章类型

    const [isShowConfigArticle, setIsShowConfigArticle] = useState(false) //是否展示配置文章
    const [isShowZXData, setIsShowZXData] = useState(false) //是否展示上传基金代码弹窗
    const [isShowGoodArticles, setIsShowGoodArticles] = useState(false) //是否展示精选文章
    const [isShowConfiguredArticles, setIsShowConfiguredArticles] = useState(false) //是否展示已配置文章
    const [isShowBlackList, setIsShowBlackList] = useState(false) //是否展示黑名单

    const columns = [
        {
          title: '基金代码',
          dataIndex: 'fundCode',
          key: 'fundCode'
        },
        {
          title: '基金名称',
          dataIndex: 'fundName',
          key: 'fundName',
        },
        {
            title: '关联同顺号',
            dataIndex: 'linkedThs',
            key: 'linkedThs',
            render: (linkedThs: TSH[]) => linkedThs.length
        },
        {
            title: '同顺号文章',
            dataIndex: 'thsCount',
            key: 'thsCount',
            render: (thsCount: string, record: any) => (
                    <div className="u-j-middle" style={{width: 100}}>
                        <p>{thsCount}</p>
                        <p
                        style = {{ color: record.thsSwitch === 2 ? 'green' : 'grey' }}
                        >{record.thsSwitch === 2 ? '已开通' : '未开通'}</p>
                    </div>
                )
        },
        {
            title: '资讯文章',
            dataIndex: 'newsCount',
            key: 'newsCount',
            render: (newsCount: string, record: any) => (
                    <div className="u-j-middle" style={{width: 100}}>
                        <p>{newsCount}</p>
                        <p
                        style = {{ color: record.newsSwitch === 1 ? 'green' : 'grey' }}
                        >{record.newsSwitch === 1 ? '已开通' : '未开通'}</p>
                    </div>
                )
        },
        {
          title: '操作',
          key: 'action',
          render: (item: FundItem) => {
              return (
                <a onClick={() => {jumpDetail(item.fundCode)}}>配置</a>
              )
          }
        }
    ];

    // useEffect(() => {
    //     handleTableChange(pagination)
    // }, [])

    useEffect(() => {
        handleTableChange({current: 1, pageSize: 10})
    }, [fundType])

    useEffect(() => {
        if (loading) _.fundLoading()
        else _.hideFundLoading()
    }, [loading])

    function handleTableChange(pagination: Pagination) {
        setLoading(true)
        fetchAllFunds({
            type: fundType,
            offer: pagination.current,
            limit: pagination.pageSize
        }).then((res: any) => {
            console.log(res)
            setLoading(false)
            if (res.status_code === 0) {
                setFundList(res.data.fundList)
                let _pagination: Pagination = {...pagination}
                _pagination.total = res.data.totalNum
                setIsOpenZXTab(res?.data?.openType === '2' ? IS_OPEN_ZX_TAB.all : IS_OPEN_ZX_TAB.some)
                setPagination(_pagination)
            } else {
                message.error(res.status_msg)
            }
        }).catch((e: any) => {
            setLoading(false)
            console.log(e.message)
            message.error('系统错误,请稍候再试~')
        })
    }

    /**
     * 跳转详情
     * @param str 基金代码或基金名称
     */
    function jumpDetail(str: string) {
        props.history.push(`/form/fundContentManage/fundConfig/fund?code=${str}`)
    }

    /**
     * 刷新
     */
    function refresh() {
        handleTableChange(pagination)
    }

    return (
        <div>
            <Header
            isOpenZXTab={isOpenZXTab}
            setIsOpenZXTab={useCallback(setIsOpenZXTab, [])}
            setIsShowConfigArticle={useCallback(setIsShowConfigArticle, [])}
            setIsShowZXData={useCallback(setIsShowZXData, [])}
            setIsShowGoodArticles={useCallback(setIsShowGoodArticles, [])}
            setIsShowConfiguredArticles={useCallback(setIsShowConfiguredArticles, [])}
            setIsShowBlackList={useCallback(setIsShowBlackList, [])}
            setArticleType={useCallback(setArticleType, [])}
            setArticleValue={useCallback(setArticleValue, [])}
            jumpDetail={useCallback(jumpDetail, [])}
            ></Header>

            <div className="u-r-middle" style={{marginTop: 20, marginBottom: 20}}>
                <Select defaultValue="0" style={{ width: 200 }} value={fundType} onChange={setFundType}>
                    <Select.Option value="0">全部基金</Select.Option>
                    <Select.Option value="1">已开通资讯基金</Select.Option>
                </Select>
            </div>

            <Table
            columns={columns}
            // rowKey={(item: FundItem) => item.fundCode}
            dataSource={fundList}
            pagination={pagination}
            loading={loading}
            onChange={useCallback(handleTableChange, [fundType])}
            ></Table>

            <ZXData
            isShowZXData={isShowZXData}
            setIsShowZXData={useCallback(setIsShowZXData, [])}
            refresh={useCallback(refresh, [pagination])}
            ></ZXData>

            <Article
            isShow={isShowConfigArticle}
            setIsShow={useCallback(setIsShowConfigArticle, [])}
            articleValue={articleValue}
            setArticleValue={useCallback(setArticleValue, [])}
            type={articleType}
            refresh={useCallback(refresh, [pagination])}
            ></Article>

            <GoodArticle
            isShowArticle={isShowConfigArticle}
            isShow={isShowGoodArticles}
            setIsShow={useCallback(setIsShowGoodArticles, [])}
            articles={goodArticles}
            setArticles={useCallback(setGoodArticles, [])}
            setIsShowConfigArticle={useCallback(setIsShowConfigArticle, [])}
            setArticleType={useCallback(setArticleType, [])}
            setArticleValue={useCallback(setArticleValue, [])}
            type="first"
            ></GoodArticle>

            <ConfiguredArticle
            isShow={isShowConfiguredArticles}
            setIsShow={useCallback(setIsShowConfiguredArticles, [])}
            refresh={useCallback(refresh, [pagination])}
            ></ConfiguredArticle>

            <BlackList
            isShow={isShowBlackList}
            setIsShow={useCallback(setIsShowBlackList, [])}
            refresh={useCallback(refresh, [pagination])}
            ></BlackList>
        </div>
    )
}