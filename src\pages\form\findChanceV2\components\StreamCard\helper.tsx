import { StreamCard, ChanceLiftType } from '../../type';
import { checkRequiredAndThrow, checkUrlAndThrow } from '../../utils';

export const mapPropToName = {
  recommendWords: '一句话推荐',
  title: '直播标题',
  organization: '直播机构',
  streamId: '直播id',
  link: '直播链接',
  cover: '直播间图片',
};
export const streamCardHelper = {
  addArrayData: function(): StreamCard {
    return {
      recommendWords: '',
      title: '',
      organization: '',
      $type: ChanceLiftType.STREAM,
      link: '',
      cover: '',
      streamId: '',
    };
  },
  addInit: function(): StreamCard[] {
    return [];
  },
  checkSingleData: function(item: FundRecommend) {
    checkRequiredAndThrow(item.recommendWords, mapPropToName['recommendWords']);
    checkRequiredAndThrow(item.title, mapPropToName['title']);
    checkRequiredAndThrow(item.streamId, mapPropToName['streamId']);
    checkRequiredAndThrow(item.link, mapPropToName['link']);
    //  checkRequiredAndThrow(item.organization, mapPropToName['organization']);
    checkRequiredAndThrow(item.cover, mapPropToName['cover']);
    checkUrlAndThrow(item.cover, mapPropToName['cover']);

    return true;
  },
  checkData: function(data: StreamCard[]) {
    data.forEach(item => {
      checkRequiredAndThrow(item.recommendWords, mapPropToName['recommendWords']);
      checkRequiredAndThrow(item.title, mapPropToName['title']);
      checkRequiredAndThrow(item.title, mapPropToName['title']);
      checkRequiredAndThrow(item.organization, mapPropToName['organization']);
      checkRequiredAndThrow(item.cover, mapPropToName['cover']);
      checkUrlAndThrow(item.cover, mapPropToName['cover']);
    });
    return true;
  },
};
