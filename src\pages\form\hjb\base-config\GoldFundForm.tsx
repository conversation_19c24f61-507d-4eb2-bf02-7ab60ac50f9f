/**
 * 黄金宝 - 基础配置 - 黄金基金
 */
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Typography, Form, List, Input, Button, Tag } from 'antd';
import { FormComponentProps } from 'antd/es/form';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

/**
 *
 */
const createGoldFund = () => ({ code: '', text: '', conversion_factor: '' });

type Props = {
  goldFundSaved: { [k: string]: unknown }[];
  goldFund: { [k: string]: unknown }[];
  onUpdateGoldFund: (goldFund: Props['goldFund']) => void;
};

//
const GoldFundForm: React.FunctionComponent<Props & FormComponentProps> = ({
  form,
  goldFundSaved,
  goldFund,
  onUpdateGoldFund,
}) => {
  //
  const [status, setStatus] = useState<'add' | 'view'>('add');
  const [current, setCurrent] = useState(null);

  const handleChangeStatus = (_status: typeof status, data) => {
    form.setFieldsValue(data);
    setStatus(_status);
  };

  const handleAdd = () => {
    form.validateFieldsAndScroll((err, values) => {
      if (err) return;

      onUpdateGoldFund([...goldFund, values]);
      handleChangeStatus('add', createGoldFund());
    });
  };

  const handleRemove = () => {
    const remove = form.getFieldsValue();
    const updated = goldFund.slice();
    const index = updated.findIndex(item => item.code == remove.code);

    if (~index) {
      updated.splice(index, 1);
      onUpdateGoldFund(updated);
    }

    handleChangeStatus('add', createGoldFund());
  };

  const handleUpdate = () => {
    form.validateFieldsAndScroll((err, values) => {
      if (err) return;

      const updated = goldFund.slice();
      const index = updated.findIndex(item => item.code == values.code);

      if (~index) {
        updated.splice(index, 1, values);
        onUpdateGoldFund(updated);
      }

      handleChangeStatus('add', createGoldFund());
    });
  };

  const handleTop = () => {
    let remove = form.getFieldsValue();
    const updated = goldFund.slice();
    const index = updated.findIndex(item => item.code == remove.code);

    if (~index) {
      remove = goldFund[index];
      updated.splice(index, 1);
      updated.unshift(remove);

      onUpdateGoldFund(updated);
    }

    handleChangeStatus('add', createGoldFund());
  };

  // prettier-ignore
  return (
    <section className={ styles['gold-fund-config'] }>
      <Typography.Title level={ 4 }>基金转黄金</Typography.Title>
      <main>
        <Form
          { ...formItemLayout }
          layout="horizontal"
        >
          <Form.Item label="基金代码">
            {
              form.getFieldDecorator('code', {
                // initialValue: ,
                rules: [{ required: true, message: '请输入基金代码' },
                { validator: (_, value) => status === 'view' || !goldFund.length || !goldFund.find(item => item.code === value), message: '基金代码不能重复' }],
              })(
                <Input
                  disabled={ status === 'view' }
                  type="number"
                  placeholder="请输入基金代码"
                />,
              )
            }
          </Form.Item>
          <Form.Item label="黄金名称">
            {
              form.getFieldDecorator('text', {
                // initialValue: ,
                rules: [{ required: true, message: '请输入黄金名称' }],
              })(
                <Input
                  type="text"
                  placeholder="请输入黄金名称"
                />,
              )
            }
          </Form.Item>
          <Form.Item label="黄金系数">
            {
              form.getFieldDecorator('conversion_factor', {
                // initialValue: ,
                rules: [{ required: true, message: '请输入黄金系数' }, { validator: (_, value) => value > 0 && parseInt(value) == parseFloat(value), message: '黄金系数必须大于 0，且为整数' }],
              })(
                <Input
                  type="text"
                  placeholder="请输入黄金系数"
                />,
              )
            }
          </Form.Item>
          <footer>
            {
              status === 'add' && <>
                <Button type="secondary" onClick={ () => handleChangeStatus('add', createGoldFund()) }>
                  重置
                </Button>
                <Button type="primary" onClick={ handleAdd }>
                  添加
                </Button>
              </>
            }
            {
              status === 'view' && <>
                {
                  goldFund.findIndex(item => item.code == form.getFieldsValue().code) !== 0 &&
                  <Button type="danger" onClick={ handleTop }>
                    置顶
                  </Button>
                }
                {
                  (!!~location.href.search('hjbAdmin=true') ||
                  !goldFundSaved.find(item => item.code == form.getFieldsValue().code)) &&
                  <Button type="danger" onClick={ handleRemove }>
                    删除
                  </Button>
                }
                <Button type="secondary" onClick={ () => handleChangeStatus('add', createGoldFund()) }>
                  取消
                </Button>
                <Button type="primary" onClick={ handleUpdate }>
                  更新
                </Button>
              </>
            }
          </footer>
        </Form>
        <List
          size="small"
          bordered
          dataSource={ goldFund }
          renderItem={ fund => <List.Item className={ styles['list-item'] } onClick={ () => handleChangeStatus('view', fund) }>
            <Tag color="gold">{ fund.code }</Tag> { fund.text } 系数{ fund.conversion_factor || 0 }</List.Item> }
        >

        </List>
      </main>
    </section>
  );
};

export default Form.create({ name: '' })(GoldFundForm);
