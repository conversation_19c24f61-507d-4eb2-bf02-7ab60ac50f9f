import React, { useState, useEffect } from 'react';
import api from 'api';
import { Table, Input, Tooltip, Button, Icon, Modal, message } from 'antd';

const { Search } = Input;
const { fetchFundLiveWhiteList, postFundLiveWhiteList, serchFundLiveFid } = api;
const { confirm } = Modal;

interface IFidProps {
  fid: string;
  companyName: string;
}

export default function() {
  const [searchResult, setSearchResult] = useState<IFidProps[]>([]); // 搜索结果
  const [savedFids, setSavedFids] = useState<IFidProps[]>([]); // 现有白名单

  const [searching, setSearching] = useState(false);

  const columns1 = [
    {
      title: '账号id/fid',
      dataIndex: 'fid',
      key: 'fid',
    },
    {
      title: '账号名称',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      render: (text: string, record: IFidProps, index: number) => {
        const { fid } = record;
        let flag = true;
        for (let i = 0; i < savedFids.length; i++) {
          if (savedFids[i].fid === fid) {
            flag = false;
            break;
          }
        }
        if (flag) {
          return (
            <Button
              onClick={() => {
                addBtn(record);
              }}
              type="primary"
              size="small"
            >
              新增
            </Button>
          );
        } else {
          return '已添加';
        }
      },
    },
  ];

  const columns2 = [
    {
      title: '账号id/fid',
      dataIndex: 'fid',
      key: 'fid',
    },
    {
      title: '账号名称',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: '操作',
      dataIndex: 'operate',
      key: 'operate',
      render: (text: string, record: IFidProps, index: number) => (
        <Button
          onClick={() => {
            delBtn(record, index);
          }}
          type="danger"
          size="small"
        >
          删除
        </Button>
      ),
    },
  ];

  // 搜索
  const searchFid = (fid: string) => {
    if (!fid) return message.error('请输入fid');
    setSearching(true);
    serchFundLiveFid({}, fid).then((res: any) => {
      setSearching(false);
      const { code, data } = res;
      if (code === '0000' && data) {
        let _name = data.name;
        if (_name) {
          setSearchResult([
            {
              fid: fid,
              companyName: _name,
            },
          ]);
        } else {
          message.error('暂无记录');
        }
      } else {
        setSearching(false);
        message.error('搜索失败，' + res.message);
      }
    });
  };

  // 获取现有白名单
  const getSavedList = () => {
    return new Promise(resolve => {
      fetchFundLiveWhiteList()
        .then((res: any) => {
          const { code, data } = res;
          if (code === '0000' && data) {
            let _list = JSON.parse(data);
            resolve(_list);
          } else {
            resolve([...savedFids]);
          }
        })
        .catch((e: Error) => {
          resolve([...savedFids]);
        });
    });
  };

  // 增加二次确认弹窗
  const addBtn = (addItem: IFidProps) => {
    const { fid, companyName } = addItem;
    confirm({
      title: '新增白名单',
      content: (
        <div style={{ textAlign: 'center' }}>
          <p>是否确认将账号：</p>
          <p style={{ fontWeight: 'bold' }}>
            {companyName}（{fid}）
          </p>
          <p>添加进基金直播白名单？</p>
        </div>
      ),
      okText: '确认',
      onOk() {
        return addSaveList(addItem);
      },
      cancelText: '取消',
    });
  };

  // 增加现有白名单
  const addSaveList = (addItem: IFidProps) => {
    return new Promise(reslove => {
      // 先获取下目前最新的数据
      getSavedList().then((list: IFidProps[]) => {
        // 先确认是否重复
        for (let i = 0; i < list.length; i++) {
          if (list[i].fid === addItem.fid) {
            // 重复了
            reslove(false);
            return message.warn('该fid已在白名单内');
          }
        }
        // 添加到最前
        list.unshift(addItem);
        reslove(updateListToClound(list));
      });
    });
  };

  // 删除二次确认弹窗
  const delBtn = (deleteItem: IFidProps, index: number) => {
    const { fid, companyName } = deleteItem;
    confirm({
      title: '删除白名单',
      content: (
        <div style={{ textAlign: 'center' }}>
          <p>是否确认将账号：</p>
          <p style={{ fontWeight: 'bold' }}>
            {companyName}（{fid}）
          </p>
          <p>从基金直播白名单删除？</p>
        </div>
      ),
      okText: '确认',
      onOk() {
        return deleteSaveList(deleteItem, index);
      },
      cancelText: '取消',
    });
  };

  // 删除现有白名单
  const deleteSaveList = (deleteItem: IFidProps, index: number) => {
    return new Promise(reslove => {
      // 先获取下目前最新的数据
      getSavedList().then((list: IFidProps[]) => {
        // 先找到要删除元素的位置
        let _deleteIndex = -1;
        if (list[index].fid === deleteItem.fid) {
          _deleteIndex = index;
        } else {
          for (let i = 0; i < list.length; i++) {
            if (list[i].fid === deleteItem.fid) {
              _deleteIndex = i;
              break;
            }
          }
        }
        if (_deleteIndex === -1) {
          reslove(false);
          message.warn('该fid已不在白名单内');
        } else {
          list.splice(_deleteIndex, 1);
          reslove(updateListToClound(list));
        }
      });
    });
  };

  // 上传列表数据
  const updateListToClound = (list: IFidProps[]) => {
    return new Promise(resolve => {
      postFundLiveWhiteList({
        value: JSON.stringify(list),
      })
        .then((res: any) => {
          if (res.code === '0000') {
            message.success('保存成功');
            setSavedFids(list);
            resolve(true);
          } else {
            message.error('保存错误：' + res.message);
            resolve(false);
          }
        })
        .catch((e: Error) => {
          message.error('保存错误：' + e.message);
          resolve(false);
        });
    });
  };

  useEffect(() => {
    fetchFundLiveWhiteList()
      .then((res: any) => {
        const { code, data } = res;
        if (code === '0000' && data) {
          let _list = JSON.parse(data);
          setSavedFids(_list);
        }
      })
      .catch((e: Error) => {
        message.error('获取现有白名单失败，' + e.message);
      });
  }, []);

  return (
    <div>
      <div>
        <h3>
          新增白名单
          <Tooltip title="本白名单针对基金详情页与自选列表页自动分发直播入口生效">
            <Icon type="question-circle" style={{ marginLeft: 4 }} />
          </Tooltip>
        </h3>
        <div style={{ width: 400, marginTop: 20 }}>
          <Search
            placeholder="输入直播账号id(fid)"
            onSearch={searchFid}
            enterButton="搜索"
            style={{ width: 300, marginBottom: 10 }}
            loading={searching}
          />
          <h4>搜索结果</h4>
          <Table
            dataSource={searchResult}
            columns={columns1}
            bordered
            size="small"
            pagination={false}
          />
        </div>
      </div>
      <div style={{ marginTop: 30 }}>
        <h3>
          现有白名单
          <Tooltip title="本白名单针对基金详情页与自选列表页自动分发直播入口生效">
            <Icon type="question-circle" style={{ marginLeft: 4 }} />
          </Tooltip>
        </h3>
        <div style={{ width: 400, marginTop: 20 }}>
          <Table
            dataSource={savedFids}
            columns={columns2}
            bordered
            size="small"
            pagination={{ pageSize: 10 }}
          />
        </div>
      </div>
    </div>
  );
}
