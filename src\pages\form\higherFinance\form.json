{"propsSchema": {"type": "object", "properties": {"onlineTalk": {"title": "在线预约", "type": "object", "properties": {"form": {"type": "array", "items": {"type": "object", "properties": {"select": {"title": "开放", "type": "boolean"}, "relation": {"title": "标签逻辑-关系", "type": "string", "enum": ["and", "or"], "enumNames": ["与", "或"]}, "conditions": {"title": "标签逻辑-标签", "type": "array", "description": "标签id(所使用标签需联系叶志飞（<EMAIL>）加入到标签接口中)", "ui:options": {"foldable": true}, "items": {"type": "object", "required": ["field", "params"], "properties": {"field": {"title": "标签id", "type": "string", "ui:width": "30%", "ui:options": {}}, "function": {"title": "逻辑", "type": "string", "enum": ["equal", "notEqual", "in", "notIn", "isSet", "notSet", "less", "greater", "lessEqual", "greaterEqual"], "enumNames": ["等于", "不等于", "字符串包括", "字符串不包括", "有值", "没值", "小于", "大于", "小于等于", "大于等于"], "ui:width": "30%"}, "params": {"title": "值", "type": "string", "ui:width": "30%"}}}}}}}}}, "telTalk": {"title": "电话沟通", "type": "object", "properties": {"form": {"type": "array", "items": {"type": "object", "properties": {"select": {"title": "开放", "type": "boolean"}, "relation": {"title": "标签逻辑-关系", "type": "string", "enum": ["and", "or"], "enumNames": ["与", "或"]}, "conditions": {"title": "标签逻辑-标签", "type": "array", "description": "标签id(所使用标签需联系叶志飞（<EMAIL>）加入到标签接口中)", "ui:options": {"foldable": true}, "items": {"type": "object", "required": ["field", "params"], "properties": {"field": {"title": "标签id", "type": "string", "ui:width": "30%", "ui:options": {}}, "function": {"title": "逻辑", "type": "string", "enum": ["equal", "notEqual", "in", "notIn", "isSet", "notSet", "less", "greater", "lessEqual", "greaterEqual"], "enumNames": ["等于", "不等于", "字符串包括", "字符串不包括", "有值", "没值", "小于", "大于", "小于等于", "大于等于"], "ui:width": "30%"}, "params": {"title": "值", "type": "string", "ui:width": "30%"}}}}}}}}}, "bannerConfig": {"title": "banner配置", "type": "object", "properties": {"form": {"type": "array", "items": {"type": "object", "required": ["actTheme", "imgUrl", "url"], "properties": {"actTheme": {"title": "活动主题", "type": "string"}, "imgUrl": {"title": "背景图片上传", "type": "string", "format": "image", "pattern": "^.+jpg|png|bmp|tif|gif|svg|psd|webp$", "ui:widget": "uploadImg", "ui:width": "50%"}, "url": {"title": "跳转链接", "type": "string", "pattern": "^http(|s)?://[^\n ，]*$", "ui:width": "50%"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "50%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "50%"}, "relation": {"title": "标签逻辑-关系", "type": "string", "enum": ["and", "or"], "enumNames": ["与", "或"]}, "type": {"title": "适用对象", "type": "string", "enum": ["0", "1", "2"], "enumNames": ["全部", "合格投资者", "非合格投资者"], "default": "0"}, "conditions": {"title": "标签逻辑-标签", "type": "array", "description": "标签id(所使用标签需联系叶志飞（<EMAIL>）加入到标签接口中)", "ui:options": {"foldable": true}, "items": {"type": "object", "required": ["field", "params"], "properties": {"field": {"title": "标签id", "type": "string", "ui:width": "30%", "ui:options": {}}, "function": {"title": "逻辑", "type": "string", "enum": ["equal", "notEqual", "in", "notIn", "isSet", "notSet", "less", "greater", "lessEqual", "greaterEqual"], "enumNames": ["等于", "不等于", "字符串包括", "字符串不包括", "有值", "没值", "小于", "大于", "小于等于", "大于等于"], "ui:width": "30%"}, "params": {"title": "值", "type": "string", "ui:width": "30%"}}}}}}}}}, "financeYL": {"title": "理财情报局配置", "type": "object", "properties": {"form": {"type": "array", "items": {"type": "object", "required": ["articleName", "url"], "properties": {"articleName": {"title": "文章名称", "type": "string"}, "imgUrl": {"title": "背景图片上传", "type": "string", "format": "image", "pattern": "^.+jpg|png|bmp|tif|gif|svg|psd|webp$", "ui:width": "50%"}, "url": {"title": "跳转链接", "type": "string", "pattern": "^http(|s)?://[^\n ，]*$", "ui:width": "50%"}, "relation": {"title": "标签逻辑-关系", "type": "string", "enum": ["and", "or"], "enumNames": ["与", "或"]}, "conditions": {"title": "标签逻辑-标签", "type": "array", "description": "标签id(所使用标签需联系叶志飞（<EMAIL>）加入到标签接口中)", "ui:options": {"foldable": true}, "items": {"type": "object", "required": ["field", "params"], "properties": {"field": {"title": "标签id", "type": "string", "ui:width": "30%", "ui:options": {}}, "function": {"title": "逻辑", "type": "string", "enum": ["equal", "notEqual", "in", "notIn", "isSet", "notSet", "less", "greater", "lessEqual", "greaterEqual"], "enumNames": ["等于", "不等于", "字符串包括", "字符串不包括", "有值", "没值", "小于", "大于", "小于等于", "大于等于"], "ui:width": "30%"}, "params": {"title": "值", "type": "string", "ui:width": "30%"}}}}}}}}}, "productCard": {"title": "产品卡配置", "type": "object", "properties": {"form": {"type": "array", "items": {"type": "object", "required": ["fundCode"], "properties": {"fundCode": {"title": "基金代码", "type": "string", "ui:width": "50%", "pattern": "^[0-9a-zA-Z]{6,}$"}, "fundName": {"title": "基金名称", "type": "string", "ui:width": "50%"}, "btnBubble": {"title": "按钮气泡", "type": "string", "ui:width": "50%"}, "text": {"title": "文案配置", "type": "array", "items": {"type": "object", "properties": {"txt": {"title": "文案", "type": "string"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "50%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "50%"}}}, "ui:options": {"foldable": true}}, "textRate": {"title": "文案的播放间隔", "type": "number"}, "relation": {"title": "标签逻辑-关系", "type": "string", "enum": ["and", "or"], "enumNames": ["与", "或"]}, "conditions": {"title": "标签逻辑-标签", "type": "array", "description": "标签id(所使用标签需联系叶志飞（<EMAIL>）加入到标签接口中)", "ui:options": {"foldable": true}, "items": {"type": "object", "required": ["field", "params"], "properties": {"field": {"title": "标签id", "type": "string", "ui:width": "30%", "ui:options": {}}, "function": {"title": "逻辑", "type": "string", "enum": ["equal", "notEqual", "in", "notIn", "isSet", "notSet", "less", "greater", "lessEqual", "greaterEqual"], "enumNames": ["等于", "不等于", "字符串包括", "字符串不包括", "有值", "没值", "小于", "大于", "小于等于", "大于等于"], "ui:width": "30%"}, "params": {"title": "值", "type": "string", "ui:width": "30%"}}}}}}}}}, "verifyConfig": {"title": "合格投资者认证配置", "type": "object", "properties": {"verifyPage": {"title": "落地页配置", "type": "object", "ui:labelWidth": 0, "properties": {"enable": {"title": "是否启用认证落地页", "type": "string", "enum": ["1", "0"], "enumNames": ["是", "否"], "ui:width": "100%", "default": "1"}, "link": {"title": "落地页链接", "type": "string", "ui:options": {}, "pattern": "(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?"}}}, "verifyCallback": {"title": "认证召回弹框配置", "type": "object", "properties": {"enable": {"title": "是否启用未完成认证召回弹窗", "type": "string", "enum": ["1", "0"], "enumNames": ["是", "否"], "default": "1"}, "content": {"title": "弹窗文案", "type": "string", "ui:options": {"allowClear": true}}, "leftButton": {"title": "弹窗按钮文案(左)", "type": "string", "ui:options": {}}, "rightButton": {"title": "弹窗按钮文案(右)", "type": "string", "ui:options": {}}}}, "finishVerify": {"title": "完成认证页面运营位", "type": "object", "properties": {"enable": {"title": "是否启用完成认证运营位", "type": "string", "enum": ["1", "0"], "enumNames": ["是", "否"], "default": "1"}, "image": {"title": "上传图片", "type": "string", "format": "image", "ui:widget": "uploadImg", "ui:options": {}}, "link": {"title": "运营位链接", "type": "string", "ui:options": {}, "pattern": "(http|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?"}}}, "packets": {"title": "认证成功红包配置", "type": "object", "properties": {"enable": {"title": "是否启用认证成功红包", "type": "string", "enum": ["1", "0"], "enumNames": ["是", "否"], "default": "1"}, "valideDate": {"title": "认证时间", "type": "string", "format": "date", "description": "认证成功时间大于等于此时间才显示红包弹窗"}, "activityId": {"title": "活动id", "type": "string", "ui:options": {}}}}, "dateMoney": {"title": "投资合格者认证活动配置", "type": "object", "properties": {"startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "50%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "50%"}, "money": {"title": "金额", "type": "number"}}}}}, "redPackage": {"title": "红包配置", "type": "object", "properties": {"showRedPackage": {"title": "是否展示红包", "type": "boolean", "ui:width": "20%"}, "isAssetRedPackage": {"title": "是否是认证红包(只展示给未认证用户)", "type": "boolean", "ui:width": "50%"}, "btnText": {"title": "按钮文案", "type": "string", "ui:width": "50%"}, "redPackageDesc": {"title": "描述文案", "type": "string", "ui:width": "50%"}, "redPackageLink": {"title": "跳转链接", "type": "string"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "50%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "50%"}}}, "financeCircle": {"title": "高端理财圈", "type": "object", "properties": {"showFinanceCircle": {"title": "是否展示高端理财圈", "type": "boolean"}}}}}, "displayType": "row", "showDescIcon": true, "labelWidth": 265}