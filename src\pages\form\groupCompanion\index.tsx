import React, { useState, useEffect } from "react";
import FormRender from 'form-render/lib/antd';
import axios from 'axios'
import api from 'api';
import { Button, message } from 'antd'

const {fetchCompanion, postCompanion} = api;

let FORM_CONFIG: any = {
    propsSchema: {
        type: 'object',
        properties: {
            title: {
                title: '主标题(1~16个字符)',
                type: 'string',
                maxLength: 16
            },
            content: {
                title: '内容简介(0～150个字符)',
                type: 'string',
				"format": "textarea",
                maxLength: 150,
            },
            url: {
                title: '跳转地址(必填,https地址)',
                type: 'string',
            },
			datestart: {
				"title": "起始时间",
				"type": "string",
				"format": "dateTime",
				"ui:width": "45%"
			},
			dateend: {
				"title": "结束时间",
				"type": "string",
				"format": "dateTime",
				"ui:width": "45%"
			}
        },
        required: ['title', 'content', 'url', 'datestart', 'dateend']
    }
};

// 
export default function () {
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState({});
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    
    useEffect(() => {
        fetchCompanion().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res) {
                    FORM_CONFIG.formData = {
                        ...res
                    };
                }
            } catch (e) {
                console.warn(e)
            }
            
            setInit(true);
            setFormConfig(FORM_CONFIG);
            setData(FORM_CONFIG.formData);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [init]);

    const onSubmit = () => {
		console.log(formData);
	
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            postCompanion({
                value: JSON.stringify(formData)
            }).then((res: any) => {
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('发布成功！')
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        }
    };

    if (!init) return '加载中'
    return (
        <div style={{ padding: 60 }}>
        <FormRender
            propsSchema={FORM_CONFIG.propsSchema}
            formData={formData}
            onChange={setData}
            onValidate={setValid}
            showDescIcon={true}
        />
        <Button type="primary" onClick={onSubmit}>提交</Button>
        </div>
    );
}
