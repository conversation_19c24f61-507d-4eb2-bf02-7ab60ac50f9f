import React, { useMemo } from 'react';
import { Alert } from 'antd';
import { parseLinkContent, validateLinkFormat } from '../utils/linkParser';

interface ContentPreviewProps {
  // 待预览的内容
  content: string;
  // 自定义样式类名
  className?: string;
  // 是否显示验证错误信息
  showValidation?: boolean;
  // 预览区域标题
  title?: string;
}

const ContentPreview: React.FC<ContentPreviewProps> = ({
  content,
  className = '',
  showValidation = true,
  title = '预览效果',
}) => {
  // 解析内容为React节点
  const parsedContent = useMemo(() => {
    return parseLinkContent(content || '');
  }, [content]);

  // 验证超链接格式
  const validation = useMemo(() => {
    return validateLinkFormat(content || '');
  }, [content]);

  // 如果内容为空，显示提示
  if (!content || content.trim() === '') {
    return (
      <div className={`content-preview ${className}`}>
        <div className="preview-title">{title}</div>
        <div className="preview-content empty">
          暂无内容
        </div>
      </div>
    );
  }

  return (
    <div className={`content-preview ${className}`}>
      <div className="preview-title">{title}</div>
      
      {/* 验证错误提示 */}
      {showValidation && !validation.isValid && (
        <Alert
          message="格式验证"
          description={
            <ul style={{ margin: 0, paddingLeft: 16 }}>
              {validation.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          size="small"
          style={{ marginBottom: 8 }}
        />
      )}

      {/* 预览内容 */}
      <div className="preview-content">
        {parsedContent.length > 0 ? (
          parsedContent.map((node, index) => (
            <React.Fragment key={index}>{node}</React.Fragment>
          ))
        ) : (
          <span className="preview-placeholder">无有效内容</span>
        )}
      </div>

      {/* 格式说明 */}
      <div className="preview-help">
        <small style={{ color: '#999' }}>
          格式说明：使用 {'{文字-链接}'} 格式创建超链接，例如：{'{证券日报网-http://example.com}'}
        </small>
      </div>
    </div>
  );
};

export default ContentPreview; 