import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSO<PERSON> from './amis.json';
import amisEnv from 'functions/amisEnv';

export default function() {
  let amisScoped: any;
  const init = () => {
    let amis = amisRequire('amis/embed');
    amisScoped = amis.embed('#whiteList', amisJSON, {}, amisEnv());
  };
  useEffect(() => {
    init();
    return () => {
      amisScoped.unmount();
    };
  }, []);

  return <div id="whiteList"></div>;
}
