import React, { useState, useEffect } from 'react';
import { Modal, Row, Col, Drawer, Radio, Button, DatePicker, Input, message } from 'antd';
import api from 'api'
import { contentProps, listProps, dataProps, noticeProps } from './interface'
const { RangePicker } = DatePicker;
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import article from '../../fundContentManage/fundConfig/component/article';
interface iProps {
    originData: contentProps,
    handelSave: (type: string, value: any, flag: string) => void
    changeAllNotice: (data: noticeProps) => void
}
export default function ({ originData, handelSave, changeAllNotice }: iProps) {

    const [data, setData] = useState<contentProps>({})
    const [edit, setEdit] = useState<Boolean>(false)


    useEffect(() => {
        if (!originData) return
        setData(originData)
        setEdit(false)
    }, [originData])

    function save() {
        console.log(data)
        if (data?.article?.show === 'true') {
            let _article = data.article
            if(!_article.startTime){
                message.error('请填写推文开始时间')
                return
            } else if(!_article.endTime){
                message.error('请填写推文结束时间')
                return
            } else if(_article.endTime < _article.endTime){
                message.error('推文结束时间小于开始时间')
                return
            } else if(!_article.content){
                message.error('请填写推文内容')
                return
            } else if(!_article.contentIntroduce){
                message.error('请填写推文内容简介')
                return
            } else if(!_article.url){
                message.error('请填写推文配置链接')
                return
            } else if(_article.url.indexOf(' ') > -1){
                message.error('推文配置链接存在空格，非法')
                return
            }
        }
        if (data?.notice?.show === 'true') {
            let _notice = data.notice
            if(!_notice.startTime){
                message.error('请填写小广播开始时间')
                return
            } else if(!_notice.endTime){
                message.error('请填写小广播结束时间')
                return
            } else if(_notice.endTime < _notice.startTime){
                message.error('小广播结束时间小于开始时间')
                return
            } else if(!_notice.content){
                message.error('请填写小广播内容')
                return
            } else if(!_notice.url){
                message.error('请填写小广播配置链接')
                return
            } else if(_notice.url.indexOf(' ') > -1){
                message.error('小广播配置链接存在空格，非法')
                return
            }
        }
        handelSave('content', data, 'drawer')
    }

    
    return (
        <section>
            {
                edit ? 
                <Button onClick={save} >保存</Button>:
                <Button onClick={() => setEdit(true)} >编辑</Button>
            }
            <Button className={'g-ml20'} onClick={() => changeAllNotice(data.notice || {})} disabled={!edit}>一键同步小广播</Button>

            {
                data ? <FormRender
                propsSchema={FORM_CONFIG}
                displayType='row'
                formData={data}
                onValidate={() => console.log()}
                onChange={setData}
                readOnly={!edit}
            /> : null
            }
            
        </section>
    )
}