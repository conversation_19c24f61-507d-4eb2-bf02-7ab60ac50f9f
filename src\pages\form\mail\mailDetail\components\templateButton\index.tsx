import React, { useEffect, useState } from 'react';
import { Button, Modal } from 'antd';
import { MailData } from '@/pages/form/mail/interface';
type FormRender = {
  formData: MailData;
};
export default function({ formData }: FormRender) {
  const [visible, setVisible] = useState<boolean>(false);
  const [url, setUrl] = useState<string>('./assets/01.png');

  useEffect(() => {
    switch (formData.mailTemplate) {
      case '0':
        setUrl('./assets/01.png');
        break;
      case '1':
        setUrl('./assets/02.png');
        break;
      case '2':
        setUrl('./assets/03.png');
        break;
      case '3':
        setUrl('./assets/04.png');
        break;
      case '4':
        setUrl('./assets/05.png');
        break;
      case '5':
        setUrl('./assets/06.png');
        break;
      case '6':
        setUrl('./assets/07.png');
        break;
      case '7':
        setUrl('./assets/08.png');
        break;
      case '8':
        setUrl('./assets/09.png');
        break;
      default:
        break;
    }
  }, [formData.mailTemplate]);
  return (
    <>
      <Button
        type="primary"
        style={{ marginLeft: '220px' }}
        onClick={() => {
          setVisible(true);
        }}
      >
        点击查看模板样式
      </Button>
      <Modal visible={visible} centered closable={false} footer={null}>
        <img src={require(`${url}`)} style={{ marginLeft: '57px' }} />
        <Button
          type="primary"
          style={{ marginLeft: '177px', marginTop: '20px' }}
          onClick={() => {
            setVisible(false);
          }}
        >
          退出预览模式
        </Button>
      </Modal>
    </>
  );
}
