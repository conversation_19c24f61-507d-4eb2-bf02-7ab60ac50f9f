import React, { useState, useEffect } from "react";
import Form<PERSON>ender from 'form-render/lib/antd';
import axios from 'axios'
import api from 'api';
import { Button, message } from 'antd'

const {fetchBannerManger, postBannerManger} = api;
const mockData = {
    hb:[
        {imgUrl:'',isUse:false, jumpUrl: ''}
    ],
    hh:[
        {imgUrl:'',isUse:false, jumpUrl: ''}
    ],
    pg:[
        {imgUrl:'',isUse:false, jumpUrl: ''}
    ],
    pz:[
        {imgUrl:'',isUse:false, jumpUrl: ''}
    ],
    qdii:[
        {imgUrl:'',isUse:false, jumpUrl: ''}
    ],
    sp:[
        {imgUrl:'',isUse:false, jumpUrl: ''}
    ],
    zs:[
        {imgUrl:'',isUse:false, jumpUrl: ''}
    ]
}
let FORM_CONFIG: any = {
    propsSchema: {
        type: 'object',
        properties: {
            pg: {
                title: '偏股型banner位',
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        imgUrl: {
                            title: '图片地址',
                            type: 'string',
							description: '请配置https图片地址，图片大小 685 * 180'
                        },
                        jumpUrl: {
                            title: '跳转地址',
                            type: 'string'
                        },
                        isUse: {
                            title: '是否启用',
                            type: 'boolean',
                            "ui:widget": 'switch'
                        }
                    },
                    required: ['imgUrl']
                },
                minItems:1,
                maxItems:1
               // "ui:readonly": true
            },
            pz: {
                title: '偏债型banner位',
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        imgUrl: {
                            title: '图片地址',
                            type: 'string',
							description: '请配置https图片地址，图片大小 685 * 180'
                        },
                        jumpUrl: {
                            title: '跳转地址',
                            type: 'string'
                        },
                        isUse: {
                            title: '是否启用',
                            type: 'boolean',
                            "ui:widget": 'switch'
                        }
                    },
                    required: ['imgUrl']
                },
                minItems:1,
                maxItems:1
                // "ui:readonly": true
            },
            qdii: {
                title: 'QDII型banner位',
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        imgUrl: {
                            title: '图片地址',
                            type: 'string',
							description: '请配置https图片地址，图片大小 685 * 180'
                        },
                        jumpUrl: {
                            title: '跳转地址',
                            type: 'string'
                        },
                        isUse: {
                            title: '是否启用',
                            type: 'boolean',
                            "ui:widget": 'switch'
                        }
                    },
                    required: ['imgUrl']
                },
                minItems:1,
                maxItems:1
                // "ui:readonly": true
            },
            zs: {
                title: '指数型banner位',
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        imgUrl: {
                            title: '图片地址',
                            type: 'string',
							description: '请配置https图片地址，图片大小 685 * 180'
                        },
                        jumpUrl: {
                            title: '跳转地址',
                            type: 'string'
                        },
                        isUse: {
                            title: '是否启用',
                            type: 'boolean',
                            "ui:widget": 'switch'
                        }
                    },
                    required: ['imgUrl']
                },
                minItems:1,
                maxItems:1
                // "ui:readonly": true
            },
            hh: {
                title: '混合型banner位',
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        imgUrl: {
                            title: '图片地址',
                            type: 'string',
							description: '请配置https图片地址，图片大小 685 * 180'
                        },
                        jumpUrl: {
                            title: '跳转地址',
                            type: 'string'
                        },
                        isUse: {
                            title: '是否启用',
                            type: 'boolean',
                            "ui:widget": 'switch'
                        }
                    },
                    required: ['imgUrl']
                },
                minItems:1,
                maxItems:1
                // "ui:readonly": true
            },
            sp: {
                title: '商品型banner位',
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        imgUrl: {
                            title: '图片地址',
                            type: 'string',
							description: '请配置https图片地址，图片大小 685 * 180'
                        },
                        jumpUrl: {
                            title: '跳转地址',
                            type: 'string'
                        },
                        isUse: {
                            title: '是否启用',
                            type: 'boolean',
                            "ui:widget": 'switch'
                        }
                    },
                    required: ['imgUrl']
                },
                minItems:1,
                maxItems:1
                // "ui:readonly": true
            },
            hb: {
                title: '货币型banner位',
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        imgUrl: {
                            title: '图片地址',
                            type: 'string',
							description: '请配置https图片地址，图片大小 685 * 180'
                        },
                        jumpUrl: {
                            title: '跳转地址',
                            type: 'string'
                        },
                        isUse: {
                            title: '是否启用',
                            type: 'boolean',
                            "ui:widget": 'switch'
                        }
                    },
                    required: ['imgUrl']
                },
                minItems:1,
                maxItems:1
                // "ui:readonly": true
            },
        }
    }
};

// 
export default function () {
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState({});
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    //去左右空格;
    function trim(str:string){
        // return str.replace(/(^\s*)|(\s*$)/g, "");
    }
    useEffect(() => {
        fetchBannerManger().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res) {
                    FORM_CONFIG.formData = {
                        ...res
                    };
                }
            } catch (e) {
                console.warn(e)
            }
            
            setInit(true);
            setFormConfig(FORM_CONFIG);
            setData(FORM_CONFIG.formData);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [init]);

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            let _postData:any = {
                ...formData
            }
            for(let key in _postData) {
              let item = _postData[key];
              for(let j in item[0]) {
                  if(item[0].hasOwnProperty(j)) {
                    if(j!=='isUse') {
                        console.log(item[0][j])
                        item[0][j] = item[0][j].toString().replace(/(^\s*)|(\s*$)/g,'')
                    }
                  }
                 // item[0].j = item[0].j.replace(/(^\s*)|(\s*$)/g,'')
              }
            }
            console.log(_postData)
            postBannerManger({
                value: JSON.stringify(_postData)
            }).then((res: any) => {
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('发布成功！');
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        }
    };

    if (!init) return '加载中'
    return (
        <div style={{ padding: 60 }}>
        <FormRender
            propsSchema={FORM_CONFIG.propsSchema}
            formData={formData}
            onChange={setData}
            onValidate={setValid}
            displayType="row"
            showDescIcon={true}
            column={2}
        />
        <Button type="primary" onClick={onSubmit}>提交</Button>
        </div>
    );
}