export interface FundTableData {
    fundName?: any, // 基金名称
    companyAndManager?: any, // 基金公司\经理 
    tradeStatus?: any, // 当前状态
    fundType?: any, // 基金类型
    soldTime?: any, // 发售周期
    fundAmount?: any, // 规模上限（万）
    riskLevel?: any, // 风险等级
    verifyStatus?: any, // 准入审核状态
    rgRates?: any // 认购费率
    sgRates?: any // 申购费率
    shRates?: any // 赎回费率
    discount?: any // 折扣率（钱包/银行卡，单位%）
}
export interface GdlcTableData {
    fundName?: string,
    fundCode?: string,
    syvalue?: string,
    sydesc?: string,
    manageRate?: string,
    tradeFeeRatio?: string,
    redeemRate?: string,
    openPeriod?: { start: string, end: string},
    deadline?: string,
    risklevel?: string
}
export interface GsdataTableDate {
    fundName?: string,
    fundCode?: string,
    deadline?: string,
    yearsy?: string,
    openPeriod?: { start: string, end: string },
    transactioncfmdate?: string,
    redeemPeriod?: { start: string, end: string },
    arrivaldate?: string,
    limitation?: string,
    isqs?: string,
    risklevel?: string
}


export interface SelectOption {
    key: 'fundKey'|'company'|'fundType'|'fundStatus',
    label: string,
    options: any[],
    placeholder?: string,
    defaultValue?: string,
    filterOption?: any
}
export interface FiltersData {
    fundKey?: string,
    company?: string,
    fundType?: string,
    fundStatus?: string,
}
export interface FiltersMap {
    fund?: FiltersData,
    gdlc?: FiltersData,
    gsdata?: FiltersData
}