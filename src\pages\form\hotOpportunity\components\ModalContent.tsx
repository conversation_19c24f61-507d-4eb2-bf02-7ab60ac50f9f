import React, { useEffect, useState, useMemo } from 'react';
import { Form, Input, Button, Spin, Modal, Switch, Tabs, Select, message } from 'antd';
import styles from '../modal.less';

const { TabPane } = Tabs as any;
import { FormComponentProps } from 'antd/lib/form/Form';
import { HotOpportunityGroup, PageStatus, EditType } from '../consts';
import {
  calculateProbability,
  getApiPrefix,
  getOperator,
  convertDataForEdit,
  createDebounce,
  cleanFundConfigData,
} from '../utils';
import { Block, OpportunityItem, OutsideTrackItem, TrackItem } from '../types';
import api from 'api';
import axios from 'axios';
import FundConfig from './FundConfig';
import IndustryChainContent from './IndustryChainContent';

const { getBlockList } = api;

const { Option } = Select as any;

interface ModalContentProps extends FormComponentProps {
  showDialog: boolean;
  onEditClose: () => void;
  currentData: OpportunityItem | {};
  edit: number;
  handleSingleData: (data: OpportunityItem) => Promise<void>;
}

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const ModalContent: React.FC<ModalContentProps> = ({
  form,
  showDialog,
  onEditClose,
  currentData,
  edit,
  handleSingleData,
}) => {
  const [loading, setLoading] = useState<boolean>(false);

  const { getFieldDecorator, setFieldsValue, getFieldValue, getFieldsValue } = form;

  const [blockList, setBlockList] = useState<Block[]>([]);
  const [insideTrackList, setInsideTrackList] = useState<TrackItem[]>([]);
  const [outsideTrackList, setOutsideTrackList] = useState<OutsideTrackItem[]>([]);

  // AI推荐板块相关状态
  const [aiRecommendedBlockName, setAiRecommendedBlockName] = useState<string | null>(null);
  const [isAiBlockMatched, setIsAiBlockMatched] = useState<boolean>(false);
  const [aiRecommendMessage, setAiRecommendMessage] = useState<string>('');

  // 产业链动态配置状态
  const [activeIndustryTypes, setActiveIndustryTypes] = useState<('upstream' | 'midstream' | 'downstream')[]>([]);

  // 产业链类型配置
  const industryTypeConfig = {
    upstream: { label: '上游产业链', key: 'upstream' },
    midstream: { label: '中游产业链', key: 'midstream' },
    downstream: { label: '下游产业链', key: 'downstream' },
  } as const;

  // AI推荐板块匹配工具函数
  const matchAiRecommendedBlock = (aiBlockName: string, blockList: Block[]): Block | null => {
    if (!aiBlockName || !blockList || blockList.length === 0) {
      return null;
    }
    
    // 精确匹配板块名称
    const matchedBlock = blockList.find(block => block.name === aiBlockName);
    return matchedBlock || null;
  };

  // 添加产业链
  const handleAddIndustryChain = (type: 'upstream' | 'midstream' | 'downstream') => {
    if (!activeIndustryTypes.includes(type)) {
      setActiveIndustryTypes(prev => [...prev, type]);
    }
  };

  // 删除产业链
  const handleRemoveIndustryChain = (type: 'upstream' | 'midstream' | 'downstream') => {
    setActiveIndustryTypes(prev => prev.filter(item => item !== type));
    // 清理对应的表单数据
    const fieldPath = `data.industryConfig.detail.${type}`;
    setFieldsValue({
      [`${fieldPath}.title`]: undefined,
      [`${fieldPath}.industry`]: undefined,
    });
  };

  // 处理AI推荐板块逻辑
  const handleAiRecommendedBlock = (data: any, blockList: Block[]) => {
    // 检查是否有AI推荐的板块名称
    const aiBlockName = data?.data?.block?.name;
    
    if (aiBlockName) {
      setAiRecommendedBlockName(aiBlockName);
      
      // 尝试匹配AI推荐的板块
      const matchedBlock = matchAiRecommendedBlock(aiBlockName, blockList);
      
      if (matchedBlock) {
        // 匹配成功，自动设置选中项
        setIsAiBlockMatched(true);
        setAiRecommendMessage(`已自动选择AI推荐板块：${aiBlockName}`);
        
        // 设置表单字段值
        setTimeout(() => {
          setFieldsValue({
            'data.conceptBlock': matchedBlock.code
          });
        }, 100);
      } else {
        // 匹配失败，显示提示
        setIsAiBlockMatched(false);
        setAiRecommendMessage(`AI推荐板块"${aiBlockName}"未找到，请手动选择`);
      }
    } else {
      // 没有AI推荐板块，清空状态
      setAiRecommendedBlockName(null);
      setIsAiBlockMatched(false);
      setAiRecommendMessage('');
    }
  };

  // 校验产业链标题配置
  const validateIndustryChainTitles = (formData: any): boolean => {
    // 如果产业链功能未开启，不需要校验
    if (!formData?.data?.industryConfig?.switch) {
      return true;
    }

    const industryDetail = formData?.data?.industryConfig?.detail;
    if (!industryDetail) {
      return false;
    }

    // 检查上游、中游、下游至少有一个配置了标题
    const upstreamTitle = industryDetail.upstream?.title;
    const midstreamTitle = industryDetail.midstream?.title;
    const downstreamTitle = industryDetail.downstream?.title;

    const hasAtLeastOneTitle = 
      (upstreamTitle && upstreamTitle.trim()) ||
      (midstreamTitle && midstreamTitle.trim()) ||
      (downstreamTitle && downstreamTitle.trim());

    return !!hasAtLeastOneTitle;
  };

  // 获取概念板块列表
  const getTotalBlock = async (): Promise<Block[]> => {
    try {
      const res = await getBlockList();
      if (res?.status_code === 0) {
        setBlockList(res.data);
        return res.data; // 返回获取到的数据
      }
      return [];
    } catch (error: any) {
      message.error(error?.message || '获取概念板块列表失败');
      return [];
    }
  };

  // 获取ETF赛道列表
  const getInsideTrackList = async () => {
    try {
      const res = await axios.get(
        `${getApiPrefix('fund')}/ranking/config/v1/track_name_list?scene=ETF`,
      );
      if (res?.data?.status_code === 0) {
        setInsideTrackList(res.data.data);
      }
    } catch (error: any) {
      message.error(error?.message || '获取ETF赛道列表失败');
    }
  };

  // 获取基金赛道列表
  const getOutsideTrackList = async () => {
    try {
      const res = await axios.get(`${getApiPrefix('fund')}/ranking/strictclass/v1/list`);
      if (res?.data?.status_code === 0) {
        setOutsideTrackList(res.data.data);
      }
    } catch (error: any) {
      message.error(error?.message || '获取基金赛道列表失败');
    }
  };

  const init = async () => {
      form.resetFields();

    // 编辑或复制时需要回填数据
    if ((edit === EditType.EDIT || edit === EditType.COPY) && currentData && Object.keys(currentData).length > 0) {
      console.log('currentData', currentData);
      try {
        // 将保存的数据转换为表单需要的格式
        setLoading(true);
        const blockListData = await getTotalBlock();
        await getInsideTrackList();
        await getOutsideTrackList();
        const editFormData = convertDataForEdit(currentData as OpportunityItem);
        console.log('editFormData', editFormData);

        // 清理编辑数据中的null值
        const cleanedEditFormData = cleanFundConfigData(editFormData);
        console.log('cleaned editFormData', cleanedEditFormData);

        // 处理AI推荐板块匹配（在blockList加载完成后）
        handleAiRecommendedBlock(currentData, blockListData);

        // 设置初始的产业链配置状态
        const industryDetail = (currentData as OpportunityItem)?.data?.industryConfig?.detail;
        const initialActiveTypes: ('upstream' | 'midstream' | 'downstream')[] = [];
        if (industryDetail?.upstream?.title || (industryDetail?.upstream?.industry && industryDetail.upstream.industry.length > 0)) {
          initialActiveTypes.push('upstream');
        }
        if (industryDetail?.midstream?.title || (industryDetail?.midstream?.industry && industryDetail.midstream.industry.length > 0)) {
          initialActiveTypes.push('midstream');
        }
        if (industryDetail?.downstream?.title || (industryDetail?.downstream?.industry && industryDetail.downstream.industry.length > 0)) {
          initialActiveTypes.push('downstream');
        }
        setActiveIndustryTypes(initialActiveTypes);

        // 设置表单字段值
        setTimeout(() => {
          setFieldsValue(cleanedEditFormData);
        }, 100);
        setLoading(false);
      } catch (error) {
        console.error('编辑数据转换失败:', error);
        message.error('编辑数据格式错误，请联系管理员');
        setLoading(false);
      }
    } else {
      // 新增时重置表单字段
      await getTotalBlock();
      await getInsideTrackList();
      await getOutsideTrackList();
      
      // 清空AI推荐状态
      setAiRecommendedBlockName(null);
      setIsAiBlockMatched(false);
      setAiRecommendMessage('');
      
      // 重置产业链配置状态
      setActiveIndustryTypes([]);
    }
  };

  // 表单数据初始化
  useEffect(() => {
    if (showDialog) {
      init();
    }
  }, [showDialog, edit]);

  // 重置表单和相关状态的统一函数
  const resetFormAndState = () => {
    form.resetFields();
    // 重置产业链配置状态
    setActiveIndustryTypes([]);
    // 清空AI推荐状态
    setAiRecommendedBlockName(null);
    setIsAiBlockMatched(false);
    setAiRecommendMessage('');
  };

  // 取消时清空表单
  const handleCancel = () => {
    resetFormAndState();
    onEditClose();
  };

  // 渲染产业链卡片
  const renderIndustryChainCard = (type: 'upstream' | 'midstream' | 'downstream') => {
    const config = industryTypeConfig[type];
    return (
      <div key={type} className={styles['industry-chain-card']}>
        <div className={styles['industry-chain-card-header']}>
          <span className={styles['industry-chain-title']}>{config.label}</span>
          <Button
            type="link"
            size="small"
            style={{ color: '#ff4d4f' }}
            onClick={() => handleRemoveIndustryChain(type)}
          >
            删除
          </Button>
        </div>
        <div className={styles['industry-chain-content']}>
          <IndustryChainContent
            type={type}
            form={form}
            initialLength={0}
            formData={currentData}
          />
        </div>
      </div>
    );
  };

  // 渲染添加产业链按钮
  const renderAddIndustryChainButton = () => {
    const availableTypes = Object.keys(industryTypeConfig).filter(
      type => !activeIndustryTypes.includes(type as any)
    ) as ('upstream' | 'midstream' | 'downstream')[];

    if (availableTypes.length === 0) {
      return null;
    }

    return (
      <div className={styles['add-industry-chain-section']}>
        <span style={{ marginRight: 8 }}>添加产业链：</span>
        {availableTypes.map(type => (
          <Button
            key={type}
            type="dashed"
            size="small"
            style={{ marginRight: 8 }}
            onClick={() => handleAddIndustryChain(type)}
          >
            + {industryTypeConfig[type].label}
          </Button>
        ))}
      </div>
    );
  };

  // 创建防抖提交函数
  const debouncedSubmit = useMemo(() => {
    return createDebounce(() => {
      form.validateFields(async (err, values) => {
        if (!err) {
          setLoading(true);
          try {
            let formData = getFieldsValue();

            // 校验产业链标题配置
            if (!validateIndustryChainTitles(formData)) {
              message.error('产业链功能已开启，请至少配置一个产业链的标题（上游、中游或下游）');
              setLoading(false);
              return;
            }

            // 在提交前添加必需的字段
            formData.group = HotOpportunityGroup;
            formData.status = PageStatus.COMPLETE;
            formData.operator = getOperator();

            if (edit === EditType.EDIT) {
              formData.configId = (currentData as OpportunityItem).configId;
            }

            console.log('submit', formData);

            // 清理基金配置数据中的null值
            formData = cleanFundConfigData(formData);
            console.log('cleaned formData', formData);

            const block = blockList.find(item => item.code === formData.data.conceptBlock);
            formData.data.conceptBlock = block;
            formData.data.name = formData.name.trim();
            if (formData.data.abTest) {
              formData.data.abValue = formData.data.abValue.join(',');
            }
            await handleSingleData(formData as OpportunityItem);
            // 提交成功后重置表单和状态
            resetFormAndState();
          } catch (error: any) {
            console.error('表单提交处理异常:', error);
            message.error(error?.message || '表单提交失败');
          } finally {
            setLoading(false);
          }
        }
      });
    }, 800, true);
  }, [form, getFieldsValue, edit, currentData, blockList, handleSingleData]);

  // 提交表单
  const onSubmit = () => {
    debouncedSubmit();
  };

  return (
    <Modal
      visible={showDialog}
      maskClosable={false}
      title={edit === EditType.ADD ? '新增' : edit === EditType.EDIT ? '编辑' : '复制'}
      closable={false}
      width={1000}
      onCancel={handleCancel}
      footer={
        <Spin spinning={loading}>
          <Button key="back" onClick={onEditClose}>
            取消
          </Button>
          <Button key="submit" type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Spin>
      }
    >
      <Spin spinning={loading}>
        <Form {...formItemLayout} labelAlign="left">
          <Form.Item label="页面名称">
            {getFieldDecorator('name', {
              rules: [{ required: true, message: '请输入名称' }],
            })(<Input placeholder="请输入名称" />)}
          </Form.Item>

          <Form.Item label="页面标题">
            {getFieldDecorator('data.title', {
              rules: [{ required: true, message: '请输入页面标题' }],
            })(<Input placeholder="请输入页面标题" />)}
          </Form.Item>

          <Form.Item label="摘要">
            {getFieldDecorator('data.digest', {
              rules: [{ required: true, message: '请输入摘要' }],
            })(<Input.TextArea placeholder="请输入摘要" rows={4} />)}
          </Form.Item>
          <Form.Item label="板块分析开关">
            {getFieldDecorator('data.isShowBlockAnalysis', {
              valuePropName: 'checked',
              initialValue: true,
              rules: [{ required: true, message: '请选择是否开启板块分析功能' }],
            })(<Switch checkedChildren="开启" unCheckedChildren="关闭" />)}
          </Form.Item>
          <Form.Item label="产业链功能">
            {getFieldDecorator('data.industryConfig.switch', {
              valuePropName: 'checked',
              initialValue: true,
              rules: [{ required: true, message: '请选择是否开启产业链功能' }],
            })(<Switch checkedChildren="开启" unCheckedChildren="关闭" />)}
          </Form.Item>

          <div style={{ display: getFieldValue('data.industryConfig.switch') ? 'block' : 'none' }}>
            <Form.Item label="产业链tab名称">
              {getFieldDecorator('data.industryConfig.tab', {
                initialValue: '产业链分析',
                rules: [
                  {
                    required: getFieldValue('data.industryConfig.switch'),
                    message: '请输入tab名称',
                  },
                ],
              })(<Input placeholder="请输入tab名称" />)}
            </Form.Item>

            <div className={styles['industry-chain-section']}>
              {/* 已配置的产业链列表 */}
              {activeIndustryTypes.map(type => renderIndustryChainCard(type))}
              
              {/* 添加产业链按钮 */}
              {renderAddIndustryChainButton()}
              
              {/* 当没有配置任何产业链时的提示 */}
              {activeIndustryTypes.length === 0 && (
                <div className={styles['empty-industry-chain-tip']}>
                  暂未配置产业链，请点击上方按钮添加
                </div>
              )}
            </div>
          </div>

          <Form.Item label="概念板块">
            {getFieldDecorator('data.conceptBlock', {
              rules: [{ required: true, message: '请选择概念板块' }],
            })(
              <Select
                placeholder="请选择概念板块（支持搜索板块名称）"
                showSearch={true}
                filterOption={(input, option) => {
                  const item = blockList.find(block => block.code === option?.props?.value);
                  if (!item) return false;
                  const searchText = input.toLowerCase();
                  return item.name.toLowerCase().includes(searchText);
                }}
                notFoundContent="未找到匹配的概念板块"
              >
                {blockList?.map((item, index) => (
                  <Option key={index} value={item.code}>
                    {item.name}
                  </Option>
                ))}
              </Select>,
            )}
            {/* AI推荐提示信息 */}
            {aiRecommendMessage && (
              <div
                className={`${styles.aiRecommendMessage} ${
                  isAiBlockMatched ? styles.success : styles.warning
                }`}
              >
                {aiRecommendMessage}
              </div>
            )}
          </Form.Item>

          <Form.Item label="AB开关">
            {getFieldDecorator('data.abTest', {
              valuePropName: 'checked',
              initialValue: false,
              rules: [{ required: true, message: '请选择是否开启AB开关' }],
            })(
              <Switch
                checkedChildren="开启(内容产品分开)"
                unCheckedChildren="关闭(内容产品合并)"
              />,
            )}
          </Form.Item>

          <div style={{ display: getFieldValue('data.abTest') ? 'block' : 'none' }}>
            <Form.Item label="AB开启用户尾号">
              {getFieldDecorator('data.abValue', {
                rules: [
                  {
                    required: getFieldValue('data.abTest'),
                    message: '请输入AB用户尾号',
                  },
                ],
              })(
                <Select mode="multiple" placeholder="请输入AB用户尾号">
                  {Array.from({ length: 10 }, (_, i) => (
                    <Option key={i} value={i}>
                      {i}
                    </Option>
                  ))}
                </Select>,
              )}
            </Form.Item>
          </div>

          <Tabs defaultActiveKey="inside" type="card">
            <TabPane tab="场内配置" key="inside" forceRender>
              <FundConfig
                type="inside"
                form={form}
                insideTrackList={insideTrackList}
                calculateProbability={calculateProbability}
                initialLength={1}
                formData={currentData}
              />
            </TabPane>
            <TabPane tab="场外配置" key="outside" forceRender>
              <FundConfig
                type="outside"
                form={form}
                insideTrackList={insideTrackList}
                outsideTrackList={outsideTrackList}
                calculateProbability={calculateProbability}
                initialLength={1}
                formData={currentData}
              />
            </TabPane>
          </Tabs>

          <Form.Item label="内容流应用id">
            {getFieldDecorator('data.newsAppId', {})(<Input placeholder="请输入id" />)}
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<ModalContentProps>()(ModalContent);
