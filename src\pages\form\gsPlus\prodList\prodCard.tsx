import React, { useState, useEffect } from 'react';
import api from 'api';

import { Radio, Input, Select, message, Checkbox, Button } from 'antd';

const { Option } = Select;

const platformOptions = [
  { label: 'App-安卓', value: 'and' },
  { label: 'App-iOS', value: 'ios' },
  { label: 'SDK-安卓', value: 'andsdk' },
  { label: 'SDK-iOS', value: 'iossdk' },
  { label: 'ios至尊', value: 'iossdkvip' },
  { label: '安卓至尊', value: 'andsdkvip' },
];
const userOptions = [
  { label: '默认', value: 'u0' },
  { label: '新手用户', value: 'u1' },
  { label: '次新用户', value: 'u2' },
  { label: '休眠新用户', value: 'u3' },
  { label: '流失新用户', value: 'u4' },
  { label: '成长用户', value: 'u5' },
  { label: '成熟用户', value: 'u6' },
  { label: '高净值用户', value: 'u7' },
  { label: '休眠用户', value: 'u8' },
  { label: '流失用户', value: 'u9' },
  { label: 'F类审核用户', value: 'F' },
];

interface dataProps {
  baseData: any;
  type: string;
  prodTag?: string[];
  baseIndex: number;
  handleProdData: any;
  prodList?: any[];
  ways?: string;
}

export default function(props: dataProps) {
  const { fetchFundNameByCode } = api;

  const { baseData, type, prodTag, baseIndex, handleProdData, prodList, ways } = props;

  const [showType, set_showType] = useState('0'); // 0: 开启 1: 关闭 2: 随机推荐
  const [fundCode, set_fundCode] = useState(''); // 基金代码
  const [fundName, set_fundName] = useState(''); // 基金名称
  const [timeRange, set_timeRange] = useState('year'); // 时间区间 year: 近1年 hyear: 近6个月 tmonth: 近3个月 month: 近1个月
  const [dataIndicator1, set_dataIndicator1] = useState('rate'); // 数据指标1 rate: 涨跌幅 maxDown: 最大回撤 formerWeekProbability: 过往周胜率 profitProbability: 持有盈利概率
  const [dataIndicator2, set_dataIndicator2] = useState('maxDown'); // 数据指标2 rate: 涨跌幅 maxDown: 最大回撤 formerWeekProbability: 过往周胜率 profitProbability: 持有盈利概率
  const [recommendText, set_recommendText] = useState(''); // 推荐文案
  const [platform, set_platform] = useState<any[]>([]); // 适用平台
  const [user, set_user] = useState<any[]>([]); // 用户类型
  const [tags, set_tags] = useState<any[]>([]); // 标签
  const [tab, set_tab] = useState<any[]>([]); // 系列
  const [jumpUrl, set_jumpUrl] = useState(''); // 跳转链接

  useEffect(() => {
    if (!baseData || JSON.stringify(baseData) === '{}') return;
    set_showType(baseData.showType);
    set_fundCode(baseData.fundCode);
    set_fundName(baseData.fundName);
    set_recommendText(baseData.recommendText);
    set_jumpUrl(baseData.jumpUrl);
    if (type === 'list') {
      set_tags(baseData.tags);
      set_tab(baseData.tab);
    } else {
      set_timeRange(baseData.timeRange);
      set_dataIndicator1(baseData.dataIndicator1);
      set_dataIndicator2(baseData.dataIndicator2);
      set_platform(baseData.platform);
      set_user(baseData.user);
    }
  }, [baseData]);

  /**
   * 获取基金名称
   */
  const checkFundCode = () => {
    if (fundCode === '') return;
    if (fundCode.length === 6 && !fundCode.includes(' ')) {
      fetchFundNameByCode({
        fundCode: fundCode,
      }).then((data: any) => {
        if (data.code === '0000') {
          data = data.data;
          if (data) {
            set_fundName(data.name);
          } else {
            message.error('该基金无数据，请核对基金代码');
          }
        } else {
          message.error('请求基金详情接口失败，请手动修改基金名称');
        }
      });
      if (type === 'showProd' && prodList) {
        // 获取推荐文案和跳转链接
        for (let i = 0; i < prodList.length; i++) {
          if (prodList[i].fundCode === fundCode) {
            let item = prodList[i];
            set_jumpUrl(item.jumpUrl || '');
            set_recommendText(item.recommendText || '');
            break;
          }
        }
      }
    } else {
      message.error('基金代码格式错误！');
    }
  };

  /**
   * 传递数据
   */
  const saveData = () => {
    if (!fundCode) {
      message.error('请填写基金代码');
      return;
    }
    if (!fundName) {
      message.error('请填写基金名称');
      return;
    }
    if (type === 'list') {
			if (jumpUrl && !/^https:\/\/[^\n ，]*$/.test(jumpUrl)) {
				message.error('跳转链接格式错误');
        return;
			}
      if (!tags.length) {
        message.error('请勾选所属标签');
        return;
      }
			if (!tab.length) {
				message.error('请勾选所属系列');
        return;
			}
      handleProdData(
        {
          showType,
          id: fundCode,
          fundCode,
          fundName,
          recommendText,
          jumpUrl,
          tags,
          tab,
        },
        baseIndex,
      );
    } else if (type === 'showProd') {
      if (!recommendText) {
        message.error('请前往全部产品列表填写推荐文案');
        return;
      }
      if (!platform.length) {
        message.error('请勾选适用平台');
        return;
      }
      if (!user.length) {
        message.error('请勾选用户类型');
        return;
      }
      handleProdData(
        {
          showType,
          id: fundCode,
          fundCode,
          fundName,
          recommendText,
          jumpUrl,
          timeRange,
          dataIndicator1,
          dataIndicator2,
          platform,
          user,
        },
        baseIndex,
      );
    }
  };

  return (
    <article>
      <Radio.Group
        onChange={e => {
          set_showType(e.target.value);
        }}
        value={showType}
        style={{ marginBottom: 10 }}
      >
        {type === 'showProd' ? <Radio value={'2'}>随机推荐</Radio> : null}
        <Radio value={'0'}>开启</Radio>
        <Radio value={'1'}>关闭</Radio>
      </Radio.Group>
      <div>
        <span>
          <b style={{ color: '#fe5d4e' }}>* </b> 基金代码
        </span>
        <Input
          style={{ width: 150, marginLeft: 10 }}
          value={fundCode}
          onChange={e => {
            set_fundCode(e.target.value);
          }}
          onBlur={checkFundCode}
          disabled={ways === 'edit'}
        />
      </div>
      <div style={{ marginTop: 20 }}>
        <span>
          <b style={{ color: '#fe5d4e' }}>* </b>基金名称
        </span>
        <Input
          style={{ width: 250, marginLeft: 10 }}
          value={fundName}
          onChange={e => {
            set_fundName(e.target.value);
          }}
          placeholder="输入基金代码自动显示，可手动编辑"
        />
      </div>
      {type === 'showProd' ? (
        <>
          <div style={{ marginTop: 20 }}>
            <span>
              <b style={{ color: '#fe5d4e' }}>* </b> 时间区间
            </span>
            <Select
              value={timeRange}
              onChange={set_timeRange}
              style={{ width: 120, marginLeft: 10 }}
            >
              <Option value="year">近1年</Option>
              <Option value="hYear">近6个月</Option>
              {/* <Option value="tmonth">近3个月</Option> */}
              {/* <Option value="month">近1个月</Option> */}
            </Select>
          </div>
          <div style={{ marginTop: 20 }}>
            <span>
              <b style={{ color: '#fe5d4e' }}>* </b> 数据指标1
            </span>
            <Radio.Group
              onChange={e => {
                set_dataIndicator1(e.target.value);
              }}
              value={dataIndicator1}
              style={{ marginLeft: 10 }}
            >
              <Radio value={'rate'} disabled={dataIndicator2 === 'rate'}>
                涨跌幅
              </Radio>
              <Radio value={'maxDown'} disabled={dataIndicator2 === 'maxDown'}>
                最大回撤
              </Radio>
              <Radio
                value={'formerWeekProbability'}
                disabled={dataIndicator2 === 'formerWeekProbability'}
              >
                过往周胜率
              </Radio>
              <Radio value={'profitProbability'} disabled={dataIndicator2 === 'profitProbability'}>
                持有盈利概率
              </Radio>
            </Radio.Group>
          </div>
          <div style={{ marginTop: 20 }}>
            <span>
              <b style={{ color: '#fe5d4e' }}>* </b> 数据指标2
            </span>
            <Radio.Group
              onChange={e => {
                set_dataIndicator2(e.target.value);
              }}
              value={dataIndicator2}
              style={{ marginLeft: 10 }}
            >
              <Radio value={'rate'} disabled={dataIndicator1 === 'rate'}>
                涨跌幅
              </Radio>
              <Radio value={'maxDown'} disabled={dataIndicator1 === 'maxDown'}>
                最大回撤
              </Radio>
              <Radio
                value={'formerWeekProbability'}
                disabled={dataIndicator1 === 'formerWeekProbability'}
              >
                过往周胜率
              </Radio>
              <Radio value={'profitProbability'} disabled={dataIndicator1 === 'profitProbability'}>
                持有盈利概率
              </Radio>
            </Radio.Group>
          </div>
        </>
      ) : null}
      <div style={{ marginTop: 20 }}>
        <span>推荐文案</span>
        <Input
          style={{ width: 400, marginLeft: 10 }}
          value={recommendText}
          onChange={e => {
            set_recommendText(e.target.value);
          }}
          disabled={type === 'showProd'}
        />
      </div>
      <div style={{ marginTop: 20 }}>
        <span>跳转链接</span>
        <Input
          style={{ width: 400, marginLeft: 10 }}
          value={jumpUrl}
          onChange={e => {
            set_jumpUrl(e.target.value);
          }}
          disabled={type === 'showProd'}
        />
      </div>
			<p>修改了跳转链接后，该产品如果有在 运营内容 - 大卡配置 中配置，需要去 运营内容 中重新发布下</p>
      {type === 'showProd' ? (
        <>
          <div style={{ marginTop: 20 }}>
            <div>
              <b style={{ color: '#fe5d4e' }}>* </b>适用平台
            </div>
            <Checkbox.Group
              style={{ marginTop: 10 }}
              options={platformOptions}
              value={platform}
              onChange={set_platform}
            />
          </div>
          <div style={{ marginTop: 20 }}>
            <div>
              <b style={{ color: '#fe5d4e' }}>* </b>用户类型
            </div>
            <Checkbox.Group
              style={{ marginTop: 10 }}
              options={userOptions}
              value={user}
              onChange={set_user}
            />
          </div>
        </>
      ) : null}
      {type === 'list' ? (
        <>
          <div style={{ marginTop: 20 }}>
            <div>
              <b style={{ color: '#fe5d4e' }}>* </b>标签
            </div>
            <Checkbox.Group
              style={{ marginTop: 10 }}
              options={prodTag}
              value={tags}
              onChange={set_tags}
            />
          </div>
          <div style={{ marginTop: 20 }}>
            <div>
              <b style={{ color: '#fe5d4e' }}>* </b>系列
            </div>
            <Checkbox.Group
              style={{ marginTop: 10 }}
              options={[{ label: '系列1', value: '001' }, { label: '系列2', value: '002' }]}
              value={tab}
              onChange={set_tab}
            />
          </div>
        </>
      ) : null}
      <Button style={{ marginTop: 30 }} type="primary" onClick={saveData}>
        保存编辑
      </Button>
    </article>
  );
}
