import React, { useEffect, useState, useRef } from 'react';
import { Table, Button, Modal } from 'antd';
import { ContentProps, EditContentType, FormDataProps } from './types';
import api from 'api';
import DrawerContent from './content';
import store from 'store';

const userName = store.get('name');
/** 渲染序号 */
const renderXh = (text: string, record: ContentProps, index: number) => (index + 1)
/** 渲染起始时间段 */
const renderStartAndEndTime = (text: string, record: ContentProps, index: number) => `${record.startTime} — ${record.endTime}`
/** 渲染是否复核 */
const renderReview = (text: string) => text === '0' ? '未复核' : '已复核';
/** 是否push */
const renderPush = (text: string) => text === '0' ? '否' : '是';
/** 推送至位置 */
const renderType = (text: string, record: ContentProps) => {
  if (text === '1') {
    return '财富有伴'
  }
  return record.dst;
}

const Index = () => {
  const [dataSource, setDataSource] = useState<ContentProps[]>([]);
  const drawerContentRef = useRef<any>();
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const pageSize = 20;
  const queryList = (offSet: number = current) => {
    api.fetchCompanyList({offSet, pageSize}).then((res: any) => {
      const { data, code } = res;
      if (code !== '0000') {
        Modal.error({content: '获取数据失败'});
        return;
      }
      setDataSource(data.dataList)
      setTotal(data.size);
      setCurrent(offSet);
    })
  }
  const afterEdit = (type: EditContentType) => {
    if (type === EditContentType.Add) {
      queryList(1);
    } else {
      queryList();
    }
  }
  const showDrawerContent = (editContentType: EditContentType, record?: ContentProps) => {
    if (editContentType === EditContentType.Review && record?.editor === userName) {
      Modal.error({ content: '不能复核自己创建或编辑的内容，请用其他账号尝试'})
      return;
    }
    drawerContentRef.current.showContent(editContentType, record)
  };
  /** 操作 */
  const renderOperate = (text: string, record: ContentProps) => {
    return <div>
      <Button type="primary" onClick={() => {showDrawerContent(EditContentType.Edit, record)}}>编辑</Button>
      {
        record.status === '0'
        && <Button className="g-ml10"
          type={userName === record.editor ? 'default' : 'primary'}
          onClick={() => showDrawerContent(EditContentType.Review, record)}>复核</Button>
      }
    </div>
  }
  const columns: { title: string, dataIndex?: string, key?: string, width?:string, render?: Function }[] = [
    { title: '序号', dataIndex: 'xh', key: 'xh', width: '50px', render: renderXh },
    { title: '是否复核', dataIndex: 'status', key: 'status',width: '90px', render: renderReview },
    { title: '标题', dataIndex: 'title', key: 'title'},
    { title: '起止时间段', key: 'startTime', render: renderStartAndEndTime },
    { title: '推送至位置', key: 'type', dataIndex: 'type', width:'25%', render: renderType },
    { title: '是否push', dataIndex: 'push', render: renderPush },
    { title: '操作', width: '180px', render: renderOperate }
  ]
  useEffect(queryList, []);
  const onPageChange = (page: number) => {
    queryList(page);
  }
  const pagination= {
    // showSizeChanger: true,
    showQuickJumper: true,
    total,
    current,
    pageSize,
    onChange: onPageChange,
    showTotal: (total: number) => `共${total}条数据`
  }
  return <div>
    <header>
      <h2>
        基金持仓列表陪伴&营销内容列表
        <Button type="primary" className="f-fr" onClick={() => showDrawerContent(EditContentType.Add)}>新增</Button>
      </h2>
    </header>
    <Table
      columns={columns as any}
      dataSource={dataSource}
      pagination={pagination}
    />
    <DrawerContent ref={drawerContentRef} afterEdit={afterEdit}/>
  </div>
}

export default Index;