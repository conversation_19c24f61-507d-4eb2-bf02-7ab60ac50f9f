import React from 'react';
import { Form, Radio, Card } from 'antd';
import { RadioChangeEvent } from 'antd/lib/radio';
import ProfessionalAnalysisForm from './ProfessionalAnalysisForm';
import WeeklyLiveForm from './WeeklyLiveForm';
import InvestToolForm from './InvestToolForm';
import InvestSchoolForm from './InvestSchoolForm';
import UpsAndDowns from './UpsAndDowns';

const OtherWelfareForm = props => {
  const {
    getFieldDecorator,
    getFieldValue,
    setFieldsValue,
    tabLocation,
    setTabLocation,
    showOtherWelfare,
    setShowOtherWelfare,
    analysisCardNum,
    setAnalysisCardNum,
    toolCardNum,
    setToolCardNum,
    schoolCardNum,
    setSchoolCardNum,
  } = props;

  const tabLocationArr = new Array(4);
  // 专业分析
  tabLocationArr[tabLocation.indexOf('professionalAnalysis')] = (
    <Card key="professionalAnalysis" style={{ marginBottom: '15px' }}>
      <UpsAndDowns
        feature="professionalAnalysis"
        name="专业分析"
        tabLocation={tabLocation}
        setTabLocation={setTabLocation}
      />
      <ProfessionalAnalysisForm
        getFieldDecorator={getFieldDecorator}
        getFieldValue={getFieldValue}
        setFieldsValue={setFieldsValue}
        analysisCardNum={analysisCardNum}
        setAnalysisCardNum={setAnalysisCardNum}
      />
    </Card>
  );
  // 每周直播
  tabLocationArr[tabLocation.indexOf('weeklyLive')] = (
    <Card key="weeklyLive" style={{ marginBottom: '15px' }}>
      <UpsAndDowns
        feature="weeklyLive"
        name="每周直播"
        tabLocation={tabLocation}
        setTabLocation={setTabLocation}
      />
      <WeeklyLiveForm
        getFieldDecorator={getFieldDecorator}
        getFieldValue={getFieldValue}
        setFieldsValue={setFieldsValue}
      />
    </Card>
  );
  // 投资工具
  tabLocationArr[tabLocation.indexOf('investmentTool')] = (
    <Card key="investmentTool" style={{ marginBottom: '15px' }}>
      <UpsAndDowns
        feature="investmentTool"
        name="投资工具"
        tabLocation={tabLocation}
        setTabLocation={setTabLocation}
      />
      <InvestToolForm
        getFieldDecorator={getFieldDecorator}
        toolCardNum={toolCardNum}
        setToolCardNum={setToolCardNum}
      />
    </Card>
  );
  // 投资学堂
  tabLocationArr[tabLocation.indexOf('investmentSchool')] = (
    <Card key="investmentSchool" style={{ marginBottom: '15px' }}>
      <UpsAndDowns
        feature="investmentSchool"
        name="投资学堂"
        tabLocation={tabLocation}
        setTabLocation={setTabLocation}
      />
      <InvestSchoolForm
        getFieldDecorator={getFieldDecorator}
        schoolCardNum={schoolCardNum}
        setSchoolCardNum={setSchoolCardNum}
      />
    </Card>
  );

  const handleRadioChange = (e: RadioChangeEvent) => {
    if (e.target.value === '0') {
      setShowOtherWelfare(false);
    } else {
      setShowOtherWelfare(true);
    }
  };
  return (
    <>
      <Form.Item label="是否展示其他福利区">
        {getFieldDecorator('otherWelfare.isShow', {
          rules: [{ required: true, message: '请选择是否展示其他福利区' }],
        })(
          <Radio.Group onChange={handleRadioChange}>
            <Radio value="1">是</Radio>
            <Radio value="0">否</Radio>
          </Radio.Group>,
        )}
      </Form.Item>
      {showOtherWelfare ? tabLocationArr : null}
    </>
  );
};

export default OtherWelfareForm;
