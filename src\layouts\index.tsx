import React, {
  PureComponent,
  //useEffect
} from 'react';
import { BackTop, Layout, Drawer, Icon, Switch } from 'antd'
import Loading from 'components/Loading'
import { 
  enquireScreen,
  unenquireScreen,
} from 'functions/enquire'
import { connect } from 'dva'
import { Redirect } from 'umi'
import SiderMenu from 'components/Menu'
import Page from 'components/Page'
import classnames from 'classnames'
import Bread from 'components/Bread'
import Header from 'components/Header'
import Footer from 'components/Footer'
// import { ConnectState } from 'types/connect'
import store from 'store'
import { title, footer } from 'config'


const { Content, Sider } = Layout;

const _getCtnDom = () => (document.querySelector('.m-page_ctn'));


class PrimaryLayout extends PureComponent<any, any> {
  state = {
    isMobile: false,
    collapsed: false,
  };

  enquireHandler: any;

  componentDidMount() {
    let {
      dispatch,
    } = this.props;
    let _account = store.get('account');
    if (_account) {
      dispatch({
        type: 'app/fetchUser',
        userid: _account,
        name: store.get('name')
      });
    } else {
      dispatch({
        type: 'app/saveCurrentUser'
      })
    }

    this.enquireHandler = enquireScreen((mobile: boolean) => {
      const { isMobile } = this.state
      if (isMobile !== mobile) {
        this.setState({
          isMobile: mobile,
        })
      }
    })
  }

  componentWillUnmount() {
    unenquireScreen(this.enquireHandler)
  }

  handleCollapsed = () => {
    this.setState({
      collapsed: !this.state.collapsed
    })
  }

  handleThemeChange = () => {
    this.props.dispatch({
      type: 'app/triggerTheme'
    });
  }

  render () {
    const { 
      isMobile,
      collapsed,
    } = this.state;

    const {
      loading,
      userinfo,
      theme,
      routeList,
      pageLoading
    } = this.props;

    if (loading) {
      return <Loading spinning={true} theme={theme}/>
    }
    
    if (!userinfo || !userinfo.userid) {
      return (<Redirect to="/login" />)
    }


    return (
      <Layout
        s-theme={theme}
      >
        {
          isMobile ? (<Drawer 
            maskClosable={true}
            closable={false}
            visible={collapsed}
            onClose={this.handleCollapsed}
            placement="left"
          >
            <SiderMenu
              routeList={routeList}
              theme={theme}
              isMobile={isMobile}
            />
          </Drawer>) : (
          <Sider
            theme={theme}
            collapsed={collapsed}
          >
            <div className="m-logo u-c-middle">
                <img alt={'logo'} src={require('../../public/logo.png')} />
                {!collapsed && <span s-word="1">{title}</span>}
            </div>
            <div className="u-h80v f-scroll_y">
            <SiderMenu
              routeList={routeList}
              theme={theme}
              isMobile={isMobile}
	      collapsed={collapsed}
            />
            </div>
            <div className="u-c-middle u-pt20">
              <span className="u-pr20">切换主题</span>
              <Switch
                  checkedChildren="黑" unCheckedChildren="白"
                  onChange={this.handleThemeChange}
                  defaultChecked={theme === 'dark'}
              />
            </div>
          </Sider>)
        }
        
        <Layout>
          <Header
            collapsed={collapsed}
            name={userinfo.name}
            handleCollapsed={this.handleCollapsed}
            userinfo={userinfo}
          />
          <Content className="m-page_ctn">
            <BackTop
              target={_getCtnDom}
            />
            <Loading spinning={pageLoading}/>
            <section className="m-main">
              {routeList && (<Bread
                routeList={routeList}
              />)}
              <Page>
                {this.props.children}
              </Page>
            </section>
            <Footer>{footer}</Footer>
          </Content>
        </Layout>
      </Layout>
    )
  }
}

export default connect(({ app, permission }: any) => ({
  userinfo: app.userinfo,
  loading: app.loading,
  pageLoading: app.pageLoading,
  theme: app.theme,
  routeList: permission.routers,
}))(PrimaryLayout);
