import React, { useState, useEffect } from "react";
import FormRender from 'form-render/lib/antd';
import axios from 'axios'
import api from 'api';
import { Button, message } from 'antd'

const {fetchClientSearch, postClientSearch} = api;

let FORM_CONFIG: any = {
    propsSchema: {
        title: '客户端搜索配置运营位',
        type: 'array',
        "minItems": 4,
        "maxItems": 4,
        "uniqueItems": true,
        "items": {
            "type": "object",
            required: ['title', 'icon', 'url', 'taid'],
            properties: {
                icon: {
                    title: 'icon地址(必填,https图片地址)',
                    type: 'string',
                },
                title: {
                    title: '文案(必填,1~7个字符)',
                    type: 'string',
                    maxLength: 7,
                },
                url: {
                    title: '跳转地址(必填,https地址)',
                    type: 'string',
                },
				taid: {
                    title: '埋点字段(必填)',
                    type: 'string',
                }
            },
        }
    }
};

// 
export default function () {
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState({});
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    
    useEffect(() => {
        fetchClientSearch().then((res: any) => {
            try {
                res = JSON.parse(res.data || '[]');
                if (res) {
                    setData(res);
                }
            } catch (e) {
                console.warn(e)
            }
            
            setInit(true);
            setFormConfig(FORM_CONFIG);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [init]);

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            let _postData = formData;
			if (_postData.length < 4) return message.error(`配置数量不得少于4个`);
			else if (!_postData.every(item => item.icon && item.title && item.url && item.taid)) return message.error(`缺少必要参数`);
            postClientSearch({
                value: JSON.stringify(_postData)
            }).then((res: any) => {
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('发布成功！')
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        }
    };

    if (!init) return '加载中'
    return (
        <div style={{ padding: 60 }}>
        <FormRender
            propsSchema={FORM_CONFIG.propsSchema}
            formData={formData}
            onChange={setData}
            onValidate={setValid}
            showDescIcon={true}
        />
        <Button type="primary" onClick={onSubmit}>提交</Button>
        </div>
    );
}
