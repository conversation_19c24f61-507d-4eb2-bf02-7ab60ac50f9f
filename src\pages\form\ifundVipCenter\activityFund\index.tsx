import React, { useState, useEffect } from 'react';
import api from 'api';
import { Table, <PERSON><PERSON>, PageHeader, Divider, Modal, DatePicker, Input, message, Popconfirm, Spin, Collapse } from 'antd';
import moment from 'moment';
import FundCard from './fundCard';

export default function() {

	const columns = [{
		title: '时间',
		dataIndex: 'date',
		key: 'date'
	}, {
		title: '活动id',
		dataIndex: 'activityId',
		key: 'activityId'
	}, {
		title: '红包名称',
		dataIndex: 'title',
		key: 'title'
	}, {
		title: '编辑人',
		dataIndex: 'operator',
		key: 'operator'
	}, {
		title: '操作',
		dataIndex: 'btns',
		key: 'btns',
		render: (item: any, record: any, index: number) => {
			return (
				<>
					<Button onClick={() => {showAddModal('modify', record, index)}} style={{marginRight: 10}} type="primary">编辑</Button>
					<Popconfirm
						title="是否确定删除"
						onConfirm={() => {deleteActivity(index)}}
						okText="确定"
						cancelText="取消"
					>
						<Button type="danger">删除</Button>
					</Popconfirm>
				</>
			)
		}
	}]

	const columns2 = [{
		title: '时间',
		dataIndex: 'date',
		key: 'date'
	}, {
		title: '活动id',
		dataIndex: 'activityId',
		key: 'activityId'
	}, {
		title: '编辑人',
		dataIndex: 'operator',
		key: 'operator'
	}, {
		title: '操作',
		dataIndex: 'btns',
		key: 'btns',
		render: (item: any, record: any, index: number) => {
			return (
				<>
					<Button onClick={() => {showAddModal2('modify', record, index)}} style={{marginRight: 10}} type="primary">编辑</Button>
					<Popconfirm
						title="是否确定删除"
						onConfirm={() => {deleteActivity2(index)}}
						okText="确定"
						cancelText="取消"
					>
						<Button type="danger">删除</Button>
					</Popconfirm>
				</>
			)
		}
	}]

	const columns3 = [{
		title: '时间',
		dataIndex: 'date',
		key: 'date'
	}, {
		title: '活动id',
		dataIndex: 'activityId',
		key: 'activityId'
	}, {
		title: '编辑人',
		dataIndex: 'operator',
		key: 'operator'
	}, {
		title: '操作',
		dataIndex: 'btns',
		key: 'btns',
		render: (item: any, record: any, index: number) => {
			return (
				<>
					<Button onClick={() => {showAddModal3('modify', record, index)}} style={{marginRight: 10}} type="primary">编辑</Button>
					<Popconfirm
						title="是否确定删除"
						onConfirm={() => {deleteActivity3(index)}}
						okText="确定"
						cancelText="取消"
					>
						<Button type="danger">删除</Button>
					</Popconfirm>
				</>
			)
		}
	}]

	const { fetchIFundVipCenterActivityFund, postIFundVipCenterActivityFund } = api;

	const [init, set_init] = useState(false);

	// 新增红包逻辑
	const [isModalVisible, set_isModalVisible] = useState(false);
	const [activityId, set_activityId] = useState('');
	const [activityTime, set_activityTime] = useState('');
	const [activityTitle, set_activityTitle] = useState('');
	// 修改红包逻辑
	const [handleType, set_handleType] = useState('add');
	const [handleIndex, set_handleIndex] = useState(-1);

	// 会员红包配置
	const [activityList, set_activityList] = useState<any[]>([]);

	// 会员产品配置
	const [fundList, set_fundList] = useState<any[]>([{}, {}, {}]);

	// 新增月份0折券逻辑
	const [isModalVisible2, set_isModalVisible2] = useState(false);
	const [activityId2, set_activityId2] = useState('');
	const [activityTime2, set_activityTime2] = useState('');
	// 修改月份0折券逻辑
	const [handleType2, set_handleType2] = useState('add');
	const [handleIndex2, set_handleIndex2] = useState(-1);

	// 会员月份0折券配置
	const [activityList2, set_activityList2] = useState<any[]>([]);

	// 新增年份0折券逻辑
	const [isModalVisible3, set_isModalVisible3] = useState(false);
	const [activityId3, set_activityId3] = useState('');
	const [activityTime3, set_activityTime3] = useState('');
	const [activityTime3_isOpen, set_activityTime3_isOpen] = useState(false);
	// 修改年份0折券逻辑
	const [handleType3, set_handleType3] = useState('add');
	const [handleIndex3, set_handleIndex3] = useState(-1);

	// 会员年份0折券配置
	const [activityList3, set_activityList3] = useState<any[]>([]);

  /**
   * 获取数据
   */
  const fetchData = () => {
    fetchIFundVipCenterActivityFund().then((res: any) => {
			try {
				res = JSON.parse(res.data);
				if (res) {
					set_activityList(res.packetList);
					set_fundList(res.fundList);
					set_activityList2(res.discountMonthList);
					set_activityList3(res.discountYearList);
				}
			} catch (e) {
				console.warn(e)
			}
			set_init(true);
	}).catch((e: Error) => {
			message.error(e.message);
	})
  };

	/**
	 * 打开 新增/编辑 红包配置弹窗
	 */
	const showAddModal = (type = 'add', record: any = {}, index = -1) => {
		if (type === 'modify') {
			set_activityId(record.activityId);
			set_activityTime(record.date);
			set_activityTitle(record.title);
			set_handleIndex(index);
			set_handleType('modify');
		} else {
			set_handleType('add');
		}
		set_isModalVisible(true);
	}

	/**
	 * 取消新增红包配置
	 */
	const cancelAddModal = () => {
		set_isModalVisible(false);
		set_activityId('');
		set_activityTitle('');
		set_activityTime('');
		set_handleIndex(-1);
	}

	/**
	 * 新增/编辑 红包活动
	 */
	const addActivity = () => {
		let addItem = {
			date: activityTime,
			activityId: activityId,
			title: activityTitle,
			operator: localStorage.name
		}
		if (!activityTime) {
			message.error('请选择时间');
			return;
		}
		if (!activityId) {
			message.error('请填写活动id');
			return;
		} else if (activityId.includes(' ')) {
			message.error('活动id不可有空格');
			return;
		}
		if (!activityTitle) {
			message.error('请填写红包名称');
			return;
		}
		let _activityList = [...activityList];
		for (let i = 0; i < _activityList.length; i++) {
			if (handleIndex === i && handleType === 'modify') continue;
			if (_activityList[i].activityId === activityId) {
				message.error('活动id不可重复');
				return;
			}
			if (_activityList[i].date === activityTime) {
			 	message.error('时间不可重复');
				return;
			}
		}
		if (handleType === 'add') {
			_activityList.push(addItem);
		} else {
			_activityList[handleIndex] = addItem;
		}
		set_activityList(_activityList);
		cancelAddModal();
	}

	/**
	 * 删除红包配置
	 */
	const deleteActivity = (index: number) => {
		let _activityList = [...activityList];
		_activityList.splice(index, 1);
		set_activityList(_activityList);
	}

	/**
	 * 改变产品数据
	 */
	const changeFundData = (data: any, index: number) => {
		let _fundList = [...fundList];
		_fundList[index] = data;
		set_fundList(_fundList);
	}

	/**
	 * 打开 新增/编辑 月份0折券配置弹窗
	 */
	 const showAddModal2 = (type = 'add', record: any = {}, index = -1) => {
		if (type === 'modify') {
			set_activityId2(record.activityId);
			set_activityTime2(record.date);
			set_handleIndex2(index);
			set_handleType2('modify');
		} else {
			set_handleType2('add');
		}
		set_isModalVisible2(true);
	}

	/**
	 * 取消新增月份0折券配置
	 */
	const cancelAddModal2 = () => {
		set_isModalVisible2(false);
		set_activityId2('');
		set_activityTime2('');
		set_handleIndex2(-1);
	}

	/**
	 * 新增/编辑 月份0折券活动
	 */
	const addActivity2 = () => {
		let addItem = {
			date: activityTime2,
			activityId: activityId2,
			operator: localStorage.name
		}
		if (!activityTime2) {
			message.error('请选择时间');
			return;
		}
		if (!activityId2) {
			message.error('请填写活动id');
			return;
		} else if (activityId2.includes(' ')) {
			message.error('活动id不可有空格');
			return;
		}
		let _activityList = [...activityList2];
		for (let i = 0; i < _activityList.length; i++) {
			if (handleIndex2 === i && handleType2 === 'modify') continue;
			if (_activityList[i].activityId === activityId2) {
				message.error('活动id不可重复');
				return;
			}
			if (_activityList[i].date === activityTime2) {
			 	message.error('时间不可重复');
				return;
			}
		}
		if (handleType2 === 'add') {
			_activityList.push(addItem);
		} else {
			_activityList[handleIndex2] = addItem;
		}
		set_activityList2(_activityList);
		cancelAddModal2();
	}

	/**
	 * 删除月份0折券配置
	 */
	const deleteActivity2 = (index: number) => {
		let _activityList = [...activityList2];
		_activityList.splice(index, 1);
		set_activityList2(_activityList);
	}

	/**
	 * 打开 新增/编辑 月份0折券配置弹窗
	 */
	 const showAddModal3 = (type = 'add', record: any = {}, index = -1) => {
		if (type === 'modify') {
			set_activityId3(record.activityId);
			set_activityTime3(record.date);
			set_handleIndex3(index);
			set_handleType3('modify');
		} else {
			set_handleType3('add');
		}
		set_isModalVisible3(true);
	}

	/**
	 * 取消新增月份0折券配置
	 */
	const cancelAddModal3 = () => {
		set_isModalVisible3(false);
		set_activityId3('');
		set_activityTime3('');
		set_handleIndex3(-1);
	}

	/**
	 * 新增/编辑 月份0折券活动
	 */
	const addActivity3 = () => {
		let addItem = {
			date: activityTime3,
			activityId: activityId3,
			operator: localStorage.name
		}
		if (!activityTime3) {
			message.error('请选择时间');
			return;
		}
		if (!activityId3) {
			message.error('请填写活动id');
			return;
		} else if (activityId3.includes(' ')) {
			message.error('活动id不可有空格');
			return;
		}
		let _activityList = [...activityList3];
		for (let i = 0; i < _activityList.length; i++) {
			if (handleIndex3 === i && handleType3 === 'modify') continue;
			if (_activityList[i].activityId === activityId3) {
				message.error('活动id不可重复');
				return;
			}
			if (_activityList[i].date === activityTime2) {
			 	message.error('时间不可重复');
				return;
			}
		}
		if (handleType3 === 'add') {
			_activityList.push(addItem);
		} else {
			_activityList[handleIndex3] = addItem;
		}
		set_activityList3(_activityList);
		cancelAddModal3();
	}

	/**
	 * 删除月份0折券配置
	 */
	const deleteActivity3 = (index: number) => {
		let _activityList = [...activityList3];
		_activityList.splice(index, 1);
		set_activityList3(_activityList);
	}

	/**
	 * 发布
	 */
	const onSubmit = () => {
		// 检查产品配置
		for (let i = 0; i < fundList.length; i++) {
			const { fundCode, fundName, fundDesc, imgUrl, jumpUrl, thsJumpUrl } = fundList[i];
			if(!fundCode) {
				message.error(`产品${i + 1}：请填写基金代码`);
				return;
			}
			if (fundCode.length !== 6 || fundCode.includes(' ')) {
				message.error(`产品${i + 1}：基金代码格式错误`);
				return;
			}
			if(!fundName) {
				message.error(`产品${i + 1}：请填写基金名称`);
				return;
			}
			if(!fundDesc) {
				message.error(`产品${i + 1}：请填写基金描述`);
				return;
			}
			if(!imgUrl) {
				message.error(`产品${i + 1}：请填写走势图链接`);
				return;
			}
			if (imgUrl && !/^https:\/\/[^\n ，]*$/.test(imgUrl)) {
				message.error(`产品${i + 1}：走势图链接格式错误`);
				return;
			}
			if (jumpUrl && !/^https:\/\/[^\n ，]*$/.test(jumpUrl)) {
				message.error(`产品${i + 1}：基金跳转链接格式错误`);
				return;
			}
			if (thsJumpUrl && !/^https:\/\/[^\n ，]*$/.test(thsJumpUrl)) {
				message.error(`产品${i + 1}：手炒跳转链接格式错误`);
				return;
			}
		}
		let postData = {
			packetList: activityList,
			discountMonthList: activityList2,
			discountYearList: activityList3,
			fundList: fundList
		};
		postIFundVipCenterActivityFund({
			value: JSON.stringify(postData)
		}).then((res: any) => {
			try {
				if (res.code !== '0000') {
					message.error(res.message);
				} else {
					message.success('发布成功！');
					setTimeout(() => {location.reload()}, 500)
				}
			} catch (e) {
				message.error(e.message);
			}
		})
	}

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <Spin spinning={!init}>
			<PageHeader title="会员红包配置" />
			<Table columns={columns} dataSource={activityList} rowKey="activityId" />
			<Button onClick={() => {showAddModal()}} style={{marginTop: 10}} type="primary">新增</Button>
			<Modal
				title={handleType === 'add' ? "新增红包配置" : "修改红包配置"}
				visible={isModalVisible}
				okText="确定"
				cancelText="取消"
				onOk={addActivity}
				onCancel={cancelAddModal}
			>
				<div>
					<span>时间<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<DatePicker.MonthPicker
						placeholder="请选择月份"
						value={activityTime ? moment(activityTime, 'YYYY-MM') : null}
						onChange={(date, dateString) => {set_activityTime(dateString)}}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>活动id<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input 
						style={{width: 200}}
						placeholder="请输入配置好的活动id"
						value={activityId}
						onChange={(e) => {set_activityId(e.target.value)}}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>红包名称<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input 
						style={{width: 200}}
						placeholder="请输入红包名称"
						value={activityTitle}
						onChange={(e) => {set_activityTitle(e.target.value)}}
					/>
				</div>
			</Modal>
			<Divider />
			<PageHeader title="会员产品配置" />
			<Collapse defaultActiveKey={['fund1', 'fund2', 'fund3']}>
				<Collapse.Panel header="产品1" key="fund1">
					<FundCard 
						baseData={fundList[0]}
						saveData={(data: any) => {changeFundData(data, 0)}}
					/>
				</Collapse.Panel>
				<Collapse.Panel header="产品2" key="fund2">
					<FundCard 
						baseData={fundList[1]}
						saveData={(data: any) => {changeFundData(data, 1)}}
					/>
				</Collapse.Panel>
				<Collapse.Panel header="产品3" key="fund3">
					<FundCard 
						baseData={fundList[2]}
						saveData={(data: any) => {changeFundData(data, 2)}}
					/>
				</Collapse.Panel>
			</Collapse>
			<Divider />
			<PageHeader title="会员0折券月配置" />
			<Table columns={columns2} dataSource={activityList2} rowKey="activityId" />
			<Button onClick={() => {showAddModal2()}} style={{marginTop: 10}} type="primary">新增</Button>
			<Modal
				title={handleType2 === 'add' ? "新增月份0折券配置" : "修改月份0折券配置"}
				visible={isModalVisible2}
				okText="确定"
				cancelText="取消"
				onOk={addActivity2}
				onCancel={cancelAddModal2}
			>
				<div>
					<span>时间<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<DatePicker.MonthPicker
						placeholder="请选择月份"
						value={activityTime2 ? moment(activityTime2, 'YYYY-MM') : null}
						onChange={(date, dateString) => {set_activityTime2(dateString)}}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>活动id<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input 
						style={{width: 200}}
						placeholder="请输入配置好的活动id"
						value={activityId2}
						onChange={(e) => {set_activityId2(e.target.value)}}
					/>
				</div>
			</Modal>
			<Divider />
			<PageHeader title="会员0折券年配置" />
			<Table columns={columns3} dataSource={activityList3} rowKey="activityId" />
			<Button onClick={() => {showAddModal3()}} style={{marginTop: 10}} type="primary">新增</Button>
			<Modal
				title={handleType3 === 'add' ? "新增年份0折券配置" : "修改年份0折券配置"}
				visible={isModalVisible3}
				okText="确定"
				cancelText="取消"
				onOk={addActivity3}
				onCancel={cancelAddModal3}
			>
				<div>
					<span>时间<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<DatePicker
						mode="year"
						placeholder="请选择年份"
						format="YYYY"
						value={activityTime3 ? moment(activityTime3, 'YYYY') : null}
						open={activityTime3_isOpen}
						onOpenChange={(status) => {
							if (status) {
								set_activityTime3_isOpen(true);
							} else {
								set_activityTime3_isOpen(false);
							}
						}}
						onPanelChange={(v) => {
							set_activityTime3_isOpen(false);
							set_activityTime3(moment(v).format('YYYY'));
						}}
						onChange={(date, dateString) => {set_activityTime3('')}}
					/>
				</div>
				<div style={{marginTop: 10}}>
					<span>活动id<i style={{color: '#fe5d4e'}}> *</i>：</span>
					<Input 
						style={{width: 200}}
						placeholder="请输入配置好的活动id"
						value={activityId3}
						onChange={(e) => {set_activityId3(e.target.value)}}
					/>
				</div>
			</Modal>
			<Divider />
			<Popconfirm
				title="发布后将修改线上配置"
				onConfirm={onSubmit}
				okText="确定"
				cancelText="取消"
			>
				<Button style={{marginTop: 10}} type="primary">发布</Button>
			</Popconfirm>
		</Spin>
  );
}
