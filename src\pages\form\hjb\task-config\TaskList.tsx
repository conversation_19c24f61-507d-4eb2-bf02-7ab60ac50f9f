/**
 * 黄金宝 - 任务配置 - 任务列表
 */
import React, { useState, useEffect } from 'react';
import { Checkbox, Typography, Button, Divider, Empty } from 'antd';
import styles from './index.less';
import clsx from 'classnames';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import TaskListItem from './TaskListItem';

type Props = {
  tasks: { [k: string]: unknown }[];
  onReorderTasks: (fromIndex: number, toIndex: number) => void;
  submitting: boolean;
  onToggle: (ids: string[]) => void;
  onDelete: (id: string) => void;
  onEdit: (id: string) => void;
  onSync: () => void;
  syncing: boolean;
};

//
const TaskList: React.FunctionComponent<Props> = ({
  tasks,
  onReorderTasks,
  submitting,
  onToggle,
  onEdit,
  onDelete,
  onSync,
  syncing,
}) => {
  //
  const [checked, setChecked] = useState([]);

  const handleToggleChecked = (ids: string[]) => {
    const updated = checked.slice();

    ids.forEach(id => {
      const index = updated.indexOf(id);

      if (~index) {
        updated.splice(index, 1);
      } else {
        updated.push(id);
      }
    });

    setChecked(updated);
  };

  const handleToggleAll = () => {
    if (checked.length !== tasks.length) {
      setChecked(tasks.map(task => task._id));
    } else {
      setChecked([]);
    }
  };

  useEffect(() => {
    setChecked([]);
  }, [tasks.length]);

  // prettier-ignore
  return (
    <div className={ styles['tasks'] }>
      <Typography.Title level={ 4 } align="center">任务列表</Typography.Title>
      <nav className={ styles['task-list-nav'] }>
        <Button type="primary" disabled={ syncing || submitting } onClick={ () => onEdit() }>新增</Button>
        <Button type="primary" disabled={ syncing || submitting } disabled={ !checked.length } onClick={ () => onToggle(checked) }>上线/下线</Button>
        <Divider type="vertical" />
        <Button type="danger" onClick={ onSync } loading={ syncing }>立即同步</Button>
      </nav>
      <main className={ styles['task-list-container'] }>
        <header className={ clsx(styles['task-list-header'], styles['task-list-row']) }>
          <div className={ styles['task-list-column'] }>
            <Checkbox checked={ checked.length === tasks.length } onChange={ handleToggleAll } />
          </div>
          <div className={ styles['task-list-column'] }>任务id</div>
          <div className={ styles['task-list-column'] }>任务名称</div>
          <div className={ styles['task-list-column'] }>任务类型</div>
          <div className={ styles['task-list-column'] }>周期内可重复完成次数</div>
          <div className={ styles['task-list-column'] }>任务条件</div>
          <div className={ styles['task-list-column'] }>任务奖励</div>
          <div className={ styles['task-list-column'] }>操作时间</div>
          <div className={ styles['task-list-column'] }></div>
        </header>
        <DragDropContext
          onDragEnd={ (...rest) => onReorderTasks(rest[0].source.index, rest[0].destination.index) }
        >
          <Droppable droppableId="task-list">
            {
              (provided, snapshot) => (
                <main
                  ref={ provided.innerRef }
                  { ...provided.droppableProps }
                  className={ styles['task-list-body'] }
                >
                  {
                    !tasks.length && <Empty image={ Empty.PRESENTED_IMAGE_SIMPLE } description="还没有配置任务哦" onClick={ () => onEdit() }/>
                  }
                  {
                    tasks.map((task, index) =>
                      <Draggable key={ task._id } draggableId={ task._id } index={ index }>
                        {
                          (provided, snapshot) => (
                            <TaskListItem
                              provided={ provided }
                              data={ task }
                              onToggle={ onToggle }
                              checked={ checked.includes(task._id) }
                              onChecked={ handleToggleChecked }
                              onEdit={ onEdit }
                              onDelete={ onDelete }
                            />
                          )
                        }
                      </Draggable>
                    )
                  }
                </main>
              )
            }
          </Droppable>
        </DragDropContext>
      </main>
    </div>
  );
};

export default TaskList;
