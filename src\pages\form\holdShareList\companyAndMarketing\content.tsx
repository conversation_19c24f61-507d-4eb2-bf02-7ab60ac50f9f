import React, { useRef, useState, useImperativeHandle, memo } from 'react';
import { But<PERSON>, Drawer, Modal, message } from 'antd';
import FormRender from 'form-render/lib/antd';
import { EditContentType, ContentProps, FormDataProps, EnumType, EnumPush } from './types';
import api from 'api';
import FORM_JSON from './form.json';
import store from 'store';
const titleMap = {
  [EditContentType.Add]: '新增',
  [EditContentType.Edit]: '编辑',
  [EditContentType.Review]: '复核',
}
enum EnumQueryType {
  Query = 'query',
  Update = 'update',
  Approve = 'approve'
}

/** 开始结束时间最小1小时 */
const minOneHour = (start: string, end: string) => {
  const startTime = +new Date(start.replace(/-/g, '/'));
  const endTime = +new Date(end.replace(/-/g, '/'));
  if (endTime - startTime < 1000 * 60 * 60) {
    return false;
  }
  return true;
}
const DrawerContent = (props: any, ref: any) => {
  // 数据
  const [formData, setFormData] = useState<FormDataProps>(FORM_JSON.formData);
  // 显示
  const [visibleDrawer, setVisibleDrawer] = useState(false);
  // 标题
  const [drawerTitle, setDrawerTitle] = useState('');
  // 编辑类型
  const [editContentType, setEditContentType] = useState(EditContentType.Add);
  // 教研
  const [valid, setValid] = useState([]);
  const oldContentRef = useRef<any>();
  /** 回写数据前 */
  const beforeSetFormData = (formData: FormDataProps) => {
    const { companyData } = formData;
    // 财富有伴是否推送为否
    if (companyData.type === EnumType.Wealth) {
      companyData.push = EnumPush.No;
    }
    setFormData(formData);
  }
  const postCompanyContent = ({type, data}: {
    type: EnumQueryType,
    data?: ContentProps
  }) => {
    const postData = {
      type,
      value: '',
      lastEditor: ''
    };
    switch (type) {
      case EnumQueryType.Query:
        postData.value = JSON.stringify({
          id: oldContentRef.current.id
        });
        postData.lastEditor = oldContentRef.current.editor;
        break;
      case EnumQueryType.Update:
        postData.value = JSON.stringify({
          ...data,
          status: undefined, // status 置空，由后端判断
          id: editContentType === EditContentType.Edit && oldContentRef.current.id || undefined
        });

        postData.lastEditor = store.get('name') || '';
        break;
      case EnumQueryType.Approve:
        postData.value = JSON.stringify({
          ...oldContentRef.current,
          status: undefined // status 置空，由后端判断
        });
        postData.lastEditor = store.get('name') || '';
      default:
        break;
    }
    return api.postCompanyContent(postData);
  }
  /** 保存 */
  const save = () => {
    if (valid.length) {
      Modal.error({
        title: '提示',
        content: '数据校验未通过'
      })
      return;
    }
    const submitData = {
      ...formData.companyData
    }
    if (!/^[\u4e00-\u9fa5]+$/i.test(submitData.label) || submitData.label.length > 6) {
      Modal.error({content: '标签只能输入汉字，且最多6个'})
      return;
    }
    if (submitData.endTime < submitData.startTime) {
      return Modal.error({content: '起始时间不能大于结束时间'})
    }
    if (!minOneHour(submitData.startTime, submitData.endTime)) {
      return Modal.error({content: '起始时间,结束时间间隔至少一个小时'});
    }
    if (submitData.type === EnumType.Wealth) {
      submitData.dst = '';
    } else {
      if (!submitData.dst) {
        return Modal.error({ content: '数据校验未通过'})
      }
      // 校验基金代码
      const splitDst = submitData.dst.split(',');
      const dstObj: { [propName: string]: boolean } = {};
      for (let i = 0; i < splitDst.length; i++) {
        const dst = splitDst[i];
        // if (!dst) {
        //   return Modal.error({content: '基金代码中间不留空'})
        // }
        if (dstObj[dst]) {
          return Modal.error({content: `存在重复基金代码${dst}`})
        }
        dstObj[dst] = true;
      }
    }
    if (submitData.push === EnumPush.No) {
      submitData.pushTime = '';
    } else if (!submitData.pushTime) {
      return Modal.error({ content: '数据校验未通过'})
    }
    Modal.confirm({
      title: '提示',
      content: '是否提交',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        postCompanyContent({ type: EnumQueryType.Update, data: submitData }).then((res: any) => {
          if (res.code === '0000') {
            message.success('提交成功');
            props.afterEdit(editContentType);
            setVisibleDrawer(false);
          }
        });
      }
    });
  }
  const handleReview = () => {
    if (oldContentRef.current.editor === store.get('name')) {
      Modal.error({content: '您不能复核自己的数据'})
      return;
    }
    Modal.confirm({
      title: '提示',
      content: '是否复核通过并生效',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        postCompanyContent({ type: EnumQueryType.Approve }).then((res: any) => {
          if (res.code === '0000') {
            message.success('复核通过');
            props.afterEdit(editContentType);
            setVisibleDrawer(false);
          }
        });
      }
    });
  }
  const showContent = (editContentType: EditContentType, record?: ContentProps) => {
    if (editContentType === EditContentType.Add) {
      // 设置标题
      setDrawerTitle(titleMap[editContentType]);
      // 设置编辑类型
      setEditContentType(editContentType);
      // 赋值数据
      setFormData(FORM_JSON.formData);
      // 显示
      setVisibleDrawer(true);
      return;
    }
    oldContentRef.current = record;
    postCompanyContent({ type: EnumQueryType.Query }).then((res: any) => {
      if (res.code !== '0000') {
        return;
      }
      const { data } = res;
      const formData = {
        companyData: {
          ...data
        },
        config: {
          disabledForm: false
        }
      }
      if (editContentType === EditContentType.Review) {
        formData.config.disabledForm = true;
      }
      // 设置标题
      setDrawerTitle(titleMap[editContentType]);
      // 设置编辑类型
      setEditContentType(editContentType);
      // 赋值数据
      setFormData(formData || FORM_JSON.formData);
      // 显示
      setVisibleDrawer(true);
    })
  }
  useImperativeHandle(ref, () => ({
    showContent
  }))
  const closeDrawer = () => setVisibleDrawer(false);
  return <Drawer
    width={800}
    title={drawerTitle}
    placement="right"
    closable={false}
    onClose={closeDrawer}
    visible={visibleDrawer}
  >
    <FormRender
      propsSchema={FORM_JSON.schema}
      formData={formData}
      onChange={beforeSetFormData}
      onValidate={setValid}
      displayType="row"
      showDescIcon={true}
    />
    <div className="f-tc">
      <Button onClick={closeDrawer}>返回</Button>
      {
        EditContentType.Review === editContentType
        ? <Button type="primary" className="g-ml10" onClick={handleReview}>复核通过并生效</Button>
        : <Button type="primary" className="g-ml10" onClick={save}>提交</Button>
      }
    </div>
  </Drawer>
}

export default memo(React.forwardRef(DrawerContent));
