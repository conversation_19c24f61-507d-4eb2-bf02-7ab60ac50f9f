import React, { useEffect, useState } from 'react';
import { Modal, Table, message, Input, Button } from  'antd';

import api from 'api'
const {
    fetchAllGoodArticles,
    fetchFundGoodArticles
} = api

export default React.memo(function({
    isShowArticle,
    isShow,
    setIsShow,
    articles,
    setArticles,
    setArticleValue,
    setIsShowConfigArticle,
    setArticleType,
    type,
    code
}: {
    isShowArticle: boolean
    isShow: boolean
    setIsShow: Function
    articles: article[]
    setArticles: Function
    setIsShowConfigArticle: Function
    setArticleValue: Function
    setArticleType?: Function
    type: string
    modifiedData?: temporaryData
    code?: string
}) {

    const [filterArticles, setFilterArticles] = useState(articles) //筛选过后文章
    const [searchText, setSearchText] = useState('') //搜索词
    const [loading, setLoading] = useState(false) //加载

    useEffect(() => {
        if (!isShow || isShowArticle) return
        setLoading(true)
        if (type === 'first') {
            fetchAllGoodArticles().then((data: any) => {
                setLoading(false)
                if (data.status_code === 0) {
                    const result: article[] = []
                    // 一篇文章配置多次会在fundInfo数组中添加，故fundInfo数组长度就是该文章配置次数
                    data.data.forEach((item: article) => {
                        (item.fundInfo as articleFundInfo[]).forEach(fundItem => {
                            // length为0表示全量配置
                            if (fundItem.funds.length === 0) {
                                result.push({
                                    ...item,
                                    fundInfo: [fundItem]
                                })
                            }
                        })
                    })
                    setArticles(result)
                    setFilterArticles(result)
                } else {
                    message.error(data.status_msg || '系统错误请稍候再试～')
                }
            }).catch((e: any) => {
                setLoading(false)
                console.log(e.message)
                message.error('系统错误请稍候再试～')
            })
        } else if (type === 'second') {
            fetchFundGoodArticles({
                fundCode: code
            }).then((data: any) => {
                setLoading(false)
                if (data.status_code === 0) {
                    const result: article[] = []
                    // 一篇文章配置多次会在fundInfo数组中添加，故fundInfo数组长度就是该文章配置次数
                    data.data.forEach((item: article) => {
                        (item.fundInfo as articleFundInfo[]).forEach(fundItem => {
                            // 只显示本只基金的文章
                            if (~fundItem.funds.indexOf((code as string))) {
                                result.push({
                                    ...item,
                                    fundInfo: [fundItem],
                                    sendSelectedHold: (
                                        fundItem.sendSelectedHold === '1' && 
                                        new Date().getTime() > new Date(fundItem.startTime).getTime() &&
                                        new Date().getTime() < new Date(fundItem.endTime).getTime()
                                    ) ? '持仓位' : '无'
                                })
                            }
                        })
                    })
                    setArticles(result)
                    setFilterArticles(result)
                } else {
                    message.error(data.status_msg || '系统错误请稍候再试～')
                }
            }).catch((e: any) => {
                setLoading(false)
                console.log(e.message)
                message.error('系统错误请稍候再试～')
            })
        }
    }, [isShow, isShowArticle])

    function filter() {
        setFilterArticles(articles.filter((article: article) => article.itemId.indexOf(searchText) > -1))
    }

    function edit(record: article) {
        setIsShowConfigArticle(true)
        setArticleType && setArticleType('edit')
        let _record: article = {...record},
            _fundInfo: any = (record?.fundInfo || [])[0]
        _record.funds = _fundInfo.funds.join(',')
        _record.label = _fundInfo.label
        _record.sendSelectedHold = _fundInfo.sendSelectedHold == 1 ? true : false
        _record.startTime = _fundInfo.startTime
        _record.endTime = _fundInfo.endTime
        setArticleValue(_record)
    }

    return (
        <Modal 
        title="精选文章" 
        visible={isShow} 
        onOk={() =>{setIsShow(false)}} 
        okText="保存"
        onCancel={() =>{setIsShow(false)}} 
        cancelText="取消"
        width={800}
        zIndex={999}
        >
            <div className="u-r-middle" style={{marginBottom: 20}}>
                <Input 
                value={searchText}
                onChange={(e) => {setSearchText(e.target.value)}}
                />
                <Button className="g-ml20" type="primary" onClick={filter}>搜索</Button>
            </div>
            <Table 
            dataSource={filterArticles}
            loading={loading}
            >
                <Table.Column title="文章id" dataIndex={'itemId'}></Table.Column>
                <Table.Column title="文章标题" dataIndex={'title'}></Table.Column>
                {
                    type === 'second'
                    ?
                    <Table.Column title="状态" dataIndex={'sendSelectedHold'}></Table.Column>
                    :
                    null
                }
                <Table.Column title="操作" render={(text, record: article, index) => {
                    return (
                        <a key={index} style={{color: 'blue'}} onClick={() => {
                            edit(record)
                        }}>配置</a>
                    )
                }}></Table.Column>
            </Table>
        </Modal>
    )
})