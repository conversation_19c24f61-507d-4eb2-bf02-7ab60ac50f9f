import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ConfigData } from '../../type';
import { Button, Card, Input, Popconfirm, Switch } from 'antd';
import InputTitle from '../InputTitle';
import UploadImg from '../UploadImg';
import { mapPropToName } from './helper';

export interface IProps {
  data: ConfigData['managerAnalyze'];
  onChange: (data: ConfigData['managerAnalyze']) => void;
  disabled: boolean;
}
const ManagerAnalyze: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleChange = (name, value) => {
    onChange({
      ...data,
      [name]: value,
    });
  };
  return (
    <div>
      <h3>基金经理解读</h3>
      <Input
        value={data.id}
        onChange={e => handleChange('id', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10, width: 300 }}
        addonBefore={<InputTitle title={mapPropToName['id']} />}
      />
      <Input
        value={data.name}
        onChange={e => handleChange('name', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10, width: 300 }}
        addonBefore={<InputTitle title={mapPropToName['name']} />}
      />
      <Input
        value={data.tags}
        onChange={e => handleChange('tags', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['tags']} />}
      />
      <Input
        value={data.intro}
        onChange={e => handleChange('intro', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['intro']} isRequired={false} />}
      />
      <div>
        {/* <InputTitle title={mapPropToName['content']} /> */}
        {Array.isArray(data.content) &&
          data.content.map((item, index) => {
            return (
              <Card key={index} style={{ width: '300px', display: 'inline-block' }}>
                <Input
                  value={item.title}
                  onChange={e => {
                    const newContent = [...data.content];
                    newContent[index]['title'] = e.target.value;
                    handleChange('content', newContent);
                  }}
                  disabled={disabled}
                  style={{ marginBottom: 10 }}
                  addonBefore={<InputTitle title={mapPropToName['content.title']} />}
                />
                <div>
                  <InputTitle title={mapPropToName['content.value']} />
                  <Input.TextArea
                    value={item.value}
                    onChange={e => {
                      const newContent = [...data.content];
                      newContent[index]['value'] = e.target.value;
                      handleChange('content', newContent);
                    }}
                    disabled={disabled}
                    style={{ marginBottom: 10 }}
                  />
                </div>
                <Popconfirm
                  title={'请确认是否删除'}
                  disabled={disabled}
                  okText={'确认'}
                  cancelText={'取消'}
                  onConfirm={() => {
                    const newContent = [...data.content];
                    newContent.splice(index, 1);
                    handleChange('content', newContent);
                  }}
                >
                  <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                    删除
                  </Button>
                </Popconfirm>
              </Card>
            );
          })}
        <Button
          size="small"
          disabled={disabled}
          onClick={() => {
            let newContent = [];
            if (Array.isArray(data.content)) {
              newContent = [...data.content];
            }

            newContent.push({ title: '', value: '' });
            handleChange('content', newContent);
          }}
        >
          新增解读内容
        </Button>
      </div>
      <UploadImg
        disabled={disabled}
        value={data.avatar}
        onChange={v => handleChange('avatar', v)}
        title={mapPropToName['avatar']}
        isRequired
        size={['74*74']}
      ></UploadImg>
    </div>
  );
};
export default ManagerAnalyze;
