import React, { useEffect, useState } from "react";
import api from 'api'
import { Button, Radio, Cascader, Modal, Table, Divider, Popconfirm, Input, message } from 'antd';
import './index.less'
import ImgUpload from '../../../frontend/compoment/uploadImg'
import SelectModel from '../../../frontend/compoment/selectModel'
import TagModel from '../../../frontend/compoment/tagModel'

const { postHomeConfig, postLazyfundcompanyTactics, postLazyCatAllConfig, fetchCBAS, fetchOLAS } = api;

export default function () {
    // 新增策略对话框控制项
    const [addStrategy, setAddStrategy] = useState(false);
    // 其他推荐产品配置项
    const [productConfig, setProductConfig] = useState([]);
    // 首页运营位产品配置项
    const [operationConfig, setOperationConfig] = useState([]);
    // 主推策略配置项
    const [mainConfig, setMainConfig] = useState([]);
    // 基金公司及其对应策略名称数据汇总
    const [fundcompanyandtactics, setFundcompanyandtactics] = useState([]);
    // 表格加载控制项
    const [loading, setLoading] = useState(true);
    // 策略类型数据汇总项
    const [typeData, setTypeData] = useState([]);
    // 选中的策略类型筛选对应策略名称
    const [strategyName, setStrategyName] = useState([])
    // 配置名称输入项
    const [addStrategyInput, setAddStrategyInput] = useState('');
    // 策略类型选择数据项
    const [strategyTypeOption, setStrategyTypeOption] = useState({});
    // 策略名称选择数据项
    const [strategyNameOption, setStrategyNameOption] = useState({});
    // KYC数据项
    const [kycTag, setKycTag] = useState([]);
    // OLAS数据项
    const [olasTag, setOlasTag] = useState([]);
    // 适用平台和用户类型选择数据项
    const [platformAndUser, setPlatformAndUser] = useState({
        platform: [],
        utype: []
    });
    // KYC和LOAS选择数据项
    const [kycAndLoas, setKycAndLoas] = useState({
        targetType: '',
        kycLogic: '',
        kycs: [],
        olasId: []
    });
    // 主推策略配置编辑项
    const [strategyEdit, setStrategyEdit] = useState({});
    // 主推策略是否为默认项
    const [isDefault, setIsDefault] = useState(0);
    // 当前主推策略编辑索引
    const [tempIndex, setTempIndex] = useState('');
    const [isEdit, setIsEdit] = useState(true);
    useEffect(() => {
        fetchTags();
        postLazyfundcompanyTactics().then(data => {
            if (data.code == '0000' && data.data) {
                setFundcompanyandtactics(data.data);
            } else {
                message.error(data.message);
            }
        })
        postLazyCatAllConfig({
            type: 'query'
        }).then(data => {
            let dataSource = data.data;
            // 策略类型和策略名称对应项
            setTypeData(dataSource);
            postHomeConfig({
                type: 'query'
            }).then(respond => {
                if (respond.code == '0000') {
                    if (respond.data) {
                        respond = JSON.parse(respond.data);
                        let source = respond.productList;
                        respond.productList = source.map(item => {
                            let data = dataSource.filter(elem => elem.typeId == item.typeId);
                            item.typeRelationList = data[0].typeRelationList ? data[0].typeRelationList : [];
                            return item;
                        })
                        respond.mainConfigList = respond.mainConfigList.map(item => {
                            let data = dataSource.filter(elem => elem.typeId == item.typeId);
                            item.typeRelationList = data[0].typeRelationList ? data[0].typeRelationList : [];
                            return item;
                        })
                        // 主推策略配置项
                        setMainConfig(respond.mainConfigList);
                        // 首页运营位配置项
                        setOperationConfig(respond.operationPosList);
                        // 其他推荐产品配置项
                        setProductConfig(respond.productList);
                    }
                } else {
                    message.error(respond.message);
                }
                setLoading(false);
            })
        })
    }, []);
    // KYC和OLAS数据请求
    const fetchTags = () => {
        const fetchKyc = fetchCBAS(),
            fetchOlas = fetchOLAS();
        Promise.all([fetchKyc, fetchOlas]).then((res) => {
            const _kycTag = res[0].data;
            const olasTags = res[1].data;
            let _olasTag = [];
            for (let prop in olasTags) {
                _olasTag.push({
                    description: olasTags[prop].description,
                    groupid: prop,
                })
            }
            setKycTag(_kycTag)
            setOlasTag(_olasTag)
        })
    }
    // 表格列配置项
    const columns = [
        {
            title: '序号',
            key: 'index',
            render: (text, record, index) => {
                return index + 1;
            }
        },
        {
            title: '配置名称',
            dataIndex: 'configName',
            key: 'configName',
        },
        {
            title: '策略类型',
            dataIndex: 'typeName',
            key: 'typeName',
        },
        {
            title: '策略名称',
            dataIndex: 'consultTacticsId',
            key: 'consultTacticsId',
            render: (text, record, index) => {
                return filterStrategyName(record);
            }
        },
        {
            title: '默认值',
            dataIndex: 'isDefault',
            key : 'isDefault',
            render: (text, record, index) => {
                return text ? '默认' : '非默认';
            }
        },
        {
            title: '操作',
            key: 'operation',
            render: (text, record, index) => (
                <span>
                    <a onClick={() => { handleAddStrategyEdit(record, index) }}>编辑</a>
                    <Divider type="vertical" />
                    <Popconfirm
                        title="确定要删除吗？"
                        okText="确定"
                        cancelText="取消"
                        onConfirm={() => { deleteStrategy(index) }}
                    >
                        <a>删除</a>
                    </Popconfirm>
                </span>
            ),
        },
    ];

    const filterStrategyName = (record) => {
        let temp = [...fundcompanyandtactics];
        let dataSource = temp.filter(item => {
            return item.taCode == record.taCode;
        });
        if (!dataSource.length) {
            return '';
        } else {
            let data = dataSource[0].consultTacticsList.filter(item => {
                return item.consultTacticsId == record.consultTacticsId;
            })
            return data[0] ? data[0].investConsultTacticsName : '';
        }
    }

    // 新增策略编辑函数
    const handleAddStrategyEdit = (record, index) => {
        setTempIndex(index);
        console.log(record);
        setAddStrategy(true);
        setStrategyEdit(record);
        let data = {
            platform: record.platform,
            utype: record.uType
        };
        console.log(data);
        setPlatformAndUser(data);
        let temp = {
            kycLogic: record.kycLogic,
            kycs: record.kycs,
            olasId: record.olasId,
            targetType: record.targetType
        }
        let tempType = {
            typeName: record.typeName,
            typeId: record.typeId
        }
        let tempName = {
            taCode: record.taCode,
            consultTacticsId: record.consultTacticsId
        };
        setStrategyNameOption(tempName);
        setStrategyTypeOption(tempType);
        setKycAndLoas(temp);
        setIsDefault(record.isDefault);
        let edit = record.isDefault === 1 ? false : true;
        setIsEdit(edit);
    }
    // 新增策略处理函数
    const handleAddStrategy = () => {
        setAddStrategy(true);
        setAddStrategyInput('');
        setStrategyEdit({});
        setPlatformAndUser({
            platform: [],
            utype: []
        });
        setKycAndLoas({
            targetType: '',
            kycLogic: '',
            kycs: [],
            olasId: []
        });
        setIsDefault(0);
        setIsEdit(true);
    }
    // 取消新增策略处理函数
    const handleAddStrategyCancel = () => {
        setTempIndex('');
        setAddStrategy(false);
        // setPlatformAndUser({});
    }
    //配置名称输入框的回车和失焦处理函数
    const handleAddStrategyInput = (e) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            setAddStrategyInput(e.target.value);
        }
    }
    // 选择策略类型处理函数
    const handleStrategyType = (value, selectedOptions, index) => {
        let dataSource = [...typeData];
        let data = dataSource.filter(item => {
            return item.typeId == value[0];
        })
        // 设置策略名称选择项
        setStrategyName(data[0].typeRelationList);

        if (index || index == '0') {
            let tempData = {
                typeId: selectedOptions[0].typeId,
                typeName: selectedOptions[0].typeName,
            }
            let datas = dataSource.filter(elem => elem.typeId == tempData.typeId);
            tempData.typeRelationList = datas[0].typeRelationList ? datas[0].typeRelationList : [];
            let tempProduct = [...productConfig];

            tempProduct[index].typeRelationList = tempData.typeRelationList;
            tempProduct[index].typeId = tempData.typeId;
            tempProduct[index].typeName = tempData.typeName;
            tempProduct[index].strategyList = [{
                taCode: "",
                consultTacticsId: "",
                content: ""
            }]

            console.log(tempProduct);
            setProductConfig(tempProduct);
        }


        let temp = {};
        temp.typeId = selectedOptions[0].typeId;
        temp.typeName = selectedOptions[0].typeName;
        setStrategyTypeOption(temp);
    }
    // 选择策略名称处理函数
    const handleStrategyName = (value, selectedOptions, type, order, index) => {
        let temp = {};
        temp.taCode = selectedOptions[0].taCode;
        temp.consultTacticsId = selectedOptions[0].consultTacticsId;
        setStrategyNameOption(temp);
        if (type) {
            let dataSource = productConfig;
            let data = dataSource[index];
            data.typeId = data.typeId ? data.typeId : strategyTypeOption.typeId;
            data.typeName = data.typeName ? data.typeName : strategyTypeOption.typeName;
            let newData = {
                ...temp,
                content: data.strategyList[order].content
            }
            data.strategyList[order] = newData;
            setProductConfig(dataSource);
        }
    }
    // 适用平台和用户类型处理函数
    const handleUserChange = (data) => {
        console.log('platform', data);
        if (data.user) {
            return;
        } else {
            let temp = {};
            temp.platform = data.platform;
            temp.utype = data.utype ? data.utype : data.user;
            setPlatformAndUser(temp);
        }
    }

    // KYC和OLAS处理函数
    const handleRelationChange = (data) => {
        console.log('kyc/olas', data);
        setKycAndLoas(data);
    }
    // 保存新增策略处理函数
    const handleAddStrategyOk = () => {
        const dataSource = mainConfig ? mainConfig : [];
        let newData = {
            configName: addStrategyInput ? addStrategyInput : strategyEdit.configName,
            platform: isDefault === 1 ? [] : platformAndUser.platform,
            uType: isDefault === 1 ? [] : platformAndUser.utype ? platformAndUser.utype : platformAndUser.user,
            ...strategyTypeOption,
            ...strategyNameOption,
            ...kycAndLoas,
            isDefault: isDefault
        }
        let tempOne;
        if (tempIndex !== '') {
            tempOne = [...dataSource];
            tempOne[tempIndex] = newData;
        } else {
            tempOne = [...dataSource, newData];
        }

        let temp = [];
        if (newData.isDefault == 1) {
            temp = tempOne.filter(item => item.isDefault == 1);
        }
        newData.olasId = [newData.olasId];
        console.log('mainConfig', newData);
        if (isDefault === 1 && !newData.configName) {
            message.warning('有必填项未填写');
        } else if (isDefault === 0 && (!newData.configName || platformAndUser.platform.length == 0 || (platformAndUser.utype ? platformAndUser.utype : platformAndUser.user).length == 0)) {
            message.warning('有必填项未填写');
        } else if (temp.length > 1) {
            message.warning('已有配置被设置为默认项');
        }
        else {
            // setAddStrategyInput('');
            setTempIndex('');
            setMainConfig([...tempOne]);
            setAddStrategy(false);
        }
    }
    // 删除操作处理函数
    const deleteStrategy = (index) => {
        let dataSource = [...mainConfig];
        dataSource.splice(index, 1);
        setMainConfig(dataSource);
    }

    // 首页运营位新增处理函数
    const handleOperationConfig = () => {
        const dataSource = [...operationConfig];
        const newData = {
            "pictureUrl": "",
            "link": ""
        };
        setOperationConfig([...dataSource, newData]);
    }

    // 首页运营位配置图片处理函数
    const handlePicture = (imageUrl, index) => {
        let _operationConfig = [...operationConfig];
        _operationConfig[index].pictureUrl = imageUrl;
        setOperationConfig(_operationConfig);
    }
    // 首页运营位配置链接处理函数
    const handleOperationConfigInput = (e, index) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            e.target.blur();
            let _operationConfig = [...operationConfig];
            _operationConfig[index].link = e.target.value;
            setOperationConfig(_operationConfig);
        }
    }
    // 首页运营位配置链接处理函数
    const deleteOperationConfig = (index) => {
        let _operationConfig = [...operationConfig];
        _operationConfig.splice(index, 1);
        setOperationConfig(_operationConfig);
    }
    // 新增产品配置处理函数
    const handleProductConfig = () => {
        const dataSource = productConfig;
        console.log('productConfig', productConfig);
        const newData = {
            typeId: "",
            typeName: "",
            strategyList: [{
                taCode: "",
                consultTacticsId: "",
                content: ""
            }]
        };
        setProductConfig([...dataSource, newData]);
    }

    // 新增产品配置介绍文案处理函数
    const handleAddProductStrategyInput = (e, order, index) => {
            let dataSource = productConfig;
            let data = dataSource[index];
            data.typeId = data.typeId ? data.typeId : strategyTypeOption.typeId;
            data.typeName = data.typeName ? data.typeName : strategyTypeOption.typeName;
            let newData = {
                ...strategyNameOption,
                content: e.target.value
            }
            data.strategyList[order] = newData;
            setProductConfig(dataSource);
    }

    // 新增产品配置名称处理函数
    const handleProductStrategy = (index) => {
        const _productConfig = [...productConfig];
        const newData = {
            taCode: "",
            consultTacticsId: "",
            content: ""
        }
        _productConfig[index].strategyList.push(newData);
        setProductConfig(_productConfig);
    }

    // 删除产品配置名称处理函数
    const deleteProductStrategy = (index, key, order) => {
        console.log('order', order);
        const _productConfig = [...productConfig];
        if (_productConfig[index].strategyList.length == 1) {
            message.warning('至少要配置一个策略');
        } else {
            console.log(_productConfig[index].strategyList);
            _productConfig[index].strategyList.splice(order, 1);
            setProductConfig(_productConfig);
        }
    }
    // 删除产品配置处理函数
    const deleteProductConfig = (index) => {
        let dataSource = [...productConfig];
        dataSource.splice(index, 1);
        setProductConfig(dataSource);
    }

    // 页面提交处理函数
    const handleSubmit = () => {
        let value = {
            mainConfigList: mainConfig,
            operationPosList: operationConfig,
            productList: productConfig
        }
        let dataSource = value.productList;
        console.log('dataSource', dataSource);
        let repect = [];
        dataSource.forEach(item => {
            let isRepeat = item.strategyList;
            let hash = {};
            for (let i in isRepeat) {
                if (!hash[isRepeat[i].consultTacticsId]) {
                    hash[isRepeat[i].consultTacticsId] = true;
                } else {
                    repect.push('1');
                }
            }
        })
        let temp = value.operationPosList.filter(item => item.link == '');
        let data = [];
        dataSource.map(item => {
            let temp = item.strategyList.filter(elem => elem.content == '');
            data = [...data, ...temp];
        });
        if (data.length || temp.length) {
            message.warning('有必填项未填写');
        }
        else if(repect.length) {
            message.warning('配置项重复，请修正或删除');
        }
        else {
            postHomeConfig({
                type: 'update',
                value: JSON.stringify(value)
            }).then(data => {
                if (data.code == '0000') {
                    message.success('提交成功');
                    window.location.reload(true);
                }
            })
        }
    }
    // 上移处理函数
    const up = (index) => {
        let dataSource = [...productConfig];
        if (index == 0) {
            message.info('已经处于置顶，无法上移')
        } else {
            let temp = dataSource[index];
            dataSource[index] = dataSource[index - 1];
            dataSource[index - 1] = temp;
        }
        console.log(dataSource);
        setProductConfig(dataSource);
    }
    // 下移函数
    const down = (index) => {
        let dataSource = [...productConfig];
        if (index + 1 == dataSource.length) {
            message.info('已经处于置底，无法下移')
        } else {
            let temp = dataSource[index];
            dataSource[index] = dataSource[index + 1];
            dataSource[index + 1] = temp;
        }
        console.log(dataSource);
        setProductConfig(dataSource);
    }

    const defaultChange = (e) => {
        console.log('默认选中项', e.target.value);
        if (e.target.value === 1) {
            setIsEdit(false);
        } else {
            setIsEdit(true);
        }
        setIsDefault(e.target.value);
    }

    return (
        <section>
            <section className="home-header">
                <span className="home-type-title">主推策略配置</span>
                <Button
                    type="primary"
                    onClick={handleAddStrategy}
                >新增策略</Button>
            </section>
            <section className="home-table">
                <Table
                    columns={columns}
                    dataSource={mainConfig}
                    pagination={false}
                    loading={loading}
                />
            </section>

            {/* 首页运营位配置 */}
            <section className="home-header">
                <span className="home-type-title">首页运营位配置</span>
                <Button
                    type="primary"
                    onClick={handleOperationConfig}
                >新增</Button>
            </section>
            {
                operationConfig && operationConfig.length > 0 && operationConfig.map((item, index) => (
                    <section className="operation-config">
                        <ImgUpload
                            isEdit={true}
                            imageUrl={item.pictureUrl}
                            handleChange={(url) => { handlePicture(url, index) }}
                            size={['700px*180px']}
                        ></ImgUpload>
                        <section className="home-addType">
                            <span>链接：</span>
                            <Input
                                defaultValue={item.link}
                                style={{ width: '300px' }}
                                placeholder="请输入跳转链接"
                                allowClear={true}
                                onPressEnter={e => { handleOperationConfigInput(e, index) }}
                                onBlur={e => { handleOperationConfigInput(e, index) }}
                            ></Input>
                            <span className="warning">*</span>
                        </section>
                        {/* <a onClick={() => { deleteOperationConfig(index) }}>删除</a> */}
                        <section className="operation-delete">
                            <Popconfirm
                                title="确定要删除吗？"
                                okText="确定"
                                cancelText="取消"
                                onConfirm={() => { deleteOperationConfig(index) }}
                            >
                                <Button
                                    type="primary"
                                >删除</Button>
                            </Popconfirm>
                        </section>
                    </section>
                ))
            }

            {/* 其他推荐产品配置 */}
            <section className="home-header">
                <span className="home-type-title">其他推荐产品配置</span>
                <Button
                    type="primary"
                    onClick={handleProductConfig}
                >新增</Button>
            </section>
            {
                productConfig && productConfig.length > 0 && productConfig.map((item, index) => (
                    <section className="other-product-config">
                        <Cascader
                            allowClear={false}
                            // defaultValue={[`${item.typeId ? item.typeId : '请选择策略类型'}`]}
                            value={[item.typeId]}
                            fieldNames={{ label: 'typeName', value: 'typeId' }}
                            options={typeData}
                            placeholder={item.typeName ? item.typeName : '请选择策略类型'}
                            className={item.typeName ? 'input-placeholder' : ''}
                            style={{ marginBottom: '20px', marginLeft: '20px', marginRight: '20px' }}
                            onChange={(value, selectedOptions) => {handleStrategyType(value, selectedOptions, index)}}
                        />
                        {
                            item.strategyList.length > 0 && item.strategyList.map((elem, order) => (
                                <section key={elem.consultTacticsId} className="product-config-wrapper">
                                    <section className="product-config">
                                        <Cascader
                                            defaultValue={[elem.consultTacticsId]}
                                            className={elem.consultTacticsId ? 'input-placeholder' : ''}
                                            allowClear={false}
                                            fieldNames={{ label: 'investConsultTacticsName', value: 'consultTacticsId' }}
                                            // options={strategyName.length ? strategyName : item.typeRelationList}
                                            options={item.typeRelationList}
                                            // placeholder={filterStrategyName(elem) ? filterStrategyName(elem) : '请选择策略名称'}
                                            placeholder="请选择策略名称"
                                            style={{ marginBottom: '20px', marginRight: '20px'}}
                                            onChange={(value, selectedOptions) => {handleStrategyName(value, selectedOptions, 'productConfig', order, index)}}
                                        />
                                        <section className="home-addType">
                                            <span>介绍文案：</span>
                                            <Input
                                                defaultValue={elem.content}
                                                style={{ width: '300px' }}
                                                placeholder="请输入介绍文案"
                                                allowClear={true}
                                                // onPressEnter={(e) => { handleAddProductStrategyInput(e, order, index) }}
                                                onChange={(e) => { handleAddProductStrategyInput(e, order, index) }}
                                            ></Input>
                                            <span className="warning">*</span>
                                        </section>
                                    </section>
                                    <section className="product-config-button">
                                        <Button size="large" type="primary" shape="circle" icon="plus" style={{ marginRight: '20px' }} onClick={() => { handleProductStrategy(index) }} />
                                        <Popconfirm
                                            title="确定要删除吗？"
                                            okText="确定"
                                            cancelText="取消"
                                            onConfirm={() => { deleteProductStrategy(index, elem.consultTacticsId, order) }}
                                        >
                                            <Button size="large" type="primary" shape="circle" icon="minus" />
                                        </Popconfirm>
                                    </section>

                                </section>
                            ))
                        }
                        <section className="product-config-move-button">
                            <Button type="primary" style={{ marginRight: '20px' }} onClick={() => { up(index) }}>上移</Button>
                            <Button type="primary" onClick={() => { down(index) }}>下移</Button>
                        </section>
                        {/* <a onClick={() => { deleteProductConfig(index) }}>删除</a> */}
                        <section className="product-delete">
                            <Popconfirm
                                title="确定要删除吗？"
                                okText="确定"
                                cancelText="取消"
                                onConfirm={() => { deleteProductConfig(index) }}
                            >
                                <Button
                                    type="primary"
                                >删除</Button>
                            </Popconfirm>
                        </section>
                    </section>
                ))
            }


            {/* 新增策略弹窗 */}
            {
                addStrategy ?
                    <Modal
                        width={700}
                        visible={addStrategy}
                        okText="保存"
                        cancelText="取消"
                        onCancel={handleAddStrategyCancel}
                        onOk={handleAddStrategyOk}
                        closable={false}
                    >
                        {/* 配置名称 */}
                        <section className="home-add-strategy">
                            <section className="home-addType">
                                <span>配置名称：</span>
                                <Input
                                    defaultValue={strategyEdit.configName}
                                    style={{ width: '300px' }}
                                    placeholder="请输入配置名称"
                                    allowClear={true}
                                    onPressEnter={handleAddStrategyInput}
                                    onBlur={handleAddStrategyInput}
                                ></Input>
                                <span className="warning">*</span>
                            </section>
                        </section>
                        {/* 策略类型和名称选择 */}
                        <section className="home-strategy-type-name" style={{ marginBottom: '20px' }}>
                            <Cascader
                                className={strategyEdit.typeName ? 'input-placeholder' : ''}
                                allowClear={false}
                                fieldNames={{ label: 'typeName', value: 'typeId' }}
                                options={typeData}
                                // placeholder={strategyEdit.typeName ? strategyEdit.typeName : '请选择策略类型'}
                                placeholder="请选择策略类型"
                                defaultValue={[strategyEdit.typeId]}
                                style={{ marginRight: '20px' }}
                                onChange={handleStrategyType}
                            />
                            <Cascader
                                className={strategyEdit.typeName ? 'input-placeholder' : ''}
                                allowClear={false}
                                fieldNames={{ label: 'investConsultTacticsName', value: 'consultTacticsId' }}
                                // options={strategyName}
                                options={strategyName.length ? strategyName : strategyEdit.typeRelationList}
                                // placeholder={filterStrategyName(strategyEdit) ? filterStrategyName(strategyEdit) : '请选择策略名称'}
                                placeholder="请选择策略名称"
                                defaultValue={[strategyEdit.consultTacticsId]}
                                onChange={handleStrategyName}
                            />
                        </section>
                        <SelectModel
                            handleChange={handleUserChange}
                            isHead={false}
                            isEdit={isEdit}
                            data={platformAndUser}
                        />
                        <TagModel
                            handleChange={handleRelationChange}
                            kycTag={kycTag}
                            olasTag={olasTag}
                            isEdit={true}
                            data={kycAndLoas}
                        />
                        默认项：
                        <Radio.Group onChange={defaultChange} value={isDefault}>
                            <Radio value={1}>默认</Radio>
                            <Radio value={0}>非默认</Radio>
                        </Radio.Group>
                    </Modal>
                    : ''
            }

            <section className="home-footer">
                <Popconfirm
                    title="确定要提交吗？"
                    okText="确定"
                    cancelText="取消"
                    onConfirm={handleSubmit}
                >
                    <Button
                        type="primary"
                    >提交</Button>
                </Popconfirm>
            </section>
        </section >
    );
}
