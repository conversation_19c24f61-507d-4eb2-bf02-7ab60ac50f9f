/*
* 南方投顾
* <AUTHOR>
* @time 2020.03
*/

import React from 'react';
import { Row, Input, Button, Select, message, Collapse, Popconfirm } from 'antd';
import api from 'api';
const {  } = api;
const {Panel} = Collapse;
const {Option} = Select;

export default function Answer ({lazyCatQue<PERSON><PERSON>s, choiceList, answerList, questionIndex, addAnswer, deleteAnswer, handleAnswerKey, handleAnswerTitle, handleAnswerJumpChosen, handleAnswerLoginNextKey, handleAnswerNoLoginNextKey, handleAnswerTacode, handleAnswerConsulttacticsid, handleAnswerCompany, handleAnswerIdea}) {
    
    return (
        <section>
            <Collapse style={{ marginBottom: '20px'}}>
                {
                    answerList.map( (item, index) => {
                        return (
                            <Panel 
                            header={'答案'+(index+1) + ': ' + item.key + '    ' + item.title} 
                            key={index} 
                            extra={
                                <Popconfirm
                                    placement="rightBottom"
                                    title={`你确定要删除答案${item.title}么`}
                                    onConfirm={() => {deleteAnswer(questionIndex, index)}} 
                                    okText="确认"
                                    cancelText="取消"
                                >
                                    <Button 
                                    type="danger" 
                                    >删除答案</Button>
                                </Popconfirm>
                                
                            }>
                                <Row style={{ marginBottom: '20px'}}>
                                    {/* <Input 
                                    style={{width: '400px'}} 
                                    addonBefore="答案标签:" 
                                    onChange={(e) => {handleAnswerKey(e, questionIndex, index)}}
                                    value={item.key}
                                    ></Input> */}
                                     <span>答案标签：</span>
                                    <Input
                                        style={{width: '317px'}}
                                        value={item.key}
                                        disabled
                                    >
                                    </Input>
                                    <Input 
                                    style={{width: '400px', marginLeft: '100px'}} 
                                    addonBefore="答案文案:"
                                    onChange={(e) => {handleAnswerTitle(e, questionIndex, index)}}
                                    value={item.title}
                                    ></Input>
                                </Row>
                                <Row style={{ marginBottom: '20px'}}>
                                    <span>请选择跳转：</span>
                                    <Select
                                        style={{width: '317px'}}
                                        defaultValue="question"
                                        onChange={(value) => {handleAnswerJumpChosen(value, questionIndex, index)}}
                                        value={item.jumpChosen}
                                    >
                                        <Option value="question">问题</Option>
                                        <Option value="product">产品</Option>
                                    </Select>
                                </Row>
                                {
                                    item.jumpChosen === 'question' ?
                                    <Row style={{ marginBottom: '20px'}}>
                                        {/* <Input 
                                        style={{width: '400px'}} 
                                        addonBefore="登录跳转标签:"
                                        onChange={(e) => {handleAnswerLoginNextKey(e, questionIndex, index)}}
                                        value={item.loginNextKey}
                                        ></Input> */}
                                        
                                        <span>登录跳转标签：</span>
                                        <Select
                                            style={{width: '200px', marginRight: '100px'}}
                                            onChange={(value) => {handleAnswerLoginNextKey(value, questionIndex, index)}}
                                            value={item.loginNextKey}
                                        >
                                            {
                                                lazyCatQueKeys.map((key, index) => {
                                                    return (
                                                        <Option value={key} key={index}>{key}</Option>
                                                    )
                                                })
                                            }
                                        </Select>
                                        {/* <Input 
                                        style={{width: '400px', marginLeft: '100px'}} 
                                        addonBefore="未登录跳转标签:"
                                        onChange={(e) => {handleAnswerNoLoginNextKey(e, questionIndex, index)}}
                                        value={item.noLoginNextKey}
                                        ></Input> */}
                                        
                                        <span>未登录跳转标签：</span>
                                        <Select
                                            style={{width: '200px'}}
                                            onChange={(value) => {handleAnswerNoLoginNextKey(value, questionIndex, index)}}
                                            value={item.noLoginNextKey}
                                        >
                                            {
                                                lazyCatQueKeys.map((key, index) => {
                                                    return (
                                                        <Option value={key} key={index}>{key}</Option>
                                                    )
                                                })
                                            }
                                        </Select>
                                    </Row>
                                    :
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '400px'}} 
                                            addonBefore="TaCode:"
                                            onChange={(e) => {handleAnswerTacode(e, questionIndex, index)}}
                                            value={item.productList[0].vc_tacode}
                                            ></Input>
                                            <Input 
                                            style={{width: '400px', marginLeft: '100px'}} 
                                            addonBefore="投顾id:"
                                            onChange={(e) => {handleAnswerConsulttacticsid(e, questionIndex, index)}}
                                            value={item.productList[0].vc_consulttacticsid}
                                            ></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '400px'}} 
                                            addonBefore="公司:"
                                            onChange={(e) => {handleAnswerCompany(e, questionIndex, index)}}
                                            value={item.productList[0].company}
                                            ></Input>
                                            <Input 
                                            style={{width: '400px', marginLeft: '100px'}} 
                                            addonBefore="投资理念:"
                                            onChange={(e) => {handleAnswerIdea(e, questionIndex, index)}}
                                            value={item.productList[0].idea}
                                            ></Input>
                                        </Row>
                                    </section>
                                }
                            </Panel>
                        )
                    })
                }
            </Collapse>
            <Button type="primary" className="g-center" onClick={() => {addAnswer(questionIndex)}}>添加一个答案</Button>
        </section>
    )
}
