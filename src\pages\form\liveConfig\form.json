{"propsSchema": {"type": "object", "required": ["headImg", "headText"], "properties": {"headImg": {"title": "顶部图片(只支持https)", "type": "string", "format": "image", "ui:options": {}}, "headText": {"title": "宣传文案", "type": "string", "ui:options": {}}, "radioList": {"title": "直播信息", "type": "array", "items": {"type": "object", "required": ["title", "statId", "bgImg", "startTime", "endTime", "sid", "fid"], "properties": {"title": {"title": "直播标题", "type": "string"}, "statId": {"title": "页面埋点（不能重复）", "description": "id为直播时配置埋点id，唯一且不可重复，命名格式：年月日+基金公司名字拼音+当日该基金公直播第几场如：20200805huaxia01", "type": "string"}, "bgImg": {"title": "专题页海报：(只支持https686 * 384)", "type": "string", "format": "image"}, "startTime": {"title": "直播开始时间", "type": "string", "format": "dateTime"}, "endTime": {"title": "直播结束时间", "type": "string", "format": "dateTime"}, "beforeStartStr": {"title": "开播前文案", "type": "string"}, "endStr": {"title": "直播结束文案", "type": "string"}, "bottomImg": {"title": "底部运营位配图(只支持https  750 * 188)", "type": "string", "format": "image", "ui:options": {}}, "bottomUrl": {"title": "运营位跳转链接", "type": "string"}, "isJumpSdk": {"title": "跳转开关：是否跳转SDK", "type": "boolean", "ui:width": "30%"}, "sid": {"title": "sid", "type": "string", "ui:width": "30%"}, "fid": {"title": "fid", "type": "string", "ui:width": "30%"}, "liveInfo": {"title": "信息", "type": "object", "required": ["url", "logo", "subTitle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "content"], "properties": {"url": {"title": "视频流地址", "type": "string", "description": ""}, "logo": {"title": "基金公司logo", "type": "string", "format": "image"}, "poster": {"title": "直播海报(只支持https)", "type": "string", "format": "image"}, "subTitle": {"title": "直播间名称（20个字）", "type": "string", "ui:options": {}}, "managerTitle": {"title": "基金经理头衔", "type": "string"}, "managerName": {"title": "基金经理姓名", "type": "string"}, "intro": {"title": "直播简介", "type": "string", "format": "textarea"}, "content": {"title": "内容简介", "type": "string", "format": "textarea"}}}, "numberObj": {"title": "观看人数(基数 + 系数 *（ 已经播出的秒数 + 1-100随机数） + 1-999随机数)", "type": "object", "properties": {"baseNumber": {"title": "基数", "type": "string", "ui:options": {}}, "indexNumber": {"title": "系数", "type": "string", "ui:options": {}}}}, "fundObj": {"title": "基金配置", "type": "object", "properties": {"fundType": {"title": "基金配置模式", "type": "string", "enum": ["1", "2", "3", "4"], "enumNames": ["小产品卡", "小标签", "大产品卡", "新发基金"]}, "fund1": {"title": "1", "type": "object", "ui:readonly": false, "ui:hidden": "{{rootValue.fundType !== '1'}}", "properties": {"fundName": {"title": "基金名称", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}, "desc": {"title": "desc", "type": "string", "enum": ["year", "now", "week", "month", "tmonth", "hyear", "twoyear", "tyear", "fyear", "nowyear"], "enumNames": ["近一年涨幅-year", "成立以来涨幅-now", "近一周涨幅-week", "近一月涨幅-month", "近三月涨幅-tmonth", "近半年涨幅-hyear", "近两年涨幅-towyear", "近三年涨幅-tyear", "近五年涨幅-fyear", "今年以来涨幅-nowyear"]}, "textList": {"title": "文案列表", "type": "array", "items": {"type": "object", "properties": {"text": {"title": "文案", "type": "string", "ui:options": {}}}}, "ui:options": {"foldable": "true"}}}}, "fund2": {"title": "2", "type": "object", "ui:hidden": "{{rootValue.fundType !== '2'}}", "properties": {"fundList": {"title": "基金数组", "type": "array", "items": {"type": "object", "properties": {"fundName": {"title": "基金名字", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}}}, "maxItems": 2, "ui:options": {"foldable": "true"}}}}, "fund3": {"title": "3", "type": "object", "ui:hidden": "{{rootValue.fundType !== '3'}}", "properties": {"fundName": {"title": "基金名称", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}, "managerss": {"title": "拟任基金经理", "type": "string", "ui:options": {}}, "startTime": {"title": "募集起始", "type": "string", "format": "date"}, "endTime": {"title": "募集结束", "type": "string", "format": "date"}}}, "fund4": {"title": "4", "type": "object", "ui:hidden": "{{rootValue.fundType !== '4'}}", "properties": {"fundName": {"title": "基金名称", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}, "desc": {"title": "文案", "type": "string", "ui:options": {}}}}}}}}, "ui:options": {"foldable": "true"}}, "videoList": {"title": "回看信息", "type": "array", "items": {"type": "object", "required": ["title", "statId", "bgImg", "startTime", "endTime"], "properties": {"title": {"title": "直播标题（16个字）", "type": "string", "ui:options": {}}, "statId": {"title": "页面埋点id（不能重复）", "description": "id为直播时配置埋点id，唯一且不可重复，命名格式：年月日+基金公司名字拼音+当日该基金公直播第几场如：20200805huaxia01", "type": "string"}, "bgImg": {"title": "专题页海报(只支持https   686 * 384)", "type": "string", "format": "image"}, "startTime": {"title": "回看开始时间", "type": "string", "format": "dateTime"}, "endTime": {"title": "回看结束时间", "type": "string", "format": "dateTime"}, "endStr": {"title": "回看结束文案", "type": "string", "ui:options": {}}, "bottomImg": {"title": "底部运营位配图(只支持https   750 * 188)", "type": "string", "format": "image", "ui:options": {}}, "bottomUrl": {"title": "运营位跳转链接", "type": "string"}, "liveInfo": {"title": "信息", "type": "object", "required": ["url", "logo", "subTitle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "content"], "properties": {"url": {"title": "视频流地址", "type": "string", "ui:options": {}}, "logo": {"title": "基金公司logo", "type": "string", "ui:options": {}, "format": "image"}, "poster": {"title": "直播海报(只支持https)", "type": "string", "ui:options": {}, "format": "image"}, "subTitle": {"title": "直播间名称（20个字）", "type": "string", "ui:options": {}}, "managerTitle": {"title": "基金经理头衔", "type": "string"}, "managerName": {"title": "基金经理姓名", "type": "string"}, "intro": {"title": "直播简介", "type": "string", "format": "textarea"}, "content": {"title": "内容简介", "type": "string", "format": "textarea", "ui:options": {}}}}, "fundObj": {"title": "基金配置", "type": "object", "properties": {"fundType": {"title": "基金配置模式", "type": "string", "enum": ["1", "2", "3", "4"], "enumNames": ["小产品卡", "小标签", "大产品卡", "新发基金"]}, "fund1": {"title": "1", "type": "object", "ui:readonly": false, "ui:hidden": "{{rootValue.fundType !== '1'}}", "properties": {"fundName": {"title": "基金名称", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}, "desc": {"title": "desc", "type": "string", "enum": ["year", "now", "week", "month", "tmonth", "hyear", "twoyear", "tyear", "fyear", "nowyear"], "enumNames": ["近一年涨幅-year", "成立以来涨幅-now", "近一周涨幅-week", "近一月涨幅-month", "近三月涨幅-tmonth", "近半年涨幅-hyear", "近两年涨幅-towyear", "近三年涨幅-tyear", "近五年涨幅-fyear", "今年以来涨幅-nowyear"]}, "textList": {"title": "文案列表", "type": "array", "items": {"type": "object", "properties": {"text": {"title": "文案", "type": "string", "ui:options": {}}}}, "ui:options": {"foldable": "true"}}}}, "fund2": {"title": "2", "type": "object", "ui:hidden": "{{rootValue.fundType !== '2'}}", "properties": {"fundList": {"title": "基金数组", "type": "array", "items": {"type": "object", "properties": {"fundName": {"title": "基金名字", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}}}, "maxItems": 2, "ui:options": {"foldable": "true"}}}}, "fund3": {"title": "3", "type": "object", "ui:hidden": "{{rootValue.fundType !== '3'}}", "properties": {"fundName": {"title": "基金名称", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}, "managerss": {"title": "拟任基金经理", "type": "string", "ui:options": {}}, "startTime": {"title": "募集起始", "type": "string", "format": "date"}, "endTime": {"title": "募集结束", "type": "string", "format": "date"}}}, "fund4": {"title": "4", "type": "object", "ui:hidden": "{{rootValue.fundType !== '4'}}", "properties": {"fundName": {"title": "基金名称", "type": "string", "ui:options": {}}, "fundCode": {"title": "基金代码", "type": "string", "ui:options": {}, "pattern": "^[A-Za-z0-9]{6}$"}, "desc": {"title": "文案", "type": "string", "ui:options": {}}}}}}}}, "ui:options": {"foldable": "true"}}}}, "formData": {"headImg": "", "headText": "", "radioList": [{"title": "", "statId": "", "bgImg": "", "startTime": "", "endTime": "", "beforeStartStr": "", "endStr": "", "bottomImg": "", "bottomUrl": "", "isJumpSdk": false, "sid": "", "fid": "", "liveInfo": {"url": "", "logo": "", "poster": "", "subTitle": "", "managerTitle": "", "managerName": "", "intro": "", "content": ""}, "numberObj": {"baseNumber": "", "indexNumber": ""}, "fundObj": {"fundType": "1", "fund1": {"fundName": "", "fundCode": "", "desc": "year", "textList": [{"text": ""}]}, "fund2": {"fundList": []}, "fund3": {"fundName": "", "fundCode": "", "managerss": "", "startTime": "", "endTime": ""}, "fund4": {"fundName": "", "fundCode": "", "desc": ""}}}], "videoList": [{"statId": "", "title": "", "bgImg": "", "startTime": "", "endTime": "", "endStr": "", "bottomImg": "", "bottomUrl": "", "liveInfo": {"url": "", "logo": "", "poster": "", "subTitle": "", "managerTitle": "", "managerName": "", "intro": "", "content": ""}, "fundObj": {"fundType": "1", "fund1": {"fundName": "", "fundCode": "", "desc": "year", "textList": [{"text": ""}]}, "fund2": {"fundList": [{"fundName": "", "fundCode": ""}]}, "fund3": {"fundName": "", "fundCode": "", "managerss": "", "startTime": "", "endTime": ""}, "fund4": {"fundName": "", "fundCode": "", "desc": ""}}}]}}