
import React, { useEffect, useState } from 'react';
import { Row, Input, Button, Icon, Select, message, Collapse, Popconfirm, DatePicker, Tooltip } from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import moment from 'moment';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";


const {fetchAllBigVEdit,postAllBigVEdit,fetchBigVEditService,postBigVEditService} = api;
export default function () {

  const [init, setInit] = useState(false);
  const [formData,setFormState] = useState({tabService: []});
  const [formConfig, setFormConfig] = useState(FORM_CONFIG.propsSchema);
  const [uiSchema, setUiSchema] = useState({});
  const [valid, setValid] = useState([]);
  useEffect(() => {
    fetchAllBigVEdit().then((res) => {
      try {
        res = JSON.parse(res.data);
        console.log(res);
        if (res) {
          setFormState(res)
        }

      } catch (e) {
        console.warn(e)
      }
      setInit(true)
    })
  },[init])



  let onSubmit = () => {
    if (valid.length > 0) {
      message.error(`校验未通过字段：${valid.toString()}`);
    }else {
      let _formData = formData;
      console.log(_formData)
      _formData.tabService.map((item:any,index:number)=> {
        postBigVEditService({
          value: JSON.stringify(item)
        }, `${item.userId}`).then((res: any) => {
          try {
            if (res.code !== '0000') {
              message.error(res.message);
            } else {
              // message.success('发布成功！');
              // setTimeout(() => {
              //   location.reload();
              // }, 1000)
            }
          } catch (e) {
            message.error(e.message);
          }
        })
      })
      postAllBigVEdit({
        value:JSON.stringify(_formData)
      }).then((res)=>{
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success('提交成功！');
          }
        } catch (e) {
          message.error(e.message);
        }
      })
    }

  }

  if (!init) return '加载中'
  return(
    <div className={'kycForm'}>
      <FormRender
        propsSchema={formConfig}
        uiSchema={uiSchema}
        formData={formData}
        onChange={setFormState}
        onValidate={setValid}
      />
      <Button type="primary" onClick={onSubmit}>提交</Button>
    </div>

  )

}

