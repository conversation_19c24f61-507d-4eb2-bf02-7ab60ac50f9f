/*
 * 机构客户数据维护
 * @Author: <PERSON><PERSON><PERSON><PERSON>@myhexin.com
 * @Date: 2023-02-16 15:56:07
 * @Last Modified by: z<PERSON><PERSON><PERSON>@myhexin.com
 * @Last Modified time: 2023-03-16 16:59:31
 */
import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';

import {
  Button,
  Col,
  Collapse,
  DatePicker,
  message,
  Modal,
  Row,
  Select,
  Spin,
  Table,
  Card,
  Input,
  Checkbox,
} from 'antd';
const { Panel } = Collapse;
const { Option } = Select;
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { operateEnum, operateMap } from '../type';
import api from 'api';
import { strategyMap } from '../type';
import record from '../../redMoney/record';
import CheckboxGroup, { CheckboxOptionType, CheckboxValueType } from 'antd/lib/checkbox/Group';

const { postHash, fetchHashAll, postHashDel, getTopicFunds } = api;
interface investProps {
  investAccountName: string;
  investPeopleArr: {
    peopleName: string;
    royaltyWeight: string;
  }[];
  returnPoints: string;
  returnFormula: string;
  managementFeeArr: {
    fundCode: string;
    fundName: string;
    feePercent: string;
  }[];
}
interface salesManProps {
  salesManName: string;
  strategy: string;
  phoneNumber: string;
  company: string;
}
const IndicatorMap = [
  { institutionTypeName: { label: '机构类型', value: 'institutionTypeName' } },
  // { investorName: { label: '投管人名称', value: 'investorName' } },
  // { accountName: { label: '投资账户名称', value: 'accountName' } },
  { accountTypeName: { label: '开户类型', value: 'accountTypeName' } },
  { bankName: { label: '托管人名称', value: 'bankName' } },
  { holdings: { label: '保有量', value: 'holdings' } },
  { moneyHoldings: { label: '货币保有量', value: 'moneyHoldings' } },
  { stockHoldings: { label: '非货保有量', value: 'stockHoldings' } },
  { bondFundHoldings: { label: '债券保有量', value: 'bondFundHoldings' } },
  { hybridFundHoldings: { label: '混合保有量', value: 'hybridFundHoldings' } },
  { stockFundHoldings: { label: '股票保有量', value: 'stockFundHoldings' } },
  { financeHoldings: { label: '资管产品保有量', value: 'financeHoldings' } },
  { tradeChannelName: { label: '交易通道', value: 'tradeChannelName' } },
  { strategyName: { label: '策略', value: 'strategyName' } },
  { saleInfoList: { label: '销售人员', value: 'saleInfoList' } },
  { custId: { label: '客户号', value: 'custId' } },
  { transactionAccountId: { label: '交易号', value: 'transactionAccountId' } },
  { moneySetDayTypeName: { label: '货基赎回交割日名称', value: 'moneySetDayTypeName' } },
  { stockSetDayTypeName: { label: '非货赎回交割日名称', value: 'stockSetDayTypeName' } },
  { openAccountDate: { label: '开户时间', value: 'openAccountDate' } },
  { returnRatio: { label: '返点比例', value: 'returnRatio' } },
  { managerFeeInfoList: { label: '管理费分成等级', value: 'managerFeeInfoList' } },
  { investment: { label: '投资状态', value: 'investment' } },
  { scFlagName: { label: '超转参数', value: 'scFlagName' } },
  { superConversionName: { label: '超转差额处理方式', value: 'superConversionName' } },
];
const canNotConvertMap = [
  { investorName: { label: '投管人名称', value: 'investorName' } },
  { accountName: { label: '投资账户名称', value: 'accountName' } },
];
const defaultIndictaotMap = [
  { institutionTypeName: { label: '机构类型', value: 'institutionTypeName' } },
  { accountTypeName: { label: '开户类型', value: 'accountTypeName' } },
  { bankName: { label: '托管人名称', value: 'bankName' } },
  { openAccountDate: { label: '开户时间', value: 'openAccountDate' } },
  { holdings: { label: '保有量', value: 'holdings' } },
  { moneySetDayTypeName: { label: '货基赎回交割日名称', value: 'moneySetDayTypeName' } },
  { stockHoldings: { label: '非货保有量', value: 'stockHoldings' } },
  { stockSetDayTypeName: { label: '非货赎回交割日名称', value: 'stockSetDayTypeName' } },
  { moneyHoldings: { label: '货币保有量', value: 'moneyHoldings' } },
  { scFlagName: { label: '超转参数', value: 'scFlagName' } },
  { financeHoldings: { label: '资管产品保有量', value: 'financeHoldings' } },
  { superConversionName: { label: '超转差额处理方式', value: 'superConversionName' } },
  { investment: { label: '投资状态', value: 'investment' } },
  { tradeChannelName: { label: '交易通道', value: 'tradeChannelName' } },
  { strategyName: { label: '策略', value: 'strategyName' } },
  { saleInfoList: { label: '销售人员', value: 'saleInfoList' } },
  { transactionAccountId: { label: '交易号', value: 'transactionAccountId' } },
];
const indicatorMapKey = IndicatorMap.map(e => {
  return Object.keys(e)[0];
});
interface IProps {
  show: boolean;
  onClose: () => void;
  onCheck: (val) => void;
}
const ModifyIndicators: FC<IProps> = ({ show, onClose, onCheck }) => {
  const [isListLoading, setIsListLoading] = useState<boolean>(false); // 列表加载loading
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [indicatorSelected, setIndicatorSelected] = useState<CheckboxOptionType[]>([]);
  const [indicatorAll, setIndicatorAll] = useState<CheckboxOptionType[]>([]);
  const [allCheckedList, setAllCheckedList] = useState<any[]>([]);
  const [selectedCheckedList, setSelectedCheckedList] = useState<any[]>([]);
  // 一个用来中专的变量
  const [indicatorBus, setIndicatorBus] = useState<CheckboxOptionType[]>([]);
  const [modalShow, setModalShow] = useState(false);
  useEffect(() => {
    setIndicatorAll(
      IndicatorMap.map((item, index) => {
        return item[Object.keys(item)[0]];
      }),
    );
    const localIndicator = JSON.parse(localStorage.getItem('JiShunTongIndicator'));
    const defaultSelctedIndicators = defaultIndictaotMap.map(item => item[Object.keys(item)[0]]);
    setIndicatorSelected(localIndicator || defaultSelctedIndicators);
    onCheck(localIndicator || defaultSelctedIndicators);
    setSelectedCheckedList(['Orange']);
  }, []);
  const getValueInMapArr = (key: string) => {
    let val = {};
    IndicatorMap.filter(item => {
      if (key === Object.keys(item)[0]) val = item[key];
    });
    return val as CheckboxOptionType;
  };
  const onChangeAllGroup = (checkedValues: CheckboxValueType[]) => {
    const tempArr: CheckboxOptionType[] = [];
    checkedValues.map((values: string, index) => {
      if (indicatorMapKey.indexOf(values) >= 0) tempArr.push(getValueInMapArr(values));
    });
    setAllCheckedList(checkedValues);
    setIndicatorBus(tempArr);
  };
  const onChangeSelectedGroup = (checkedValues: CheckboxValueType[]) => {
    const tempArr: any[] = [];
    const tempIndicatorBus: CheckboxOptionType[] = [];
    checkedValues.map((values: string, index) => {
      if (indicatorMapKey.indexOf(values) >= 0) {
        tempArr.push(values);
        tempIndicatorBus.push(getValueInMapArr(values));
      }
    });
    setIndicatorBus(tempIndicatorBus);
    setSelectedCheckedList(tempArr);
  };
  // 添加到已选列表
  const addToSelected = () => {
    const res = new Map();
    setIndicatorSelected(prev => {
      return [...prev, ...indicatorBus].filter(
        item => !res.has(item['value']) && res.set(item['value'], 1),
      );
    });
    const busValue = indicatorBus.map(e => {
      return e.value;
    });
    setSelectedCheckedList(prev => {
      return Array.from(new Set([...busValue, ...prev]));
    });
    setAllCheckedList([]);
  };
  // 指标删除
  const deleteFromSelected = () => {
    setIndicatorSelected(prev => {
      indicatorBus.forEach(item => {
        // if(prev.indexOf(item['value'])>=0){
        let index = prev.findIndex(({ label }) => label === item.label);
        prev.splice(index, 1);
        // }
      });
      return [...prev];
    });
    const busValue = indicatorBus.map(e => {
      return e.value;
    });
    setSelectedCheckedList(prev => {
      busValue.forEach(item => {
        let index = prev.findIndex(val => {
          val === item;
        });
        prev.splice(index, 1);
      });
      return Array.from(new Set(prev));
    });
    setAllCheckedList([]);
    // };
  };
  // 获取Option对应得value
  const getValueFromOption = (options: CheckboxOptionType[]) => {
    return (options.map(e => {
      return e.value;
    }) as unknown) as CheckboxValueType[];
  };
  return (
    <div>
      <Modal
        onCancel={() => {
          onClose();
        }}
        width={'60%'}
        visible={show}
        onOk={() => {
          let setLocalArr = indicatorSelected.map(item => item[Object.keys(item)[0]]);
          localStorage.setItem('JiShunTongIndicator', JSON.stringify(indicatorSelected));
          onCheck(indicatorSelected);
          onClose();
        }}
      >
        <Card title={'选择指标'}>
          <Row gutter={24}>
            <Col span={10}>
              <Card title={'全部指标'}>
                <CheckboxGroup
                  options={indicatorAll}
                  value={allCheckedList}
                  onChange={onChangeAllGroup}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Row type="flex" justify="center">
                <Col>
                  <Button onClick={addToSelected} style={{ marginBottom: '20px' }}>
                    {'添加 >'}
                  </Button>
                </Col>
              </Row>
              <Row type="flex" justify="center">
                <Button onClick={deleteFromSelected} style={{ marginBottom: '40px' }}>
                  {'< 删除'}
                </Button>
              </Row>
              <Row type="flex" justify="center">
                <Button
                  style={{ marginBottom: '20px' }}
                  onClick={() => {
                    setSelectedCheckedList([]);
                    setIndicatorSelected([]);
                  }}
                >
                  {'清空已选'}
                </Button>
              </Row>
              <Row type="flex" justify="center">
                <Button
                  style={{ marginBottom: '40px' }}
                  onClick={() => {
                    const defaultSelctedIndicators = [...defaultIndictaotMap].map(
                      item => item[Object.keys(item)[0]],
                    );
                    setIndicatorSelected(defaultSelctedIndicators);
                  }}
                >
                  {'恢复默认'}
                </Button>
              </Row>
            </Col>
            <Col span={10}>
              <Card title={'已选指标'}>
                <CheckboxGroup
                  options={indicatorSelected}
                  onChange={onChangeSelectedGroup}
                  value={selectedCheckedList}
                />
              </Card>
            </Col>
          </Row>
        </Card>
      </Modal>
    </div>
  );
};
export default ModifyIndicators;
