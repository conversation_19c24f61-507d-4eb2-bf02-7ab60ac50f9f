import React, { useState } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import api from 'api';
import UploadForm from '../Upload';

const { queryCotaid } = api;

const ProfessionalAnalysisForm = props => {
  const {
    getFieldDecorator,
    getFieldValue,
    setFieldsValue,
    analysisCardNum,
    setAnalysisCardNum,
  } = props;
  const handleQuery = (i: number) => {
    queryCotaid({
      themeId: getFieldValue(`otherWelfare.professionalAnalysis.cardList[${i}].cardId`),
    })
      .then(res => {
        if (res.status_code === 0) {
          setFieldsValue({
            [`otherWelfare.professionalAnalysis.cardList[${i}].cardKey`]: res.data,
          });
        } else {
          message.error('查询失败，请输入正确的id');
        }
      })
      .catch(err => {
        message.error(err);
      });
  };
  const analysisCardList = [];
  for (let i = 0; i < analysisCardNum; i++) {
    const analysisCardItem = (
      <Card key={i}>
        <Form.Item label="输入cota平台id">
          {getFieldDecorator(`otherWelfare.professionalAnalysis.cardList[${i}].cardId`, {
            rules: [{ required: true, message: '请输入平台id' }],
          })(<Input />)}
        </Form.Item>
        <Button style={{ float: 'right', zIndex: 99 }} onClick={() => handleQuery(i)}>
          查询
        </Button>
        <Form.Item label="查询到的id">
          {getFieldDecorator(`otherWelfare.professionalAnalysis.cardList[${i}].cardKey`, {
            rules: [{ required: true, message: '请查询平台id' }],
          })(<Input disabled={true} />)}
        </Form.Item>
        <UploadForm
          getFieldDecorator={getFieldDecorator}
          fieldLocation={`otherWelfare.professionalAnalysis.cardList[${i}].icon`}
          label="图标"
          required={true}
        />
        <Form.Item label="卡片名称">
          {getFieldDecorator(`otherWelfare.professionalAnalysis.cardList[${i}].cardName`, {
            rules: [{ required: true, message: '请输入卡片名称' }],
          })(<Input />)}
        </Form.Item>
      </Card>
    );
    analysisCardList.push(analysisCardItem);
  }
  return (
    <>
      <Form.Item label="tab名称">
        {getFieldDecorator('otherWelfare.professionalAnalysis.tabName', {
          rules: [{ required: true, message: '请输入tab名称' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="角标">
        {getFieldDecorator('otherWelfare.professionalAnalysis.mark')(<Input />)}
      </Form.Item>
      {analysisCardList}
      <Button
        onClick={() => {
          setAnalysisCardNum(analysisCardNum + 1);
        }}
      >
        新增
      </Button>
      <Button
        onClick={() => setAnalysisCardNum(analysisCardNum - 1)}
        disabled={analysisCardNum === 0}
      >
        删除
      </Button>
      <Form.Item label="查看更多跳转链接">
        {getFieldDecorator('otherWelfare.professionalAnalysis.seeMoreUrl')(<Input />)}
      </Form.Item>
    </>
  );
};

export default ProfessionalAnalysisForm;
