import React, { useState, useEffect } from 'react';
import { Radio, Select, Input, Button, Popconfirm, message, Upload, Row, Col } from 'antd';
import api from 'api';
import styles from '../../index.less'
import classNames from 'classnames';

const { fetchAccountDownLoad, fetchAccountTemplate } = api;

interface iProps {
    name: string,
    position: number,
    pageType: string,
    fileType: string,
    saveFile: (file: any, type: string) => void,
    isEdit: boolean,
    ifHidden?: boolean,
    describe?: string,
    isExist?: boolean //是否存在与可下载
}
function getFileName(type: string) {
    switch (type) {
        case 'blackListFile':
            return '用户黑名单'
        case 'holdFundFile':
            return '持仓-自定义基金列表文件'
        case 'holdDetailFundFile':
            return '持仓详情-自定义基金列表文件'
        case 'myFundFile':
            return '我的-自定义基金列表文件'
        case 'selectFundFile':
            return '自选-自定义基金列表文件'
        case 'singleFundFile':
            return '个基-自定义基金列表文件'
        case 'whiteListFile':
            return '用户白名单文件'
        default:
            return '未知文件'
    }
}

export default React.memo(function ({ name, fileType, saveFile, isEdit, ifHidden, describe, isExist, pageType, position }: iProps) {
    const [fileList, setFileList] = useState<any>([]);

    const readWorkbookFromLocalFile = (file: any) => {
        let fileArr = [file];
        setFileList(fileArr);

        saveFile(file, fileType);
        return false
    }
    const download = () => {
        fetchAccountDownLoad({}, `?type=${pageType}&filename=${fileType}&configIndex=${position}`, '', { responseType: 'blob' }).then((res: any) => {
            if (!res.success) message.error(res.message)
        })
    }
    const downloadModel = () => {
        fetchAccountTemplate({}, `?type=${pageType}&filename=${fileType}`, '', { responseType: 'blob' }).then((res: any) => {
            if (!res.success) message.error(res.message)
        })
    }
    const removeFile = () => {
        setFileList([]);
        saveFile(null, fileType);
    }
    return (
        <section>
            <div key='file' className={classNames(styles['m-tagModel-row'], ifHidden ? 'z-hide' : '')}>
                <Row style={{width: '100%'}}>
                    <Col span={3}><p className={styles['m-card-label']}>{name}:</p></Col>
                    <Col span={2} className={isExist ? '' : 'z-hide'}>
                        <a onClick={download} className={'f-unl'}>{getFileName(fileType)}</a>
                    </Col>
                    <Col span={2} offset={1}>
                        <Upload
                            accept=".xls,.xlsx"
                            beforeUpload={readWorkbookFromLocalFile}
                            onRemove={removeFile}
                            fileList={fileList}
                        >
                            <Button disabled={!isEdit}>上传文件</Button>
                        </Upload>

                    </Col>
                    <Col span={2}>
                        <Button disabled={!isEdit} onClick={downloadModel}>下载模板</Button>
                    </Col>
                    <Col span={7}>
                        <p style={{ color: "red" }}>{describe}</p>
                    </Col>
                </Row>
            </div>
        </section>
    )
})

