import api from 'api';
import { FundDetail, FundRate, PlateData, FundFormData, ApiResponse } from '../types/index';
/**
 * 获取基金详情
 * @param fundCode 基金代码
 */
export const getFundDetail = async (fundCode: string): Promise<FundDetail> => {
  const res = await api.cooperateGetDetail({ fundCode }) as ApiResponse<FundDetail>;
  if (res?.code !== '0000') {
    throw new Error(res?.message || '获取基金数据失败');
  }
  return res.data;
};

/**
 * 获取基金热度值
 * @param code 基金代码
 */
export const getFundPopularity = async (code: string): Promise<FundRate> => {
  const res = await api.cooperateGetPopularity({ code }) as ApiResponse<FundRate>;
  if (res?.code !== '0000') {
    throw new Error(res?.message || '获取热度值失败');
  }
  return res.data;
};

/**
 * 获取基金自选值
 * @param code 基金代码
 */
export const getFundSelfSelect = async (code: string): Promise<FundRate> => {
  const res = await api.cooperateGetSelfSelectCount({ code }) as ApiResponse<FundRate>;
  if (res?.code !== '0000') {
    throw new Error(res?.message || '获取自选值失败');
  }
  return res.data;
};

/**
 * 获取板块数据
 * @param keyword 搜索关键词
 */
export const getPlateData = async (keyword: string): Promise<PlateData> => {
  const res = await api.cooperatePlateGet({}, keyword) as ApiResponse<PlateData>;
  if (res?.code !== '0000') {
    throw new Error(res?.message || '获取板块数据失败');
  }
  return res.data;
};

/**
 * 保存基金数据
 * @param data 基金数据
 * @param fundCode 基金代码
 * @param isEdit 是否为编辑模式
 */
export const saveFundData = async (data: FundFormData, fundCode: string, isEdit: boolean): Promise<void> => {
  const method = isEdit ? 'cooperateEdit' : 'cooperateAdd';
  const res = await api[method]({ fundNewsEtfStr: JSON.stringify(data) }, fundCode) as ApiResponse<void>;
  if (res?.code !== '0000') {
    throw new Error(res?.message || '保存失败，请稍后重试');
  }
  return res.data;
};
