import React, { useEffect, useState } from 'react';
import api from 'api';
import zhCN from 'antd/es/locale/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { DatePicker, ConfigProvider, Select, message, Button } from 'antd';
import styles from './index.less';
import { AccountTypeList, InvestorInfoList, strategyKeyMap, OrganizationInfoList, SaleInfoList, strategyMap, TradeChannelList, ClientDatIndicator, SaleInfoListZ, showFiltersMapProps } from '../type';
import { mockFilterData } from '../mock'
moment.locale('en');
const { Option } = Select;
const { RangePicker } = DatePicker;
const { fetchChannel, fetchAccountInfo, fetchFofQueryCondition } = api;

export function SummaryFilter(props: any) {
  const showArr = props.showArr as showFiltersMapProps;
  const { propSalesManInit } = props
  // 渠道
  const [formChannnel, setFormChannel] = useState({});
  // 账户名称
  const [formAccount, setFormAccount] = useState<any[]>([]);
  const [saleInfoList, setSaleInfoList] = useState<SaleInfoListZ[]>([]);
  const [organizationInfoList, setOrganizationInfoList] = useState<OrganizationInfoList[]>([]);
  const [investorInfoList, setInvestorInfoList] = useState<InvestorInfoList[]>([]);
  const [tradeChannelList, setTradeChannelList] = useState<TradeChannelList[]>([]);
  const [accountTypeList, setAccountTypeList] = useState<AccountTypeList[]>([]);
  const [searchParams, setSearchParams] = useState<any>();
  const [searchMap, setSearchMap] = useState<any>({
    saleInfoKeyMap: {},
    organizationInfoKeyMap: {},
    investorInfoKeyMap: {},
  });
  // 渠道与账户类型
  const [ajaxParams, setParams] = useState<{
    types: string;
    channels: string[];
  }>({
    types: '',
    channels: [],
  });
  useEffect(() => {
    // 获取所有渠道信息
    fetchFofQueryCondition()
      .then((res: any) => {
        if (res.status_code === 0) {
          console.log('fetchFofQueryCondition', res)
          // setFormChannel(res.data);
          let result = res.data as Partial<ClientDatIndicator>;
          setSaleInfoList(result.saleInfoList || []);
          if (propSalesManInit) { propSalesManInit(result.saleInfoList || []) }
          const tempSaleInfoMap = {};
          const tempInvestInfoMap = {};
          const tempOrganizationInfoMap = {};
          result.saleInfoList?.forEach((item) => { tempSaleInfoMap[item.saleName] = item.saleId });
          setInvestorInfoList(result.investorInfoList || []);
          result.investorInfoList?.forEach((item) => { tempInvestInfoMap[item.investorName] = item.orgId });
          setOrganizationInfoList(result.accountInfoList || []);
          result.accountInfoList?.forEach(item => { tempOrganizationInfoMap[item.accountName] = item.custId });
          setTradeChannelList(result.tradeChannelList)
          setSearchMap({
            saleInfoKeyMap: tempSaleInfoMap,
            organizationInfoKeyMap: tempOrganizationInfoMap,
            investorInfoKeyMap: tempInvestInfoMap
          })
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
        let res = mockFilterData;

        message.warn('网络请求错误，请稍后再试');

      });
  }, []);
  // 统计时间变化
  const handleTimeChange = (date: any) => {
    triggerChange({ startDate: moment(date[0]).format('yyyyMMDD'), endDate: moment(date[1]).format('yyyyMMDD') });
  };
  const triggerChange = (changedValue: any) => {
    // const { onChange, value } = props;
    // if (onChange) {
    setSearchParams(prev => ({
      ...prev,
      ...changedValue,
    }));
    // }
  };
  // 可选时间范围
  const disabledDate = (currentDate: any) => {
    return currentDate && currentDate > moment().endOf('day');
  };


  return (
    <article className={styles['m-filter-warpper']}>

      <section>
        <ConfigProvider locale={zhCN}>
          <RangePicker
            allowClear={false}
            mode="date"
            disabledDate={disabledDate}
            onChange={handleTimeChange}
          />
        </ConfigProvider>
      </section>
      {showArr.strategy && <section>
        策略：
        <Select
          style={{ width: 200, marginRight: 20 }}
          placeholder="策略"
          onChange={(val) => {
            triggerChange({ strategyCode: val })
          }}
        >
          {Object.keys(strategyMap)?.map((nam, index) => {
            return <Option value={nam} key={nam}>{strategyMap[nam]}</Option>
          })}
        </Select>
      </section>}
      {showArr.sale && <section>
        销售人员：
        <Select
          showSearch={true}
          mode="multiple"
          style={{ width: '200px' }}
          onChange={(val) => {
            triggerChange({ saleId: val?.map(v => (searchMap.saleInfoKeyMap[v])).join(',') })
          }}
        >
          {(saleInfoList as SaleInfoListZ[]).map(item => (
            <Option key={item.saleId} value={item.saleName}>
              {item.saleName}
            </Option>
          ))}
        </Select>
      </section>}
      {showArr.account && <section>
        投管账户名称：
        <Select
          showSearch={true}
          mode="multiple"
          style={{ width: '200px' }}
          onChange={(val) => {
            console.log('val', val)
            triggerChange({ custId: val?.map(v => { return v.match(/（(.+?)）/)[1] }).join(',') })
          }}
        >
          {(organizationInfoList).map((item, index) => (
            <Option key={item.custId + index} value={`${item.accountName}（${item.custId}）`}>
              {`${item.accountName}（${item.custId}）`}
            </Option>
          ))}
        </Select>
      </section>}

      {showArr.investor && <section>
        投管人名称：
        <Select
          showSearch={true}
          mode="multiple"
          style={{ width: '200px' }}
          onChange={(val) => {
            triggerChange({ orgId: val?.map(v => (searchMap.investorInfoKeyMap[v])).join(',') })
          }}
        >
          {(investorInfoList).map(item => (
            <Option key={item.custId} value={item.investorName}>
              {item.investorName}
            </Option>
          ))}
        </Select>
      </section>}

      {showArr.tradeChannel && <section>
        交易通道：
        <Select
          style={{ width: '200px' }}
          onChange={(val) => {
            triggerChange({ tradeChannel: val })
          }}
        >
          {(tradeChannelList).map(item => (
            <Option key={item.name} value={item.code}>
              {item.name}
            </Option>
          ))}
        </Select>
      </section>}
      {showArr.accountType && <section>
        账户类型：
        <Select
          style={{ width: '150px' }}
          onChange={(val) => {
            triggerChange({ accountType: val })
          }}
        >
          {[{ '0': '机构户' }, { '2': '产品' }].map(item => (
            <Option key={Object.keys(item)[0]} value={Object.keys(item)[0]}>
              {item[`${Object.keys(item)[0]}`]}
            </Option>
          ))}
        </Select>
      </section>
      }
      <section>
        <Button onClick={() => { const { submitSearch } = props; submitSearch(searchParams) }}>查询</Button>
      </section>
    </article>
  );
}
