import React, { useEffect, useState } from 'react';
import { Modal, Input, message, Popconfirm, Button } from  'antd';
import api from 'api'

const {
    fetchZxFunds,
    updateZxFunds
} = api

export default React.memo(function({
    isShowZXData,
    setIsShowZXData,
    refresh
}: {
    isShowZXData: boolean
    setIsShowZXData: Function
    refresh: Function
}) {

    const [ZXFunds, setZXFunds] = useState('') //个基开启资讯tab的基金

    useEffect(() => {
        if (!isShowZXData) return
        fetchZxFunds().then((data: any) => {
            if (data.status_code === 0) {
                const _fundList = data?.data
                if (!_fundList) setZXFunds('')
                else setZXFunds(_fundList.join(','))
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            console.log(e.message)
            message.error('系统错误请稍候再试～')
        })
    }, [isShowZXData])

    function upload() {
        let funds: string[] = ZXFunds.split(',').filter(item => item)
        updateZxFunds({
            data: funds
        }).then((data: any) => {
            if (data.status_code === 0) {
                message.success('更新成功')
                setIsShowZXData(false)
                refresh()
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            message.error('系统错误请稍候再试～')
        })
    }

    return (
        <Modal 
        title="上传基金代码（代码间用英文逗号隔开，请注意不要重复）" 
        visible={isShowZXData} 
        onCancel={() =>{setIsShowZXData(false)}}
        footer={
            <div className="u-r-middle">
                <Button type="primary" onClick={() =>{setIsShowZXData(false)}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={upload}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button type="danger" >提交</Button>
                </Popconfirm>
            </div>
        }
        >
            <Input.TextArea 
            value={ZXFunds}
            onChange={(e) => {setZXFunds(e.target.value)}}
            rows={5}
            ></Input.TextArea>
        </Modal>
    )
})