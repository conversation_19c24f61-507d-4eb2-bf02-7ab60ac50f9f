.m-feed-config {
  > p {
    font-size: 20px;
    font-weight: bold;
  }
  > p:nth-child(3) {
    margin-top: 40px;
  }
  .m-edit {
    [class*="m-value-item"] {
      margin: 10px 0;
    }
  }
  .m-table {
    .m-table-icon {
      width: 40px;
      height: 40px;
    }
    .m-pagination {
      margin-top: 20px;
    }
  }
}
:global {
  .m-value-item {
    > em {
      font-size: 14px;
      font-family: SimSun, sans-serif;
    }
    > span {
      min-width: 100px;
      display: inline-block;
    }
    &.m-type-title {
      > input {
        width: 320px;
      }
    }
    .m-required-icon {
      color: red;
    }
    .m-tip {
      margin-left: 16px;
      color: red;
    }
  }
  .m-input {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: 'tnum';
    position: relative;
    display: inline-block;
    width: 140px;
    height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
    overflow: visible;
    &.m-required {
      border-color: #e21c1c;
    }
    &.m-required:hover {
      border-color: #e21c1c;
    }
    &.m-required:focus {
      border-color: #e21c1c;
      box-shadow: 0 0 0 2px rgba(226, 28, 28, 0.2);
    }
    &:hover {
      border-color: #40a9ff;
      border-right-width: 1px !important;
    }
    &:focus {
      border-color: #40a9ff;
      border-right-width: 1px !important;
      outline: 0;
      box-shadow: 0 0 0 2px rgba(24,144,255,.2);
    }
  }
  .m-select {
    width: 180px;
    &.m-required > div {
      border-color: #f16a6a;
    }
    &.m-required:hover > div {
      border-color: #f16a6a;
    }
    &.m-required:focus > div {
      border-color: #f16a6a;
      box-shadow: 0 0 0 2px rgba(226, 28, 28, 0.2);
    }
  }
  [class*="frontend__m-card-label"] {
    text-align: left !important;
  }
}