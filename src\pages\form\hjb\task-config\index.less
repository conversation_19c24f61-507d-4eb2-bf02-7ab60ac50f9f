/* styles */

.tasks-config {
  height: calc(100vh - 150px - 48px);
  padding-right: 16px;
  margin-right: -16px;
  overflow: auto;
}

.tasks {
  height: 100%;
  display: flex;
  flex-direction: column;

  > .task-list-nav {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 32px;

    > * {
      margin-left: 8px;
    }

    > div {
      height: 75%;
      margin: 0 16px 0 24px;
    }
  }

  > .task-list-container {
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;

    .task-list-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 8px 0;
    }

    .task-list-column {
      text-align: center;
      flex: 1;
      width: 0;

      &:nth-child(1) {
        // text-align: center;
        flex: none;
        width: 48px;
      }

      &:nth-child(2) {
        flex: none;
        width: 60px;
      }

      // &:nth-child(3) {
      //   flex: none;
      //   width: 120px;
      // }

      &:nth-child(4) {
        flex: none;
        width: 120px;
      }

      &:nth-child(5) {
        flex: none;
        width: 180px;
      }

      &:nth-child(6) {
        flex: none;
        width: 160px;
      }

      &:nth-child(7) {
        flex: none;
        width: 180px;
      }

      // &:nth-child(8) {
      //   flex: none;
      //   width: 160px;
      // }

      &:nth-child(8) {
        flex: none;
        width: 148px;
      }

      &:last-child {
        flex: none;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 96px;

        > * + * {
          margin-left: 8px;
        }
      }
    }

    > .task-list-header {
      padding: 12px 0;
      background-color: #f5f5f5;

      > .task-list-column {
        font-weight: bold;
      }
    }

    > .task-list-body {
      flex: 1;
      height: 0;
      overflow-y: auto;
      padding-bottom: 40px;

      > .task-list-item {
        border-bottom: 1px solid #eee;

        &:hover {
          background-color: #eee;
        }

        .tag {
          margin: 0 0 0 6px;
        }
      }
    }
  }
}

.task-form {
  height: 100%;
  display: flex;
  flex-direction: column;

  > .task-form-nav {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 16px;
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);

    > *:first-child {
      margin-right: auto;
    }

    > *:last-child {
      margin-left: 8px;
    }
  }

  > .task-form-body {
    flex: 1;
    height: 0;
    overflow: auto;

    > form {
      margin: 0 auto;
      max-width: 640px;
      padding: 16px 16px 32px;

      .task-form-block {
        overflow: hidden;
        padding: 16px 16px 8px;
        border: 1px dashed #ddd;
        border-radius: 8px;
      }

      .task-form-block + .task-form-block {
        margin-top: 16px;
      }
    }
  }
}
