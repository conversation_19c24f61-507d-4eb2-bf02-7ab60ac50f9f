{"type": "page", "body": [{"type": "crud", "syncLocation": false, "api": {"url": "/common_config/hash_data_get_all?key=AUTH_ERROR_DICT", "adaptor": "const finalData = []\r\nObject.keys(payload.data).map(keyItem=>{console.log(JSON.parse((payload.data[keyItem])))\r\nfinalData.push({THSErrorCode: keyItem,value:JSON.parse(payload.data[keyItem]).value,value1:JSON.stringify(JSON.parse(payload.data[keyItem]).value.text)})}) \r\n return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.message,\r\n data: finalData\r\n}"}, "loadDataOnce": true, "autoGenerateFilter": true, "filterSettingSource": ["THSErrorCode", "value1"], "columns": [{"name": "THSErrorCode", "label": "同花顺错误码", "id": "u:ae3832892f91", "searchable": {"type": "input-text", "name": "THSErrorCode", "label": "同花顺错误码", "clearable": true, "multiple": true, "searchable": true, "id": "u:6fd7affc805b"}}, {"name": "value1", "label": "同花顺错误描述及解决方案", "id": "u:16f1ac2f1dc8", "searchable": {"type": "input-text", "name": "value1", "label": "同花顺错误描述及解决方案", "clearable": true, "multiple": true, "searchable": true, "id": "u:6fd7affc805b"}}, {"type": "operation", "label": "操作", "id": "u:96d5b5297574", "buttons": [{"label": "编辑", "type": "button", "actionType": "dialog", "level": "link", "dialog": {"title": "编辑", "size": "lg", "body": {"type": "form", "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"key": "AUTH_ERROR_DICT", "propName": "${THSErrorCode}", "&": "$$"}, "dataType": "form", "requestAdaptor": "const { url, data } = api;\r\nconst postBody = JSON.stringify(data)\r\nconsole.log(postBody)\r\napi.data={value:postBody,key:'AUTH_ERROR_DICT',propName:`${data.propName}`} \r\nreturn { ...api  };", "adaptor": "return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.poolFundHistoryDtoList || []\r\n  }\r\n};"}, "body": [{"type": "input-text", "name": "THSErrorCode", "label": "同花顺错误码", "id": "u:4eee8d512aa3", "required": true}, {"name": "value", "label": "同花顺错误描述及解决方案", "id": "u:b37156aca728", "type": "combo", "multiLine": true, "items": [{"name": "text", "label": "主文案", "id": "u:ce358b0ab8dc", "type": "input-text", "size": "full"}, {"name": "highlight", "label": "高亮文本组", "type": "combo", "multiple": true, "items": [{"name": "highlightText", "label": "高亮文本", "id": "u:ce358b0ab12c", "type": "input-text"}, {"name": "textColor", "label": "颜色", "id": "u:ce152b0ab12c", "type": "input-text"}, {"name": "jumpUrl", "label": "跳转链接", "id": "u:ce128b0ab12c", "type": "input-text"}]}]}]}}, "id": "u:11d4d8ca99d5"}, {"type": "button", "label": "删除", "actionType": "ajax", "level": "link", "className": "text-danger", "confirmText": "确定要删除？", "api": {"method": "post", "url": "/common_config/hash_data_del", "dataType": "form", "messages": {}, "adaptor": "return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.message}", "data": {"key": "AUTH_ERROR_DICT", "propName": "${THSErrorCode}"}}, "id": "u:4eee8d50eaa3"}]}], "id": "u:f229bd411595", "features": ["create"], "bulkActions": [], "headerToolbar": [{"label": "新增", "type": "button", "actionType": "dialog", "level": "primary", "dialog": {"title": "新增", "size": "lg", "body": {"type": "form", "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"key": "AUTH_ERROR_DICT", "propName": "${THSErrorCode}", "&": "$$"}, "dataType": "form", "requestAdaptor": "const { url, data } = api;\r\nconst postBody = JSON.stringify(data)\r\nconsole.log(postBody)\r\napi.data={value:postBody,key:'AUTH_ERROR_DICT',propName:`${data.propName}`} \r\nreturn { ...api  };", "adaptor": "return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.poolFundHistoryDtoList || []\r\n  }\r\n};"}, "body": [{"type": "input-text", "name": "THSErrorCode", "label": "同花顺错误码", "id": "u:4eee8d512aa3", "required": true}, {"name": "value", "label": "同花顺错误描述及解决方案", "id": "u:b37156aca728", "type": "combo", "multiLine": true, "items": [{"name": "text", "label": "主文案", "id": "u:ce358b0ab8dc", "type": "input-text", "size": "full"}, {"name": "highlight", "label": "高亮文本组", "type": "combo", "multiple": true, "items": [{"name": "highlightText", "label": "高亮文本", "id": "u:ce358b0ab12c", "type": "input-text"}, {"name": "textColor", "label": "颜色", "id": "u:ce152b0ab12c", "type": "input-text"}, {"name": "jumpUrl", "label": "跳转链接", "id": "u:ce128b0ab12c", "type": "input-text"}]}]}]}}, "id": "u:16b6381a0efd"}, "export-csv", "bulkActions"]}], "id": "u:1e770f769e23"}