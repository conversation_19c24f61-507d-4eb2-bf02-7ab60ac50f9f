export interface EditData {
  fundPoolId?: string;
  icon?: string;
  marketReason?: string;
  buttonReason?: string;
}
export interface tableData extends EditData {
  key?: string;
  createUser?: string; // 创建人
  updateUser?: string; // 下线人
  updateTime?: string; // 最近修改时间
  poolStatus?: string; // 策略状态 上线/下线
}
export type Col = {
  dataIndex: tableKey;
  key: tableKey;
  title: string;
  len?: number;
  render?: Function;
  tableItem?: boolean; // 是否仅是表格数据
}
export type editKey = 'fundPoolId'|'icon'|'marketReason'|'buttonReason';
export type tableKey = editKey|'createUser'|'updateUser'|'updateTime'|'option'

export type TabItem = {
  tag: string;
  tabName: string;
}