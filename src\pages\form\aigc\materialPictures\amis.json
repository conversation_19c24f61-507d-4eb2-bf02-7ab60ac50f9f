{"title": "图片审核", "body": {"type": "crud", "syncLocation": false, "api": {"url": "/aigc/audit/page", "method": "post", "data": {"userLabel": "${userLabel|default:undefined}", "industryLabel": "${industryLabel|default:undefined}", "auditStatus": "${auditStatus|default:undefined}", "fileType": "1", "timeType": "${timeType|default:undefined}", "minModifyTime": "${dateRange|split|nth: 0|default:undefined}", "maxModifyTime": "${dateRange|split|nth: 1|default:undefined}", "offSet": "${currentPage}", "pageSize": "${pageSize}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.aigcAuditInfoList || []\r\n  }\r\n};"}, "footerToolbar": ["switch-per-page", "pagination", "statistics"], "columnsTogglable": false, "keepItemSelectionOnPageChange": false, "checkOnItemClick": true, "alwaysShowPagination": true, "name": "crud_materialPictures", "className": "text-lg", "filterTogglable": true, "pageField": "currentPage", "perPageField": "pageSize", "showPerPage": true, "showPageInput": true, "perPageAvailable": [40, 100, 200, 500], "perPage": 40, "mode": "cards", "filter": {"title": "", "body": [{"type": "select", "name": "userLabel", "label": "图片使用词（用户侧）", "clearable": true, "clearValueOnEmpty": true, "size": "md", "required": false, "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "return {\r\n  status: 0,\r\n  msg: '', data: payload.data.imgUserList\r\n}", "cache": 2000}}, {"type": "select", "name": "industryLabel", "label": "图片使用词（基金侧）", "clearable": true, "clearValueOnEmpty": true, "size": "md", "required": false, "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "return {\r\n  status: 0,\r\n  msg: '', data: payload.data.imgFundList\r\n}", "cache": 2000}}, {"type": "select", "label": "状态", "name": "auditStatus", "multiple": false, "options": [{"label": "全部", "value": ""}, {"label": "待审核", "value": "0"}, {"label": "审核通过", "value": "1"}, {"label": "审核不通过", "value": "2"}], "value": "", "clearValueOnEmpty": true, "size": "sm"}, {"type": "select", "label": "皮肤", "name": "timeType", "multiple": false, "options": [{"label": "全部", "value": ""}, {"label": "浅色版", "value": "0"}, {"label": "深色版", "value": "1"}], "value": "", "size": "sm"}, {"type": "input-datetime-range", "name": "date<PERSON><PERSON><PERSON>", "label": "入库时间", "format": "YYYY-MM-DD HH:mm:ss", "placeholder": "-", "className": "text-lg", "clearable": true, "size": "md"}, {"type": "submit", "label": "查询", "level": "primary"}, {"type": "reset", "label": "重置", "level": "primary"}, {"type": "button", "label": "上传图片", "onEvent": {"click": {"actions": [{"dialog": {"type": "dialog", "size": "lg", "title": "图片上传", "body": [{"type": "form", "title": "表单", "mode": "horizontal", "horizontal": {"leftFixed": true}, "reload": "crud_materialPictures", "body": [{"type": "input-image", "name": "ii_materialPictures", "label": "上传图片", "multiple": true, "drag": true, "receiver": "/aigc/audit/upload/img", "fileField": "files", "hideUploadButton": true, "autoUpload": false}], "wrapWithPanel": false}], "showCloseButton": true, "showErrorMsg": false, "showLoading": true, "closeOnEsc": true, "id": "u:788364bf06fc", "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"componentId": "u:788364bf06fc", "groupType": "component", "actionType": "confirm"}]}}, "level": "primary"}]}, "actionType": "dialog"}]}}, "level": "enhance"}]}, "bulkActions": [{"label": "批量通过", "actionType": "ajax", "api": {"url": "/aigc/audit/approve", "sendOn": "${LENGTH(ARRAYMAP(ARRAYFILTER(items, item => item.auditStatus != 1), item => item.id)) > 0}", "method": "post", "data": {"approve": "1", "idList": "${ARRAYMAP(ARRAYFILTER(items, item => item.auditStatus != 1), item => item.id)}"}}}, {"label": "批量不通过", "actionType": "ajax", "api": {"url": "/aigc/audit/approve", "method": "post", "sendOn": "${LENGTH(ARRAYMAP(ARRAYFILTER(items, (item.auditStatus != 1 && item.auditStatus != 2)), item => item.id)) > 0}", "data": {"approve": "2", "idList": "${ARRAYMAP(ARRAYFILTER(items, (item.auditStatus != 1 && item.auditStatus != 2)), item => item.id)}"}}}, {"label": "批量删除", "actionType": "ajax", "api": {"url": "/aigc/audit/delete", "method": "post", "data": {"idList": "${ARRAYMAP(items, item => item.id)}"}}}], "draggable": false, "card": {"body": [{"type": "image", "src": "$urlContent", "width": "240px", "enlargeAble": true, "showToolbar": true}, {"name": "userLabel", "label": "图片词（用户）", "labelClassName": "w-28"}, {"name": "industryLabel", "label": "图片词（基金）", "labelClassName": "w-28"}, {"name": "auditStatus", "label": "审核状态", "type": "mapping", "map": {"0": "<span class='label label-warning'>待审核</span>", "1": "<span class='label label-success'>审核通过</span>", "2": "<span class='label label-default'>审核不通过</span>"}, "labelClassName": "w-28"}, {"name": "modifyTime", "label": "入库时间", "labelClassName": "w-28"}, {"name": "fileVersion", "label": "展示优先级", "labelClassName": "w-28"}, {"name": "timeType", "label": "皮肤", "type": "mapping", "map": {"0": "浅色版", "1": "深色版"}, "labelClassName": "w-28"}], "actions": [{"type": "button", "label": "通过", "actionType": "ajax", "disabledOn": "auditStatus == 1", "api": {"url": "/aigc/audit/approve", "method": "post", "data": {"approve": "1", "idList": ["${id}"]}}}, {"type": "button", "label": "不通过", "actionType": "ajax", "disabledOn": "auditStatus == 1 || auditStatus == 2", "api": {"url": "/aigc/audit/approve", "method": "post", "data": {"approve": "2", "idList": ["${id}"]}}}, {"type": "button", "label": "删除", "actionType": "ajax", "api": {"url": "/aigc/audit/delete", "method": "post", "data": {"idList": ["${id}"]}}}]}}}