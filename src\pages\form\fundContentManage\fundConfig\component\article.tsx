import React, { useEffect } from 'react';
import FormRender from 'form-render/lib/antd';
import { Modal, message, Popconfirm, Button } from 'antd';
import { NEW_ARTICLE, OLD_ARTICLE } from './../const';

import api from 'api';

const { postArticle } = api;

export default React.memo(function({
  isShow,
  setIsShow,
  articleValue,
  setArticleValue,
  type,
  refresh,
}: any) {
  useEffect(() => {
    if (!isShow) return;
    if (articleValue.sendSelectedHold && !articleValue.startTime && !articleValue.endTime) {
      let _value = { ...articleValue };
      console.log(new Date());
      // 计算时间 取消时间偏移 并加上时区时间
      _value.startTime = handleTime(new Date().getTime());
      _value.endTime = handleTime(+new Date(_value.startTime) + 3600 * 24 * 7 * 1000);
      setArticleValue(_value);
    }
  }, [articleValue]);

  function handleTime(date: string | number) {
    if (!date) return '';
    let _date = new Date(date);
    return `${_date.getFullYear()}-${
      _date.getMonth() + 1 >= 10 ? _date.getMonth() + 1 : '0' + (_date.getMonth() + 1)
    }-${_date.getDate() >= 10 ? _date.getDate() : '0' + _date.getDate()} ${
      _date.getHours() >= 10 ? _date.getHours() : '0' + _date.getHours()
    }:${_date.getMinutes() >= 10 ? _date.getMinutes() : '0' + _date.getMinutes()}:${
      _date.getSeconds() >= 10 ? _date.getSeconds() : '0' + _date.getSeconds()
    }`;
  }

  function upload() {
    const {
      contentType,
      endTime,
      funds,
      sendSelectedHold,
      itemId,
      label,
      selectedContent,
      startTime,
    } = articleValue;
    if (!itemId) return message.error('请填写id～');
    if (!funds && selectedContent === '0' && type === 'new') {
      return message.error('请填写基金代码～');
    } else if (funds) {
      let _funds = funds.split(',');
      if (_funds.some((fund: string) => !/^([A-Za-z0-9]{6})$/.test(fund)))
        return message.error('基金格式填写错误～');
    }
    if (contentType === 1 && type === 'new') {
      if (!/^6([0-9]{8})$/.test(itemId)) return message.error('资讯文章填写错误');
    } else if (contentType === 2 && type === 'new') {
      //if (!/^1([0-9]{8})$/.test(itemId)) return message.error('同顺号文章填写错误')
    }
    if (new Date(endTime).getTime() < new Date(startTime).getTime()) {
      return message.error('结束时间不能小于起始时间');
    }
    let reqObj = {
      itemId: Number(itemId),
      index: type === 'new' ? -1 : 0,
      contentType: contentType,
      selectedContent,
      editor: localStorage.getItem('name'),
      fundInfo: JSON.stringify({
        funds: funds.split(',').filter((item: string) => item),
        startTime: handleTime(startTime),
        endTime: handleTime(endTime),
        label,
        sendSelectedHold: sendSelectedHold ? '1' : '0',
      }),
    };
    postArticle(reqObj)
      .then((data: any) => {
        if (data.status_code === 0) {
          message.success('提交成功！');
          refresh && refresh();
          setIsShow(false);
        } else {
          message.error(data.status_msg || '系统错误请稍候再试～');
        }
      })
      .catch((e: any) => {
        message.error('系统错误请稍候再试～');
      });
  }

  return (
    <Modal
      visible={isShow}
      width={800}
      onCancel={() => {
        setIsShow(false);
      }}
      footer={
        <div className="u-r-middle">
          <Button
            type="primary"
            onClick={() => {
              setIsShow(false);
            }}
          >
            取消
          </Button>
          <Popconfirm
            placement="rightBottom"
            title={'你确定要提交么'}
            onConfirm={upload}
            okText="确认"
            cancelText="取消"
          >
            <Button type="danger">提交</Button>
          </Popconfirm>
        </div>
      }
    >
      <FormRender
        propsSchema={type === 'new' ? NEW_ARTICLE : OLD_ARTICLE}
        uiSchema={{}}
        onValidate={() => {}}
        formData={articleValue}
        onChange={setArticleValue}
        displayType="row"
        showDescIcon={true}
      />
    </Modal>
  );
});
