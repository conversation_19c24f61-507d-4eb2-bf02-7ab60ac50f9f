import React, { useState, useEffect } from 'react';
import DragSort from './DragSort.jsx';
import api from 'api';
import {Button, Popconfirm, message, Drawer, Table } from 'antd';
import WrappedInvestForm from './investDrawer/index';
import { dateFormat } from '@/utils/utils';

const { composeInvestment } = api;
interface iQueryAllDetails {
  endDate: string
  groupId: string
  groupName: string
  groupType: string
  offSaleDate: string
  onSaleDate: string
  startDate: string
  status: string
  updateDate: string
  [key: string]: any
}
function CJInvestment() {
    const [init, setInit] = useState(false);
    const [edit, setEdit] = useState(0); // 1 新增 2 编辑
    const [dataSource, setDataSource] = useState<iQueryAllDetails[]>([]);
    const [currentData, setCurrentData] = useState<any>({});
    const columns = [
      {
        title: '组合ID',
        dataIndex: 'groupId',
        key: 'groupId',
      },
      {
        title: '组合名称',
        dataIndex: 'groupName',
        key: 'groupName',
      },
      {
        title: '开放开始日期',
        dataIndex: 'startDate',
        key: 'startDate',
        render: (startDate: string) => {
          return (
          <span>{startDate ?? '--'}</span>
          )
        }
      },
      {
        title: '开放结束日期',
        dataIndex: 'endDate',
        key: 'endDate',
        render: (endDate: string) => {
          return (
          <span>{endDate ?? '--'}</span>
          )
        }
      },
      {
        title: '上架时间',
        dataIndex: 'onSaleDate',
        key: 'onSaleDate',
        render: (onSaleDate: string) => {
          return (
          <span>{onSaleDate ?? '--'}</span>
          )
        }
      },
      {
        title: '下架时间',
        dataIndex: 'offSaleDate',
        key: 'offSaleDate',
        render: (offSaleDate: string) => {
          return (
          <span>{offSaleDate ?? '--'}</span>
          )
        }
      },
      {
        title: '修改时间',
        dataIndex: 'updateDate',
        key: 'updateDate',
        render: (updateDate: string) => {
          return (
          <span>{updateDate ?? '--'}</span>
          )
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => {
          return (
          <span>{status === '1' ? '下架' : status === '0' ? '上架' : '--'}</span>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text: any, record: iQueryAllDetails) => {
          const { groupId, status } = record;
          return (
            <>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => handleEdit(groupId)}>编辑</Button>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => handleGoUp(groupId, status)}>{status === '0' ? '下架' : '上架'}</Button>
              <Popconfirm title="删除后将无法恢复产品数据，确定要删除吗?" onConfirm={() => handleDelete(groupId)}>
                <Button type="primary">删除</Button>
              </Popconfirm>
            </>
          )
        }
      },
    ];
    useEffect(() => {
      _queryAllData(1);
    }, [])

    const handleEdit = (groupId: string) => {
      composeInvestment({ type: 'query', groupId }).then((res: any) => {
        const { status_code, status_msg, data } = res;
        if (status_code === 0) { 
          if (data) {
            let { handler, recommendFundList, groupInitFundList, wxArticleList, ...rest } = data;
            let str = '', str1 = '';
            recommendFundList?.forEach((item: any) => {
              str += `${item.code},${item.share};`
            })
            groupInitFundList?.forEach((item: any) => {
              str1 += `${item.code},${item.share};`
            })
            wxArticleList?.forEach((item: any) => {
              let time = item?.publishTime?.replace(/-/g, '/');
              item.realTime = new Date(time)?.getTime();
            })
            wxArticleList.sort((a: any, b: any) => {
              return b.realTime - a.realTime;
            })
            rest = { ...handler, ...rest, recommendFundList: str, groupInitFundList: str1, wxArticleList };
            setCurrentData(rest);
            setEdit(2);
          }
        } else {
          message.error(status_msg);
        }
      }).catch((e: any) => {
        message.error(e?.message || '查询数据失败');
      })
    }
    const handleGoUp = (groupId: string, status: string) => {
      composeInvestment({ type: 'change', groupId }).then((res: any) => {
        const { status_code, status_msg } = res;
        if (status_code === 0) { 
          _queryAllData();
          message.success(`该产品定投${status === '0' ? '下架' : '上架'}成功`);
        } else {
          message.error(status_msg);
        }
      }).catch((e: any) => {
        message.error(e?.message || `该产品定投${status === '0' ? '下架' : '上架'}失败`);
      })
    }
    const handleDelete = (groupId: string) => {
      composeInvestment({ type: 'delete', groupId }).then((res: any) => {
        const { status_code, status_msg, data } = res;
        if (status_code === 0) { 
          _queryAllData();
          message.success('删除产品数据成功');
        } else {
          message.error(status_msg);
        }
      }).catch((e: any) => {
        message.error(e?.message || '删除产品数据失败');
      })
    }
    const _queryAllData = (type = 0) => {
      composeInvestment({ type: 'queryAll' }).then((res: any) => {
        const { status_code, status_msg, data } = res;
        if (status_code === 0) { 
          let arr: iQueryAllDetails[] = []; 
          data?.forEach((item: iQueryAllDetails, index: number) => {
            const { startDate } = item;
            item.key = index;
            item.startDate = startDate;
            arr.push({...item})
          })
          arr.sort((a, b) => {
            return a.groupId > b.groupId ? 1 : -1;
          })
          setDataSource(arr);
        } else {
          message.error(status_msg);
        }
        if (type === 1) {
          setInit(true);
        }
      }).catch((e: any) => {
        if (type === 1) {
          setInit(true);
        }
        message.error(e?.message || '查询数据失败');
      })
    }
    const handleDataSource = (dragIndex: number, hoverIndex: number) => {
      let arr = JSON.parse(JSON.stringify(dataSource));
      const dragRow = arr[dragIndex];
      arr.splice(dragIndex, 1);
      arr.splice(hoverIndex, 0, dragRow);
      setDataSource(arr)
    }
    const onEditClose = () => {
      setEdit(0);
    }
    const newAdd = () => {
      setCurrentData({});
      setEdit(1);
    }

    if (!init) {
      return '加载中'
    }
    return (
      <div style={{ padding: 40 }}>
          <h1 className="g-fs28 f-bold">柴进定投</h1>
          {/* <DragSort columns={columns} dataSource={dataSource} handleDataSource={handleDataSource}></DragSort> */}
          <Table
            columns={columns}
            dataSource={dataSource}
          />
          <div style={{marginTop: 20}}>
              <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
          </div>
          <Drawer
              width={1000}
              title="产品信息配置"
              placement="right"
              closable
              onClose={onEditClose}
              visible={Boolean(edit)}
              >
              { Boolean(edit) && <WrappedInvestForm edit={edit} currentData={currentData} queryAllData={_queryAllData} onEditClose={onEditClose}></WrappedInvestForm> }
          </Drawer>
      </div>
    )
}

export default CJInvestment;