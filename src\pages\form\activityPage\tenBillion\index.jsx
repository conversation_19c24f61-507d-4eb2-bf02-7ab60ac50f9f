import React, { useState, useEffect } from 'react';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import classnames from 'classnames';
import styles from './index.less';
import api from 'api';
import {Row, Col, Input,Select, Upload, Button, DatePicker, Divider, ConfigProvider, message, Drawer, Table, Modal, Spin, Icon } from 'antd';
import 'moment/locale/zh-cn';
import moment from 'moment';
import { DraggableArea } from '../../../../components/DragTags';

const { fetchTenBillion, addTenBillion, operateTenBillion, editTenBillion, commonUpload, uploadBlackList, downloadBlackList} = api;
const { confirm } = Modal;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Option  } = Select;
message.config({
    duration: 3,// 持续时间
    maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
    top: 70,// 到页面顶部距离
});

export default function() {
    const tableColumns = [
        {
            title: '活动id',
            dataIndex: 'tenBillionId',
            key: 'tenBillionId',
        },
        {
            title: '手炒地址',
            key: 'scurl',
            render: (row, record, index) => (
                <Icon style={{cursor:'pointer'}} type="copy" onClick={()=>{copyUrl(row, record, index,'sc')}} />
            ),
        },
        {
            title: '爱基金地址',
            key: 'ijjurl',
            render: (row, record, index) => (
                <Icon style={{cursor:'pointer'}} type="copy" onClick={()=>{copyUrl(row, record, index,'ijj')}} />
            ),
        },
        {
          title: '开始时间',
          dataIndex: 'startTime',
          key: 'startTime',
        },
        {
          title: '结束时间',
          dataIndex: 'endTime',
          key: 'endTime',
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          render: (row, record, index) => (
            <div>{activityStatus(record.status)}</div>
        ),
        },
        {
          title: '最后编辑人',
          dataIndex: 'lastEditorName',
          key: 'lastEditorName',
        },
        {
          title: '最后编辑时间',
          dataIndex: 'lastEditTime',
          key: 'lastEditTime',
        },
        {
          title: '操作',
          key: 'action',
          render: (row, record, index) => (
              <div>
                    <div style={{display: record.status === '0' ? '' : 'none'}}>
                        <Button type="danger" style={{marginRight: '20px'}} onClick={()=>{handleOperateTenBillion(row, record, index)}}>审核</Button>
                        <Button type="primary" onClick={()=>{handleEditTenBillion(row, record, index)}}>编辑</Button>
                    </div>
                    <div style={{display: record.status === '1' ? '' : 'none'}}>
                        <Button type="danger" style={{marginRight: '20px'}} onClick={()=>{handleOperateTenBillion(row, record, index)}}>中止</Button>
                        <Button type="primary" onClick={()=>{handleSeeTenBillion(row, record, index)}}>查看</Button>
                    </div>
                    <div style={{display: record.status === '2' ||　record.status === '3' ? '' : 'none'}}>
                        <Button type="primary" onClick={()=>{handleSeeTenBillion(row, record, index)}}>查看</Button>
                    </div>
              </div>
          ),
        },
    ]; // 活动列表对应列
    const [listData, setListData] = useState([]);// 活动列表
    const [pageLoading,setPageLoading] = useState(false); // 页面loading
    const [isUploading,setIsUploading] = useState(false); // 页面loading
    const [drawerStatus, setDrawerStatus] = useState(0); // 添加：0；修改：1；查看：2。
    const [isShowAddDrawer, setIsShowAddDrawer] = useState(false); // 是否显示抽屉
    const [formData, setFormData] = useState({
        tenBillionId: '',
        entryDescription: '爱基金-首页-百元活动',
        startTime: null,
        endTime: null,
        headImage: '',
        rule: '',
        rewardList: [
            {
                reward: ''
            },
            {
                reward: ''
            },
            {
                reward: ''
            },
            {
                reward: ''
            },
            {
                reward: ''
            },
        ],
        rewardDays: '',
        recommendModules: [
            {
                id: 0,
                type: '0',
                listType: 'sy',
                rateRange: 'month'
            },
            {
                id: 1,
                type: '1',
                list: [
                    {
                        fundCode: '',
                        contentOne: '',
                        contentTwo: '',
                        rateRange: 'month'
                    },
                    {
                        fundCode: '',
                        contentOne: '',
                        contentTwo: '',
                        rateRange: 'month'
                    }
                ]
            },
            {
                id: 2,
                type: '2',
                image: '',
                url: ''
            }
        ]
    }); // 表单数据

    useEffect(()=>{
        handleFetchTenBillion();
    },[]);

    // 初始化表单
    const initFormData = ()=>{
        let _formData = {
            entryDescription: '爱基金-首页-百元活动',
            startTime: null,
            endTime: null,
            headImage: '',
            rule: '',
            rewardList: [
                {
                    reward: ''
                },
                {
                    reward: ''
                },
                {
                    reward: ''
                },
                {
                    reward: ''
                },
                {
                    reward: ''
                },
            ],
            rewardDays: '',
            recommendModules: [
                {
                    id: 0,
                    type: '0',
                    listType: 'sy',
                    rateRange: 'month'
                },
                {
                    id: 1,
                    type: '1',
                    list: [
                        {
                            fundCode: '',
                            contentOne: '',
                            contentTwo: '',
                            rateRange: 'month'
                        },
                        {
                            fundCode: '',
                            contentOne: '',
                            contentTwo: '',
                            rateRange: 'month'
                        }
                    ]
                },
                {
                    id: 2,
                    type: '2',
                    image: '',
                    url: ''
                }
            ]
        }
        setFormData(_formData);
    }
    // 活动状态
    const activityStatus = (status)=>{
        switch (status) {
            case '0':
                return '待审核'
            case '1':
                return '进行中'
            case '2':
                return '已中止'
            case '3':
                return '已结束'
            default:
                break;
        }
    }
    // 复制当前活动地址
    const copyUrl = (row, record, index, scOrIjj)=>{
        let url = '';
        if ( window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ) {
            url = scOrIjj === 'sc' ? 
            `https://testfund.10jqka.com.cn/scym_scsy/public/tg/billionPacket/dist/?planId=${record.tenBillionId}` : 
            `https://testfund.10jqka.com.cn/ifundapp_app/public/tg/billionPacket/dist/?planId=${record.tenBillionId}`;
        } else {
            url = scOrIjj === 'sc' ? 
            `https://fund.10jqka.com.cn/scym_scsy/public/tg/billionPacket/dist/?planId=${record.tenBillionId}` : 
            `https://fund.10jqka.com.cn/ifundapp_app/public/tg/billionPacket/dist/?planId=${record.tenBillionId}`;
        }
        let oInput = document.createElement('input');
        oInput.value = url;
        document.body.appendChild(oInput);
        oInput.select();
        document.execCommand('copy');
        oInput.remove();
        message.success('复制成功');
    }
    // 关闭抽屉
    const closeDrawer = ()=> {
        initFormData();
        setIsShowAddDrawer(false);
    }
    // 上传图片
    const uploadImg = (options,type,index)=> {
        if (options.file.size > 5120000) {
            message.error('文件大小不得超过5M');
            return;
        }
        setIsUploading(true);
        message.info('图片上传中');
        let params = new FormData();
        params.append('file', options.file);
        commonUpload(params).then((res)=>{
            if (res.status_code === 0) {
                let _formData = {...formData};
                switch (type) {
                    case 'headImage':
                        _formData.headImage = res.data;
                        break;
                    case 'banner':
                        _formData.recommendModules[index].image = res.data;
                        break;
                    default:
                        break;
                }
                setFormData(_formData);
                message.success('上传成功');
            } else {
                message.error(res.status_msg);
            }
            setIsUploading(false);
        }).catch(()=>{
            message.error('上传失败');
            setIsUploading(false);
        });
    }
    // 上传黑名单
    const handleUploadBlackList = (options)=> {
        if (options.file.name.slice(-4) !== 'xlsx') {
            message.error('文件格式必须为.xlsx');
            return;
        }
        setIsUploading(true);
        message.info('上传中');
        let params = new FormData();
        params.append('file', options.file);
        uploadBlackList(params).then((res)=>{
            if (res.status_code === 0) {
                message.success('上传成功');
            } else {
                message.error(res.status_msg);
            }
            setIsUploading(false);
        }).catch(()=>{
            message.error('上传失败')
            setIsUploading(false);
        })
    }
    // 下载黑名单
    const handleDownloadBlackList = ()=>{
        message.success('开始下载文件');
        downloadBlackList({}, '', '', { responseType: 'blob' }).then(()=>{
        }).catch(()=>{
            message.error('下载失败');
        });
    }
    // 修改表单项
    const changeFormItem = (type, value, index,index2) => {
        const _formData = {...formData};
        switch(type) {
            case 'activityDuration':
                _formData.startTime = value[0];
                _formData.endTime = value[1];
                break;
            case 'reward':
                _formData.rewardList[index].reward = value;
                break;
            case 'listType':
                _formData.recommendModules[index][type] = value;
                if (value === 'sy') {
                    _formData.recommendModules[index].rateRange = 'month';
                } else {
                    _formData.recommendModules[index].rateRange = '';
                }
                break;
            case 'listRateRange':
                _formData.recommendModules[index].rateRange = value;
                break;
            case 'fundCode':
                _formData.recommendModules[index].list[index2].fundCode = value;
                break;
            case 'pickRateRange':
                _formData.recommendModules[index].list[index2].rateRange = value;
                break;
            case 'contentOne':
                _formData.recommendModules[index].list[index2].contentOne = value;
                break;
            case 'contentTwo':
                _formData.recommendModules[index].list[index2].contentTwo = value;
                break;
            case 'banner':
                _formData.recommendModules[index].url = value;
                break;
            default:
                _formData[type] = value;
                break
        }
        setFormData(_formData);
    }
    // 增加或减少签到奖励天数
    const changeRewardDays = (type)=>{
        const _formData = {...formData};
        switch (type) {
            case 'add':
                _formData.rewardList.push({
                    reward: ''
                });
                break;
            case 'reduce':
                _formData.rewardList.pop();
                break;
            default:
                break;
        }
        setFormData(_formData);
    }
    // 添加模块
    const addModule = (type)=>{
        const _formData = {...formData};
        let addModuleId = 0;
        for (let i = 0;i<_formData.recommendModules.length+1;i++) {
            const result = _formData.recommendModules.every((item,index)=>{
                return item.id !== addModuleId;
            });
            if (!result) {
                addModuleId+=1;
                continue;
            } else {
                break;
            }
        }
        switch (type) {
            case 'list':
                _formData.recommendModules.push({
                    id: addModuleId,
                    type: '0',
                    listType: 'sy',
                    rateRange: 'month'
                });
                break;
            case 'strictSelect':
                _formData.recommendModules.push({
                    id: addModuleId,
                    type: '1',
                    list: [
                        {
                            fundCode: '',
                            contentOne: '',
                            contentTwo: '',
                            rateRange: 'month'
                        },
                        {
                            fundCode: '',
                            contentOne: '',
                            contentTwo: '',
                            rateRange: 'month'
                        },
                        {
                            fundCode: '',
                            contentOne: '',
                            contentTwo: '',
                            rateRange: 'month'
                        },
                    ]
                },);
                break;
            case 'banner':
                _formData.recommendModules.push({
                    id: addModuleId,
                    type: '2',
                    image: '',
                    url: ''
                });
                break;
            default:
                break;
        }
        setFormData(_formData);
    }
    // 删除模块
    const deleteModule = (index)=>{
        const _formData = {...formData};
        _formData.recommendModules.splice(index,1);
        setFormData(_formData);
    }
    // 添加严选好基
    const addStrictSelect = (index)=> {
        const _formData = {...formData};
        _formData.recommendModules[index].list.push({
            fundCode: '',
            contentOne: '',
            contentTwo: '',
            rateRange: 'month'
        });
        setFormData(_formData);
    }
    // 删除严选好基
    const deleteStrictSelect = (index,index2)=> {
        const _formData = {...formData};
        _formData.recommendModules[index].list.splice(index2,1);
        setFormData(_formData);
    }
    // 获取百亿活动列表
    const handleFetchTenBillion = ()=>{
        setPageLoading(true);
        fetchTenBillion().then((res)=>{
            if (res.status_code === 0) {
                setListData(res.data)
            } else {
                message.error(res.status_msg);
            }
            setPageLoading(false);
        }).catch(()=>{
            message.error('获取活动列表失败');
            setPageLoading(false);
        });
    }
    // 查看
    const handleSeeTenBillion = (row, record, index)=>{
        let _formData = {...formData};
        _formData.tenBillionId = record.tenBillionId;
        _formData.entryDescription = record.entryDescription;
        _formData.startTime = record.startTime ? moment(record.startTime,'YYYY-MM-DD HH:mm:ss') : null;
        _formData.endTime = record.endTime ? moment(record.endTime,'YYYY-MM-DD HH:mm:ss') : null;
        _formData.headImage = record.headImage;
        _formData.rule = record.rule;
        _formData.rewardList = [...record.rewardList];
        _formData.rewardDays = record.rewardDays;
        _formData.recommendModules = JSON.parse(JSON.stringify(record.recommendModules));
        setFormData(_formData);
        setDrawerStatus(2);
        setIsShowAddDrawer(true);
    }
    // 编辑
    const handleEditTenBillion = (row, record, index)=>{
        let _formData = {...formData};
        _formData.tenBillionId = record.tenBillionId;
        _formData.entryDescription = record.entryDescription;
        _formData.startTime = record.startTime ? moment(record.startTime,'YYYY-MM-DD HH:mm:ss') : null;
        _formData.endTime = record.endTime ? moment(record.endTime,'YYYY-MM-DD HH:mm:ss') : null;
        _formData.headImage = record.headImage;
        _formData.rule = record.rule;
        _formData.rewardList = [...record.rewardList];
        _formData.rewardDays = record.rewardDays;
        _formData.recommendModules = JSON.parse(JSON.stringify(record.recommendModules));
        setFormData(_formData);
        setDrawerStatus(1);
        setIsShowAddDrawer(true);
    }
    // 操作
    const handleOperateTenBillion = (row, record, index)=> {
        const params = {
            lastEditTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            lastEditorId: JSON.parse(localStorage.userInfos).userId,
            lastEditorName: JSON.parse(localStorage.userInfos).username, 
        }
        // 审核操作
        if (record.status === '0') {
            setPageLoading(true);
            operateTenBillion(params,record.tenBillionId,'check').then((res)=>{
                if (res.status_code === 0) {
                    message.success('审核通过');
                    handleFetchTenBillion();
                } else {
                    message.error(res.status_msg);
                }
                setPageLoading(false);
            }).catch(()=>{
                message.error('操作失败');
                setPageLoading(false);
            });
        }
        // 中止操作
        if (record.status === '1') {
            confirm({
                title: '提示',
                content: '确定要将活动中止吗',
                okText: "确定",
                cancelText: "取消",
                onOk() {
                    setPageLoading(true);
                    operateTenBillion(params,record.tenBillionId,'stop').then((res)=>{
                        if (res.status_code === 0) {
                            message.success('活动已中止');
                            handleFetchTenBillion();
                        } else {
                            message.error(res.status_msg);
                        }
                        setPageLoading(false);
                    }).catch(()=>{
                        message.error('操作失败');
                        setPageLoading(false);
                    });
                }
            });
        }
    }
    // 推荐文案长度校验
    const contentLengthCheck = ()=>{
        return formData.recommendModules.every((item,index)=>{
            if (item.type === '1') {
                return item.list.every((item2,index2)=>{
                    let contentOneLength = 0;
                    let contentTwoLength = 0;
                    for (let i = 0;i < item2.contentOne.length;i++) {
                        if (item2.contentOne.charCodeAt(i) > 255) {
                            contentOneLength+=2;
                        } else {
                            contentOneLength+=1;
                        }
                    }
                    for (let i = 0;i < item2.contentTwo.length;i++) {
                        if (item2.contentTwo.charCodeAt(i) > 255) {
                            contentTwoLength+=2;
                        } else {
                            contentTwoLength+=1;
                        }
                    }
                    return contentOneLength <= 20 && contentTwoLength <= 24;
                });
            }
            return true;
        });
    }
    // 检查活动入口字符长度
    const entranceLengthCheck = ()=> {
        let entranceLength = 0;
        for (let i = 0;i < formData.entryDescription.length;i++) {
            if (formData.entryDescription.charCodeAt(i) > 255) {
                entranceLength+=2;
            } else {
                entranceLength+=1;
            }
        }
        return entranceLength <= 30;
    }
    // 提交时校验表单
    const checkFormData = ()=>{
        let checkResult = false;
        switch (true) {
            case !entranceLengthCheck():
                message.error('活动能够入口不得超过30个字符，汉字算两个字符');
                break;
            case !formData.entryDescription:
                message.error('请填写活动入口描述');
                break;
            case moment().diff(formData.endTime) > 0:
                message.error('活动结束时间不得早于当前时间');
                break;
            case !formData.startTime || !formData.endTime:
                message.error('请选择活动开始至结束时间');
                break;
            case !formData.headImage:
                message.error('请上传活动头图');
                break;
            case !formData.rule:
                message.error('请填写活动规则');
                break;
            case !formData.rewardList.every((item,index)=>{return item.reward}):
                message.error('请填写活动签到奖励');
                break;
            case !formData.rewardDays:
                message.error('请填写活动持续天数');
                break;
            case !formData.recommendModules.every((item,index)=>{
                if (item.type === '1') {
                    return item.list.every((item2,index2)=>{
                        return item2.fundCode.length === 6;
                    });
                }
                return true;
            }):
                message.error('基金代码未填写或格式不正确');
                break;
            case !contentLengthCheck():
                message.error('主文案最大长度为10个汉字，副文案最大长度为12个汉字');
                break;
            case !formData.recommendModules.every((item,index)=>{
                if (item.type === '1') {
                    return item.list.every((item2,index2)=>{
                        return item2.fundCode && item2.contentOne && item2.contentTwo;
                    });
                }
                if (item.type === '2') {
                    return item.image && item.url;
                }
                return true;
            }):
                message.error('请填写完整基金推荐模块数据');
                break;
            default:
                checkResult = true;
                break;
        }
        return checkResult;
    }
    // 保存
    const saveForm  = ()=>{
        if (!checkFormData()) {
            return;
        }
        confirm({
            title: '提示',
            content: '确定要将活动提交吗',
            okText: "确定",
            cancelText: "取消",
            centered: true,
            onOk() {
                let _formData = {...formData};
                _formData.startTime = _formData.startTime ? _formData.startTime.format('YYYY-MM-DD HH:mm:ss') : '';
                _formData.endTime = _formData.endTime ? _formData.endTime.format('YYYY-MM-DD HH:mm:ss') : '';
                const params = {
                    entryDescription: _formData.entryDescription,
                    endTime: _formData.endTime,
                    startTime: _formData.startTime,
                    headImage: _formData.headImage,
                    lastEditTime: moment().format('YYYY-MM-DD HH:mm:ss'),
                    lastEditorId: JSON.parse(localStorage.userInfos).userId,
                    lastEditorName: JSON.parse(localStorage.userInfos).username,
                    recommendModules: _formData.recommendModules,
                    rewardDays: _formData.rewardDays,
                    rewardList: _formData.rewardList,
                    rule: _formData.rule,
                    status: '0'
                };
                if (drawerStatus === 0) {
                    addTenBillion(params).then((res)=>{
                        if (res.status_code === 0) {
                            closeDrawer();
                            handleFetchTenBillion();
                            message.success('保存成功');
                        } else {
                            message.error(res.status_msg);
                        }
                    }).catch(()=>{
                        message.error('保存失败');
                    });
                }
                if (drawerStatus === 1) {
                    editTenBillion(params,formData.tenBillionId).then((res)=>{
                        if (res.status_code === 0) {
                            closeDrawer();
                            handleFetchTenBillion();
                            message.success('保存成功');
                        } else {
                            message.error(res.status_msg);
                        }
                    }).catch(()=>{
                        message.error('保存失败');
                    });
                }
            }
        });   
    }
    return (
        <div className={styles['ten-billion']}>
            <Spin spinning={pageLoading}>
                <Button type="primary" style={{marginBottom: '20px'}} onClick={()=>{setIsShowAddDrawer(true);setDrawerStatus(0)}}>新增活动</Button>
                <Table
                    columns={tableColumns}
                    dataSource={listData}
                    pagination={false}
                    rowKey={record=>record.tenBillionId}
                />
                <Drawer
                    className="ten-billion-drawer"
                    title={drawerStatus === 0 ? '新增' : drawerStatus === 1 ? '编辑' : '查看'}
                    placement="right"
                    width="1500"
                    maskClosable={false}
                    destroyOnClose={true}
                    onClose={closeDrawer}
                    visible={isShowAddDrawer}
                >
                    <ConfigProvider locale={zh_CN}>
                        {/* 活动入口描述 */}
                        <Row gutter={[40,14]}>
                            <Col span={8}>
                                <div><b style={{color: '#fe5d4e'}}>* </b>活动入口描述：</div>
                                <Input value={formData.entryDescription} onChange={(e)=>{changeFormItem('entryDescription',e.target.value)}}/>
                            </Col>
                        </Row>
                        {/* 活动开始至结束时间 */}
                        <Row gutter={[40,14]}>
                            <Col span={8}>
                                <div><b style={{color: '#fe5d4e'}}>* </b>活动时间：</div>
                                <RangePicker showToday={false} style={{width:'100%'}} showTime value={[formData.startTime, formData.endTime]} onChange={(e)=>{changeFormItem('activityDuration',e)}}/>
                            </Col>
                        </Row>
                        {/* 活动头图上传 */}
                        <Row gutter={[40,14]}>
                            <Col span={8} style={{fontWeight: '600'}}><b style={{color: '#fe5d4e'}}>* </b>活动头图上传：</Col>
                        </Row>
                        <Row gutter={[40,14]}>
                            <Col span={8} style={{alignItems: 'flex-end'}}>
                                <div className={classnames(styles['head-thumbnail'],'head-thumbnail')}>
                                    <img style={{width: '100%',height: '100%',display: formData.headImage ? '' : 'none'}} src={formData.headImage} alt=""/>
                                </div>
                                <Upload
                                    customRequest={(options)=>{uploadImg(options,'headImage')}}
                                    showUploadList={false}
                                >
                                    <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                                </Upload>
                            </Col>
                        </Row>
                        {/* 活动规则 */}
                        <Row gutter={[40,14]}>
                            <Col span={24} style={{fontWeight: '600'}}><b style={{color: '#fe5d4e'}}>* </b>活动规则：</Col>
                        </Row>
                        <Row gutter={[40,14]}>
                            <Col span={24}>
                                <TextArea size='default' autoSize={{minRows: 4, maxRows: 10}} value={formData.rule} onChange={(e)=>{changeFormItem('rule',e.target.value)}}/>
                            </Col>
                        </Row>
                        {/* 薅羊毛用户黑名单 */}
                        <Row gutter={[40,14]}>
                            <Col span={24} style={{fontWeight: '600'}}>薅羊毛用户黑名单：</Col>
                            <Col span={24}>
                                <div style={{color: '#1890ff',cursor: 'pointer',marginRight: '20px'}} onClick={()=>{handleDownloadBlackList()}}>黑名单.xlsx</div>
                                <Upload
                                    customRequest={(options)=>{handleUploadBlackList(options)}}
                                    showUploadList={false}
                                    accept=".xlsx"
                                >
                                    <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                                </Upload>
                            </Col>
                        </Row>
                        {/* 活动签到奖励 */}
                        <Row gutter={[40,14]}>
                            <Col span={24} style={{fontWeight: '600'}}>
                                <span><b style={{color: '#fe5d4e'}}>* </b>活动签到奖励：</span>
                                <Icon type="plus-square" style={{cursor:'pointer',marginRight: '10px'}} onClick={()=>{changeRewardDays('add')}}/>
                                <Icon type="minus-square" style={{cursor:'pointer'}} onClick={()=>{changeRewardDays('reduce')}}/>
                            </Col>
                        </Row>
                        <Row gutter={[40,14]}>
                            {
                                formData.rewardList && formData.rewardList.map((item,index)=>{
                                    return (
                                        <Col span={3} key={index}>
                                            <div>{`第${index+1}天：`}</div>
                                            <Input value={formData.rewardList[index].reward} onChange={(e)=>{changeFormItem('reward',e.target.value,index)}}/>
                                        </Col> 
                                    )
                                })
                            }
                        </Row>
                        {/* 活动持续天数 */}
                        <Row gutter={[40,14]}>
                            <Col span={24} style={{fontWeight: '600'}}><b style={{color: '#fe5d4e'}}>* </b>活动持续天数（交易日）：</Col>
                        </Row>
                        <Row gutter={[40,14]}>
                            <Col span={3}>
                                <Input value={formData.rewardDays} onChange={(e)=>{changeFormItem('rewardDays',e.target.value)}}/>
                            </Col> 
                        </Row>
                        {/* 基金推荐模块 */}
                        <Row gutter={[40,14]}>
                            <Col span={24} style={{fontWeight: '600'}}>
                                <span>基金推荐模块：</span>
                                <Button type="link" onClick={()=>{addModule('list')}}>添加榜单</Button>
                                <Button type="link" onClick={()=>{addModule('strictSelect')}}>添加严选好基</Button>
                                <Button type="link" onClick={()=>{addModule('banner')}}>添加banner</Button>
                            </Col>
                        </Row>
                        <Row gutter={[40,14]}>
                            <Col span={24}>
                                <DraggableArea
                                    isList
                                    tags={formData.recommendModules}
                                    render={({tag, index}) => {
                                        return (
                                            <div className={styles['tag']} style={{width: 1446}}>
                                                <div className={classnames(styles['m-row'], 'f-tl', 'u-l-middle')}>
                                                    <div style={{display: tag.type === '0' ? '': 'none',width:'100%'}}>
                                                        <div className={classnames(styles['m-header'])}>
                                                            <div className={classnames(styles['m-title'])}><b style={{color: '#fe5d4e'}}>* </b>基金榜单</div>
                                                            <div className={classnames(styles['m-delete'])} onClick={()=>{deleteModule(index)}}>删除</div>
                                                        </div>
                                                        <div>
                                                            <span style={{marginRight: '20px'}}>展示榜单:</span>
                                                            <Select style={{width:'300px',marginRight: '20px'}} defaultValue={'sy'} value={tag.listType} onChange={(e) => {changeFormItem('listType',e,index)}}>
                                                                <Option value={'sy'}>收益榜</Option>
                                                                <Option value={'rq'}>人气榜</Option>
                                                                <Option value={'rs'}>热搜榜</Option>
                                                                <Option value={'rx'}>热销榜</Option>
                                                                <Option value={'dt'}>定投榜</Option>
                                                            </Select>
                                                            <span style={{marginRight: '20px', display: tag.listType === 'sy' ? '': 'none'}}>涨幅:</span>
                                                            <Select style={{width:'300px', display: tag.listType === 'sy' ? '': 'none'}} defaultValue={'month'} value={tag.rateRange} onChange={(e) => {changeFormItem('listRateRange',e,index)}}>
                                                                <Option value={'month'}>近1个月</Option>
                                                                <Option value={'tmonth'}>近3个月</Option>
                                                                <Option value={'hyear'}>近半年</Option>
                                                                <Option value={'year'}>近1年</Option>
                                                                <Option value={'tyear'}>近3年</Option>
                                                            </Select>
                                                        </div>
                                                    </div>
                                                    <div style={{display: tag.type === '1' ? '': 'none',width:'100%'}}>
                                                        <div className={classnames(styles['m-header'])}>
                                                            <div className={classnames(styles['m-title'])}>
                                                                <span><b style={{color: '#fe5d4e'}}>* </b>严选好基&nbsp;</span>
                                                                <span className={classnames(styles['m-add'])} onClick={()=>{addStrictSelect(index)}}>添加</span>
                                                            </div>
                                                            <div className={classnames(styles['m-delete'])} onClick={()=>{deleteModule(index)}}>删除</div>
                                                        </div>
                                                        {
                                                            tag.list && tag.list.map((item2,index2)=>{
                                                                return (
                                                                    <div key={index2} style={{marginBottom: '10px'}}>
                                                                        <span style={{marginRight: '20px'}}>基金代码:</span>
                                                                        <Input style={{width:'150px',marginRight: '20px'}} value={item2.fundCode} onChange={(e) => {changeFormItem('fundCode',e.target.value,index,index2)}}/>
                                                                        <span style={{marginRight: '20px'}}>涨幅:</span>
                                                                        <Select style={{width:'150px',marginRight: '20px'}} value={item2.rateRange} onChange={(e) => {changeFormItem('pickRateRange',e,index,index2)}}>
                                                                            <Option value={'month'}>近1个月</Option>
                                                                            <Option value={'tmonth'}>近3个月</Option>
                                                                            <Option value={'hyear'}>近半年</Option>
                                                                            <Option value={'year'}>近1年</Option>
                                                                            <Option value={'tyear'}>近3年</Option>
                                                                        </Select>
                                                                        <span style={{marginRight: '20px'}}>主文案:</span>
                                                                        <Input style={{width:'300px',marginRight: '20px'}} value={item2.contentOne} onChange={(e) => {changeFormItem('contentOne',e.target.value,index,index2)}}/>
                                                                        <span style={{marginRight: '20px'}}>副文案:</span>
                                                                        <Input style={{width:'300px',marginRight: '20px'}} value={item2.contentTwo} onChange={(e) => {changeFormItem('contentTwo',e.target.value,index,index2)}}/>
                                                                        <span className={classnames(styles['m-delete'])} onClick={()=>{deleteStrictSelect(index,index2)}}>删除</span>
                                                                    </div>
                                                                )
                                                            })
                                                        }
                                                    </div>
                                                    <div style={{display: tag.type === '2' ? '': 'none',width:'100%'}}>
                                                        <div className={classnames(styles['m-header'])}>
                                                            <div className={classnames(styles['m-title'])}><b style={{color: '#fe5d4e'}}>* </b>banner（图片尺寸：710×200）</div>
                                                            <div className={classnames(styles['m-delete'])} onClick={()=>{deleteModule(index)}}>删除</div>
                                                        </div>
                                                        <div>
                                                            <div className={classnames(styles['thumbnail'])}>
                                                                <img style={{width: '100%',height: '100%',display: tag.image ? '' : 'none'}} src={tag.image} alt=""/>
                                                            </div>
                                                            <Upload
                                                                customRequest={(options)=>{uploadImg(options,'banner',index)}}
                                                                showUploadList={false}
                                                            >
                                                                <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                                                            </Upload>
                                                            <span style={{marginRight: '20px'}}>跳转链接:</span>
                                                            <Input style={{width:'600px',marginRight: '20px'}} value={tag.url} onChange={(e)=>{changeFormItem('banner',e.target.value,index)}}/>
                                                        </div>
                                                    </div>
                                                </div> 
                                            </div> 
                                        )
                                    }}
                                    onChange={(data) => {
                                        const _formData = {...formData};
                                        _formData.recommendModules = data;
                                        setFormData(_formData);
                                    }}
                                />
                            </Col> 
                        </Row>
                        {/* 保存 */}
                        <Row gutter={[40,14]} style={{display: drawerStatus === 2 ? 'none' : ''}}>
                            <Col span={8}>
                                <Button type="primary" onClick={()=>{saveForm()}}>保存</Button>
                            </Col>
                        </Row>
                    </ConfigProvider>
                </Drawer>
            </Spin>
        </div>
    )
}
