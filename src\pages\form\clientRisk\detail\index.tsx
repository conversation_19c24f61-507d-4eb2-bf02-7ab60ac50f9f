import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import api from 'api';
import FormRender from 'form-render/lib/antd';
import FORM_JSON from './form.json';
import { Button, Form, Input, DatePicker, message } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import moment from 'moment';
import styles from './index.less';
import UserIdList from '../components/fileImport';
import Checkbox from '../components/checkbox';
import SearchBtn from '../components/searchBtn';
import { CommandItem, ClientRisk } from '../tsFile';

const { postCommandConfig } = api;
// 头部位置信息
type HeaderInfo<T> = {
  id: T;
  commandListName: T;
  startDate: T;
  endDate: T;
};
// 表单项信息
type ClientRiskFormData = {
  commandList: CommandItem<string>[];
};
const ClientRisk: React.FC<FormComponentProps> = ({ form }) => {
  const history = useHistory();
  // 获取路由信息
  const queryId: string[] | null = history.location.search.match(/(?<=(id=))\w+/g);
  // 注册表单项API和验证表单项API
  const { getFieldDecorator, validateFields } = form;
  // 表单信息
  const [formData, setFormData] = useState<ClientRiskFormData>();
  const [headerInfo, setHeaderInfo] = useState<HeaderInfo<string>>({
    id: '',
    commandListName: '',
    startDate: '',
    endDate: '',
  });
  // 保存formData必填项校验中未校验通过的表单id
  const [formValid, setFormValid] = useState<string[]>([]);
  // 是否显示校验失败提示
  const [showValidate, setShowValidate] = useState(false);

  useEffect(() => {
    // 编辑页AJAX
    queryId &&
      postCommandConfig({
        type: 'query',
        id: queryId.join(),
      })
        .then((res: ClientRisk) => {
          if (res.status_code === 0 && res.data) {
            const { id, commandListName, startDate, endDate, commandList } = res.data;
            let _headerInfo = {
              id,
              commandListName,
              startDate,
              endDate,
            };
            setHeaderInfo(_headerInfo);
            setFormData({ commandList });
          } else {
            message.error(res.status_msg);
          }
        })
        .catch((err: unknown) => {
          console.log(err);
          message.error('网络请求错误，请稍后再试');
        });
  }, []);
  // 新增页保存或发布 编辑页保存或发布 接口
  const _postCommandConfig = (values: HeaderInfo<string>, status: '0' | '1') => {
    // 过滤掉object[]中所有以tip开头的属性
    let commandList: CommandItem<string>[] = [];
    formData?.commandList.map(item => {
      // 过滤掉以tip开头的表单项key
      let tempArr = Object.keys(item).filter(key => {
        return key.substring(0, 3) !== 'tip';
      });
      let tempObj: any = {};
      tempArr.map(key => {
        tempObj[key] = item[key];
      });
      commandList.push(tempObj);
    });
    let body = {
      ...values,
      status: status,
      commandList,
    };
    let _body = null;
    queryId
      ? (_body = {
          type: 'update',
          jsonDto: JSON.stringify({ ...body, id: queryId.join() }),
        })
      : (_body = { type: 'add', jsonDto: JSON.stringify(body) });
    postCommandConfig(_body)
      .then((res: ClientRisk) => {
        if (res.status_code === 0) {
          message.success('成功');
          history.replace('/form/clientRisk/list');
        } else {
          message.error(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
        message.error('网络请求错误，请稍后再试');
      });
  };
  // AJAX之前的表单校验
  const beforeAjax = (status: '0' | '1') => {
    validateFields((err, values) => {
      setShowValidate(true);
      let _values = {
        ...values,
        startDate: values.startDate ? values.startDate.format('YYYY-MM-DD hh:mm:ss') : '',
        endDate: values.endDate ? values.endDate.format('YYYY-MM-DD hh:mm:ss') : '',
      };
      if (!err && formValid.length < 1) {
        setShowValidate(false);
        _postCommandConfig(_values, status);
      }
    });
  };
  // 发布
  const onSubmit = () => {
    beforeAjax('1');
  };
  // 保存
  const onSave = () => {
    beforeAjax('0');
  };
  return (
    <article>
      <section className={styles['m-client-risk-header']}>
        <Form layout="inline">
          <div className={styles['m-header-item']}>
            <Form.Item label="命令id">
              <span style={{ marginLeft: '20px' }}>{headerInfo.id}</span>
            </Form.Item>
            <Form.Item label="功能名称">
              {getFieldDecorator('commandListName', {
                rules: [
                  {
                    required: true,
                    message: '不能为空',
                  },
                ],
                initialValue: headerInfo.commandListName,
              })(<Input />)}
            </Form.Item>
            <Form.Item>
              <Button type="primary" onClick={onSubmit}>
                发布
              </Button>
              <Button type="primary" onClick={onSave}>
                保存
              </Button>
            </Form.Item>
          </div>
          <div className={styles['m-header-item']}>
            <Form.Item label="开始时间">
              {getFieldDecorator('startDate', {
                initialValue: headerInfo.startDate && moment(headerInfo.startDate),
              })(<DatePicker showTime locale={locale} />)}
            </Form.Item>
            <Form.Item label="结束时间">
              {getFieldDecorator('endDate', {
                initialValue: headerInfo.endDate && moment(headerInfo.endDate),
              })(<DatePicker showTime locale={locale} />)}
            </Form.Item>
          </div>
        </Form>
      </section>
      <section className={styles['m-client-risk-detail']}>
        <FormRender
          propsSchema={FORM_JSON}
          formData={formData ? formData : {}}
          onChange={setFormData}
          onValidate={(valid: string[]) => {
            if (formData) {
              // form-render插件对于根据按钮动态生成的组件是不会自动必填校验的，需要手动控制
              formData.commandList.map((commandItem: CommandItem<string>) => {
                // 根据分发模式动态控制必填校验
                if (commandItem.distributionMode === '1' && !commandItem.contentRules) {
                  valid = [...valid, 'contentRules'];
                }
                if (commandItem.distributionMode === '2' && !commandItem.userIdList) {
                  valid = [...valid, 'userIdList'];
                }
                // 根据系统选择动态控制必填校验
                if (
                  commandItem.systemSelection === '2' &&
                  commandItem.applicationMarket.length < 1
                ) {
                  valid = [...valid, 'applicationMarket'];
                }
              });
            }
            setFormValid(valid);
          }}
          showValidate={showValidate}
          displayType="row"
          widgets={{
            fileImport: UserIdList,
            selectMode: Checkbox,
            searchBtn: SearchBtn,
          }}
        />
      </section>
    </article>
  );
};

export default Form.create<FormComponentProps>()(ClientRisk);
