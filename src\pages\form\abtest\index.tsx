import React, { useState, useEffect } from 'react';
import { Table, Divider, Button, message, Popconfirm } from 'antd';
import { useHistory } from 'react-router-dom';
import api from 'api';

const { postAbTest } = api;

interface queryAll {
  abTestName: string;
  endDate: string;
  functionList: any;
  id: string;
  key: string;
  startDate: string;
  status: string;
  taskNumber: string;
}

export default function() {
  const columns = [
    {
      title: 'Id',
      dataIndex: 'id',
    },
    {
      title: '名称',
      dataIndex: 'abTestName',
    },
    {
      title: '任务号',
      dataIndex: 'taskNumber',
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
    },
    {
      title: '任务状态',
      dataIndex: 'status',
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (text: any, record: any, index: number) => {
        return (
          <span>
            <Button
              disabled={statusList[index] === '0' ? false : true}
              onClick={() => {
                publish(record.id);
              }}
            >
              发布
            </Button>
            <Divider type="vertical" />
            <Button
              onClick={() => {
                history.push(`abtest/detail?id=${record.id}`);
              }}
            >
              编辑
            </Button>
            <Divider type="vertical" />
            <Popconfirm
              title="是否确认删除？"
              okText="确认"
              cancelText="取消"
              onConfirm={() => {
                deleteList(text, record);
              }}
            >
              <Button type="danger">删除</Button>
            </Popconfirm>
          </span>
        );
      },
    },
  ];
  const history = useHistory();
  // 表格数据
  const [data, setData] = useState();
  // 状态列表
  const [statusList, setStatusList] = useState([]);
  useEffect(() => {
    postAbTest({
      type: 'queryAll',
    })
      .then((res: any) => {
        switch (res.status_code) {
          case 0:
            res.data.map((item: queryAll) => {
              // 保存任务状态
              const statusListT: any = statusList;
              statusListT.push(item.status);
              setStatusList(statusListT);
              // 添加列表key值
              item.key = item.id;
              // 过滤状态
              item.status === '0' ? (item.status = '已保存待发布') : (item.status = '已发布');
            });
            setData(res.data);
            break;
          case 1002:
            console.log('传参为空出错');
            break;
          case 9100:
            console.log('系统内部错误');
            break;
          case 1000:
            console.log('鉴权失败');
            break;
          case 1001:
            console.log('校验字段格式错误');
            break;
        }
      })
      .catch((error: any) => {
        console.warn(error);
      });
  }, []);

  function deleteList(text: undefined, record: queryAll) {
    postAbTest({
      type: 'delete',
      id: record.id,
    })
      .then((res: any) => {
        if (res.status_code === 0) {
          message.success('删除成功');
          location.reload();
        } else {
          message.error('删除成功');
        }
      })
      .catch((error: any) => {
        console.warn(error);
      });
  }
  function publish(id: number) {
    postAbTest({
      type: 'change',
      id,
    })
      .then((res: any) => {
        if (res.status_code === 0) {
          message.success('发布成功');
          location.reload();
        } else {
          message.error('服务器错误');
        }
      })
      .catch((error: any) => {
        console.warn(error);
      });
  }
  return (
    <div>
      <div>
        <Button
          style={{ marginLeft: '89%' }}
          onClick={() => {
            history.push(`abtest/detail`);
          }}
        >
          添加
        </Button>
        <Table dataSource={data} pagination={false} columns={columns}></Table>
      </div>
    </div>
  );
}
