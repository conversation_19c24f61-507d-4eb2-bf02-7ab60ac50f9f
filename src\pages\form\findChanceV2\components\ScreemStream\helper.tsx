import { HotSpot, ScreenStream } from '../../type';
import { checkRequiredAndThrow, checkUrlAndThrow } from '../../utils';

export const mapPropToName = {
  // open: "",
  streamId: '直播id',
  link: '直播链接',

  cover: '直播间图片',
};
export const screemStreamHelper = {
  addInit: function(): ScreenStream {
    return {
      open: false,
      link: '',
      streamId: '',
      cover: '',
    };
  },

  checkData: function(data) {
    checkRequiredAndThrow(data.streamId, mapPropToName['streamId']);
    checkRequiredAndThrow(data.link, mapPropToName['link']);
    checkUrlAndThrow(data.link, mapPropToName['link']);

    return true;
  },
};
