// 权益
interface IRights {
  openAccountEncourage: {
    encourageList: {
      feature: string;
      value: string;
      name: string;
      type: '0' | '1' | '2' | '3';
      id: string;
    }[];
    buttonText: string;
  };
  exchangeRightsUrl: string;
  redPacket: {
    isGiveRedPacket: '0' | '1';
    kycConfig: {
      packetId: string;
      financialKnowledge: string;
      kycFilterId: string;
      startTime: string;
      endTime: string;
      isOpenConditionMatching: '0' | '1';
      optional: {
        kycFilterId: string;
        packetId: string;
        financialKnowledge: string;
        startTime: string;
        endTime: string;
      };
    };
  };
}

// 基金推荐
interface IFundRecommend {
  isShowFundTrend: '0' | '1';
  title: string;
  mainFund: {
    mainTitle: string;
    recommendTag: string;
    recommendLogic: string;
    isNew: '0' | '1';
    fundCode: string;
    mainHighLight1: string;
    subHightLight1: string;
    optional: {
      noShowFundTrend: {
        mainHighLight2: string;
        subHighLight2: string;
      };
      isNewFund: {
        managerName: string;
        managerPhoto: string;
        managerTag: string;
        mainHighLight2: string;
        subHighLight2: string;
        buyPeople: string;
        jumpUrl: string;
      };
      isOldFund: {
        incomeRange: string;
      };
    };
  };
  subFund: { mainTitle: string; fundCode: string; incomeRange: string; jumpUrl: string }[];
  seeMoreUrl: string;
}

// 浮窗
interface IFloatingWindow {
  image: string;
  jumpUrl: string;
}

// 其他福利
interface IOtherWelfare {
  isShow: '0' | '1';
  professionalAnalysis: {
    tabName: string;
    mark: string;
    cardList: { cardKey: string; icon: string; cardName: string }[];
  };
  weeklyLive: {
    tabName: string;
    mark: string;
    showCard: string;
  };
  investmentTool: {
    tabName: string;
    mark: string;
    cardList: { image: string; name: string; introduction: string; value: string; label: string }[];
  };
  investmentSchool: {
    tabName: string;
    mark: string;
    cardList: {
      image: string;
      name: string;
      introduction: string;
      form: '0' | '1';
      classHour: string;
      label: string;
    }[];
  };
  seeMoreUrl: string;
}

// 评论区
interface IComments {
  isShow: '0' | '1';
  commentList: {
    commentName: string;
    nickname: string;
    avatar: string;
    likeNumber: string;
    commentContent: string;
  }[];
}

interface IStrategyContent {
  name: string;
  // 权益
  rights: IRights;
  // 基金推荐区
  fundRecommend: IFundRecommend;
  // 浮窗区
  floatingWindow: IFloatingWindow;
  // 其他福利区
  otherWelfare: IOtherWelfare;
  // 用户精评区
  comments: IComments;
}

export interface ITableItem {
  isEnable: boolean;
  key: string;
  createAuthor: string;
  createTime: string;
  lastEditor: string;
  lastEditTime: string;
  strategyContent: IStrategyContent;
}
