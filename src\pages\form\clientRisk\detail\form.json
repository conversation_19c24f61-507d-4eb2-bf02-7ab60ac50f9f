{"type": "object", "properties": {"commandList": {"type": "array", "default": [{"commandId": ""}], "ui:className": "m-client-risk-list", "items": {"type": "object", "properties": {"commandId": {"title": " ", "type": "string", "ui:widget": "html", "ui:className": "m-client-risk-version", "ui:labelWidth": 200, "ui:width": "50%"}, "tipBtn": {"type": "string", "ui:width": "108px", "ui:className": "m-client-risk-search", "ui:widget": "searchBtn"}, "commandName": {"title": "指令名称", "type": "string", "ui:labelWidth": 200, "ui:width": "60%"}, "applicationType": {"title": "应用类型", "description": "", "type": "array", "items": {"type": "string"}, "enum": [1, 2, 3], "enumNames": ["APP", "SDK-普通版", "SDK-至尊版"], "ui:labelWidth": 200}, "systemSelection": {"title": "系统选择", "type": "string", "enum": ["1", "2"], "enumNames": ["iOS端", "Android端"], "ui:labelWidth": 200, "ui:widget": "radio"}, "tip0": {"title": "版本号填区间，左小右大，全留空则代表默认应用到全部版本，一边留空则是>=（或<=）", "type": "string", "ui:widget": "html", "ui:className": "m-client-risk-version", "ui:labelWidth": 698}, "iosAppLeft": {"title": "iOS端APP版本号", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '2'}}", "ui:width": "350px", "ui:labelWidth": 200, "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "iosAppRight": {"title": "~", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '2'}}", "ui:labelWidth": -30, "ui:width": "190px", "ui:className": "m-client-risk-version", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "tip1": {"title": "APP版本号格式为XX.XX.XX，使用英文字符，例如6.23.11", "type": "string", "ui:widget": "html", "ui:hidden": "{{rootValue.systemSelection  === '2'}}", "ui:className": "m-client-risk-version", "ui:labelWidth": 560}, "iosSystemLeft": {"title": "iOS端系统版本号", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '2'}}", "ui:width": "350px", "ui:labelWidth": 200, "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "iosSystemRight": {"title": "~", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '2'}}", "ui:labelWidth": -30, "ui:width": "190px", "ui:className": "m-client-risk-version", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "tip2": {"title": "系统版本号格式为XX.XX.XX，使用英文字符，例如14.6写作14.6.0", "type": "string", "ui:widget": "html", "ui:hidden": "{{rootValue.systemSelection  === '2'}}", "ui:className": "m-client-risk-version", "ui:labelWidth": 610}, "androidAppLeft": {"title": "Android端APP版本号", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '1'}}", "ui:width": "350px", "ui:labelWidth": 200, "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "androidAppRight": {"title": "~", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '1'}}", "ui:labelWidth": -30, "ui:width": "190px", "ui:className": "m-client-risk-version", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "tip3": {"title": "APP版本号格式为XX.XX.XX，使用英文字符，例如6.23.11", "type": "string", "ui:widget": "html", "ui:hidden": "{{rootValue.systemSelection  === '1'}}", "ui:className": "m-client-risk-version", "ui:labelWidth": 560}, "androidSystemLeft": {"title": "Android端系统版本号", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '1'}}", "ui:width": "350px", "ui:labelWidth": 200, "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "androidSystemRight": {"title": "~", "type": "string", "ui:hidden": "{{rootValue.systemSelection  === '1'}}", "ui:labelWidth": -30, "ui:width": "190px", "ui:className": "m-client-risk-version", "pattern": "^[0-9]+\\.{1}[0-9]+\\.{1}[0-9]+$", "message": {"pattern": "请检查是否输入正确格式"}}, "tip4": {"title": "系统版本号格式为XX.XX.XX，使用英文字符，例如4.4写作4.4.0", "type": "string", "ui:widget": "html", "ui:hidden": "{{rootValue.systemSelection  === '1'}}", "ui:className": "m-client-risk-version", "ui:labelWidth": 596}, "applicationMarket": {"type": "array", "items": {"type": "string"}, "ui:hidden": "{{rootValue.systemSelection === '1'}}", "ui:widget": "selectMode"}, "userIdType": {"title": "用户id类型", "type": "string", "enum": ["1", "2", "3", "4"], "enumNames": ["IID（设备识别码）", "User-id(手抄用户号)", "Cust-id(基金客户号)", "DeviceCode(设备指纹)"], "ui:labelWidth": 200, "ui:width": "60%"}, "tip5": {"title": " ", "default": "1、IID选择数字0-9或者小写字母a-z或者大写字母A-Z; 2、Userid选择数字0-9；3、Custid选择数字0-9；4、DeviceCode选择数字0-9或者大写字母A-Z", "type": "string", "ui:widget": "html", "ui:className": "m-client-risk-version", "ui:labelWidth": 200}, "distributionMode": {"title": "分发模式", "type": "string", "enum": ["1", "2"], "enumNames": ["按规则筛选", "手动导入文件"], "ui:labelWidth": 200, "ui:widget": "radio"}, "contentRules": {"title": "规则内容", "type": "string", "ui:hidden": "{{rootValue.distributionMode === '2'}}", "ui:labelWidth": 200, "ui:width": "60%", "pattern": "^[A-Za-z0-9]+([,][A-Za-z0-9]+)*$", "message": {"pattern": "请使用英文字符输入，多个以“,”分隔"}}, "tip6": {"title": "请使用英文字符输入，多个以“,”分隔", "type": "string", "ui:widget": "html", "ui:hidden": "{{rootValue.distributionMode  === '2'}}", "ui:className": "m-client-risk-version", "ui:labelWidth": 250, "ui:width": "40%"}, "userIdList": {"title": "导入客户号", "type": "string", "ui:hidden": "{{rootValue.distributionMode === '1'}}", "ui:widget": "fileImport", "ui:labelWidth": 200}, "commandType": {"title": "命令类型", "type": "string", "enum": ["delete_file"], "enumNames": ["删除文件"], "ui:labelWidth": 200, "ui:widget": "radio"}, "commandValue": {"title": "命令内容", "type": "string", "ui:labelWidth": 200, "ui:width": "60%", "ui:widget": "textarea"}, "tip7": {"title": "json字符串", "type": "string", "ui:widget": "html", "ui:className": "m-client-risk-version", "ui:labelWidth": 100, "ui:width": "40%"}, "commandExecutionTimes": {"title": "执行次数", "type": "string", "ui:labelWidth": 200, "ui:width": "60%"}, "tip8": {"title": "小于-1配置错误，不执行；-1:执行无限次,0:不执行,n>0:执行n次", "type": "string", "ui:widget": "html", "ui:className": "m-client-risk-version", "ui:labelWidth": 602}, "phoneModel": {"title": "手机型号", "type": "string", "ui:labelWidth": 200, "ui:width": "60%"}, "commit": {"title": "git的commit号", "type": "string", "ui:labelWidth": 200, "ui:width": "60%"}, "mode": {"title": "开关", "type": "string", "enum": ["0", "1", "2"], "enumNames": ["全量关", "全量开", "匹配规则"], "ui:labelWidth": 200, "ui:width": "60%"}}, "required": ["commandName", "applicationType", "systemSelection", "userIdType", "distributionMode", "contentRules", "userIdList", "commandType", "commandValue", "commandExecutionTimes", "mode"]}}}}