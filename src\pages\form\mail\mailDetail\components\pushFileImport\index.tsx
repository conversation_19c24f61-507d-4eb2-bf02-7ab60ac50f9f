import React, { useEffect } from 'react';
import { Button, Upload, Input, message } from 'antd';
import styles from './index.less';
import api from 'api';
import { MailData } from '@/pages/form/mail/interface';
const { postMailTxt } = api;
const { TextArea } = Input;

type FormRender = {
  value: string;
  name: string;
  onChange: Function;
  formData: MailData;
};
type TelImportInreface = {
  code: '0000' | '9100' | '1002' | '1001';
  data: {
    s3Url: string;
    uploadHashKey: string;
  };
  message: string;
  statusCode?: number;
  success?: boolean;
};

export default function({ value, onChange, name, formData }: FormRender) {
  const props = {
    // 隐藏文件列表
    fileList: [],
    // 传输文件之前的钩子函数
    beforeUpload(file: any) {
      // txt => 文本内容
      const reader = new FileReader();
      reader.readAsText(file);
      reader.onload = () => {
        let result;
        if (typeof reader.result === 'string') {
          // 返回以换行符为终止符的所有字符数组
          result = reader.result.match(/.+/g);
          if (result) {
            // 去除除了逗号以外的无用符号
            result = result.join();
            result = result.replace(/[^(0-9,)]+/g, '');
            // 替换逗号为换行符
            result = result.replace(/,/g, '\r');
          }
        }
        onChange(name, result);
        // txt => 上传给服务器
        let _formData = new FormData();
        _formData.append('fileName', file);
        formData.SendPushInfo?.pushUserType &&
          _formData.append('userType', formData.SendPushInfo?.pushUserType);
        postMailTxt(_formData)
          .then((res: TelImportInreface) => {
            if (res.code === '0000') {
              // 与后端协商进行过滤
              let _s3Url = res.data['s3Url'].split('/');
              sessionStorage.setItem('mailPushs3Url', _s3Url[1]);
              sessionStorage.setItem('mailPushuploadHashKey', res.data['uploadHashKey']);
            } else {
              message.error(res.message);
            }
          })
          .catch((err: unknown) => {
            console.warn(err);
            message.error('网络请求错误，请稍后再试');
          });
      };
      return true;
    },
  };
  useEffect(() => {
    onChange(name, '');
  }, []);
  return (
    <div className={styles['m-file-import']}>
      <div>
        <Button onClick={() => onChange(name, '')}>清空</Button>
        <Upload {...props}>
          <Button>导入TXT</Button>
        </Upload>
        <a
          onClick={() =>
            window.open(
              'https://testfund.10jqka.com.cn/s3/fund-resource/%E5%AF%BC%E5%85%A5%E8%8C%83%E4%BE%8B.txt',
            )
          }
        >
          查看范例
        </a>
      </div>
      <TextArea rows={8} value={value} />
    </div>
  );
}
