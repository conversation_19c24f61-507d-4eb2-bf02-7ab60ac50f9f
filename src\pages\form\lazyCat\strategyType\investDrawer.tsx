import {
    Form,
    Input,
    But<PERSON>,
    Popconfirm,
    Select
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';

const { TextArea } = Input;
const { Option } = Select;
interface baseDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: any;
    handleData: (data: any) => void;
}
class baseDrawer extends React.Component<baseDrawerProps, any> {
    constructor(props: baseDrawerProps) {
        super(props);
    }

    formItemLayout = {
        labelCol: {
            span: 4
        },
        wrapperCol: {
            span: 19
        },
    };
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { currentData } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                values = { 
                    ...currentData,
                    ...values,
                };
                console.log(values);
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;
        const { onEditClose } = this.props;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="投顾策略名称：">
                        <span>{this.props.currentData?.investConsultTacticsName}</span>
                    </Form.Item>
                    <Form.Item label="是否显示配置内容：" >
                        {getFieldDecorator('isShowConfig', {
                            initialValue: this.props.currentData?.isShowConfig || "1",
                        })(
                            <Select style={{width: '200px'}}>
                                <Option value="0" key="0">是</Option>
                                <Option value="1" key="1">否</Option>
                            </Select>
                        )}
                    </Form.Item>
                    <Form.Item label="卡片主文案：" wrapperCol={{span: 6}}>
                        {getFieldDecorator('cardMainText', {
                            initialValue: this.props.currentData?.cardMainText,
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="卡片副文案：" wrapperCol={{span: 6}}>
                        {getFieldDecorator('cardSubText', {
                            initialValue: this.props.currentData?.cardSubText,
                        })(
                            <Input />
                        )}
                    </Form.Item> 
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                            <Button type="primary">
                                提交
                            </Button>
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedBaseDrawer = Form.create<baseDrawerProps>({ name: 'baseDrawer' })(baseDrawer);
export default WrappedBaseDrawer