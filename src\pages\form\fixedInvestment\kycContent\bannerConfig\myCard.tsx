import {
    Form,
    Input,
    Button,
    Popconfirm,
    Select,
    Row,
    Col,
    Checkbox,
    message
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import { iBannerConfigData } from './index';
import styles from '../index.less';

const { Option } = Select;

interface iBannerCardProps extends FormComponentProps {
    currentData: iBannerConfigData;
    handleData: (data: iBannerConfigData) => void;
    handleDelete: () => void;
    allData: iBannerConfigData[];
    index: number;
}
const applyPeople: any = {
    xsd: '学生党',
    zcxr: '职场新人',
    zclr: '职场老炮',
    zrz: '责任族',
    txz: '退休族'
}
class BannerConfigCard extends React.Component<iBannerCardProps, any> {
    constructor(props: iBannerCardProps) {
        super(props);
        this.state = {
            functionType: props.currentData?.functionType,
            cardTitle: props.currentData?.fundData?.cardTitle,
            contentTitle: props.currentData?.fundData?.contentTitle,
            btnText: props.currentData?.fundData?.btnText,
            jumpUrl: props.currentData?.fundData?.jumpUrl,
            checkType: props.currentData?.fundData?.checkType,
            fundText: props.currentData?.fundData?.fundText,
            productId: props.currentData?.fundData?.productId,
            productName: props.currentData?.fundData?.productName,
            rate: props.currentData?.fundData?.rate,
            isEdit: false,
        }
    }
    formItemLayout = {
        labelCol: {
            span: 3
        },
        wrapperCol: {
            span: 19
        },
    };

    handleInputValue = (e: any, type: string) => {
        let val = e?.target?.value;
        if (type === 'productId') {
            val = val.replace(/\s/g, '');
            val = val.replace(/[^\d]/g, '');
            if (val?.length === 6) {
                this.getJsonp(val);
            } else {
                this.setState({
                    productName: ''
                })
            }
        }
        this.setState({
            [type]: val
        })
        
    }
    getJsonp(code: string) {
        const { index } = this.props;
        let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/fund/multiFundInfo/${code}_name?return=jsonp&jsonp=jsonp${index}`
        console.log(_script)
        document.body.appendChild(_script);
        _script.onload = function() {
            document.body.removeChild(_script);
        };
    }
    handleFunctionType = (val: string) => {
        this.setState({
            functionType: val
        })
    }
    handleSubmit = () => { 
        const { currentData, allData } = this.props;
        const { functionType, isEdit } = this.state;
        if (isEdit) {
            if (functionType === 'kyc') {
                for (let i = 0,len = allData?.length; i < len; i++ ) {
                    let item = allData[i];
                    if (item?.functionType === 'kyc' && item?.key !== currentData.key) {
                        message.error('仅支持添加一个KYC定投方案');
                        return;
                    }
                }
                this.props.form.validateFields((err, values) => {
                    if (!err) {
                        let obj: iBannerConfigData = { kycData: {...values}, functionType, key: currentData.key };
                        this.props.handleData(obj);
                        this.setState({
                            isEdit: !isEdit
                        })
                    }
                });
            } else {
                const { functionType, cardTitle, contentTitle, btnText, jumpUrl, checkType,
                    fundText, productId, productName, rate } = this.state;
                if (!cardTitle) {
                    message.error('请填写卡片标题');
                    return;
                } 
                if(!contentTitle) {
                    message.error('请填写内容主题');
                    return;
                } 
                if (checkType) {
                    if (checkType === 'text') {
                        if (!fundText) {
                            message.error('请填写亮点文案');
                            return;
                        }
                        let arr = fundText.split('|');
                        for (let i = 0, len = arr?.length; i < len; i++) {
                            if (arr[i]?.length > 5) {
                                message.error('文案每个限制5个字以内');
                                return;
                            }
                        }
                    } else {
                        if (!productId) {
                            message.error('请填写产品ID');
                            return;
                        } else if(!productName) {
                            message.error('请填写产品名称');
                            return;
                        } if (rate === null || rate === undefined || rate === '') {
                            message.error('请填写收益率');
                            return;
                        }
                    }
                       
                }
                if(!btnText) {
                    message.error('请填写按钮文案');
                    return;
                } 
                if (!jumpUrl) {
                    message.error('请填写跳转链接');
                    return;
                } 
                this.props.handleData({
                    fundData: {cardTitle, contentTitle, btnText, jumpUrl, checkType,
                        fundText, productId, productName, rate}, functionType, key: currentData.key
                });
                this.setState({
                    isEdit: !isEdit
                })
            }
        } else {
            this.setState({
                isEdit: !isEdit
            })
        }
        
    };
    handleSelectValue = (val: number) => {
        this.setState({
            rate: val
        })
    }
    onCheckChange = (e: any, value: string) => {
        let checked = e.target.checked;
        if (checked) {
            this.setState({
                checkType: value,
            });
        } else {
            this.setState({
                checkType: value === 'text' ? 'product' : value,
            });
        }
    }
    // static getDerivedStateFromProps(nextProps: any, prevState: any) {
    //     const { functionType } = prevState;
    //     if (nextProps.currentData?.functionType !== functionType ) {
    //         return {
    //             functionType: nextProps.currentData?.functionType,
    //         }
    //     }
    //     return null;
    // }
    componentDidMount() {
        const _this = this;
        const { index } = this.props;
        window[`jsonp${index}`] = function (res: any) {
            const { error, data } = res;
            if (error?.id === 0 && data) {
                Object.keys(data)?.forEach((key: string) => {
                    console.log(data[key]);
                    _this.setState({
                        productName: data[key]?.name
                     })
                })
            }
        }
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const { handleDelete, currentData } = this.props;
        const { functionType, cardTitle, contentTitle, btnText, jumpUrl, checkType,
            fundText, productId, productName, rate, isEdit } = this.state;
        const { kycData } = currentData;
        return (
            <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button className={styles['m-button']} onClick={this.handleSubmit}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={handleDelete}
                        okText="是"
                        cancelText="否"
                    >
                        <Button type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                
                <div className={styles['m-tagModel-row']}>
                    <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>功能类型：</p>
                    <Select value={functionType} style={{ width: 180 }} onChange={this.handleFunctionType} disabled={!isEdit}>
                        <Option value="kyc">KYC定投方案</Option>
                        <Option value="jjtj">基金推荐</Option>
                    </Select>
                </div>
                { functionType === 'kyc' ? (<>
                    <Form {...this.formItemLayout}>
                        { ['xsd', 'zcxr', 'zclr', 'zrz', 'txz'].map((item) => {
                            return (
                                <React.Fragment key={item}>
                                    <h1>{applyPeople[item]}</h1>
                                    <Form.Item label="目标文案：">
                                        <Row gutter={8}>
                                            <Col span={12}>
                                                {getFieldDecorator(`${item}targetText`, {
                                                    initialValue: kycData && kycData[`${item}targetText`],
                                                    rules: [{ required: true, message: '请填写目标文案' }],
                                                })(
                                                    <Input disabled={!isEdit} maxLength={30}/>
                                                )}
                                            </Col>
                                            <Col span={12}>
                                                <span className={styles['m-card-required']}>红色字体以‘#’分隔</span>
                                            </Col>
                                        </Row>
                                    
                                    </Form.Item>
                                    <Form.Item label="按钮文案：">
                                        {getFieldDecorator(`${item}btnTitle`, {
                                            initialValue: kycData && kycData[`${item}btnTitle`],
                                            rules: [{ required: true, message: '请填写按钮文案' }],
                                        })(
                                            <Input disabled={!isEdit} maxLength={8}/>
                                        )}
                                    </Form.Item>
                                </React.Fragment>
                            )
                        }) }
                        
                    </Form>
                </>) : (
                    <>
                        <div className={styles['m-tagModel-row']}>
                            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>卡片标题:</p>
                            <Input value={cardTitle} onChange={(e) => this.handleInputValue(e, 'cardTitle')} disabled={!isEdit} maxLength={4}></Input>
                        </div>
                        <div className={styles['m-tagModel-row']}>
                            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>内容主题:</p>
                            <Input value={contentTitle} onChange={(e) => this.handleInputValue(e, 'contentTitle')} disabled={!isEdit} maxLength={14}></Input>
                        </div>
                        <div style={{marginBottom: 10}}>
                            <p><span className={styles['m-required']}>*</span>亮点:（文案或产品二选一）</p>
                            <Row gutter={8}>
                                <Col span={3}>
                                    <Checkbox
                                        checked={checkType === 'text'}
                                        onChange={(e) => this.onCheckChange(e, 'text')}
                                        disabled={!isEdit}
                                    >
                                        <span>文案：</span>
                                    </Checkbox>
                                </Col>
                                <Col span={10}>
                                    <Input value={fundText} onChange={(e) => this.handleInputValue(e, 'fundText')} disabled={!isEdit} maxLength={17}/>
                                </Col>
                                <Col span={6}>
                                    <p><span className={styles['m-required']}>*</span>多个以'|'分隔，每个限制5个字</p>
                                </Col>
                            </Row>
                            <div>产品</div>
                            <div>
                                <Row gutter={8}>
                                    <Col span={3}>
                                        <Checkbox
                                            checked={checkType === 'product'}
                                            onChange={(e) => this.onCheckChange(e, 'product')}
                                            disabled={!isEdit}
                                        >
                                            <span>产品ID:</span>
                                        </Checkbox>
                                        
                                    </Col>
                                    <Col span={4}>
                                        <Input value={productId} onChange={(e) => this.handleInputValue(e, 'productId')} disabled={!isEdit}/>
                                    </Col>
                                    <Col span={4}>
                                        <Input value={productName} onChange={(e) => this.handleInputValue(e, 'productName')} disabled={!isEdit}/>
                                    </Col>
                                    <Col span={8}>
                                        <div>
                                            <p className={styles['m-card-label']}>收益率展示:</p>
                                            <Select style={{width: 180}}  value={rate} onChange={this.handleSelectValue} disabled={!isEdit}>
                                                <Option key={10} value={10}>成立以来年化收益率</Option>
                                                <Option key={9} value={9}>近一年收益率</Option>
                                                {/* <Option key={8} value={8}>7日年化收益率</Option> */}
                                                <Option key={0} value={0}>近一年定投收益率</Option>
                                                <Option key={2} value={2}>近三年定投收益率</Option>
                                            </Select>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                        
                        <div className={styles['m-tagModel-row']}>
                            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>按钮文案:</p>
                            <Input value={btnText} onChange={(e) => this.handleInputValue(e, 'btnText')} disabled={!isEdit}  maxLength={8}></Input>
                        </div>
                        <div className={styles['m-tagModel-row']}>
                            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>跳转链接:</p>
                            <Input value={jumpUrl} onChange={(e) => this.handleInputValue(e, 'jumpUrl')} disabled={!isEdit}></Input>
                        </div>
                        <span style={{color: '#FF330A', paddingLeft: 110}}>跳转链接使用客户端协议跳转（例：client.html?action=webpage,title='',url=https://mams.10jqka.com.cn/new/server/html/11103.html）</span>
                    </>
                ) }
            </div>
        )
    }
    
}

const WrappedBannerCard = Form.create<iBannerCardProps>({ name: 'fundConfigCard' })(BannerConfigCard);
export default WrappedBannerCard;