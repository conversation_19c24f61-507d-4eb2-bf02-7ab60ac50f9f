/*
 * 机构客户数据维护
 * @Author: <PERSON><PERSON><PERSON><PERSON>@myhexin.com
 * @Date: 2023-02-16 15:56:07
 * @Last Modified by: z<PERSON><PERSON><PERSON>@myhexin.com
 * @Last Modified time: 2023-02-16 20:00:44
 */
import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';

import { Button, Col, Collapse, DatePicker, message, Modal, Row, Select, Spin, Table, Card, Input, Form, ConfigProvider, Pagination } from 'antd';
const { Panel } = Collapse;
const { Option } = Select;
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import styles from '../index.less'
import api from 'api';
import { SummaryFilter } from '../components/filter';
import { fofProductProps, maintainFilterMap, ProductInfoList, SaleInfoListZ } from '../type';
import { mockSearchService } from '../mock';
import { generateSaleInfoListInTable, generateManagerFeeInfoListInTable } from '../util';
import zhCN from 'antd/es/locale/zh_CN';
const { postHash, fetchHashAll, postHashDel, getTopicFunds, fetchFofQueryList, postEditProductCommission, postEditSaleInfo } = api;
interface investProps {
  investAccountName?: string;
  saleInfoList?: {
    peopleName: string;
    divideRatio: string;
  }[];
  returnPoints?: string;
  returnRatioExpression?: string;
  managerFeeInfoList?: {
    fundCode: string;
    fundName: string;
    feePercent: string;
  }[]
}
export interface IProps { }
let init = {
  saleInfoList: [{
    peopleName: '',
    divideRatio: ""
  }]
}
const clientDataMaintain: FC<IProps> = () => {
  const [isListLoading, setIsListLoading] = useState<boolean>(false); // 列表加载loading
  const [dataSource, setDataSource] = useState<Partial<ProductInfoList>[]>([]);
  const [investNameOption, setInvestNameOption] = useState<any[]>();
  const [investAccountNameOption, setInvestAccountName] = useState<any[]>();
  const [modalShow, setModalShow] = useState(false);
  const [saleInfoList, setSaleInfoList] = useState<SaleInfoListZ[]>([]);
  const [nowEditInfo, setNowEditInfo] = useState<ProductInfoList>({});
  const [revokeInfo, setRevokeInfo] = useState<string[]>([]);
  const [allName, setAllName] = useState([]);
  const [pageNum, setPageNum] = useState(1);
  const [searchItem, setSearchItem] = useState<any>({});
  const [tableTotal, setTableTotal] = useState(0);
  const [editSaleInfo, setEditSaleInfo] = useState<any[]>([]);
  const [pageSize, setPageSize] = useState(10);
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
    },
    {
      title: '投管人名称',
      dataIndex: 'investorName',
      key: 'investorName',
    },
    {
      title: '投资账户名称',
      dataIndex: 'accountName',
      key: 'accountName',
    },

    {
      title: '开户日期',
      dataIndex: 'openAccountDate',
      key: 'openAccountDate',
    },
    {
      title: '策略',
      dataIndex: 'strategyName',
      key: 'strategyName',
    },
    {
      title: '销售人员',
      dataIndex: 'saleInfoList',
      key: 'saleInfoList',
      render: (text: unknown, record: any) => {
        return <span>{generateSaleInfoListInTable(text)}</span>
      }
    },
    {
      title: '返点比例',
      dataIndex: 'returnRatio',
      key: 'returnRatio',
      render: (text: unknown, record: any) => {
        return <span>{`${Number(text) * 100}%`}</span>
      }
    },
    {
      title: '返点计算公式',
      dataIndex: 'returnRatioExpression',
      key: 'returnRatioExpression',
    },
    {
      title: '管理费分成登记',
      dataIndex: 'managerFeeInfoList',
      key: 'managerFeeInfoList',
      render: (text: unknown, record: any) => {
        return <span>{`${generateManagerFeeInfoListInTable(text)}`}</span>
      }
    },
    {
      title: '操作',
      dataIndex: 'options',
      render: (text: unknown, record: any) => {
        return (
          <section>
            <Button
              onClick={() => {
                setModalShow(true);

                // let adapterSale = record.saleInfoList.map((item) => ({...item, isValid: 1}))
                setNowEditInfo({ ...record, saleInfoList: record.saleInfoList });
              }}
              type="primary"
            >
              维护
            </Button>
          </section>
        );
      },
    },
  ];
  // 初始化数据
  useEffect(() => {
    fetchFofQueryList({}, '')
      .then((res: any) => {
        if (res.status_code === 0) {
          let resultSingleData = res.data as Partial<fofProductProps>;
          setDataSource(resultSingleData.productInfoList?.map((item, index) => ({ ...item, index: index })))
          if (resultSingleData?.count) setTableTotal(resultSingleData?.count)
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
  }, []);
  const editProductCommission = async (obj) => {
    postEditProductCommission(obj)
      .then((res: any) => {
        if (res.status_code === 0) {
          // message.success('请刷新页面');
          // setTimeout(() => { location.reload(); }, 500)
        } else {
          message.warn(res.message);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
        message.warn('系统错误');
      });
    let promiseArr: any[] = []
    obj.saleInfoList.forEach(async element => {
      let tempPromise = postEditSaleInfo;
      let pro = await tempPromise({ ...element, fofId: nowEditInfo.fofId }).then((res: any) => {
        if (res.status_code === 0) {
          // message.success('请刷新页面');
          // setTimeout(() => { location.reload(); }, 500)
        } else {
          message.warn(res.message);
        }
      })
      console.log('donepro', pro)
      // promiseArr.push(pro)
    });
    // setTimeout(() => { location.reload(); }, 3000)
  }
  // 用户名称修改事件
  const handlePageNum = (current: number) => {
    const pageObj = { ...searchItem, pageNum: current }
    setPageNum(current)
    fetchFofQueryList({}, getUrlParmFromObject(pageObj))
      .then((res: any) => {
        if (res.status_code === 0) {
          let resultSingleData = res.data as Partial<fofProductProps>;
          setDataSource(resultSingleData.productInfoList?.map((item, index) => ({ ...item, index: index })));
          // if (resultSingleData?.count) setTableTotal(resultSingleData?.count)
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
  }
  useEffect(() => {
    const pageObj = { ...searchItem, pageNum: pageNum, pageSize: pageSize }
   !( pageNum === 1 && pageSize === 10) && fetchFofQueryList({}, getUrlParmFromObject(pageObj))
      .then((res: any) => {
        if (res.status_code === 0) {
          let resultSingleData = res.data as Partial<fofProductProps>;
          console.log(resultSingleData.productInfoList?.length,'zzzzzsd')
          // setDataSource((resultSingleData.productInfoList || []).map((item, index) => ({ ...item, index: index })))
          // if (resultSingleData?.count) setTableTotal(resultSingleData?.count)
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
  }, [pageSize, pageNum])
  // 公式添加
  const handleFormula = (factor: string) => {
    setRevokeInfo(prev => ([...prev, factor]))
    setNowEditInfo((prev) => {
      return { ...prev, returnRatioExpression: (prev.returnRatioExpression || '') + factor }
    })
  }
  // 页容量 控制器
  const handlePageSize = (pageNum: number, pageSize: number) => {
    console.log('页容量控制器', pageNum, pageSize);
    setPageSize(pageSize);
  };
  const getUrlParmFromObject = obj => {
    let url = '?';
    Object.keys(obj || {})?.forEach((key, index) => {
      if (obj[key]) {
        url += key + '=' + obj[key] + (index !== Object.keys(obj || {}).length - 1 ? '&' : '');
      }
    });
    return url;
  };
  const revokeFormula = () => {
    const revokeArr = [...revokeInfo]
    const revoke = revokeArr.pop();
    if (revoke) {
      setNowEditInfo((prev) => {
        return { ...prev, returnRatioExpression: prev.returnRatioExpression?.substring(0, prev.returnRatioExpression?.lastIndexOf(revoke)) }
      })
    }
    setRevokeInfo(revokeArr)
  }
  return (
    <div>
      <Form layout="inline">
        <Form.Item>
          <SummaryFilter
            submitSearch={param => {
              setSearchItem(param)
              setSearchItem(param)
              fetchFofQueryList({}, getUrlParmFromObject(param))
                .then((res: any) => {
                  if (res.status_code === 0) {
                    let resultSingleData = res.data as Partial<fofProductProps>;
                    setDataSource(resultSingleData.productInfoList?.map((item, index) => ({ ...item, index: index })))
                    if (resultSingleData?.count) setTableTotal(resultSingleData?.count)
                  } else {
                    message.warn(res.status_msg);
                  }
                })
                .catch((err: unknown) => {
                  console.log(err);
                  let res = mockSearchService;
                });
            }}
            showArr={maintainFilterMap}
            propSalesManInit={(val) => { setSaleInfoList(val) }}
          />
        </Form.Item>
      </Form>
      <ConfigProvider locale={zhCN}>
        <Table columns={columns}
          style={{ marginTop: 50 }}
          dataSource={dataSource}
          rowKey={record => record.index}
          scroll={{ x: 1300 }}
          useFixedHeader={true}
        >
        </Table>
      </ConfigProvider>
      <div className={classnames(styles['m-table-pagination'], styles['m-top'])}>
        <ConfigProvider locale={zhCN}>
        <Pagination
            className={classnames(styles['m-pagination'])}
            current={pageNum}
            pageSize={pageSize}
            total={tableTotal}
            onChange={handlePageNum}
            showSizeChanger
            pageSizeOptions={['10', '20', '30', '50']}
            onShowSizeChange={handlePageSize}
            hideOnSinglePage={tableTotal < 10}
          />
        </ConfigProvider>
      </div>
      <Modal
        title="信息维护"
        visible={modalShow}
        width={800}
        onOk={() => {
          console.log('nowEditing', nowEditInfo);
          const postObject = {
            fofId: nowEditInfo.fofId,
            returnRatio: Number(nowEditInfo.returnRatio).toString(),
            returnRatioExpression: nowEditInfo.returnRatioExpression,
            managerFeeInfoList: nowEditInfo.managerFeeInfoList.map(item => ({
              ...item,
              ratio: Number(item.ratio).toString()
            })),
            saleInfoList: nowEditInfo.saleInfoList.map(item => ({
              ...item,
              divideRatio: Number(item.divideRatio).toString(),
              isValid: item.isValid !== undefined ? item.isValid : 1
            }))
          };
          // console.log('postObject', postObject)
          let dataSourceTemp = [...dataSource]
          dataSourceTemp[nowEditInfo.index] = {...nowEditInfo}

          const result = dataSourceTemp?.saleInfoList?.filter(item=>item.isValid===1)
          console.log('result',dataSourceTemp?.saleInfoList)
          dataSourceTemp.saleInfoList = result
          setDataSource(dataSourceTemp)
          editProductCommission(postObject)
        }}
        onCancel={() => {
          setModalShow(false);
        }}
      >
        <Card
          style={{ marginTop: 16 }}
          type="inner"
        >
          <span style={{ marginRight: 30 }}>投资账户名称:</span><span>{nowEditInfo.accountName}</span>
        </Card>
        <Card
          style={{ marginRight: 30 }}
          type="inner"
          title="销售人员"
        >
          <>
            {nowEditInfo.saleInfoList?.map((people, index) => {
              return (
                people.isValid !== 0 && <Row style={{ marginBottom: 12 }}>
                  <Col span={10}>
                    <span>{`${index + 1}.姓名:`}</span>
                    <Select
                      value={people.saleName}
                      showSearch={true}
                      placeholder="姓名"
                      style={{ width: '100px' }}
                      onChange={e => {
                        setNowEditInfo((prev) => {
                          const arr = [...prev.saleInfoList];
                          arr[index].saleName = e
                          arr[index].saleId = (e || '').match(/（(.+?)）/)[1];
                          return {
                            ...prev, saleInfoList: arr
                          }
                        })
                      }}
                    >
                      {(saleInfoList as SaleInfoListZ[]).map(item => (
                        <Option key={item.saleId} value={`${item.saleName}（${item.saleId}）`}>
                          {`${item.saleName}（${item.saleId}）`}
                        </Option>
                      ))}
                    </Select>
                  </Col>
                  <Col span={10}>
                    <Input
                      defaultValue={people.divideRatio}
                      onChange={e => {
                        e.persist()
                        setNowEditInfo((prev) => {
                          const arr = [...prev.saleInfoList];
                          arr[index].divideRatio = e.target.value;
                          return {
                            ...prev, saleInfoList: arr
                          }
                        })
                      }}
                      addonBefore={<span>提成权重</span>}
                      addonAfter={<span>小数</span>}>

                    </Input>
                  </Col><Col span={4}>
                    <Button onClick={() => {
                      setNowEditInfo((prev) => {
                        let arr = [...prev.saleInfoList];
                        arr[index] = { ...arr[index], isValid: 0 }
                        console.log('arrrA', arr[index])
                        return {
                          ...prev, saleInfoList: arr
                        }
                      })
                    }}>删除</Button>
                  </Col>
                </Row>)
            })}
            <Col span={4} offset={20}>
              <Button type='primary' onClick={() => {
                setNowEditInfo((prev) => {
                  let arr = [...prev.saleInfoList];
                  arr.push({
                    saleName: '',
                    divideRatio: "",
                    isValid: 1
                  })
                  return {
                    ...prev, saleInfoList: arr
                  }
                })

              }}>添加</Button>
            </Col>
          </>

        </Card>
        <Card style={{ marginTop: 16 }}
          type="inner"
          title="返点合作登记"
        >
          <Row>
            <Col span={4} style={{ fontSize: 18 }}>计算方式</Col>
          </Row>
          <Row justify="space-between" type='flex' style={{ marginTop: 8 }}>
            <Col span={4}>计算参数：</Col>
            <Col span={5}><div className={styles['blue-card']} onClick={() => { handleFormula('客户维护费') }}>客户维护费</div></Col>
            <Col span={5}><div className={styles['blue-card']} onClick={() => { handleFormula('销售服务费') }}>销售服务费</div></Col>
            <Col span={3}><div className={styles['blue-card']} onClick={() => { handleFormula('税费') }}>税费</div></Col>
            <Col span={3}><div className={styles['blue-card']} onClick={() => { handleFormula('监管费') }}>监管费</div></Col>
            <Col span={3}><div className={styles['blue-card']} onClick={() => { handleFormula('支付费') }}>支付费</div></Col>
          </Row>
          <Row justify="space-between" type='flex' style={{ marginTop: 8 }}>
            <Col span={4}>计算符号：</Col>
            <Col span={3}><div className={styles['orange-card']} onClick={() => { handleFormula('(') }}>{'('}</div></Col>
            <Col span={3}><div className={styles['orange-card']} onClick={() => { handleFormula(')') }}>{')'}</div></Col>
            <Col span={3}><div className={styles['orange-card']} onClick={() => { handleFormula('+') }}>{'+'}</div></Col>
            <Col span={3}><div className={styles['orange-card']} onClick={() => { handleFormula('-') }}>{'-'}</div></Col>
            <Col span={3}><div className={styles['orange-card']} onClick={() => { handleFormula('*') }}>{'*'}</div></Col>
            <Col span={3}><div className={styles['orange-card']} onClick={() => { handleFormula('/') }}>{'/'}</div></Col>
          </Row>
          <Row justify="space-between" type='flex' style={{ marginTop: 8 }}>
            <Col span={4}>返点比例：</Col>
            <Col span={5}><div className={styles['blue-card']}>比例</div></Col>
            <Col span={15}>
              <Input
                defaultValue={nowEditInfo.returnRatio * 100}
                value={nowEditInfo.returnRatio}
                onChange={e => {
                  e.persist()
                  setNowEditInfo((prev) => {
                    return {
                      ...prev, returnRatio: e.target.value
                    }
                  })
                }}
                addonAfter={<span>小数</span>}></Input>
            </Col>
          </Row>
          <Row>
            <Button onClick={revokeFormula}>撤销</Button>
          </Row>
          <Row style={{ marginTop: 8 }}>
            <Col span={4}>返点费用=</Col>
            <Col span={20}><Input disabled={true} value={nowEditInfo.returnRatioExpression}></Input></Col>
          </Row>
        </Card>
        <Card
          style={{ marginTop: 16 }}
          type="inner"
          title="管理费分成登记"
        >
          <>
            {nowEditInfo.managerFeeInfoList?.map((people, index) => {
              return (
                <><Row style={{ marginBottom: 12 }}>
                  <Col span={10}>

                    <Input
                      value={people.fundCode}
                      onChange={e => {
                        e.persist()
                        setNowEditInfo((prev) => {
                          const arr = [...prev.managerFeeInfoList];
                          arr[index].fundCode = e.target.value;
                          return {
                            ...prev, managerFeeInfoList: arr
                          };
                        });
                      }}
                      addonBefore={<span>{`${index + 1}.基金代码:`}</span>}
                    />
                  </Col><Col span={10}>
                    <Input
                      defaultValue={people.ratio * 100}
                      value={people.ratio}
                      onChange={e => {
                        e.persist()
                        setNowEditInfo((prev) => {
                          const arr = [...prev.managerFeeInfoList];
                          arr[index].ratio = e.target.value;
                          return {
                            ...prev, managerFeeInfoList: arr
                          };
                        });
                      }}
                      addonBefore={<span>分成比例</span>}
                      addonAfter={<span>小数</span>}></Input>
                  </Col><Col span={4}>
                    <Button onClick={() => {
                      setNowEditInfo((prev) => {
                        let arr = [...prev.managerFeeInfoList];
                        arr.splice(index, 1);
                        return {
                          ...prev, managerFeeInfoList: arr
                        };
                      });
                    }}>删除</Button>
                  </Col>
                </Row></>)
            })}
            <Col span={4} offset={20}>
              <Button type='primary' onClick={() => {
                setNowEditInfo((prev) => {
                  let arr = [...prev.managerFeeInfoList || []];
                  arr.push({
                    fundCode: '',
                    ratio: ""
                  })
                  return {
                    ...prev, managerFeeInfoList: arr
                  }
                })

              }}>添加</Button>
            </Col>
          </>

        </Card>
      </Modal>
    </div>
  );
};
export default clientDataMaintain;
