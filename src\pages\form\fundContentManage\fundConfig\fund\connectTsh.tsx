import React, { useState, useEffect } from 'react';
import { Modal, Input, message } from  'antd';
import api from 'api';
import './../types'

const {
    fetchTshName
} = api

export default React.memo(function({
    isShow,
    setIsShow,
    connectTsh,
    modifiedData,
    save
}: {
    isShow: boolean
    setIsShow: Function
    connectTsh: TSH[]
    modifiedData: temporaryData
    save: Function
}) {

    const [textValue, setTextValue] = useState('') //编辑框文案

    useEffect(() => {
        let _value: string = connectTsh.map((item: TSH) => item.id).join(',')
        setTextValue(_value)
    }, [connectTsh])

    function handleTextArea(e: any) {
        let _value: string = e.target.value
        setTextValue(_value)
    }

    function upload() {
        //如果输入框为空
        if (!textValue) {
            let _modifiedData: temporaryData = {...modifiedData}
            _modifiedData.delLinkedThsList = connectTsh.map((tsh: TSH) => tsh.id)
            setIsShow(false)
            return save(_modifiedData)
        }
        fetchTshName({
            data: textValue
        }).then((data: any) => {
            if (data.status_code === 0) {
                let _newConnectTsh: TSH[] = data.data
                //通过这个判断同顺号输入是否有误
                if (textValue.split(',').filter(item => item).length === _newConnectTsh.length) {
                    let _addTsh: string[] = [],
                        _delTsh: string[] = []
                    setIsShow(false)
                    _newConnectTsh.forEach((tsh: TSH) => {
                        if(connectTsh.filter((item: TSH) => item.id === tsh.id).length === 0) {
                            _addTsh.push(tsh.id)
                        }
                    })
                    connectTsh.forEach((tsh: TSH) => {
                        if(_newConnectTsh.filter((item: TSH) => item.id === tsh.id).length === 0) {
                            _delTsh.push(tsh.id)
                        }
                    })
                    let _modifiedData: temporaryData = {...modifiedData}
                    _modifiedData.addLinkedThsList = _addTsh
                    _modifiedData.delLinkedThsList = _delTsh
                    return save(_modifiedData)
                }
            } 
            return message.error('同顺号输入有误')
        }).catch((e: any) => {
            message.error('同顺号输入有误')
        })
    }

    return (
        <Modal 
        title="关联同顺号" 
        visible={isShow} 
        width={800}
        okText="提交"
        cancelText="取消"
        onCancel={() => {setIsShow(false)}}
        onOk={upload}
        >
            <p>输入要关联该基金的同顺号id，若存在多个id请用英文逗号隔开</p>
            <Input.TextArea 
            value={textValue}
            onChange={handleTextArea}
            rows={5}
            ></Input.TextArea>
        </Modal>
    )
})