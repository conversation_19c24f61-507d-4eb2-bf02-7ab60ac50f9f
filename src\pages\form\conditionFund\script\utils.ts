// export const handleApi = (hash: string) => {
//     let get =  '';
//     let post = ''
//     switch(hash) {

// import { STRICT_PROPNAME, VIP_PROPNAME } from '../const';

//         case 'coupon':
//             get = 'fetchSYPacket';
//             post = 'fetchSYPacket';
//             break;

//         case 'threesteps':
//             get = 'fetchSYNewAccount';
//             post = 'fetchSYNewAccount';
//             break;

//     }
//     return {
//         get,
//         post
//     };
// }

// export const modelName = (hash: string) => {
//   const obj = {
//     [STRICT_PROPNAME]: {
//       // location: '优惠券',
//       name: 'title',
//       // type: 'packet'
//     },

//     [VIP_PROPNAME]: {
//       // location: '新人三步走',
//       name: 'title',
//       // type: 'newaccount'
//     },
//   };
//   return obj[hash];
// };

// export const fetchData = (item: any, hash: string) => {
//   let formData: any = {};
//   switch (hash) {
//     case STRICT_PROPNAME:
//       formData = {
//         title: item.title,
//         mainCondition: {
//           normal: item.mainConditionStr,
//           parsed: item.mainConditionParsed,
//         },
//         condition: {
//           normal: item.conditionStr,
//           parsed: item.conditionParsed,
//         },
//       };
//       break;

//     case VIP_PROPNAME:
//       formData = {
//         title: item.title,
//         mainCondition: {
//           normal: item.mainConditionStr,
//           parsed: item.mainConditionParsed,
//         },
//         condition: {
//           normal: item.conditionStr,
//           parsed: item.conditionParsed,
//         },
//         vipInfo: item.vipInfo,
//       };
//       break;
//   }
//   return formData;
// };
// export const addData = (hash: string) => {
//   let formData: any = {};
//   switch (hash) {
//     case STRICT_PROPNAME:
//       formData = {
//         title: '',
//         mainCondition: {
//           normal: '',
//           parsed: [],
//         },
//         condition: {
//           normal: '',
//           parsed: [],
//         },
//       };
//       break;

//     case VIP_PROPNAME:
//       formData = {
//         title: '',
//         mainCondition: {
//           normal: '',
//           parsed: [],
//         },
//         condition: {
//           normal: '',
//           parsed: [],
//         },
//         vipInfo: {
//           avatar: '',
//           name: '',
//           background: '',
//           mind: '',
//           jumpUrl: '',
//         },
//       };
//       break;
//   }
//   return formData;
// };
export const checkUrl = (str: string, sort: string = '') => {
  if (str) {
    if (!/^(http:\/\/|https:\/\/|client.html).+/.test(str)) {
      return {
        isError: true,
        msg: `请填写${sort}正确的跳转链接`,
      };
    }
    if (str.length !== str.trim().length) {
      return {
        isError: true,
        msg: `${sort}跳转链接前后不能有空格`,
      };
    } else {
      return {
        isError: false,
        msg: '',
      };
    }
  } else {
    return {
      isError: false,
      msg: '',
    };
  }
};
export const setRandomId=(id)=>{
  if(!window["$$ids"]){
    window["$$ids"]={
      [id]:true
    }
  }else{
    window["$$ids"][id]=true;
  }
}
export const getRandomId=(id)=>{
  return window["$$ids"]?.[id]
}
export const generateId = (prefix = '', length = 6) => {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars[randomIndex];
  }
  const myid=`${prefix}${result}`
  if(getRandomId(myid)){
    return generateId(prefix,length)
  }
  setRandomId(myid)
  return myid;
};
