import React from 'react';
import classnames from 'classnames';
import styles from './index.less';

export const renderText = (value: string) => {
  if (!value) return '--';
  return value;
};

/**
 * 普通基金render
 */
export const fundRender = {
  tradeStatusList: [
    '交易',
    '发行',
    '发行成功',
    '发行失败',
    '停止交易',
    '停止申购',
    '停止赎回',
    '权益登记',
    '红利发放',
    '基金封闭',
    '基金终止',
  ],
  verifyStatusList: ['待审核', '已审核'],
  renderFundName: (value: { name: string; code: string }) => {
    if (!value) return '--';
    return (
      <p className={classnames(styles['m-complex'])}>
        <span>{value.name}</span>
        <br />
        <span>{value.code}</span>
      </p>
    );
  },
  renderCompanyAndManager: (value: { admin: string; manager: string[] }) => {
    if (!value) return '--';
    return (
      <p className={classnames(styles['m-complex'])}>
        <span>{value.admin}</span>
        <br />
        <span>{value.manager.join(',')}</span>
      </p>
    );
  },
  renderTradeStatus: (value: number) => {
    if (!value) return '--';
    return fundRender.tradeStatusList[value];
  },
  renderSoldTime: (value: { start: string; end: string }) => {
    if (!value) return '--';
    if (value.start && value.end) {
      return (
        <span>
          {value.start.replace(/-/g, '.')}-{value.end.replace(/-/g, '.')}
        </span>
      );
    } else {
      return '--';
    }
  },
  renderFundAmount: (value: string) => {
    if (!value) return '--';
    return (Number(value) * 10000).toFixed(0);
  },
  renderVerifyStatus: (value: string) => {
    if (!value) return '--';
    return fundRender.verifyStatusList[Number(value)];
  },
  renderRates: (type: 'rg' | 'sg' | 'sh', value: any[]) => {
    if (!value) return '--';
    if (type === 'sh') {
      return (
        <div>
          {value.map((item: { rate: string; time: string }, index: number) => {
            return (
              <p key={index} className={classnames(styles['m-complex'])}>
                <span>{item.time}</span> <span>{item.rate}</span>
              </p>
            );
          })}
        </div>
      );
    } else {
      return (
        <div>
          {value.map((item: { rate: string; money: string }, index: number) => {
            return (
              <p key={index} className={classnames(styles['m-complex'])}>
                <span>{item.money}</span> <span>{item.rate}</span>
              </p>
            );
          })}
        </div>
      );
    }
  },
  renderDiscount: (value: { wallet: string; card: string }) => {
    if (!value) return '--/--';
    return (
      <p className={classnames(styles['m-complex'])}>
        <span>{value.wallet ? (Number(value.wallet) * 100).toFixed(0) : '--'} </span>/
        <span> {value.card ? (Number(value.card) * 100).toFixed(0) : '--'}</span>
      </p>
    );
  },
};

/**
 * 高端理财render
 */
export const gdlcRender = {
  tradeStatusList: ['预约', '在售', '售罄', '开售提醒', '即将开售', '封闭期'],
  riskLevels: ['低风险', '中低风险', '中风险', '中高风险', '高风险'],
  renderRatio: (value: string) => {
    if (!value) {
      return '--';
    }
    return value.replace(/%/g, '');
  },
  renderOpenPeriod: (value: { start: any; end: any }) => {
    if (!value || (!value.start && !value.end)) {
      return '--';
    }
    let start = value.start
      ? value.start.substr(0, 4) + '.' + value.start.substr(4, 2) + '.' + value.start.substr(6)
      : 'xx';
    let end = value.end
      ? value.end.substr(0, 4) + '.' + value.end.substr(4, 2) + '.' + value.end.substr(6)
      : 'xx';
    return (
      <span>
        {start} - {end}
      </span>
    );
  },
  renderRiskLevel: (value: string) => {
    if (!value) {
      return '--';
    }
    return gdlcRender.riskLevels[Number(value) - 1];
  },
};

/**
 * 券商理财render
 */
const renderPeriod = (value: { start: any; end: any }) => {
  if (!value || (!value.start && !value.end)) return '--';
  let start = value.start ? value.start.replace(/-/g, '') : '';
  let end = value.end ? value.end.replace(/-/g, '') : '';
  start = start ? start.substr(0, 4) + '.' + start.substr(4, 2) + '.' + start.substr(6) : '--';
  end = end ? end.substr(0, 4) + '.' + end.substr(4, 2) + '.' + end.substr(6) : '';
  return (
    <span>
      {start ? start : '无'} - {end ? end : '无'}
    </span>
  );
};
export const gsdataRender = {
  tradeStatusList: ['火爆预售中', '火爆发售中', '已售罄'],
  redeemType: ['自动续约', '自动赎回'],
  renderOpenPeriod: renderPeriod,
  renderRedeemPeriod: renderPeriod,
  renderLimitation: (value: string) => {
    if (!value) return '--';
    return (Number(value) / 10000).toFixed();
  },
  renderIsqs: (value: string) => {
    if (!value) return '--';
    return gsdataRender.redeemType[Number(value)];
  },
  renderrisklevel: gdlcRender.renderRiskLevel,
  renderTime: (value: string) => {
    if (!value) return '--';
    return value.replace(/-/g, '.');
  },
};
