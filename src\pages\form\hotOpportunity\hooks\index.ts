import { useState, useRef, useEffect } from 'react';

const getArr = (num: number) => {
  let arr = [];
  for (let i = 0; i < num; i++) {
    arr[i] = i;
  }
  return arr;
};

export const useFieldArray = ({ arrNum = 1 } = {}) => {
  const arrRef = useRef<{ maxIndex: number }>({ maxIndex: arrNum - 1 });
  const [arr, setArr] = useState(getArr(arrNum));

  useEffect(() => {
    arrRef.current.maxIndex = arrNum - 1;
    setArr(getArr(arrNum));
  }, [arrNum]);

  const onAdd = () => {
    arrRef.current.maxIndex++;
    setArr([...arr, arrRef.current.maxIndex]);
  };

  const onRemove = (item: typeof arrRef.current.maxIndex) => {
    // 改进删除逻辑：过滤掉要删除的项，然后重新创建连续索引
    const filteredArr = arr.filter(i => i !== item);
    
    // 重新生成连续的索引，避免稀疏数组导致的表单字段映射问题
    const reindexedArr = filteredArr.map((_, index) => index);
    
    // 更新最大索引
    arrRef.current.maxIndex = Math.max(reindexedArr.length - 1, 0);
    
    setArr(reindexedArr);
    
    // 在控制台记录删除操作，便于调试
    console.log('useFieldArray: 删除项目', item, '更新后数组:', reindexedArr);
  };

  return {
    arr,
    onAdd,
    onRemove,
  };
};
