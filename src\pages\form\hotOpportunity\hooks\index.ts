import { useState, useRef, useEffect } from 'react';

const getArr = (num: number) => {
  let arr = [];
  for (let i = 0; i < num; i++) {
    arr[i] = i;
  }
  return arr;
};

export const useFieldArray = ({ arrNum = 1 } = {}) => {
  const arrRef = useRef<{ maxIndex: number }>({ maxIndex: arrNum - 1 });
  const [arr, setArr] = useState(getArr(arrNum));

  useEffect(() => {
    arrRef.current.maxIndex = arrNum - 1;
    setArr(getArr(arrNum));
  }, [arrNum]);

  const onAdd = () => {
    arrRef.current.maxIndex++;
    setArr([...arr, arrRef.current.maxIndex]);
  };

  const onRemove = (item: typeof arrRef.current.maxIndex) => {
    // 直接过滤掉要删除的项，保持其他项的索引不变
    const filteredArr = arr.filter(i => i !== item);
    setArr(filteredArr);

    // 在控制台记录删除操作，便于调试
    console.log('useFieldArray: 删除项目', item, '更新后数组:', filteredArr);
  };

  return {
    arr,
    onAdd,
    onRemove,
  };
};
