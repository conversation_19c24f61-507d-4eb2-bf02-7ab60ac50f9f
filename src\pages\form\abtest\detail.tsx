import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import FormRender from 'form-render/lib/antd';
import { Button, message } from 'antd';
import CheckBox from './components/checkbox';
import { InputLeft, InputRight, HtmlText, HeaderNote } from './components/input';
import FileImport from './components/fileImport';
import FORM_JSON from './form.json';
import HEADER_JSON from './header.json';
import api from 'api';
import { formatUrlParams, cheackFuntionList, ifRepeat, ifSelfFunctionListRepeat } from './util';
import styles from './detail.less';

const { postAbTest } = api;

export default function() {
  const history = useHistory();
  // 表单数据与非自定义组件校验
  const [headerData, setHeaderData] = useState({});
  const [headerValid, setHeaderValid] = useState([]);
  const [formData, setFormData] = useState<Object[]>([]);
  const [formValid, setFormValid] = useState([]);
  // 校验错误提示
  const [showValidate, setShowValidate] = useState(false);
  // 控制功能模块的数量，初始化一个
  const [renderArray, setRenderArray] = useState<number[]>([Math.random() * 1000]);
  // 编辑页初始化页面
  useEffect(() => {
    let getUrl: any = formatUrlParams();
    // 根据是否有传参判断是编辑页面还是新增页面
    if (getUrl) {
      postAbTest({
        type: 'query',
        id: getUrl.id,
      })
        .then((res: any) => {
          if (res.status_code === 0) {
            let tRenderArray = [];
            res.data.startDateTime = res.data.startDate.substring(11);
            res.data.endDateTime = res.data.endDate.substring(11);
            res.data.startDate = res.data.startDate.substring(0, 11);
            res.data.endDate = res.data.endDate.substring(0, 11);

            if (res.data.functionList.length >= 2) {
              for (let i = 1; i <= res.data.functionList.length; i++) {
                tRenderArray.push(Math.random() * 1000);
              }
            } else {
              tRenderArray.push(Math.random() * 1000);
            }
            setRenderArray(tRenderArray);
            setFormData(res.data.functionList);
            setHeaderData({ ...res.data });
          } else {
            message.error('服务器错误');
          }
        })
        .catch((error: any) => {
          console.warn(error);
        });
    }
  }, []);
  // 删除功能模块
  function deleteFunction(index: number) {
    const renderT = renderArray;
    const formDataT = formData;

    renderT.splice(index, 1);
    formDataT.splice(index, 1);
    setRenderArray([...renderT]);
    setFormData(formDataT);
    /*
      公用必填项和非自定义组件的校验，
      如果被删除的模块有校验失败表单项，
      此时不请空重置就会使那个校验失败的数据项永久存在，
      导致保存/发布无法成功还没有任何提示
    */
    setFormValid([]);
    setShowValidate(false);
  }
  // 发布按钮
  function onSubmit() {
    let temporaryFormData: any = formData;
    let temporaryHeaderData: any = headerData;
    // 任务重复判断，默认不重复
    let verificationResults = true;
    // 功能模块重复判断，默认不重复
    let _verificationResults = true;
    // 提取url参数
    const getUrl: any = formatUrlParams();
    // temporyValid-数组形式-元素意义为每个功能模块是否校验成功的布尔值
    const temporyValid = cheackFuntionList(temporaryFormData);
    // 对表头时间进行合并处理 "2021-07-18" "00.00.00" => "2021-07-18 00.00.00"
    if (temporaryHeaderData.startDate) {
      if (!temporaryHeaderData.startDateTime) {
        temporaryHeaderData.startDate = temporaryHeaderData.startDate + ' ' + '00:00:00';
      } else {
        temporaryHeaderData.startDate =
          temporaryHeaderData.startDate + ' ' + temporaryHeaderData.startDateTime;
      }
      delete temporaryHeaderData.startDateTime;
    }
    if (temporaryHeaderData.endDate) {
      if (!temporaryHeaderData.endDateTime) {
        temporaryHeaderData.endDate = temporaryHeaderData.endDate + ' ' + '00:00:00';
      } else {
        temporaryHeaderData.endDate =
          temporaryHeaderData.endDate + ' ' + temporaryHeaderData.endDateTime;
      }
      delete temporaryHeaderData.endDateTime;
    }
    if (temporaryHeaderData.endDate) {
      if (temporaryHeaderData.startDate > temporaryHeaderData.endDate) {
        message.error('开始时间不可以大于结束时间');
        return false;
      }
    }
    /*
     *必填项和非自定义组件的校验失败string[]
     *form-render自定义组件只有校验失败的样式提示，但并没有上传到formValid
     */
    setShowValidate(true);

    if (headerValid.length > 0 || formValid.length > 0) {
      return false;
    }
    // 对功能id和任务号进行空格过滤
    temporaryHeaderData.taskNumber = temporaryHeaderData.taskNumber.replace(/ /g, '');
    for (let item of temporaryFormData) {
      item.functionId = item.functionId.replace(/ /g, '');
    }
    // 根据分发模式决定上传内容
    for (let item of temporaryFormData) {
      if (item.distributionMode === '1') {
        item.userIdList = '';
      }
      if (item.distributionMode === '2') {
        item.contentRules = '';
      }
    }
    // 当其中某一个功能模块校验失败，就跳出函数
    for (let i = 0; i < temporyValid.length; i++) {
      if (temporyValid[i] === false) {
        return false;
      }
    }
    // 当系统选择为安卓时应用市场变为必选项
    for (let item of temporaryFormData) {
      let flag = true;
      if (item.systemSelection === 'android') {
        item.applicationMarket.length > 0 ? (flag = true) : (flag = false);
      }
      if (!flag) {
        message.error('系统选择为andriod时应用市场为必填项');
        return flag;
      }
    }
    ifRepeat(temporaryHeaderData, temporaryFormData)
      .then(val => {
        verificationResults = val;
      })
      .then(() => {
        ifSelfFunctionListRepeat(temporaryFormData).then(val => {
          _verificationResults = val;
        });
      })
      .then(() => {
        if (verificationResults && _verificationResults) {
          if (getUrl) {
            postAbTest({
              type: 'update',
              jsonDto: JSON.stringify({
                ...temporaryHeaderData,
                status: 0,
                functionList: temporaryFormData,
              }),
            })
              .then((res: any) => {
                if (res.status_code === 0) {
                  postAbTest({
                    type: 'change',
                    id: getUrl.id,
                  })
                    .then((res: any) => {
                      if (res.status_code === 0) {
                        message.success('发布成功');
                        history.go(-1);
                      } else {
                        message.error('服务器错误');
                      }
                    })
                    .catch((error: any) => {
                      console.warn(error);
                    });
                } else {
                  message.error('服务器错误');
                }
              })
              .catch((error: any) => {
                console.warn(error);
              });
          } else {
            postAbTest({
              type: 'add',
              jsonDto: JSON.stringify({
                ...temporaryHeaderData,
                status: 0,
                functionList: temporaryFormData,
              }),
            })
              .then((res: any) => {
                if (res.status_code === 0) {
                  postAbTest({
                    type: 'change',
                    id: res.data.id,
                  })
                    .then((res: any) => {
                      if (res.status_code === 0) {
                        message.success('发布成功');
                        history.go(-1);
                      } else {
                        message.error('服务器错误');
                      }
                    })
                    .catch((error: any) => {
                      console.warn(error);
                    });
                }
              })
              .catch((error: any) => {
                console.warn(error);
              });
          }
        } else {
          message.error('任务重复');
        }
      });
  }
  // 保存表单信息
  function onSave() {
    let temporaryFormData: any = formData;
    let temporaryHeaderData: any = headerData;
    // 任务重复判断，默认不重复
    let verificationResults = true;
    // 功能模块重复判断，默认不重复
    let _verificationResults = true;
    // 提取url参数
    const getUrl: any = formatUrlParams();
    // temporyValid-数组形式-元素意义为每个功能模块是否校验成功的布尔值
    const temporyValid = cheackFuntionList(temporaryFormData);
    // 对表头时间进行合并处理 "2021-07-18" "00.00.00" => "2021-07-18 00.00.00"
    if (temporaryHeaderData.startDate) {
      if (!temporaryHeaderData.startDateTime) {
        temporaryHeaderData.startDate = temporaryHeaderData.startDate + ' ' + '00:00:00';
      } else {
        temporaryHeaderData.startDate =
          temporaryHeaderData.startDate + ' ' + temporaryHeaderData.startDateTime;
      }
      delete temporaryHeaderData.startDateTime;
    }
    if (temporaryHeaderData.endDate) {
      if (!temporaryHeaderData.endDateTime) {
        temporaryHeaderData.endDate = temporaryHeaderData.endDate + ' ' + '00:00:00';
      } else {
        temporaryHeaderData.endDate =
          temporaryHeaderData.endDate + ' ' + temporaryHeaderData.endDateTime;
      }
      delete temporaryHeaderData.endDateTime;
    }
    if (temporaryHeaderData.endDate) {
      if (temporaryHeaderData.startDate > temporaryHeaderData.endDate) {
        message.error('开始时间不可以大于结束时间');
        return false;
      }
    }
    /*
     *必填项和非自定义组件的校验失败string[]
     *form-render自定义组件只有校验失败的样式提示，但并没有上传到formValid
     */
    setShowValidate(true);

    if (headerValid.length > 0 || formValid.length > 0) {
      return false;
    }
    // 对功能id和任务号进行空格过滤
    temporaryHeaderData.taskNumber = temporaryHeaderData.taskNumber.replace(/ /g, '');
    for (let item of temporaryFormData) {
      item.functionId = item.functionId.replace(/ /g, '');
    }
    // 根据分发模式决定上传内容
    for (let item of temporaryFormData) {
      if (item.distributionMode === '1') {
        item.userIdList = '';
      }
      if (item.distributionMode === '2') {
        item.contentRules = '';
      }
    }
    // 当其中某一个功能模块校验失败，就跳出函数
    for (let i = 0; i < temporyValid.length; i++) {
      if (temporyValid[i] === false) {
        return false;
      }
    }
    // 当系统选择为安卓时应用市场变为必选项
    for (let item of temporaryFormData) {
      let flag = true;
      if (item.systemSelection === 'android') {
        item.applicationMarket.length > 0 ? (flag = true) : (flag = false);
      }
      if (!flag) {
        message.error('系统选择为andriod时应用市场为必填项');
        return flag;
      }
    }
    ifRepeat(temporaryHeaderData, temporaryFormData)
      .then(val => {
        verificationResults = val;
      })
      .then(() => {
        ifSelfFunctionListRepeat(temporaryFormData).then(val => {
          _verificationResults = val;
        });
      })
      .then(() => {
        if (verificationResults && _verificationResults) {
          if (getUrl) {
            postAbTest({
              type: 'update',
              jsonDto: JSON.stringify({
                ...temporaryHeaderData,
                status: 0,
                functionList: temporaryFormData,
              }),
            })
              .then((res: any) => {
                if (res.status_code === 0) {
                  message.success('保存成功');
                  history.go(-1);
                } else {
                  message.error('服务器错误');
                }
              })
              .then((error: any) => {
                console.warn(error);
              });
          } else {
            postAbTest({
              type: 'add',
              jsonDto: JSON.stringify({
                ...temporaryHeaderData,
                status: 0,
                functionList: temporaryFormData,
              }),
            })
              .then((res: any) => {
                if (res.status_code === 0) {
                  message.success('保存成功');
                  history.go(-1);
                } else {
                  message.error('服务器错误');
                }
              })
              .catch((error: any) => {
                console.warn(error);
              });
          }
        } else {
          message.error('任务重复');
        }
      });
  }
  return (
    <div>
      <div className={styles['test-header']}>
        <div className={styles['header-top']}>
          <p>AB-test 详情</p>
          <Button className={styles['header-button']} onClick={onSubmit}>
            发布
          </Button>
          <Button onClick={onSave}>保存</Button>
        </div>
        <FormRender
          propsSchema={HEADER_JSON}
          formData={headerData}
          onChange={setHeaderData}
          onValidate={setHeaderValid}
          showValidate={showValidate}
          displayType="row" // 详细配置见下
          widgets={{
            html: HtmlText,
            note: HeaderNote,
          }}
        />
      </div>
      <div>
        {renderArray.map((val, index) => {
          return (
            <div className={styles['form-content']} key={val}>
              <Button
                className={styles['delete-button']}
                onClick={() => {
                  deleteFunction(index);
                }}
              >
                X
              </Button>
              <FormRender
                propsSchema={FORM_JSON}
                formData={formData[index]}
                onChange={(e: any) => {
                  formData.splice(index, 1, e);
                  return setFormData(formData);
                }}
                onValidate={setFormValid}
                showValidate={showValidate}
                displayType="row"
                widgets={{
                  customInputLeft: InputLeft,
                  customInputRight: InputRight,
                  html: HtmlText,
                  fileImport: FileImport,
                  selectMode: CheckBox,
                }}
              />
            </div>
          );
        })}
        <Button
          onClick={() => {
            setRenderArray(renderArray.concat([Date.now()]));
            setShowValidate(false);
          }}
          className={styles['button-submit']}
        >
          添加功能
        </Button>
      </div>
    </div>
  );
}
