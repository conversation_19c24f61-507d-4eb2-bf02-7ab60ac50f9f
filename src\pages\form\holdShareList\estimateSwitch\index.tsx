import React, { useEffect, useState } from 'react';
import { Switch, message } from 'antd';
import api from 'api';
/**
 * 净值估算开关
 */
function EstimateSwitch() {
  const [switchDate, setSwitchData] = useState({
    holdEstimate: false,
    forwordEstimate: false
  })
  useEffect(() => {
    api.fetchEstimateSwitch().then((res: any) => {
      if (res.code === '0000') {
        if (res.data) {
          setSwitchData(JSON.parse(res.data));
        }
      } else {
        message.error(res.message || '服务器开小差了');
      }
    })
  }, [])

  const handleSwitchChange = (type: string, checked: boolean) => {
    const formData = {
      ...switchDate,
      [type]: checked
    }
    // 基金持仓列表展示估算净值关闭， http://localhost:8000/也要关闭
    if (type === 'holdEstimate' && !checked) {
      formData.forwordEstimate = false;
    }
    api.postEstimateSwitch({
      value: JSON.stringify(formData)
    }).then((res: any) => {
      if (res.code === '0000') {
        setSwitchData(formData);
      } else {
        message.error(res.message || '服务器开小差了');
      }
    })
  }
  return <>
    <div className="g-fs34 f-bold g-mb30">净值估算开关</div>
    <div className="g-fs20 u-pl30">
      <div className="g-mb20">
        <span className="u-pr10">基金持仓列表展示估算净值</span>
        <Switch
          checkedChildren="开"
          unCheckedChildren="关"
          checked={switchDate.holdEstimate}
          onChange={(checked) => handleSwitchChange('holdEstimate', checked)}
        />
      </div>
      <div>
        <span className="u-pr10">交易日14~15点估算净值前移功能</span>
        <Switch
          checkedChildren="开"
          unCheckedChildren="关"
          checked={switchDate.forwordEstimate}
          disabled={!switchDate.holdEstimate}
          onChange={(checked) => handleSwitchChange('forwordEstimate', checked)}
        />
      </div>
    </div>
  </>
}

export default EstimateSwitch;
