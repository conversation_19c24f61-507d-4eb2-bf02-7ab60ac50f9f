import React from 'react';
import { Form, Input, Card, Button, Radio } from 'antd';
import UploadForm from '../Upload';

const InvestSchoolForm = props => {
  const { getFieldDecorator, schoolCardNum, setSchoolCardNum } = props;
  const schoolCardList = [];
  for (let i = 0; i < schoolCardNum; i++) {
    const schoolCardItem = (
      <Card key={i} style={{ marginBottom: '15px' }}>
        <UploadForm
          getFieldDecorator={getFieldDecorator}
          fieldLocation={`otherWelfare.investmentSchool.cardList[${i}].image`}
          label="图片"
          required={true}
        />
        <Form.Item label="名称">
          {getFieldDecorator(`otherWelfare.investmentSchool.cardList[${i}].name`, {
            rules: [{ required: true, message: '请输入名称' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="简介">
          {getFieldDecorator(`otherWelfare.investmentSchool.cardList[${i}].introduction`, {
            rules: [{ required: true, message: '请输入简介' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="形式">
          {getFieldDecorator(`otherWelfare.investmentSchool.cardList[${i}].form`, {
            rules: [{ required: true, message: '请选择课程形式' }],
          })(
            <Radio.Group>
              <Radio value="0">视频</Radio>
              <Radio value="1">图文</Radio>
            </Radio.Group>,
          )}
        </Form.Item>
        <Form.Item label="课时">
          {getFieldDecorator(`otherWelfare.investmentSchool.cardList[${i}].classHour`, {
            rules: [{ required: true, message: '请输入课时' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="门槛标签">
          {getFieldDecorator(`otherWelfare.investmentSchool.cardList[${i}].label`, {
            rules: [{ required: true, message: '请输入门槛标签' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="跳转链接">
          {getFieldDecorator(`otherWelfare.investmentSchool.cardList[${i}].jumpUrl`, {
            rules: [{ required: true, message: '请输入跳转链接' }],
          })(<Input />)}
        </Form.Item>
      </Card>
    );
    schoolCardList.push(schoolCardItem);
  }
  return (
    <>
      <Form.Item label="tab名称">
        {getFieldDecorator('otherWelfare.investmentSchool.tabName', {
          rules: [{ required: true, message: '请填写tab名称' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="角标">
        {getFieldDecorator('otherWelfare.investmentSchool.mark')(<Input />)}
      </Form.Item>
      {schoolCardList}
      <Button onClick={() => setSchoolCardNum(schoolCardNum + 1)}>新增</Button>
      <Button onClick={() => setSchoolCardNum(schoolCardNum - 1)} disabled={schoolCardNum === 0}>
        删除
      </Button>
      <Form.Item label="查看更多跳转链接">
        {getFieldDecorator('otherWelfare.investmentSchool.seeMoreUrl')(<Input />)}
      </Form.Item>
    </>
  );
};

export default InvestSchoolForm;
