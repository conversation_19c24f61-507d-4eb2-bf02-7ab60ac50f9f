import React, { useState, useEffect } from 'react';
// 配置文件
import FORM_JSON from './form.json';
// 接口
import api from 'api';
// 样式
import styles from './index.less';
// 组件
import { UploadImg } from '../component/index';
import FormRender from 'form-render/lib/antd';
import { Button, message } from 'antd';
// 路由
import router from 'umi/router';
// TypeScript
import {
  allCompetitionInfo,
  PositioItem,
  PositionFormData,
  FirmOfferCompetition,
  CusRouter,
} from '../tsFile';
// 时间工具库
import moment from 'moment';
// 国际化
import 'moment/locale/zh-cn';
// webStorage库
import store from 'store';

const { postFirmOffer } = api;
type FormRenderProps = {
  name: string;
  value: string;
  onChange: (name: string, value: string) => void;
};

export default function({ location }: { location: CusRouter }) {
  const [formData, setFormData] = useState<PositionFormData<PositioItem<string>>>();
  const [formValid, setFormValid] = useState<string[]>([]);
  const [showValidate, setShowValidate] = useState(false);
  // 所有页面信息
  const [allCompetitionInfo, setAllCompetitionInfo] = useState<allCompetitionInfo>();
  useEffect(() => {
    if (location.state) {
      // 初始化持仓运营位
      if (location.state['holdOperationConfig']) {
        let holdOperationConfig = location.state['holdOperationConfig'].value;
        setFormData(holdOperationConfig);
      }
      setAllCompetitionInfo(location.state);
    }
  }, []);
  // 处理表单信息
  const handleForm = () => {
    setShowValidate(true);
    if (formValid.length === 0 && formData) {
      setShowValidate(false);
      postFirmOffer({
        value: JSON.stringify({
          ...allCompetitionInfo,
          holdOperationConfig: {
            value: {
              ...formData,
            },
            editer: store.get('name'),
            time: moment().format('YYYY-MM-DD HH:mm:ss'),
          },
        }),
      })
        .then((res: FirmOfferCompetition) => {
          if (res.code === '0000') {
            message.success('保存成功');
            router.push('list');
          } else {
            message.error(res.message);
          }
        })
        .catch((err: unknown) => {
          console.log(err);
          message.error('网络请求错误，请稍后再试');
        });
    }
  };
  const UploadImage = ({ onChange, name, value }: FormRenderProps) => {
    return (
      <UploadImg
        ifFormItem={false}
        width={686}
        height={160}
        name={name}
        onChange={onChange}
        value={value}
      />
    );
  };
  return (
    <article className={styles['m-etf-position']}>
      <FormRender
        propsSchema={FORM_JSON}
        formData={formData ? formData : {}}
        onChange={setFormData}
        onValidate={setFormValid}
        showValidate={showValidate}
        displayType="row"
        widgets={{
          UploadImg: UploadImage,
        }}
      />
      <Button
        onClick={() => router.push('list')}
        style={{ marginLeft: '300px', marginRight: '40px' }}
      >
        取消
      </Button>
      <Button type="danger" onClick={handleForm}>
        保存
      </Button>
    </article>
  );
}
