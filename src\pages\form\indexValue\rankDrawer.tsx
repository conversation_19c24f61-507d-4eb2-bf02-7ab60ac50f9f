import React, { useEffect, useState } from 'react';
import { Table, Button, Popconfirm, message, Drawer, Input, Row, Col, Radio } from 'antd';
import styles from './index.less'
import api from 'api';


const { fetchFundNameByCode, fetchPEPB } = api;
const { TextArea } = Input
interface dataProps {
    flag?: number,
    fundCode?: string,
    fundName?: string,
    name?: string,
    type?: string,
    introduce?: string,
    innerFund?: string,
    innerName?: string,
    innerText?: string,
    outterFund?: string,
    outterName?: string,
    outterText?: string,
    applyArea?: string,
    pe?: string,
    pb?: string,
}
interface iProps {
    show?: boolean,
    closeDrawer(data: boolean): void,
    data?: dataProps,
    save(data: dataProps): void,

}
export default function ({ show, closeDrawer, data, save }: iProps) {
    const [visible, setVisible] = useState(false);
    const [form, setForm] = useState<dataProps>({})

    //父节点只可进行打开操作
    useEffect(() => {
        if (show === true) setVisible(show)

    }, [show])
    //填充数据
    useEffect(() => {
        setForm(data)
    }, [data])


    const onClose = () => {
        setVisible(false)
        closeDrawer(false)
    };

    //表单修改
    const handleChange = (value: any, type: string) => {
        console.log(value, type)
        let _form: dataProps = { ...form }
        _form[type] = value
        setForm(_form)

    }

    //code栏填充后失焦时自动填充名字
    const getInfo = (code: string, name: string) => {

        try {
            if (code === 'fundCode') {
                fetchPEPB({
                    marketEvaluateID: form.fundCode
                }).then((res: any) => {
                    if (res?.code === '0000') {
                        if (res.data) {
                            let _form = { ...form }
                            _form.fundName = res.data.marketEvaluate
                            _form.pb = res.data.PB ? parseFloat(res.data.PB).toFixed(2) : '--'
                            _form.pe = res.data.PE ? parseFloat(res.data.PE).toFixed(2) : '--'

                            setForm(_form)

                        } else {
                            let _form = { ...form }
                            _form.fundName = `获取不到${form[code]}的名称`
                            _form.pb = `获取不到${form[code]}的pb`
                            _form.pe = `获取不到${form[code]}的pe`
                            setForm(_form)
                            message.error(`获取不到${form[code]}的相关信息`)
                        }
                    }
                })
            } else {
                fetchFundNameByCode({
                    fundCode: [form[code]]
                }).then((res: any) => {
                    if (res?.code === '0000') {
                        if (res.data) {
                            let _form = { ...form }
                            _form[name] = res.data.name

                            setForm(_form)

                        } else {
                            let _form = { ...form }
                            _form[name] = `获取不到${form[code]}的名称`

                            setForm(_form)
                            message.error(`获取不到${form[code]}的名称`)
                        }
                    }
                })
            }
        } catch (e) {
            message.error(e)
        }
    }
    const handleSave = () => {
        save(form)
        setVisible(false)
    }

    return <Drawer
        // title="Basic Drawer"
        placement="right"
        closable={false}
        onClose={onClose}
        visible={visible}
        width={'800px'}
    >
        <Popconfirm
            title=" 确定保存?"
            okText="确认"
            cancelText="取消"
            onConfirm={handleSave}

        >
            <Button type="primary" className={'g-mb20'}>保存</Button>
        </Popconfirm>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >指数代码</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => { handleChange(e.target.value, 'fundCode') }}
                    value={form.fundCode || ''}
                    onBlur={() => getInfo('fundCode', 'fundName')}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >指数名称</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    value={form.fundName || ''}
                    disabled={true}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >展示名称</div>
            </Col>
            <Col offset={1} span={17}>
                <TextArea
                    autoSize={{ minRows: 1, maxRows: 5 }}
                    onChange={(e) => { handleChange(e.target.value, 'name') }}
                    value={form.name || ''}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >指数类型</div>
            </Col>
            <Col offset={1} span={17}>
                <Radio.Group onChange={(e) => { handleChange(e.target.value, 'type') }} value={form.type || ''}>
                    <Radio value={'宽基'}>宽基</Radio>
                    <Radio value={'行业'}>行业</Radio>
                    <Radio value={'主题'}>主题</Radio>
                </Radio.Group>
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >指数介绍</div>
            </Col>
            <Col offset={1} span={17}>
                <TextArea
                    autoSize={{ minRows: 1, maxRows: 5 }}
                    onChange={(e) => { handleChange(e.target.value, 'introduce') }}
                    value={form.introduce || ''}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >场内基金代码</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => { handleChange(e.target.value, 'innerFund') }}
                    value={form.innerFund || ''}
                    onBlur={() => getInfo('innerFund', 'innerName')}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >场内基金名称</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    value={form.innerName || ''}
                    disabled={true}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >场内展示文案</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => { handleChange(e.target.value, 'innerText') }}
                    value={form.innerText || ''}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >场外基金代码</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => { handleChange(e.target.value, 'outterFund') }}
                    value={form.outterFund || ''}
                    onBlur={() => getInfo('outterFund', 'outterName')}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >场外基金名称</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    value={form.outterName || ''}
                    disabled={true}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >场外展示文案</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => { handleChange(e.target.value, 'outterText') }}
                    value={form.outterText || ''}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >适用指标</div>
            </Col>
            <Col offset={1} span={17}>
                <Radio.Group onChange={(e) => { handleChange(e.target.value, 'applyArea') }} value={form.applyArea || ''}>
                    <Radio value={'PB'}>PB</Radio>
                    <Radio value={'PE'}>PE</Radio>
                </Radio.Group>
            </Col>

        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >pe</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    value={form.pe || ''}
                    disabled={true}
                />
            </Col>
        </Row>
        <Row className={'g-mb20'}>
            <Col span={6} className={styles['edit-title']}>
                <div >pb</div>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    value={form.pb || ''}
                    disabled={true}
                />
            </Col>
        </Row>
    </Drawer>

}