.blue-card {
  padding: 2px 2px;
  border-radius: 6px;
  background-color: #1890ff;
  color: white;
  text-align: center;
}

.orange-card {
  text-align: center;
  padding: 2px 4px;
  border-radius: 6px;
  background-color: orange;
  color: white;
}

.m-fundConfigCheck {
  p {
    margin: 0;
  }

  .m-filters {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;

    .m-option {
      >span {
        margin-right: 10px;
        display: inline-block;
      }

      >input {
        width: 400px;
        margin-right: 30px;
      }

      :global {
        .ant-select {
          min-width: 250px;
          margin-right: 30px;
        }
      }
    }
  }

  .m-tabs {
    border-top: 1px #e8e8e8 solid;
    margin-top: 10px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: flex-end;

    .m-tab {
      width: 94px;
      height: 46px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      cursor: pointer;

      .m-underline {
        bottom: 0;
        position: absolute;
        display: none;
      }
    }

    .m-tab.m-now {
      font-size: 22px;

      .m-underline {
        width: 100%;
        height: 4px;
        display: block;
        background: #40a9ff;
      }
    }
  }

  .m-table-pagination {
    display: flex;
    align-items: center;

    >p {
      >span {
        color: #40a9ff;
      }
    }

    .m-pagination {
      margin-left: 10px;
    }

    &.m-top {
      margin-left: auto;
      margin-bottom: 6px;
    }

    &.m-bottom {
      margin-top: 10px;
    }
  }
}
.stock-summary {
  .g-stock-summary-btn {
    position: relative;
    top: -36px;
    left: 90%;
    margin-right: 10px;
  }

  .m-stock-summary-warpper {
    .m-stock-summary-title {
      font-size: 32px;
      margin: 40px 0 80px 40%;
    }

    .m-stock-summary-title-sticky {
      font-size: 18px;
      font-weight: bold;

      span {
        margin: 30px 2vw 30px 2vw;
      }

      span:nth-child(1) {
        margin: 30px 10vw 30px 5vw;
      }

      span:nth-child(4) {
        margin: 30px 5vw 30px 10vw;
      }
    }

    .m-stock-summary-title-font {
      position: absolute;
      font-size: 18px;
      font-weight: bold;
      right: 80px;
      top: 150px;
    }
  }
}

.m-filter-warpper {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: space-between;

  section {
    margin-right: 50px;
  }
}

.a-table {
  margin-top: 20px;
  margin-left: 20px;

  table {
    border-collapse: collapse;
  }

  table td,
  th {
    border: 1px solid #000000;
    text-align: center;
  }

}
