import React, { useState } from 'react';
import { Modal, Input, Switch, message, But<PERSON>, Popconfirm } from  'antd';
import api from 'api';

const {
    fetchTshName
} = api

export default React.memo(function({
    isShow,
    setIsShow,
    tsh,
    setTsh,
    modifiedData,
    setModifiedData,
    blackAllTsh
}: any) {

    const [tshId, setTshId] = useState('')
    const [isAll, setIsAll] = useState(false)

    /**
     * 添加黑名单
     */
    function addTshBlackList() {
        fetchTshName({
            data: tshId
        }).then((data: any) => {
            let addBlackThsList = [...modifiedData.addBlackThsList],
                _modifiedData = {...modifiedData}
            if (data.status_code === 0 && data.data.length === 1) {
                if (tsh.filter((item: TSH) => item.id === tshId).length) {
                    return message.error('该同顺号已经在黑名单中～')
                } else  {
                    data.data[0].isBlack = true
                    setTsh([...tsh, data.data[0]])
                    let _id = data.data[0].id
                    addBlackThsList.push(_id)
                    _modifiedData.addBlackThsList = addBlackThsList
                    if (~_modifiedData.delBlackThsList.indexOf(_id)) {
                        _modifiedData.delBlackThsList.splice(_modifiedData.delBlackThsList.indexOf(_id), 1)
                    }
                    setModifiedData(_modifiedData)
                }
                setTshId('')
                setIsShow(false)
            } else {
                message.error('没有该同顺号')
            }
        }).catch((e: any) => {
            console.log(e.message)
            message.error('没有该同顺号')
        })
    }

    function upload() {
        blackAllTsh(tshId)
    }

    return (
        <Modal 
        title="新增黑名单" 
        visible={isShow} 
        width={600}
        onCancel={() =>{setIsShow(false)}}
        footer={
            <div className="u-r-middle">
                <Button type="primary" onClick={() => {setIsShow(false)}}>取消</Button>
                {
                    isAll
                    ?
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么？该操作会对所有基金生效！请谨慎操作！'}
                        onConfirm={upload}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button type="danger" >提交</Button>
                    </Popconfirm>
                    :
                    <Button type="primary" className="g-ml20" onClick={() => {
                        addTshBlackList()
                    }}>提交</Button>
                }
            </div>
        }
        >
            <Input 
            value={tshId}
            onChange={(e) => {setTshId(e.target.value)}}
            placeholder="输入要添加入黑名单的同顺号id（只能输入一个id）"
            ></Input>
            <div className="u-l-middle g-mt30">
                <Switch 
                checked={isAll}
                onChange={setIsAll}
                className='g-mr20'
                />
                并对所有基金拉黑该同顺号
            </div>
        </Modal>
    )
})