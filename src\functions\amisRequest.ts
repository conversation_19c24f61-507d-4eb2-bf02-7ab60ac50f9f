import { apiPrefix } from 'config'
import Axios,{AxiosResponse} from 'axios'
import store from 'store'

interface HeaderState {
    ['Content-Type']: string;
    user?: string;
    Authentication?: string;
}

// 当前正在请求的ajaxMap，防止重复请求。
let ajaxMap:any = {};
// 默认请求头
let defaultRequestHeader: HeaderState = {
    'Content-Type': 'application/x-www-form-urlencoded',
};
// 用户token
let old_token = store.get('user_token');
old_token && (defaultRequestHeader['Authentication'] = old_token);
// 用户name
let userName = store.get('name');
userName && (defaultRequestHeader['user'] = encodeURIComponent(userName));

// axios实例
let amisAxios = Axios.create({
    timeout: 15000
});

interface resultInterface {
    data?: object;
    status_code?: number;
    status_msg?: string;
}

/**
 * @function handleSuccess
 * @description request sucess callback
 */
function handleSuccess (res: AxiosResponse, url: any):Promise<any> {
    const { statusText, status, data } = res;
    if (data instanceof Blob) { // 文件下载
        let reader = new FileReader();
        reader.readAsDataURL(data);
        reader.onload = function(e: any) {
            try {
                let a: any = document.createElement('a');
                let fileName: string = res.headers["content-disposition"].split("=");
                fileName = decodeURI(fileName[fileName.length - 1]);
                fileName = fileName.replace(/"/g, "");
                a.download = fileName;
                a.href = e.target.result;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            } catch(e:any) {
                console.log(e.message)
            }
        }
        delete ajaxMap[url];
        return Promise.resolve({
            success: true,
            message: statusText,
            statusCode: status,
            data: {
                data,
                status: 0,
                msg: 'Success',
            }
        })
    } else { // 普通get请求
        let result: resultInterface = {};
        typeof data === 'object' ? result = data : result.data = data;
        delete ajaxMap[url];
        return Promise.resolve({
            success: true,
            message: statusText,
            statusCode: status,
            data: {
                ...result,
                status: result.status_code,
                msg: result.status_msg,
            },
        })
    }
}

/**
 * @function handleError
 * @description request fail callback
 */
function handleError (e: any, url: any):Promise<any> {
    const { response, message } = e;
    let msg, statusCode;
    delete ajaxMap[url];
    if (response && response instanceof Object) {
        const { data,  statusText } = response;
        statusCode = response.status;
        msg = data.message || statusText;
    } else {
        statusCode = 600;
        msg = message || 'Network Error';
    }
    return Promise.reject({
        success: false,
        statusCode,
        message: msg,
        data: {
            status: -1,
            msg: '请求异常'
        }
    })
}

/**
 *处理重复请求
 */
function handleRepeatRequest ():Promise<any> {
    return Promise.reject({
        success: false,
        statusCode: -1,
        message: "请求频繁",
        data: {
            status: -1,
            msg: '请求频繁'
        }
    })
}


export function amisGet (url: string, data?: any, config?:any) {
    if (ajaxMap[url]) return handleRepeatRequest();
    ajaxMap[url] = 1;
    let newStore = store.get('user_token')
    if (newStore && newStore !== old_token) {
        defaultRequestHeader['Authentication'] = newStore;
        old_token = newStore;
    }
    return amisAxios.get(url, {
        headers: {
            ...defaultRequestHeader
        },
        responseType: config.responseType
    }).then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}

export function amisPost (url: string, data?: any, config?:any) {
    if (ajaxMap[url]) return handleRepeatRequest();
    ajaxMap[url] = 1;
    let requestHeader:any;
    config && (requestHeader = config.headers || {});
    return amisAxios.post(url, data, {
        headers: {
            ...defaultRequestHeader,
            ...requestHeader
        }
    }).then(res => handleSuccess(res, url)).catch(e => handleError(e, url))
}

export function amisHttpRequest (url:string, method: string, data: any, config: any) {
    if (url.indexOf('http') >= 0 || url.indexOf('https') >= 0) {
        url = url;
    } else {
        url = (~location.host.indexOf('localhost') ? '/api' : apiPrefix) + url;
    }
    const AJAX = {
        get: amisGet,
        post: amisPost,
    }
    return AJAX[method || 'get'](url, data, config).catch((e: any) => {
        if (e.statusCode === 401) location.hash = '#/login';
        return Promise.reject(e);
    });
}