{"type": "object", "properties": {"code": {"title": "板块代码", "type": "string", "props": {}, "required": true}, "interpretText": {"title": "解读文案", "type": "string", "format": "textarea", "props": {}}, "interpretUrl": {"title": "解读文案跳转", "type": "string", "props": {}}, "liveText": {"title": "直播文案", "type": "string", "format": "textarea", "props": {}}, "liveUrl": {"title": "直播文案跳转", "type": "string", "props": {}}}, "labelWidth": 120, "displayType": "row"}