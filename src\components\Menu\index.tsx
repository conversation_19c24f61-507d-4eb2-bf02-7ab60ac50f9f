import React, { PureComponent } from 'react'
import { Menu, Icon } from 'antd'
import Navlink from 'umi/navlink'
import withRouter from 'umi/withRouter'
import store from 'store'
const { SubMenu } = Menu


class SiderMenu extends PureComponent<any, any> {
    constructor (props: any) {
        super(props);
        this.state = {
            openKeys: store.get('openKeys') || [],
        };  

        this.generateMenus = this.generateMenus.bind(this);
        this.onOpenChange = this.onOpenChange.bind(this);
    }

    onOpenChange (openKeys: any) {
      const { routeList: menus } = this.props
      const rootSubmenuKeys = menus.filter((_: any) => !_.menuParentId).map((_: any) => _.id)
  
      const latestOpenKey = openKeys.find(
        (key: any) => this.state.openKeys.indexOf(key) === -1
      )
  
      let newOpenKeys = openKeys
      if (rootSubmenuKeys.indexOf(latestOpenKey) !== -1) {
        newOpenKeys = latestOpenKey ? [latestOpenKey] : []
      }
  
      this.setState({
        openKeys: newOpenKeys,
      })
      store.set('openKeys', newOpenKeys)
    }


    generateMenus (data: any) {
        return data.map((item: any) => {
          if (item.children) {
            return (
              <SubMenu
                key={item.path}
                title={<>{item.icon && <Icon type={item.icon} />}<span>{item.name}</span></>}
              >
                {this.generateMenus(item.children)}
              </SubMenu>
            )
          }
          return (
            <Menu.Item key={item.path}>
              <Navlink to={item.path || '#'}>
                {item.icon && <Icon type={item.icon} />}
                <span>{item.name}</span>
              </Navlink>
            </Menu.Item>
          )
        })
    }
    
    render() {
        const {
          collapsed,
          theme,
          //menus,
          routeList,
          location,
          isMobile,
        } = this.props;

        const menuProps = collapsed
          ? {}
          : { openKeys: this.state.openKeys, }
    
        return (
          <Menu
            mode="inline"
            theme={theme}
            onOpenChange={this.onOpenChange}
            {...menuProps}
          >
            {this.generateMenus(routeList)}
          </Menu>
        )
    }
}

export default withRouter(SiderMenu)
