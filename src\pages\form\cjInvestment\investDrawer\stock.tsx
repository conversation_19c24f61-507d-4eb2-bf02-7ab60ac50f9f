import {
    Form,
    Input,
    Button,
    message,
    Popconfirm
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import api from 'api';
import StockModal from '../stockModal/stockModal';
import { dateFormat } from '@/utils/utils';

const { modifyFundPosition, getTradeDay } = api;
const { TextArea } = Input;

interface StockFormProps extends FormComponentProps {
    groupId: any
    queryAllData: any
    handleRemainShares: (val: any) => void
}

class stockForm extends React.Component<StockFormProps, any> {
    constructor(props: StockFormProps) {
        super(props);
        this.state = {
            stockFormData: [],
            visible: false,
            stockInfo: [], 
            stocks: [],
            showDescription: false,
            isEdit: false
        }
    }

    formItemLayout = {
        labelCol: {
            span: 5
        },
        wrapperCol: {
            span: 19
        },
    };
    handleSubmit = (e: any) => { 
        e.preventDefault();
        this.props.form.validateFields((err, values) => {
            if (!err) {
                let { fundDetail, adjustDescription } = values;
                const { groupId } = this.props;
                const { stocks } = this.state;
                
                let len = fundDetail.length;
                if (fundDetail.charAt(len - 1) === ';') {
                    fundDetail = fundDetail.slice(0, len - 1);
                }
                let arr = fundDetail.split(';'), stockList: any = [];
                let show = true, num = 0;
                if (stocks.length === arr.length) {
                    for (let i = 0; i < stocks.length; i++) {
                        let item = stocks[i];
                        if (fundDetail.indexOf(item) === -1) {
                            break;
                        } else {
                            num++;
                        }
                    }
                    show = num !== stocks.length;
                } else {
                    show = true;
                }
                if (show === false) {
                    message.error('无效操作：成分基金未发生变更');
                    return;
                }

                arr.forEach((item: any) => {
                    let arr1 = item.split(',');
                    stockList.push({
                        "code": arr1[0],
                        "share": arr1[1]
                    })
                })
                let jsonDto = {
                    groupId,
                    detail: adjustDescription,
                    stockList 
                }
                modifyFundPosition({ type: 'add', jsonDto: JSON.stringify(jsonDto)}).then((res: any) => {
                    const { status_code, status_msg, data } = res;
                    if (status_code === 0) { 
                        if (data?.stockList?.length > 0) {
                            let arr: string[] = [];
                            data.stockList?.forEach((item: any) => {
                                const { code, share } = item;
                                let str = `${code},${share}`;
                                arr.push(str);
                            })
                            this.setState({
                                stocks: [...arr]
                            })
                        }
                        this.props.handleRemainShares(data.remainShares)
                        const { setFieldsValue } = this.props.form;
                        setFieldsValue({
                            fundDetail: '',
                            adjustDescription: ''
                        })
                        message.success('产品信息保存成功');
                    } else {
                        message.error(status_msg ?? '产品信息保存失败，请重试');
                    }
                    
                }).catch((e: any) => {
                    message.error('产品信息保存失败，请重试');
                })
            }
        });
    };
    handleModal = (flag= false) => {
        this.setState({
            visible: flag
        })
    }
    getStockList = (type = true) => {
        const { groupId } = this.props;
        modifyFundPosition({ type: 'queryAll', groupId}).then((res: any) => {
            const { status_code, status_msg, data } = res;
            if (status_code === 0) { 
              if (type === false) {
                if (data?.length > 0) {
                    let arr: string[] = [];
                    let obj = data[0];
                    obj?.stockList?.forEach((item: any) => {
                        const { code, share } = item;
                        let str = `${code},${share}`;
                        arr.push(str);
                    })
                    this.setState({
                        stocks: [...arr]
                    })
                } else {
                    this.setState({
                        showDescription: true
                    })
                }
              } else {
                this.handleModal(true);
                let stockInfo:any = [];
                data.forEach((item: any) => {
                    const { confDate, detail, stockList } = item;
                    let obj = {
                        confDate,
                        detail,
                        stockList: ''
                    }
                    let list = ''
                    stockList.forEach((item1: any) => {
                        list += `${item1.code},${item1.share};`
                    })
                    obj.stockList = list;
                    stockInfo.push(obj);
                })
                this.setState({
                    stockInfo
                })
              }
              
            } else {
              message.error(status_msg || '查询调仓记录失败');
            }
        }).catch((e: any) => {
            message.error(e?.message || '查询调仓记录失败');
        })
    }
    handleChange = (e: any) => {
        console.log(e.target.value)
        if (e?.target?.value) {
            const { stocks } = this.state;
            let value = e.target.value;
            let len = value.length;
            if (value.charAt(len - 1) === ';') {
                value = value.slice(0, len - 1);
            }
            let arr = value.split(';');
            let show = true, num = 0;
            if (stocks.length === arr.length) {
                for (let i = 0; i < stocks.length; i++) {
                    let item = stocks[i];
                    if (value.indexOf(item) === -1) {
                        break;
                    } else {
                        num++;
                    }
                }
                show = num !== stocks.length;
            } else {
                show = true;
            }
            this.setState({
                showDescription: show
            })
        }
    }
    handleTradeDay = () => {
        getTradeDay().then((res: any) => {
            const { status_code, status_msg, data } = res;
            if (status_code === 0) { 
                let nowTime = Date.now();
                let str = dateFormat(nowTime);
                if (str === data) {
                    str = `${str.slice(0,4)}/${str.slice(4,6)}/${str.slice(6,8)}`
                    let baseTime = new Date(str)?.getTime()
                    let startTime = baseTime + 13 * 3600 * 1000;
                    let endTime = baseTime + 15 * 3600 * 1000;
                    if (nowTime >= startTime && nowTime <= endTime) {
                        this.setState({
                            isEdit: true
                        })
                    }
                }
            } else {
                message.error(status_msg ?? '网络错误，请重试');
            }
        }).catch((e: any) => {
            message.error('网络错误，请重试')
        })
    }
    componentDidMount() {
        this.getStockList(false);
        this.handleTradeDay();
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const { visible, stockInfo, showDescription, isEdit } = this.state;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="成分基金及份数">
                        {getFieldDecorator('fundDetail', {
                            rules: [{ required: true, message: '请输入成分基金及份数' }],
                        })(
                            <Input onChange={this.handleChange} disabled={!isEdit}/>
                        )}
                        <span>填写示例：基金代码1,份数;基金代码2,份数;...(注：交易日15：00前，基金和份数调整完成并保存后立即生效，调仓记录将在下方进行展示。上述填写内容需要使用英文逗号和英文分号)</span>
                    </Form.Item>
                    <Form.Item label="调仓说明">
                        {getFieldDecorator('adjustDescription', {
                            rules: [{ required: showDescription, message: '请输入调仓说明' }],
                        })(
                            <TextArea disabled={!isEdit}/>
                        )}
                        <span className="u-block">发生调仓时，此项必填</span>
                        <span style={{color: '#1890ff', cursor: 'pointer'}} onClick={() => this.getStockList(true)}>调仓记录</span>
                    </Form.Item>
                    <Form.Item style={{textAlign: 'right'}} wrapperCol={{span: 24}}>
                        <Popconfirm placement="left" title="确定要保存吗?" disabled={!isEdit} onConfirm={this.handleSubmit}>
                            <Button type="primary" disabled={!isEdit}>
                                保存
                            </Button>
                        </Popconfirm>
                        
                    </Form.Item>
                </Form>
                <StockModal title="调仓记录" visible={visible} handleModal={this.handleModal} stockInfo={stockInfo}/>
            </>
        )
    }
}
const WrappedStockForm = Form.create<StockFormProps>({ name: 'stock' })(stockForm);
export default WrappedStockForm