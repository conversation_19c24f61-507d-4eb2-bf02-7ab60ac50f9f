import React, { SetStateAction, useEffect, useState } from 'react';
import { Form, Button, Table, Popconfirm, message, Modal, Input, Divider } from 'antd';
import { ColumnProps } from 'antd/lib/table';

import { getNewObjectArray, checkChinese } from './utils';
import api from 'api';
import styles from './index.less'
import { ISectorData } from './types'

const tableKey = 'button_title_config';
const { postHash, postHashDel, fetchHashAll } = api;

const sectorIndexTable = (props) => {
  const { getFieldDecorator, setFields, resetFields, validateFields } = props.form

  const [sectorData, setSectorData] = useState<ISectorData[]>([]);
  const [showForm, setShowForm] = useState(false);
  // 是编辑状态还是添加状态
  const [isEditing, setIsEditing] = useState(false)

  // 发请求初始化sectorData
  useEffect(() => {
    fetchHashAll({ key: tableKey }).then(res => {
      if (res?.code !== '0000') {
        throw new Error(res?.message)
      }
      const initSectorData: ISectorData[] = []
      const data = res.data
      for (let key in data) {
        if (data.hasOwnProperty(key)) {
          data[key] = JSON.parse(data[key])
          data[key].key = key
          initSectorData.push(data[key])
        }
      }
      setSectorData(initSectorData)
    }).catch(err => {
      message.error(err.message)
    })
  }, []);

  const handleAddSector = () => {
    resetFields()
    setIsEditing(false)
    setShowForm(true);
  };
  const handleConfirmDelete = (e: React.MouseEvent<HTMLElement, MouseEvent>, param: ISectorData) => {
    e.preventDefault()
    // 给后端发送删除请求
    postHashDel({ key: tableKey, propName: param.sectorCode }).then(res => {
      if (res.code !== '0000') {
        throw new Error(res.message)
      }
      setSectorData(sectorData.filter(val => val.sectorCode !== param.sectorCode))
      message.success('删除成功')
    }).catch(err => {
      message.error(err.message)
    })
  };
  const handleEdit = (e: React.MouseEvent<HTMLAnchorElement, MouseEvent>, param: ISectorData) => {
    e.preventDefault()
    setIsEditing(true)
    const index = sectorData.findIndex(val => val.sectorCode === param.sectorCode)
    if (index !== -1) {
      setFields({
        sectorCode: {
          value: param.sectorCode
        },
        buttonName: {
          value: param.buttonName
        },
        relationFundCode: {
          value: param.relationFundCode
        },
        jumpUrl: {
          value: param.jumpUrl
        }
      })
      setShowForm(true)
    } else {
      message.error('未知错误')
    }
  }
  const handleCancelEdit = () => {
    setShowForm(false);
  };
  const handleSubmit = (e) => {
    e.preventDefault()
    validateFields((err, values: Omit<ISectorData, 'key'>) => {
      if (!err) {
        postHash({ key: tableKey, propName: values.sectorCode, value: JSON.stringify(values) })
          .then(res => {
            if (res.code !== '0000') {
              throw new Error(res?.message)
            }
            const newSectorData = getNewObjectArray(sectorData, values.sectorCode, { ...values, key: values.sectorCode })
            setSectorData(newSectorData as SetStateAction<ISectorData[]>)
            setShowForm(false)
            message.success('添加或修改成功')
          })
          .catch(err => {
            message.error(err.message)
          })
      }
    })
  };

  const columns: ColumnProps<ISectorData>[] = [
    {
      title: '板块代码',
      dataIndex: 'sectorCode',
      key: 'sectorCode',
    },
    {
      title: '按钮标题',
      dataIndex: 'buttonName',
      key: 'buttonName',
    },
    {
      title: '基金代码',
      key: 'relationFundCode',
      dataIndex: 'relationFundCode',
    },
    {
      title: '跳转链接',
      key: 'jumpUrl',
      dataIndex: 'jumpUrl',
    },
    {
      title: '操作',
      key: 'action',
      render: (param: ISectorData) => (
        <span>
          <a onClick={(e) => handleEdit(e, param)}>编辑</a>
          <Divider type="vertical" />
          <Popconfirm
            title="确定要删除这行数据吗?"
            onConfirm={e => handleConfirmDelete(e, param)}
            okText="是"
            cancelText="否"
          >
            <a href="#">删除</a>
          </Popconfirm>
        </span>
      ),
    },
  ];

  return (
    <>
      <div className={styles['add-btn-box']}>
        <Button type="primary" onClick={handleAddSector}>
          新增板块指数
        </Button>
      </div>
      <Table columns={columns} dataSource={sectorData} bordered pagination={false} />
      <Modal visible={showForm} title={'请输入相应代码或名称'} onCancel={handleCancelEdit} footer={null}>
        <Form onSubmit={handleSubmit}>
          <Form.Item>
            <span>板块代码</span>
            {
              getFieldDecorator('sectorCode', {
                rules: [{ required: true, message: '板块代码不能为空' }],
                getValueFromEvent: (e) => checkChinese(e.target.value)
              })(<Input disabled={isEditing} placeholder='板块代码需配置后缀，如.TI' />)
            }
          </Form.Item>
          <Form.Item>
            <span>按钮标题</span>
            {
              getFieldDecorator('buttonName')(
                <Input maxLength={5} placeholder='按钮标题不超过5个汉字' />
              )
            }
          </Form.Item>
          <Form.Item>
            <span>基金代码</span>
            <div style={{ fontSize: '10px', color: 'red', lineHeight: '14px' }}>需配置六位数字的场外基金</div>
            {
              getFieldDecorator('relationFundCode')(
                <Input type='number' onChange={e => {
                  if (e.target.value.length > 6) {
                    e.target.value = e.target.value.slice(0, 6)
                  }
                }} />
              )
            }
          </Form.Item>
          <Form.Item>
            <span>跳转链接</span>
            {
              getFieldDecorator('jumpUrl')(
                <Input onChange={(e) => e.target.value = e.target.value.trim()} />
              )
            }
          </Form.Item>
          <Form.Item>
            <div>
              <Button type="primary" htmlType="submit" className={styles['submit-btn']}>
                确定
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

const WrappedNormalLoginForm = Form.create({ name: 'sectorIndexTable' })(sectorIndexTable)

export default WrappedNormalLoginForm;
