import { message } from 'antd';
import api from 'api';
const { postAbTest } = api;

interface FunctionList {
  androidAppLeft: string
  androidAppRight: string
  androidSystemLeft: string
  androidSystemRight: string
  applicationMarket: string[]
  applicationType: number[]
  contentRules: string
  userIdList: string
  distributionMode: string
  functionId: string
  functionName: string
  iosAppLeft: string
  iosAppRight: string
  iosSystemLeft: string
  iosSystemRight: string
  notes1: string
  notes2: string
  notes4: string
  notes5: string
  notes6: string
  notes7: string
  notes8: string
  notes9: string
  selectApplication: any
  mode: string
  systemSelection: string
  userIdType: string
}
/**
 * @function formatUrlParams
 * @description 获取页面url中的参数
 * @return {Object}
 */
export function formatUrlParams() {
  let url = location.href;
  let obj: any = {};
  if (url.indexOf('?') !== -1) {
    let startIndex = url.indexOf('?') + 1;
    let str = url.substring(startIndex);
    let strs = str.split('&');
    for (let i = 0; i < strs.length; i++) {
      obj[strs[i].split('=')[0]] = strs[i].split('=')[1];
    }
    return obj;
  }
}
/**
 * @function checkVersion
 * @description 校验实参是否为xx.xx.xx格式
 * @return [boolean] false不符合匹配规则 true符合匹配规则
 */
function checkVersion(str: string) {
  if (str) {
    let regResult: any = str.match(/^[0-9]+\.{1}[0-9]+\.{1}[0-9]+$/g)
    if (regResult === null) {
      return false
    } else {
      return true
    }
  } else {
    return true
  }
}
/**
 * @function handleVersion
 * @description 判断版本号是否左小右大(left<right)或相等
 * @param leftData 入参格式 "xx.xx.xx" 
 * @param rightData 入参格式 "xx.xx.xx"
 * @param errorInfo 入参格式 "xx"
 * @return [boolean] true 校验通过 false 校验失败
 */
function handleVersion(leftData: string, rightData: string, errorInfo: string) {
  let arr: string[] = []
  // "10.06.01" "11.08.03" => ["10","06","01","11","08","03"]
  leftData.replace(/[0-9]+/g, function (word: string) {
    arr.push(word)
    return word
  }
  )
  rightData.replace(/[0-9]+/g, function (word: string) {
    arr.push(word)
    return word
  })
  if (+arr[0] > +arr[3]) {
    message.error(errorInfo)
    return false
  }
  if (+arr[0] === +arr[3]) {
    if (+arr[1] > +arr[4]) {
      message.error(errorInfo)
      return false
    }
    if (+arr[1] === +arr[4]) {
      if (+arr[2] > +arr[5]) {
        message.error(errorInfo)
        return false
      }
    }
  }
  return true
}

/**
 * @function cheackFuntionList
 * @description 检查表单项校验
 * @param temporyFormData 所有功能模块信息 数据格式：boolean[]
 * @return [boolean] false校验未通过 true校验通过
 */
export function cheackFuntionList(temporyFormData: any) {
  const temporyValid = temporyFormData.map((item: FunctionList) => {
    const versionCheckResult = []
    // 校验是否符合xx.xx.xx格式
    const status1 = checkVersion(item.iosAppLeft)
    const status2 = checkVersion(item.iosAppRight)
    const status3 = checkVersion(item.iosSystemLeft)
    const status4 = checkVersion(item.iosSystemRight)
    const status5 = checkVersion(item.androidAppLeft)
    const status6 = checkVersion(item.androidAppRight)
    const status7 = checkVersion(item.androidSystemLeft)
    const status8 = checkVersion(item.androidSystemRight)
    if (!status1 || !status2 || !status3 || !status4 || !status5 || !status6 || !status7 || !status8) {
      return false
    }
    if (!item.userIdList && !item.contentRules) {
      message.error('规则内容或客户号至少存在一项');
      return false;
    }
    if (item.distributionMode === "1") {
      switch (item.userIdType) {
        case '1':
          if (item.contentRules.match(/[^A-Za-z0-9,{1}]/g) === null) {
            break;
          } else {
            message.error('规则内容只能输入小写字母或数字');
            return false;
          }
        case '2':
          if (item.contentRules.match(/[^0-9,{1}]/g) === null) {
            break;
          } else {
            message.error('规则内容只能输入数字');
            return false;
          }
        case '3':
          if (item.contentRules.match(/[^0-9,{1}]/g) === null) {
            break;
          } else {
            message.error('规则内容只能输入数字');
            return false;
          }
        case '4':
          if (item.contentRules.match(/[^A-Z0-9,{1}]/g) === null) {
            break;
          } else {
            message.error('规则内容只能输入大写字母或数字');
            return false;
          }
      }
    }
    if (item.distributionMode === "2") {
      if (item.userIdList.match(/[^0-9\n\r]/g) !== null) {
        message.error('导入客户号内容只允许输入数字');
        return false;
      }
    }
    // 校验输入框的值要符合left<right
    if (item.iosAppLeft !== '' && item.iosAppRight !== '') {
      versionCheckResult.push(handleVersion(item.iosAppLeft, item.iosAppRight, 'iOS端APP版本号应该左小右大'))
    }
    if (item.iosSystemLeft !== '' && item.iosSystemRight !== '') {
      versionCheckResult.push(handleVersion(item.iosSystemLeft, item.iosSystemRight, 'iOS端系统版本号应该左小右大'))
    }
    if (item.androidAppLeft !== '' && item.androidAppRight !== '') {
      versionCheckResult.push(handleVersion(item.androidAppLeft, item.androidAppRight, 'Android端APP版本号应该左小右大'))
    }
    if (item.androidSystemLeft !== '' && item.androidSystemRight !== '') {
      versionCheckResult.push(handleVersion(item.androidSystemLeft, item.androidSystemRight, 'Android端系统版本号应该左小右大'))
    }
    // 校验全部通过：true 校验某一项失败：false
    const versionCheckResultFlag = versionCheckResult.every((value) => {
      return value === true
    })
    return versionCheckResultFlag
  });
  return temporyValid
}
/**
 * @function handleVerRepeat
 * @description 对实参进行比较，分大于、小于、等于三种情况
 * @param leftData 数据示例 "xx.xx.xx"
 * @param rightData 数据示例 "xx.xx.xx"
 * @return string "more" left>right "less" left<right "equal" left=right
 */
function handleVerRepeat(leftData: string, rightData: string) {
  let arr: string[] = []
  // 当数据项为空，左输入框为最小，右输入框为最大
  if (!leftData) {
    leftData = "0.0.0"
  }
  if (!rightData) {
    rightData = "10000.0.0"
  }
  // "10.06.01" "11.08.03" => ["10","06","01","11","08","03"]
  leftData.replace(/[0-9]+/g, function (word: string) {
    arr.push(word)
    return word
  })
  rightData.replace(/[0-9]+/g, function (word: string) {
    arr.push(word)
    return word
  })
  if (+arr[0] > +arr[3]) {
    return 'more'
  }
  if (+arr[0] === +arr[3]) {
    if (+arr[1] > +arr[4]) {
      return 'more'
    }
    if (+arr[1] === +arr[4]) {
      if (+arr[2] > +arr[5]) {
        return 'more'
      }
      if (+arr[2] === +arr[5]) {
        return 'equal'
      }
    }
  }
  return 'less'
}
/**
 * @function handleVersionOverlap
 * @description 处理版本号重叠校验
 * @param aData 对比项一
 * @param bData 对比项二
 * @return false 校验重叠 true 校验不重叠
 */
function handleVersionOverlap(aData: any, bData: any,) {
  let ifIosAppOverlap = false;
  let ifIosSystemOverlap = false;
  let ifAndroidAppOverlap = false;
  let ifAndroidSystemOverlap = false;
  // 本地左与数据库右对比
  const iosAppFlag = handleVerRepeat(aData.iosAppLeft, bData.iosAppRight)
  // 数据库左与本地右对比
  const _iosAppFlag = handleVerRepeat(bData.iosAppLeft, aData.iosAppRight)
  // 本地左与数据库右对比
  const iosSystemFlag = handleVerRepeat(aData.iosSystemLeft, bData.iosSystemRight)
  // 数据库左与本地右对比
  const _iosSystemFlag = handleVerRepeat(bData.iosSystemLeft, aData.iosSystemRight)
  // 本地左与数据库右对比
  const androidAppFlag = handleVerRepeat(aData.androidAppLeft, bData.androidAppRight)
  // 数据库左与本地右对比
  const _androidAppFlag = handleVerRepeat(bData.androidAppLeft, aData.androidAppRight)
  // 本地左与数据库右对比
  const androidSystemFlag = handleVerRepeat(aData.androidSystemLeft, bData.androidSystemRight)
  // 数据库左与本地右对比
  const _androidSystemFlag = handleVerRepeat(bData.androidSystemLeft, aData.androidSystemRight)
  function handelReapeat(flag: any, _flag: any, leftData: any, rightData: any) {
    if (flag === 'more') {
      if (aData[leftData] && bData[rightData]) {
        if (!aData[rightData] && !bData[leftData]) {
          return true //不重叠
        }
        if (aData[rightData] || bData[leftData]) {
          return true //不重叠
        }
        if (aData[rightData] && bData[leftData]) {
          return true //不重叠
        }
      }
      return false //重叠
    }
    if (_flag === 'more') {
      if (aData[rightData] && bData[leftData]) {
        if (aData[leftData] || bData[rightData]) {
          return true //不重叠
        }
        if (aData[leftData] && bData[rightData]) {
          return true //不重叠
        }
        if (!aData[leftData] && !bData[rightData]) {
          return true //不重叠
        }
      }
      return false //重叠
    }
    return false //重叠
  }
  ifIosAppOverlap = handelReapeat(iosAppFlag, _iosAppFlag, 'iosAppLeft', 'iosAppRight')
  ifIosSystemOverlap = handelReapeat(iosSystemFlag, _iosSystemFlag, 'iosSystemLeft', 'iosSystemRight')
  ifAndroidAppOverlap = handelReapeat(androidAppFlag, _androidAppFlag, 'androidAppLeft', 'androidAppRight')
  ifAndroidSystemOverlap = handelReapeat(androidSystemFlag, _androidSystemFlag, 'androidSystemLeft', 'androidSystemRight')
  if (aData.systemSelection === 'ios') {
    if (!ifIosAppOverlap && !ifIosSystemOverlap) {
      return false
    } else {
      return true
    }
  } else {
    if (!ifAndroidAppOverlap && !ifAndroidSystemOverlap) {
      return false
    } else {
      return true
    }
  }
}
/**
 * @function ifRepeat
 * @description 任务是否重复
 * @param headData 当前任务的头部信息
 * @param formData 当前任务的表单信息
 * @return true 不重复 false 重复
 */
export async function ifRepeat(headData: any, formData: any) {
  let flag = true;
  /* 
  当前测试任务功能模块的功能id数组、应用类型数组
  */
  let functionIdArr: any = [];
  let applicationTypeArr: any = [];
  formData.map((item: any) => {
    functionIdArr.push(item.functionId);
    applicationTypeArr.push(item.applicationType);
  });
  /*
  以任务号为核心，根据任务号分批校验
  校验结果全部重复则修改verificationResults变量不去AJAX
  */
  await postAbTest({
    type: 'queryAll',
  }).then((res: any) => {
    if (res.status_code === 0) {
      // 任务号
      for (let taskItem of res.data) {
        if (taskItem.taskNumber === headData.taskNumber) {
          // 阻止编辑页去拿自己校验
          if (taskItem.id === headData.id) {
            continue
          }
          taskItem.functionList.map((taskItem: any) => {
            if (functionIdArr.includes(taskItem.functionId)) {
              for (let applicationType of applicationTypeArr) {
                let typeFlag = applicationType.some((item: any) => {
                  return taskItem.applicationType.includes(item)
                })
                if (typeFlag) {
                  formData.map((item: any) => {
                    if (item.systemSelection === taskItem.systemSelection) {
                      formData.map((item: any) => {
                        flag = handleVersionOverlap(item, taskItem)
                      });
                    }
                  });
                }
              }
            } else {
              return false;
            }
          })
        }
      }
    } else {
      message.error("服务器错误")
    }
  }).catch((error: any) => {
    console.warn(error);
  })
  return flag;
}
/**
 * @function ifSelfFunctionListRepeat
 * @description 判断任务功能模块是否重复
 * @param headData 当前任务的头部信息
 * @param formData 当前任务的表单信息
 * @return true 不重复 false 重复
 */
export async function ifSelfFunctionListRepeat(formData: any) {
  let flag = true;
  if (formData.length >= 2) {
    formData.map((item: any, index: number) => {
      for (let i = index + 1; i < formData.length; i++) {
        // 功能id
        if (formData[index].functionId === formData[i].functionId) {
          // 系统选择
          if (formData[index].systemSelection === formData[i].systemSelection) {
            let applicationTypeFlag = false
            // 应用类型
            for (let value of formData[index].applicationType) {
              formData[i].applicationType.includes(value) ? applicationTypeFlag = true : ''
            }
            // 版本号
            if (applicationTypeFlag) {
              formData.map(() => {
                flag = handleVersionOverlap(formData[index], formData[i])
              });
            }
          }
        }
      }

    })
  }
  return flag
}