import React, { FC, useEffect, useState } from 'react';
import api from 'api';
import zhCN from 'antd/es/locale/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { DatePicker, ConfigProvider, Select, message, Button } from 'antd';
import styles from './index.less';
import { handleSuccessWithFile } from '../util';
import axios from 'axios'
import store from 'store';
export enum ExportType {
  SALE_MAN_TYPE = '1',
  CLIENT_DATA_TYPE = '2',
  CLIENT_DATA_SINGLE_TYPE = '3',
}
interface exportProps {
  data: any;
  exportType: ExportType;
}

export const ExportButton: FC<exportProps> = ({ data, exportType }) => {
  const _exportStockTradeInfo = (temp: 'api' | 'yytjapi', apiPath, fileName, data?) => {
    // 导出基顺通存量汇总信息
    axios.get(`/${temp}${apiPath}`, {
      headers: {
        Authentication: store.get('user_token'),
      },
      params: data,
      responseType: 'blob'
    }).then(data => {
      let reader = new FileReader();
      reader.onload = function () {
        try {
          let resData = JSON.parse(this.result as string);
          if (resData && resData['code']) {
            message.error(resData['message']);
          }
        } catch (error) {
          var blob = new Blob([data.data])
          var downloadElement = document.createElement('a');
          var href = window.URL.createObjectURL(blob); //创建下载的链接
          downloadElement.href = href;
          downloadElement.download = `${fileName}.xlsx`; //下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); //点击下载
          document.body.removeChild(downloadElement); //下载完成移除元素
          window.URL.revokeObjectURL(href); //释放掉blob对象
        }
      }
      reader.readAsText(data.data);
    }).catch(() => {
      message.error('网络错误，请稍后再试');
    })
  }

  const exportExcel = (data, exportType) => {
    if (exportType === ExportType.SALE_MAN_TYPE) {
      if (location.host.indexOf('localhost') === 0) {
        _exportStockTradeInfo('api', `/dataReport/fofSale/export`, '机构人员业务报表', data)
      } else {
        _exportStockTradeInfo('yytjapi', `/dataReport/fofSale/export`, '机构人员业务报表', data)
      }
    } else if (exportType === ExportType.CLIENT_DATA_TYPE) {
      if (location.host.indexOf('localhost') === 0) {
        _exportStockTradeInfo('api', `/dataReport/fofProduct/export`, '机构数据报表', data)
      } else {
        _exportStockTradeInfo('yytjapi', `/dataReport/fofProduct/export`, '机构数据报表', data)
      }
    } else if (exportType === ExportType.CLIENT_DATA_SINGLE_TYPE) {

      if (location.host.indexOf('localhost') === 0) {
        _exportStockTradeInfo('api', `/dataReport/fofProduct/singleData-export?fofId=${data}`, '投资者全景信息')
      } else {
        _exportStockTradeInfo('yytjapi', `/dataReport/fofProduct/singleData-export?fofId=${data}`, '投资者全景信息')
      }
    };
  }
  return (
    <article className={styles['m-filter-warpper']}>
      <Button
        type="primary"
        onClick={() => {
          exportExcel(data, exportType);
        }}
      >
        导出
      </Button>
    </article>
  );
};


