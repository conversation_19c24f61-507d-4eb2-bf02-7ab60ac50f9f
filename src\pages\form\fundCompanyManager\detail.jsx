import React, { useEffect, useState } from "react";
import api from 'api';
import { Descriptions, Divider, Button, Table, Spin, Input, Modal, message, InputNumber } from 'antd';
// import FormRender from 'form-render/lib/antd';
import FundList from './fundList';
import './index.less';

const { TextArea } = Input;
const { fetchFundCompany, postFundCompanyPHPData, fetchFundCompanyFid, postFundCompanyFid } = api;

export default function (props) {

    const { record } = props;

    const columns1 = [{
        title: '基金名称',
        dataIndex: 'fundName',
        key: 'fundName'
    }, {
        title: '基金代码',
        dataIndex: 'fundCode',
        key: 'fundCode'
    }, {
        title: '持有金额',
        dataIndex: 'shareValue',
        key: 'shareValue'
    }, {
        title: '占比',
        dataIndex: 'ratio',
        key: 'ratio',
        render: text => (text + '%')
    }];

    const columns2 = [{
        title: '订单号',
        dataIndex: 'appSheetSerialNo',
        key: 'appSheetSerialNo'
    }, {
        title: '基金名称',
        dataIndex: 'fundName',
        key: 'fundName'
    }, {
        title: '基金代码',
        dataIndex: 'fundCode',
        key: 'fundCode'
    }, {
        title: '交易类型',
        dataIndex: 'businessName',
        key: 'businessName'
    }, {
        title: '交易金额',
        dataIndex: 'applicationAmount',
        key: 'applicationAmount'
    }, {
        title: '交易份额',
        dataIndex: 'applicationVol',
        key: 'applicationVol'
    }, {
        title: '申请日期',
        dataIndex: 'transactionDate',
        key: 'transactionDate'
    }, {
        title: '确认日期',
        dataIndex: 'transactionCfmDate',
        key: 'transactionCfmDate'
    }]

    const columns3 = [{
        title: '调仓日期',
        dataIndex: 'transactionDate',
        key: 'transactionDate'
    }, {
        title: '调仓原因',
        dataIndex: 'tcReason',
        key: 'tcReason'
    }, {
        title: '发布时间',
        dataIndex: 'acceptTime',
        key: 'acceptTime'
    }, {
        title: '状态',
        dataIndex: 'status',
        key: 'status', // 0 确认中 1 已完成 2 已撤销
        render: (text) => (
            text === '0' ? '确认中' :
            text === '1' ? '已完成' : '已撤销'
        )
    }, {
        title: '操作',
        key: 'operator',
        render: (text, record) => (
            <a onClick={() => {openDetail(record)}}>查看详情</a>
        )
    }]

    const columns5 = [{
        title: '业务名称',
        dataIndex: 'businessName',
        key: 'businessName',
        render: (text) => (
            <span style={{color: text === '买入' ? '#fe5d4e' : '#11a6fc'}}>{text}</span>
        )
    }, {
        title: '基金信息',
        dataIndex: 'fundInfo',
        key: 'fundInfo'
    }, {
        title: '交易份额/交易金额',
        dataIndex: 'trade',
        key: 'trade'
    }, {
        title: '原因',
        dataIndex: 'reason',
        key: 'reason',
        render: (text) => (
            <span style={{color: text === '可执行' ? '#009801' : '#fe5d4e'}}>{text}</span>
        )
    }]

    const columns6 = [{
        title: '基金名称/代码',
        dataIndex: 'fundCode',
        key: 'fundCode'
    }, {
        title: '上期占比',
        dataIndex: 'oldRatio',
        key: 'oldRatio'
    }, {
        title: '目标占比',
        dataIndex: 'newRatio',
        key: 'newRatio'
    }]

    const columns7 = [{
        title: '订单号',
        dataIndex: 'appSheetSerialNo',
        key: 'appSheetSerialNo'
    }, {
        title: '基金名称',
        dataIndex: 'fundName',
        key: 'fundName'
    }, {
        title: '基金代码',
        dataIndex: 'fundCode',
        key: 'fundCode'
    }, {
        title: '交易类型',
        dataIndex: 'businessName',
        key: 'businessName'
    }, {
        title: '交易金额',
        dataIndex: 'applicationAmount',
        key: 'applicationAmount'
    }, {
        title: '交易份额',
        dataIndex: 'applicationVol',
        key: 'applicationVol'
    }, {
        title: '申请日期',
        dataIndex: 'transactionDate',
        key: 'transactionDate'
    }, {
        title: '确认日期',
        dataIndex: 'transactionCfmDate',
        key: 'transactionCfmDate'
    }]

    const [dataSource1, setDataSource1] = useState([]);
    const [dataSource2, setDataSource2] = useState([]);
    const [dataSource3, setDataSource3] = useState([]);
    const [dataSource4, setDataSource4] = useState([]);
    const [valid, setValid] = useState([]);
    const [dataSource5, setDataSource5] = useState([]);
    const [dataSource6, setDataSource6] = useState([]);
    const [dataSource7, setDataSource7] = useState([]);

    const [spinToggle1, setSpinToggle1] = useState(true);
    const [spinToggle2, setSpinToggle2] = useState(true);
    const [spinToggle3, setSpinToggle3] = useState(true);
    const [spinToggle6, setSpinToggle6] = useState(true);
    const [spinToggle7, setSpinToggle7] = useState(true);

    const [goSettingLoading, setGoSettingLoading] = useState(false);
    const [confirmSettingLoading, setConfirmSettingLoading] = useState(false);

    const [allShareValue, setAllShareValue] = useState(''); // 市值
    const [groupid, setGroupid] = useState('');

    const [UI_fid, setUI_fid] = useState('');
    const [FID_OBJ, setFID_OBJ] = useState({config: {}});
    const [showFid, setShowFid] = useState(false);

    // 建仓/调仓 弹窗
    const [settingModalVisible, setSettingModalVisible] = useState(false);
    const [settingAmount, setSettingAmount] = useState('0'); // 加仓金额
    const [tcReason, setTcReason] = useState(''); // 调仓原因
    const [fundListRef, setFundListRef] = useState(null);

    // 检查是否可以执行交易指令 弹窗
    const [checkModalVisible, setCheckModalVisible] = useState(false);
    const [checked, setChecked] = useState(false);

    // 调仓记录详情 弹窗
    const [detailModalVisible, setDetailModalVisible] = useState(false);
    const [detailModalDate, setDetailModalDate] = useState('');
    const [detailModalReason, setDetailModalReason] = useState('');
    const [withdrawLoading, setWithdrawSettingLoading] = useState(false);
    const [detailAppSheetSerialNo, setDetailAppSheetSerialNo] = useState('');
    const [tCStatue, setTCStatue] = useState('');


    useEffect(() => {
        fetchDetail();
    }, [])

    /**
     * 获取 fid
     */
    const fetchFid = (groupId) => {
        fetchFundCompanyFid().then(data => {
            console.log(data);
            if (data.code === '0000') {
                data = JSON.parse(data.data);
                if (data && data.config) {
                    setFID_OBJ(data);
                    data = data.config;
                    setUI_fid(data[groupId] || '');
                }
                setShowFid(true);
            } else {
                message.error('获取fid配置失败');
            }
        })
    }

    /**
     * 保存fid
     */
    const saveFid = () => {
        if (!UI_fid) {message.error('fid 不能为空');return;}
        let _obj = FID_OBJ;
        _obj.config[groupid] = UI_fid;
        postFundCompanyFid({value: JSON.stringify(_obj)}).then((data) => {
            if (data.code === "0000") {
                message.success('fid 保存成功')
            } else {
                message.error('fid 保存失败')
            }
        })
    }

    /**
     * 获取基金公司实盘详情
     */
    const fetchDetail = () => {
        let sendToData = {
            type: "detail",
            value: JSON.stringify({
                groupAccountId: record.groupAccountId
            })
        }
        fetchFundCompany(sendToData).then((data) => {
            console.log(data);
            if (data.code === "0000") {
                data = data.data;
                let infoCursorListDataBeanMap = data.infoCursorListDataBeanMap;
                let infoCursorRecordsDataBeanMap = data.infoCursorRecordsDataBeanMap;
                let infoCursorSingleDataBeanMap = data.infoCursorSingleDataBeanMap;
                setSpinToggle1(false);
                setSpinToggle2(false);
                setSpinToggle3(false);
                setDataSource1(infoCursorListDataBeanMap.cursor);
                setDataSource2(infoCursorListDataBeanMap.cursor2);
                setDataSource3(infoCursorRecordsDataBeanMap.cursor3);
                if (infoCursorSingleDataBeanMap.cursor1) {
                    setAllShareValue(infoCursorSingleDataBeanMap.cursor1.allShareValue);
                    setGroupid(infoCursorSingleDataBeanMap.cursor1.groupId);
                    fetchFid(infoCursorSingleDataBeanMap.cursor1.groupId)
                }
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            message.error('网络错误，请重试')
        })
    }

    /**
     * 打开调仓建仓弹窗
     */
    const openSetting = () => {
        setDataSource4(dataSource1);
        setSettingModalVisible(true);
    }

    /**
     * 关闭调仓建仓弹窗
     */
    const closeSetting = () => {
        setSettingModalVisible(false);
        setDataSource4([]);
        setSettingAmount('0');
        setTcReason('');
    }

    /**
     * 检查调仓可行性
     */
    const goSetting = () => {
        if (record.status !== '1' && (settingAmount === "" || settingAmount == 0)) return message.error('加仓金额必输，且非0');
        let _allRatio = 0;
        let _dataSource4 = fundListRef.submit();
        let _fundCodeArr = [];
        for (let i = 0; i < _dataSource4.length; i++) {
            if (_dataSource4[i].newRatio === '') return message.error('请填写目标占比');
            if (~_fundCodeArr.indexOf(_dataSource4[i].fundCode)) return message.error(`此基金(${_dataSource4[i].fundCode})指令记录已存在`);
            _fundCodeArr.push(_dataSource4[i].fundCode);
            _allRatio += parseFloat(_dataSource4[i].newRatio);
        }
        if (parseFloat(_allRatio.toFixed(6)) !== 100) return message.error('占比相加不为100%');
        if (!tcReason) return message.error('请填写调仓原因');
        if (tcReason.length < 5) return message.error('调仓原因需大于5个字')
        let fundList = [];
        for (let i = 0; i < _dataSource4.length; i++) {
            fundList.push(`${_dataSource4[i].fundCode},${_dataSource4[i].newRatio}`);
        }
        fundList = fundList.join(';');
        let sendToData = {
            type: "check",
            value: JSON.stringify({
                groupAccountId: record.groupAccountId,
                amount: settingAmount || '0',
                fundList: fundList
            })
        }
        setGoSettingLoading(true);
        fetchFundCompany(sendToData).then((data) => {
            console.log(data);
            setGoSettingLoading(false);
            if (data.code === '0000') {
                data = data.data;
                let _noData = data.filter(item => item.reason !== '可执行');
                if (_noData.length === 0) {
                    // 可执行
                    setChecked(true);
                }
                setDataSource5(data);
                setCheckModalVisible(true);
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            setGoSettingLoading(false);
            message.error('网络错误，请重试')
        })
    }

    const getJSONP = () => {
        let _script = document.createElement("script");
         _script.type = "text/javascript";
         _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/rabbitmq/newyypushlish?key=bigVData&param=${groupid}`;
         document.body.appendChild(_script);
         _script.onload = function (){
             document.body.removeChild(_script)
         } 
     }

    /**
     * 调仓
     */
    const confirmSetting = () => {
        let fundList = [];
        let _dataSource4 = fundListRef.submit()
        for (let i = 0; i < _dataSource4.length; i++) {
            fundList.push(`${_dataSource4[i].fundCode},${_dataSource4[i].ratio || '0'}`);
        }
        fundList = fundList.join(';');
        let fundNewList = [];
        for (let i = 0; i < _dataSource4.length; i++) {
            fundNewList.push(`${_dataSource4[i].fundCode},${_dataSource4[i].newRatio}`);
        }
        fundNewList = fundNewList.join(';');
        let sendToData = {
            type: "trade",
            value: JSON.stringify({
                operator: '999',
                groupAccountId: record.groupAccountId,
                amount: settingAmount || '0',
                fundList: fundList,
                fundNewList: fundNewList,
                tcReason: tcReason
            })
        }
        let _sendToPHP = [];
        if (dataSource3) {
            for (let i = 0; i < dataSource3.length; i++) {
                if (dataSource3[i].status !== '2') {
                    _sendToPHP.push({
                        content: dataSource3[i].tcReason,
                        date: dataSource3[i].transactionDate,
                        dvcustid: '************',
                        dvaccountid: record.groupAccountId,
                        source: "yy_java"
                    })
                }
            }
        }
        setConfirmSettingLoading(true);
        fetchFundCompany(sendToData).then(data => {
            setConfirmSettingLoading(false);
            if (data.code === '0000') {
                _sendToPHP.unshift({
                    content: tcReason,
                    date: data.data.transactionDate,
                    dvcustid: '************',
                    dvaccountid: record.groupAccountId,
                    source: "yy_java"
                });
                console.log(_sendToPHP);
                postFundCompanyPHPData({value: JSON.stringify(_sendToPHP)}, `${record.groupAccountId}`).then(data => {
                    message.success('调仓成功');
                    setCheckModalVisible(false);
                    closeSetting();
                    fetchDetail();
                    if (data.code === '0000') {
                        getJSONP();
                    } else {
                        message.error('调仓记录时增加ssdb数据失败');
                    }
                }).catch(() => {
                    message.success('调仓成功');
                    setCheckModalVisible(false);
                    closeSetting();
                    fetchDetail();
                    message.error('调仓记录时增加ssdb数据失败');
                })
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            setConfirmSettingLoading(false);
            message.error('网络错误，请重试');
        })
    }

    /**
     * 打开调仓记录详情
     */
    const openDetail = (record) => {
        setDataSource6([]);
        setDataSource7([]);
        setDetailModalReason('');
        setDetailModalDate('');
        setDetailModalVisible(true);
        setDetailAppSheetSerialNo(record.appSheetSerialNo);
        let sendToData = {
            type: "tcDetail",
            value: JSON.stringify({
                originalAppSheetSerialNo: record.appSheetSerialNo
            })
        }
        setSpinToggle6(true);
        setSpinToggle7(true);
        setTCStatue(record.status);
        fetchFundCompany(sendToData).then((data) => {
            setSpinToggle6(false);
            setSpinToggle7(false);
            if (data.code === '0000') {
                data = data.data;
                if (data.tcDetailMap) {
                    setDataSource6(data.tcDetailMap.cursor);
                    setDataSource7(data.tcDetailMap.cursor1);
                }
                setDetailModalReason(data.tcReason);
                setDetailModalDate(data.transactionDate);
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            setSpinToggle6(false);
            setSpinToggle7(false);
            message.error('网络错误，请重试');
        })
    }

    /**
     * 撤单
     */
    const withdraw = () => {
        let sendToData = {
            type: "cancel",
            value: JSON.stringify({
                originalAppSheetSerialNo: detailAppSheetSerialNo,
                groupAccountId: record.groupAccountId,
				operator: "999"
            })
        }
        fetchFundCompany(sendToData).then((data) => {
            setWithdrawSettingLoading(false);
            if (data.code === '0000') {
                let _sendToPHP = [];
                for (let i = 0; i < dataSource3.length; i++) {
                    if (dataSource3[i].appSheetSerialNo !== detailAppSheetSerialNo) {
                        _sendToPHP.push({
                            content: dataSource3[i].tcReason,
                            date: dataSource3[i].transactionDate,
                            dvcustid: '************',
                            dvaccountid: record.groupAccountId,
                            source: "yy_java"
                        })
                    }
                }
                postFundCompanyPHPData({value: JSON.stringify(_sendToPHP)}, `${record.groupAccountId}`).then(data => {
                    message.success('撤销成功');
                    setDetailModalVisible(false);
                    fetchDetail();
                    if (data.code === '0000') {
                        getJSONP();
                    } else {
                        message.error('调仓记录时增加ssdb数据失败');
                    }
                }).catch(() => {
                    message.success('撤销成功');
                    setDetailModalVisible(false);
                    fetchDetail();
                    message.error('调仓记录时增加ssdb数据失败');
                })
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            setWithdrawSettingLoading(false);
            message.error('网络错误，请重试');
        })
    }

    return (
        <div>
            <div>
                <Descriptions
                    title="基本信息"
                >
                    <Descriptions.Item label="账号">{record.groupAccountId}</Descriptions.Item>
                    <Descriptions.Item label="归属方">{record.spAccoTaName}</Descriptions.Item>
                    <Descriptions.Item label="运行时间">{record.holdDate}</Descriptions.Item>
                    <Descriptions.Item label="持仓金额">{allShareValue}</Descriptions.Item>
                    <Descriptions.Item label="成立以来收益率"></Descriptions.Item>
                    <Descriptions.Item label="日涨幅"></Descriptions.Item>
                    <Descriptions.Item label="净值"></Descriptions.Item>
                </Descriptions>
                {
                    showFid ?
                    <div>
                        <Input 
                            addonBefore="fid"
                            style={{width: 200, marginTop: 10}}
                            onChange={(e) => {setUI_fid(e.target.value.replace(/[^\w\.\/]/ig,''))}}
                            value={UI_fid}
                        /><br />
                        <Button
                            type="primary"
                            onClick={saveFid}
                            style={{marginTop: 10}}
                        >保存fid</Button>
                    </div> : null
                }
            </div>
            <Divider />
            <div>
                <Descriptions title="持仓信息"></Descriptions>
                <Spin spinning={spinToggle1} tip="加载中...">
                    <Table 
                        columns={columns1}
                        dataSource={dataSource1}
                    />
                </Spin>
            </div>
            <Divider />
            <div>
                <Descriptions title="在途交易"></Descriptions>
                <Spin spinning={spinToggle2} tip="加载中...">
                    <Table 
                        columns={columns2}
                        dataSource={dataSource2}
                    />
                </Spin>
            </div>
            <Divider />
            <div>
                <Descriptions title="调仓记录"></Descriptions>
                <Button 
                    type="primary"
                    onClick={openSetting}
                    style={{marginBottom: 10}}
                >建仓/调仓</Button>
                <Spin spinning={spinToggle3} tip="加载中...">
                    <Table 
                        columns={columns3}
                        dataSource={dataSource3}
                    />
                </Spin>
            </div>
            {/* 建仓调仓弹窗 */}
            <Modal
                title="建仓/调仓"
                visible={settingModalVisible}
                width="80%"
                onCancel={closeSetting}
                footer={null}
                maskClosable={false}
                keyboard={false}
                bodyStyle={{height: '500px', overflow: 'scroll'}}
            >
                <div>
                    <span>加仓金额</span>
                    <InputNumber 
                        style={{width: 140, margin: '0 4px'}}
                        step={0.01}
                        value={settingAmount}
                        onChange={(value) => {setSettingAmount(value)}}
                        min={0}
						max={999999999}
                    />
                    <span>元</span>
                    <span
                        style={{color: '#fe5d4e', marginLeft: 10}}
                    >(加仓前请确保账户资金充足)</span>
                </div>
                <Descriptions title="调仓目标"></Descriptions>
                <FundList 
                    data={dataSource4}
                    onRef={setFundListRef}
                />
                <Descriptions title="调仓原因"></Descriptions>
                <TextArea 
                    rows={4}
                    placeholder="请输入调仓原因，不少于5个字"
                    value={tcReason}
                    onChange={(e) => {setTcReason(e.target.value)}}
                />
                <div
                    style={{marginTop: 10}}
                >
                    <Button 
                        type="primary"
                        onClick={goSetting}
                        loading={goSettingLoading}
                    >发布调仓</Button>
                </div>
            </Modal>
            {/* 检测调仓可行性弹窗 */}
            <Modal
                title={checked ? <span style={{color: '#009801'}}>交易指令可执行</span> : <span style={{color: '#fe5d4e'}}>交易指令不可执行</span>}
                visible={checkModalVisible}
                footer={null}
                onCancel={() => {setCheckModalVisible(false)}}
                width="50%"
            >
                <Table 
                    columns={columns5}
                    dataSource={dataSource5}
                    position={{pageSize: 5}}
                />
                <div
                    style={{marginTop: 10}}
                >
                    {
                        checked ? 
                        <Button type="primary" onClick={confirmSetting} loading={confirmSettingLoading} >确认发布</Button> :
                        <Button type="primary" onClick={() => {setCheckModalVisible(false)}} >返回</Button>
                    }
                </div>
            </Modal>
            {/* 调仓记录 */}
            <Modal
                title={detailModalDate + "调仓记录"}
                visible={detailModalVisible}
                footer={null}
                onCancel={() => {setDetailModalVisible(false)}}
                width="80%"
                bodyStyle={{height: '500px', overflow: 'scroll'}}
            >
                <Descriptions title="调仓原因">
                    <Descriptions.Item label="">{detailModalReason}</Descriptions.Item>
                </Descriptions>
                <Descriptions title="持仓变动"></Descriptions>
                <Spin spinning={spinToggle6} tip="加载中...">
                <Table 
                    columns={columns6}
                    dataSource={dataSource6}
                />
                </Spin>
                <Descriptions title="持仓变动"></Descriptions>
                <Spin spinning={spinToggle7} tip="加载中...">
                <Table 
                    columns={columns7}
                    dataSource={dataSource7}
                />
                </Spin>
                <div
                    style={{marginTop: 10}}
                >
                    {
                        (new Date() < new Date(detailModalDate + ' 14:55:00') && tCStatue === '0') ? 
                        <Button type="primary" onClick={withdraw} loading={withdrawLoading}>撤销</Button> : null
                    }
                </div>
            </Modal>
        </div>
    );
}