import React from 'react';

/**
 * 解析包含超链接格式的文本内容
 * 格式：{文字-链接} 例如：{证券日报网-http://stock.10jqka.com.cn/20250529/c668532698.shtml}
 * 
 * 解析策略：
 * 1. 匹配大括号内的完整内容
 * 2. 使用第一个连字符作为文字和链接的分隔符
 * 3. 支持链接中包含多个连字符的情况
 * 
 * 边界情况处理：
 * - 如果没有连字符，保留原始文本
 * - 如果连字符在开头或结尾，保留原始文本
 * - 如果文字或链接部分为空，保留原始文本
 * 
 * @param content 包含超链接格式的文本
 * @returns 解析后的React元素数组
 */
export const parseLinkContent = (content: string): React.ReactNode[] => {
  if (!content) {
    return [];
  }

  // 正则表达式匹配大括号内的完整内容
  const linkRegex = /\{([^}]+)\}/g;
  const result: React.ReactNode[] = [];
  let lastIndex = 0;
  let match;

  while ((match = linkRegex.exec(content)) !== null) {
    const [fullMatch, innerContent] = match;
    const matchStart = match.index;

    // 添加链接前的普通文本
    if (matchStart > lastIndex) {
      const beforeText = content.slice(lastIndex, matchStart);
      if (beforeText) {
        result.push(beforeText);
      }
    }

    // 寻找第一个连字符的位置来分割文字和链接
    const firstDashIndex = innerContent.indexOf('-');
    
    if (firstDashIndex === -1) {
      // 没有连字符，保留原始文本
      result.push(fullMatch);
    } else if (firstDashIndex === 0) {
      // 连字符在开头，保留原始文本
      result.push(fullMatch);
    } else if (firstDashIndex === innerContent.length - 1) {
      // 连字符在结尾，保留原始文本
      result.push(fullMatch);
    } else {
      // 正常情况：以第一个连字符为分界点分割
      const text = innerContent.slice(0, firstDashIndex);
      const url = innerContent.slice(firstDashIndex + 1);

      // 验证URL格式
      const isValidUrl = (url: string): boolean => {
        try {
          new URL(url);
          return true;
        } catch {
          // 如果不是完整URL，检查是否是相对路径
          return url.startsWith('/') || url.startsWith('./') || url.startsWith('../');
        }
      };

      // 创建链接元素
      if (text.trim() && url.trim() && isValidUrl(url.trim())) {
        result.push(
          React.createElement(
            'a',
            {
              key: `link-${result.length}`,
              href: url.trim(),
              target: '_blank',
              rel: 'noopener noreferrer',
              style: {
                color: '#1890ff',
                textDecoration: 'none',
              },
              onMouseEnter: (e: React.MouseEvent<HTMLAnchorElement>) => {
                e.currentTarget.style.textDecoration = 'underline';
              },
              onMouseLeave: (e: React.MouseEvent<HTMLAnchorElement>) => {
                e.currentTarget.style.textDecoration = 'none';
              },
            },
            text.trim()
          )
        );
      } else {
        // 如果格式不正确，保留原始文本
        result.push(fullMatch);
      }
    }

    lastIndex = linkRegex.lastIndex;
  }

  // 添加最后剩余的文本
  if (lastIndex < content.length) {
    const remainingText = content.slice(lastIndex);
    if (remainingText) {
      result.push(remainingText);
    }
  }

  return result;
};

/**
 * 获取解析后的纯文本内容（用于字符计数等场景）
 * 使用第一个连字符作为文字和链接的分隔符
 * @param content 包含超链接格式的文本
 * @returns 纯文本内容
 */
export const getPlainText = (content: string): string => {
  if (!content) {
    return '';
  }

  // 使用新的解析策略将超链接格式替换为纯文本
  return content.replace(/\{([^}]+)\}/g, (match, innerContent) => {
    const firstDashIndex = innerContent.indexOf('-');
    if (firstDashIndex > 0) {
      // 返回连字符前的文字部分
      return innerContent.slice(0, firstDashIndex);
    }
    // 如果格式不正确，返回原始内容（不包括大括号）
    return innerContent;
  });
};

/**
 * 验证超链接格式是否正确
 * 使用第一个连字符作为文字和链接的分隔符进行验证
 * @param content 待验证的文本内容
 * @returns 验证结果和错误信息
 */
export const validateLinkFormat = (content: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (!content) {
    return { isValid: true, errors: [] };
  }

  // 检查是否有未闭合的大括号
  const openBraces = (content.match(/\{/g) || []).length;
  const closeBraces = (content.match(/\}/g) || []).length;
  
  if (openBraces !== closeBraces) {
    errors.push('存在未闭合的大括号');
  }

  // 检查超链接格式，使用新的解析策略
  const linkRegex = /\{([^}]+)\}/g;
  let match;
  
  while ((match = linkRegex.exec(content)) !== null) {
    const [fullMatch, innerContent] = match;
    
    // 寻找第一个连字符的位置来分割文字和链接
    const firstDashIndex = innerContent.indexOf('-');
    
    if (firstDashIndex === -1) {
      errors.push(`缺少连字符分隔符：${fullMatch}`);
      continue;
    }
    
    if (firstDashIndex === 0) {
      errors.push(`链接文字不能为空：${fullMatch}`);
      continue;
    }
    
    if (firstDashIndex === innerContent.length - 1) {
      errors.push(`链接地址不能为空：${fullMatch}`);
      continue;
    }
    
    // 分割文字和链接
    const text = innerContent.slice(0, firstDashIndex);
    const url = innerContent.slice(firstDashIndex + 1);
    
    if (!text.trim()) {
      errors.push(`链接文字不能为空：${fullMatch}`);
    }
    
    if (!url.trim()) {
      errors.push(`链接地址不能为空：${fullMatch}`);
    } else {
      // 简单的URL格式验证
      const urlPattern = /^(https?:\/\/|\/|\.\/|\.\.\/)/;
      if (!urlPattern.test(url.trim())) {
        errors.push(`链接地址格式可能不正确：${url.trim()}`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}; 