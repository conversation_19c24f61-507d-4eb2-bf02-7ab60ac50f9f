import React, { useEffect, useState } from 'react';
import { Upload, Button, message } from 'antd';
import styles from './index.less';
import { MailData } from '@/pages/form/mail/interface';
import api from 'api';
const { uploadImg } = api;
type FormRender = {
  name: string;
  onChange: Function;
  formData: MailData;
};
type iImage = {
  code?: string;
  data?: any;
  message?: string;
};
export function Drawing({ name, onChange, formData }: FormRender) {
  let { mailTemplate, drawing } = formData;
  const [imageUrl, setImageUrl] = useState<string>();
  useEffect(() => {
    let imageCanvas: any = document.getElementById('imageCanvas');
    imageCanvas.width = 311;
    imageCanvas.height = 132;
    setImageUrl('');
  }, [mailTemplate]);
  // 编辑页初始化
  useEffect(() => {
    if (drawing) {
      const image: any = new Image();
      image.src = drawing;
      image.onload = () => {
        let imageCanvas: any = document.getElementById('imageCanvas');
        let imageCanvas2D = imageCanvas.getContext('2d');
        if (mailTemplate === '7') {
          // 绘制画布宽高以及将图片绘制到画布中
          imageCanvas.width = 311;
          imageCanvas.height = (311 / image.width) * image.height;
          imageCanvas2D.drawImage(image, 0, 0, imageCanvas.width, imageCanvas.height);
          setImageUrl(drawing);
        } else {
          // 绘制画布宽高以及将图片绘制到画布中
          imageCanvas.width = 311;
          imageCanvas.height = 132;
          imageCanvas2D.drawImage(image, 0, 0, imageCanvas.width, imageCanvas.height);
          setImageUrl(drawing);
        }
      };
    }
  }, [drawing]);
  // 数据同步在form-render表单
  useEffect(() => {
    onChange(name, imageUrl);
  }, [imageUrl]);
  const _uploadImg = (imageUrl: any) => {
    uploadImg({
      base64str: imageUrl,
    })
      .then((res: iImage) => {
        switch (res.code) {
          case '0000':
            res.data.url && setImageUrl(res.data.url);
            break;
          default:
            message.error('服务器上传失败');
            break;
        }
      })
      .catch((err: unknown) => {
        console.warn(err);
      });
  };
  // 将导入图片画在canvas容器中，再将容器中处理好的图片转换为base64地址返回给回调函数
  const getBase64 = (img: any, callback: any) => {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const image: any = new Image();
      image.src = e.target.result;
      image.onload = () => {
        let imageCanvas: any = document.getElementById('imageCanvas');
        let imageCanvas2D = imageCanvas.getContext('2d');
        if (mailTemplate === '7') {
          // 绘制画布宽高以及将图片绘制到画布中
          imageCanvas.width = 311;
          imageCanvas.height = (311 / image.width) * image.height;
          imageCanvas2D.drawImage(image, 0, 0, imageCanvas.width, imageCanvas.height);
          // 给回调函数传参
          callback(imageCanvas.toDataURL());
        } else {
          // 绘制画布宽高以及将图片绘制到画布中
          imageCanvas.width = 311;
          imageCanvas.height = 132;
          imageCanvas2D.drawImage(image, 0, 0, imageCanvas.width, imageCanvas.height);
          // 给回调函数传参
          callback(imageCanvas.toDataURL());
        }
      };
    };
    reader.readAsDataURL(img);
  };
  const customUpload = (info: any) => {
    getBase64(info.file, (imageUrl: any) => {
      _uploadImg(imageUrl);
    });
  };
  return (
    <div className={styles['m-mail-drawing']}>
      <canvas
        id="imageCanvas"
        style={{
          width: `311px`,
          border: '1px solid #d3d3d3',
          background: '#ffffff',
        }}
      ></canvas>
      <Upload showUploadList={false} customRequest={customUpload}>
        <div className={styles['m-drawing-btn']}>
          <Button style={{ width: '100px' }}>选择文件</Button>
          <span>
            除“图片”模板外，默认图片宽高为311*132； 如果是“图片”模板，则宽度固定为311，高度不限
          </span>
        </div>
      </Upload>
    </div>
  );
}
/* 迭代用的push小配图 */
// export function DrawingSmall({ name, onChange }: DrawingSmall) {
//   // 存储图片地址(url/base64)
//   const [imageUrl, setImageUrl] = useState<string>();
//   // 将导入图片画在canvas容器中，再将容器中处理好的图片转换为base64地址返回给回调函数
//   const getBase64 = (img: any, callback: any) => {
//     const reader = new FileReader();
//     reader.addEventListener('load', (e: any) => {
//       const image: any = new Image();
//       image.src = e.target.result;
//       image.onload = () => {
//         let imageCanvas: any = document.getElementById('imageCanvasSmall');
//         let imageCanvas2D = imageCanvas.getContext('2d');
//         // 绘制画布宽高以及将图片绘制到画布中
//         imageCanvas.width = 120;
//         imageCanvas.height = 120;
//         imageCanvas2D.drawImage(image, 0, 0, 120, 120);
//         // 给回调函数传参
//         callback(imageCanvas.toDataURL());
//       };
//     });
//     reader.readAsDataURL(img);
//   };
//   const handleChange = (info: any) => {
//     // status uploading done error removed
//     if (info.file.status === 'done') {
//       getBase64(info.file.originFileObj, (imageUrl: any) => {
//         console.log(imageUrl);
//         setImageUrl(imageUrl);
//       });
//     }
//   };
//   // 数据同步在form-render表单
//   useEffect(() => {
//     onChange(name, imageUrl);
//   }, [imageUrl]);
//   return (
//     <div className={styles['m-mail-drawing-small']}>
//       <canvas
//         id="imageCanvasSmall"
//         style={{
//           width: `120px`,
//           height: `120px`,
//           border: '1px solid #d3d3d3',
//           background: '#ffffff',
//         }}
//       ></canvas>
//       <Upload showUploadList={false} onChange={handleChange}>
//         <div className={styles['m-drawing-btn']}>
//           <Button>选择文件</Button>
//           <span>宽高尺寸为120X120</span>
//         </div>
//       </Upload>
//     </div>
//   );
// }
