import { HotSpot, SectorAnalyze } from '../../type';
import { checkRequiredAndThrow, checkUrlAndThrow } from '../../utils';
export const mapPropToName = {
  topicFundId: '主题选基id',
  topicFundName: '主题选基',
  // assessFundId: '指数估值id',
  // assessFundName: '指数估值',
  prosperityId: '景气度id',
  assessId: '估值id',
  indexType: '指数估值类型',
  moreLink: '板块详情跳转链接',
  lineChartOpen: '折线图',
  indexOpen: '景气度和估值',
};
export const sectorAnalyzeHelper = {
  addInit: function(): SectorAnalyze {
    return {
      topicFundId: '',
      topicFundName: '',
      // assessFundId: '',
      // assessFundName: '',
      prosperityId: '',
      assessId: '',
      indexType: '',
      moreLink: '',
      lineChartOpen: false,
      indexOpen: false,
    };
  },

  checkData: function(data: SectorAnalyze) {
    try {
      checkUrlAndThrow(data.moreLink, mapPropToName['moreLink']);
    } catch (e) {
      throw new Error(`板块分析-${e.message}`);
    }
    // checkRequiredAndThrow(data.assessId, mapPropToName['assessId']);
    // checkRequiredAndThrow(data.prosperityId, mapPropToName['prosperityId']);
    // checkRequiredAndThrow(data.indexType, mapPropToName['indexType']);

    return true;
  },
};
