import {
    Form,
    Input,
    Button,
    Popconfirm,
    Select,
    message
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import api from 'api';
import { iFundConfigData } from './index';
import styles from '../index.less';

const { Option } = Select;
const { TextArea } = Input;
interface FundConfigProps extends FormComponentProps {
    currentData: iFundConfigData;
    handleData: (data: iFundConfigData) => void;
    handleDelete: () => void;
}
const TypeMap = {
    rqbd: 48,
    syph: 48,
    tsyx: 94,
    rqdtb: 72,
}
class FundConfigCard extends React.Component<FundConfigProps, any> {
    constructor(props: FundConfigProps) {
        super(props);
        this.state = {
            isEdit: false,
            wordNumber: 48,
            paperWork: '',
            rankType: 'rqbd'
        }
    }
    formItemLayout = {
        labelCol: {
            span: 3
        },
        wrapperCol: {
            span: 19
        },
    };
    handleSubmit = () => { 
        const { isEdit, rankType, paperWork } = this.state;
        if (isEdit) {
            const { currentData } = this.props;
            if (!rankType) {
                message.error('请选择榜单类型');
                return;
            }
            let values = { ...currentData, rankType, paperWork };
            this.props.handleData(values);
            this.setState({
                isEdit: !isEdit
            })
        } else {
            this.setState({
                isEdit: !isEdit
            })
        }
        
    };
    handleSelect = (val: string) => {
        let paperWork = this.state.paperWork;
        let num = TypeMap[val] ?? 94;
        this.setState({
            wordNumber: TypeMap[val],
            paperWork: paperWork.slice(0, num),
            rankType: val
        })
    }
    handleText = (e: any) => {
        let value = e?.target?.value ?? '';
        this.setState({
            paperWork: value
        })
    }
    static getDerivedStateFromProps(nextProps: any, prevState: any) {
        const { functionType } = prevState;
        if (nextProps.currentData?.functionType !== functionType ) {
            return {
                functionType: nextProps.currentData?.functionType,
            }
        }
        return null;
    }
    componentDidMount() {
        const { currentData } = this.props;
        let type = currentData?.rankType ?? 'rqbd';
        this.setState({
            rankType: type,
            wordNumber: TypeMap[type],
            paperWork: currentData?.paperWork 
        })
    }
    render() {
        const { handleDelete } = this.props;
        const { isEdit, wordNumber, paperWork, rankType } = this.state;
        return (
            <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button className={styles['m-button']} onClick={this.handleSubmit}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={handleDelete}
                        okText="是"
                        cancelText="否"
                    >
                        <Button type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <Form {...this.formItemLayout}>
                    <Form.Item label="榜单类型：">
                        <Select style={{ width: 120 }} disabled={!isEdit} onChange={this.handleSelect} value={rankType}>
                            <Option key={'rqbd'} value={'rqbd'}>人气之选</Option>
                            <Option key={'syph'} value={'syph'}>业绩领跑</Option>
                            <Option key={'tsyx'} value={'tsyx'}>同顺严选</Option>
                            <Option key={'rqdtb'} value={'rqdtb'}>人气定投榜</Option>
                        </Select>
                    </Form.Item>
                    <Form.Item label="运营文案：">
                        <TextArea maxLength={wordNumber} value={paperWork} disabled={!isEdit} onChange={this.handleText}/>
                    </Form.Item>
                </Form>
            </div>
        )
    }
}
const WrappedFundConfig = Form.create<FundConfigProps>({ name: 'fundConfigCard' })(FundConfigCard);
export default WrappedFundConfig;