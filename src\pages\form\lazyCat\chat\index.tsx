import React, { useState, useEffect } from "react";
import Form<PERSON>ender from 'form-render/lib/antd';
import axios from 'axios'
import api from 'api';
import { Button, message } from 'antd'

const { fetchLazyCatChat, postLazyCatChat } = api;

let FORM_CONFIG: any = {
  propsSchema: {
    type: 'object',
    properties: {
      newUser: {
        title: '初始主页',
        type: 'object',
        properties: {
          firstEntry: {
            title: '首次进入',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          },
          secondEntry: {
            title: '非首次进入',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
            // type: 'array',
            // enum: ['0', '1', '2'],
            // enumNames: [
            //   '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
            //   '该计划持有人数XX人，正收益比例XX%',
            //   '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
            // ],
            // "ui:widget": "select",
            // "ui:options": {
            //   "mode": "tags"
            // }
          }
        }
      },
      noTestUser: {
        title: '未测完主页',
        type: 'object',
        properties: {
          firstBack: {
            title: '首次返回',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          },
          secondEntry: {
            title: '非首次进入',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          }
        }
      },
      answerUser: {
        title: '已完成答题未购买主页',
        type: 'object',
        properties: {
          firstBack: {
            title: '首次返回',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          },
          nextPayDayEntry: {
            title: '测完下个交易日后进来',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          }
        }
      },
      buyUser: {
        title: '已购买主页',
        type: 'object',
        properties: {
          login: {
            title: '已登录',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          },
          nologin: {
            title: '未登录',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          },
          assetsMoreTwo: {
            title: '持仓数大于2个',
            type: 'array',
            minItems: 1,
            items: {
              type: 'object',
              properties: {
                showTheme: {
                  type: 'string',
                  enum: ['0', '1', '2'],
                  enumNames: [
                    '截至X月X日，懒猫运行XX天，已为XX用户管理XX钱',
                    '该计划持有人数XX人，正收益比例XX%',
                    '主人，你的个性计划生成日（X月X日）至今，已错过收益XX%'
                  ],
                  "ui:hidden": "{{rootValue.showType === true}}",
                  "ui:width": "500px"
                },
                showType: {
                  title: '自定义',
                  type: 'boolean'
                },
                showText: {
                  type: 'string',
                  format: "textarea",
                  "ui:hidden": "{{rootValue.showType === false}}"
                }
              }
            }
          }
        }
      }
    },
  }
};

// 
export default function () {
  const [init, setInit] = useState(false);
  const [formConfig, setFormConfig] = useState({});
  const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);

  useEffect(() => {
    fetchLazyCatChat().then((res: any) => {
      try {
        res = JSON.parse(res.data);
        if (res) {
          for (let key1 in res) {
            for (let key2 in res[key1]) {
              let arr = res[key1][key2];
              let newArr:Array<object> = [];
              arr.map((item:string) => {
                if (item === '0' || item === '1' || item === '2') {
                  newArr.push({
                    showTheme: item,
                    showType: false,
                    showText: ''
                  })
                } else {
                  newArr.push({
                    showTheme: '',
                    showType: true,
                    showText: item
                  })
                }
              })
              res[key1][key2] = newArr;
            }
          }
          FORM_CONFIG.formData = {
            ...res
          };
        }
      } catch (e) {
        console.warn(e)
      }

      setInit(true);
      setFormConfig(FORM_CONFIG);
      setData(FORM_CONFIG.formData);
    }).catch((e: Error) => {
      message.error(e.message);
    })
  }, [init]);

  const onSubmit = () => {
    if (valid.length > 0) {
      message.error(`校验未通过字段：${valid.toString()}`);
    } else {
      let _postData:Object = {
        ...formData
      }
      console.log(_postData);
      for (let key1 in _postData) {
        for (let key2 in _postData[key1]) {
          let obj = _postData[key1][key2];
          let newArr:Array<string> = [];
          obj.map((item:object) => {
            if (item.showType) {
              newArr.push(item.showText);
            } else {
              newArr.push(item.showTheme);
            }
          })
          _postData[key1][key2] = newArr;
        }
      }
      postLazyCatChat({
        value: JSON.stringify(_postData)
      }).then((res: any) => {
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success('发布成功！')
          }
        } catch (e) {
          message.error(e.message);
        }
      })
    }
  };

  if (!init) return '加载中'
  return (
    <div style={{ padding: 60 }}>
      <FormRender
        propsSchema={FORM_CONFIG.propsSchema}
        formData={formData}
        onChange={setData}
        onValidate={setValid}
      />
      <Button type="primary" onClick={onSubmit}>提交</Button>
    </div>
  );
}