import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classnames from 'classnames';
import { Checkbox, But<PERSON>, Popconfirm, Row, Col, message, Select } from 'antd';
import { FundItem } from './type';
import { checkoutInput, setSearch } from './fn';
const { Option } = Select;

interface iProps {
  index: number;
  dataItem: FundItem;
  onChange: (index: number, dataItem: FundItem) => void;
  onDel: (index: number) => void;
  titleLength: number;
  ids: string[];
}

export default function Fund ({
  index = 0,
  name,
  dataItem = { id: '' },
  onChange = () => {},
  onDel = () => {},
  titleLength = 0,
  ids = [],
}: iProps) {
  const { select, title, logo } = dataItem;
  const [options, setOptions] = useState<any[]>([]);
  useEffect(() => {
    if (ids?.length > 0) {
      const _options = ids.map((item) => {
        return {
          value: name === 'grid' ? item.gridId : item.cardId,
          name: name === 'grid' ? item.gridName : item.cardName,
          disabled: item.disabled,
          bgImage: name === 'grid' ? item.iconDay : item.iconDay,
        }
      })
      setOptions(_options);
    }
  }, [ids])

  const renderOptions = (options: any[]) => {
    return options.map((item, index) => (
      <Option key={item.value} disabled={item.disabled} value={item.value}>{item.name}</Option>
    ))
  }

  const handleSearch = (val: any) => {
    const _options = setSearch(val, ids);
    setOptions(_options);
  }
  const handleChange = (type: string, value: string|string[], index_title?: number) => {
    const item = options.find(item => item.value === value);
    switch (type) {
      case 'fcode': 
      dataItem.select = value as string;
      // todo logo
      dataItem.logo = item.bgImage;
      break;
    }
    onChange(index, dataItem, name);
  }
  const handleDel = () => {
    onDel(index, name);
  }

  return (
    <div className={classnames(styles['tag'], styles['m-fund-item'])}>
      <Row gutter={[32, 16]} className={classnames(styles['m-list-item'], styles['m-row'])}>
        <Col span={2}>
          <span>{index + 1}</span>
        </Col>
        <Col span={6}>
          <img src={logo} className={classnames(styles['m-logo'])} />
        </Col>        
        <Col span={10}>
          <div className="m-value-item">
            <i className="m-required-icon">*</i><span>卡片：</span>
            {/* <input
              type="text"
              className={classnames('m-input', fundCode ? '' : 'm-required')}
              value={fundCode}
              onChange={() => {}}
              onInput={(e: any) => handleChange('fcode', e.target.value)}
            /> */}
            <Select
              value={select}
              className={classnames('m-select', select ? '' : 'm-required')}
              showSearch
              placeholder="请选择"
              onChange={(value: any) => handleChange('fcode', value)}
              onSearch={handleSearch}
              optionFilterProp="children"
            >
              {renderOptions(options)}
            </Select>
          </div>
        </Col>
        {/* <Col span={8}>
          <div className={classnames(styles['m-dataItem-titles'], "m-value-item m-type-title")}>
            <i className="m-required-icon">*</i><span>基金标题：</span>
            <input
              type="text"
              className={classnames('m-input', (title && title.length <= titleLength) ? '' : 'm-required')}
              value={title}
              onChange={() => {}}
              onInput={(e: any) => handleChange('ftitle', e.target.value)}
            />
          </div>
        </Col> */}
        <Col span={6}>
          <Popconfirm title="确认删除吗？" onConfirm={handleDel} okText="确认" cancelText="取消">
            <Button type="danger">删除</Button>
          </Popconfirm>
        </Col>
      </Row>
    </div>
  )
}