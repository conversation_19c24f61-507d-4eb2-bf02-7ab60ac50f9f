import React, {useState, useEffect} from 'react';
import classnames from 'classnames';
import styles from './index.less';
import { Input, Button, Divider, Table, Drawer, message, Spin, Col, Row, Upload, Icon } from 'antd';
import api from 'api';

const { TextArea } = Input;
const { newCommonUpload, fetchHashAll, postHash } = api;

const HASHKEY = 'lzx_AccountOpeningResultConfig';
// 初始化表单
const initFormData = () => {
  return {
    pageId: '', // 页面id
    remarks: '', // 备注
    finished: {
      fundRewardImg: '', // 基金奖励图片
      fundRewardLink: '', // 基金奖励跳转链接
      productRecommendTitle: '', // 产品推荐模块标题
      productRecommendReduceNum: '', // 新人立减金额
      productRecommendProducts:  [
        {
          tabDesc: '', // tab文案
          reason: '', // 推荐理由
          fundCode: '', // 基金代码
          fundTags: '', // 基金标签(多个标签用逗号分隔),
          detailUrl: '', // 详情跳转链接
        },
        {
          tabDesc: '', // tab文案
          reason: '', // 推荐理由
          fundCode: '', // 基金代码
          fundTags: '', // 基金标签(多个标签用逗号分隔),
          detailUrl: '', // 详情跳转链接
        }
      ], // 推荐产品列表
      newPersonGuidanceTitle: '', // 新人引导模块标题
      newPersonGuidanceEntrys: [
        {
          img: '', // 入口图片
          link: '', // 入口跳转链接
        },
        {
          img: '', // 入口图片
          link: '', // 入口跳转链接
        },
      ], // 新人引导模块入口列表
    }, // 已开基金户未交易用户
    notFinished: {
      headerImg: '', // 头部背景图
      accountOpeningGuidance: {
        img: '', // 引导图片
        link: '', // 开户引导跳转链接
      }, // 开户引导模块
      newPersonGuidanceTitle: '', // 新人引导模块标题
      newPersonGuidanceEntrys: [
        {
          img: '', // 入口图片
          link: '', // 入口跳转链接
        },
        {
          img: '', // 入口图片
          link: '', // 入口跳转链接
        },
      ], // 新人引导模块入口列表
    }, // 未开基金户用户
  };
}

export default function () {
  const [formData, setFormData] = useState(initFormData()); // 表单
  const [listData,setListData] = useState<any>([]); //显示的列表内容
  const [drawerStatus, setDrawerStatus] = useState(''); // 抽屉状态。'add'：新增。'edit'：编辑。
  const [showDrawer, setShowDrawer] = useState<boolean>(false); // 是否显示抽屉
  const [isSaveLoading, setIsSaveLoading] = useState<boolean>(false); // 保存按钮loading
  const [isListLoading, setIsListLoading] = useState<boolean>(false); // 列表加载loading
  const [isUploading,setIsUploading] = useState(false); // 图片上传中loading
  const tableColumns = [
    {
      title: '页面id',
      dataIndex: 'pageId',
      key: 'pageId',
      width: '20%',
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      width: '70%',
    },
    {
      title: '操作',
      key: 'action',
      width: '10%',
      render: (row:any, record:any, index:number) => (
        <div>
          <span
            style={{ color: '#1890FF', cursor: 'pointer', marginRight: '10px' }}
            onClick={()=>{openEditDrawer(row)}}
          >
            编辑
          </span>
        </div>
      ),
    },
  ]; // 列表列

  useEffect(() => {
    handleGetListData();
  }, []);

  // 获取列表数据
  const handleGetListData = () => {
    setIsListLoading(true);
    fetchHashAll({key: HASHKEY}).then((res: any)=>{
      setIsListLoading(false);
      if (res.code === '0000') {
        const data = res.data || {};
        const _listData = [];
        for (let key in data) {
          _listData.push(JSON.parse(data[key]));
        }
        setListData(_listData);
      } else {
        message.error('获取列表失败');
      }
    }).catch(()=>{
      setIsListLoading(false);
      message.error('获取列表失败');
    });
  };
  // 打开新增面板
  const addNewPage = () => {
    setDrawerStatus('add');
    setShowDrawer(true);
  };
  // 打开编辑面板
  const openEditDrawer = (row:any) => {
    let _formData = deepClone(row);
    setFormData(_formData);
    setDrawerStatus('edit');
    setShowDrawer(true);
  };
  // 关闭抽屉
  const closeDrawer = () => {
    setFormData(initFormData());
    setShowDrawer(false);
  };
  // 修改表单项
  const changeFormItem = (page: string, type:string,value:string,index?:number) => {
    const _formData = deepClone(formData);
    const _finished = _formData.finished;
    const _notFinished = _formData.notFinished;
    const _index = index || 0;
    // 公共配置
    if (page === 'common') {
      _formData[type] = value;
    } else if (page === 'finished') { // 已开户未交易用户
      switch (type) {
        case 'productTabDesc':
          _finished.productRecommendProducts[_index].tabDesc = value;
          break;
        case 'productReason':
          _finished.productRecommendProducts[_index].reason = value;
          break;
        case 'productFundCode':
          _finished.productRecommendProducts[_index].fundCode = value;
          break;
        case 'productFundTags':
          _finished.productRecommendProducts[_index].fundTags = value;
          break;
        case 'productDetailUrl':
          _finished.productRecommendProducts[_index].detailUrl = value;
          break;
        case 'guidanceLink':
          _finished.newPersonGuidanceEntrys[_index].link = value;
          break;
        default:
          _finished[type] = value;
          break;
      }
    } else if (page === 'notFinished') { // 未开户用户
      switch (type) {
        case 'KHGuidanceLink':
          _notFinished.accountOpeningGuidance.link = value;
          break;
        case 'XRGuidanceLink':
          _notFinished.newPersonGuidanceEntrys[_index].link = value;
          break;
        default:
          _notFinished[type] = value;
          break;
      }
    }
    setFormData(_formData);
  };
  /**
   * @param options 
   * @param page 当前页面。已开户：finished。未开户：notFinished。
   * @param type 更改的表单项
   * @param index 
   * @returns 
   */
  const uploadImg = (options: any, page: string, type: string,index?:number)=> {
    const _index = index || 0;
    if (options.file.size > 5120000) {
        message.error('文件大小不得超过5M');
        return;
    }
    setIsUploading(true);
    message.info('图片上传中');
    let params = new FormData();
    params.append('file', options.file);
    newCommonUpload(params).then((res:any)=>{
        if (res.status_code === 0) {
          let _url = '';
          if (res.data.includes('http')) {
            //兼容以前
            _url = res.data.replace('http://', 'https://');
          } else {
            //区分正式环境、测试环境
            if (
              window.location.hostname.includes('localhost') ||
              window.location.hostname.includes('febs.')
            ) {
              _url = 'https://testo.thsi.cn/' + res.data;
            } else {
              _url = 'https://o.thsi.cn/' + res.data;
            }
          }
          let _formData = deepClone(formData);
          switch (true) {
            case page === 'finished' && type === 'fundRewardImg':
              _formData.finished.fundRewardImg = _url;
              break;
            case page === 'finished' && type === 'guidanceImg':
              _formData.finished.newPersonGuidanceEntrys[_index].img = _url;
              break;
            case page === 'notFinished' && type === 'headerImg':
              _formData.notFinished.headerImg = _url;
              break;
            case page === 'notFinished' && type === 'KHGuidanceImg':
              _formData.notFinished.accountOpeningGuidance.img = _url;
              break;
            case page === 'notFinished' && type === 'XRGuidanceImg':
              _formData.notFinished.newPersonGuidanceEntrys[_index].img = _url;
              break;
            default:
              break;
          }
          setFormData(_formData);
          message.success('上传成功');
        } else {
            message.error(res.status_msg);
        }
        setIsUploading(false);
    }).catch(()=>{
        message.error('上传失败');
        setIsUploading(false);
    });
  };
  // 增加/减少入口
  const changeEntryNum = (page: string, type: string)=>{
    const _formData = deepClone(formData);
    switch (type) {
      case 'add':
          _formData[page].newPersonGuidanceEntrys.push({
            img: '',
            link: '',
          });
          break;
      case 'reduce':
          if (_formData[page].newPersonGuidanceEntrys.length > 0) {
            _formData[page].newPersonGuidanceEntrys.pop();
          } else {
              message.error('当前入口数量为0');
          }
          break;
      default:
          break;
    }
    setFormData(_formData);
  };
  // 保存
  const handleSave = () => {
    if (checkFormData()) {
      setIsSaveLoading(true);
      postHash({
        key: HASHKEY,
        propName: formData.pageId,
        value: JSON.stringify(formData)
      }).then((res:any)=>{
        setIsSaveLoading(false);
        if (res.code === '0000') {
          closeDrawer();
          handleGetListData();
          message.success('保存成功');
        } else {
          message.error('保存失败');
        }
      }).catch(()=>{
        setIsSaveLoading(false);
        message.error('获取列表失败');
      })
    }
  };
  // 表单校验
  const checkFormData= () => {
    let isPass = false;
    switch (true) {
      case formData.pageId === '':
        message.error('请填写页面id');
        break;
      case formData.finished.fundRewardImg === '': 
        message.error('请上传基金奖励图片');
        break;
      case formData.finished.fundRewardLink === '': 
        message.error('请填写基金奖励跳转链接');
        break;
      case formData.finished.productRecommendTitle === '': 
        message.error('请填写产品推荐模块标题');
        break;
      case formData.finished.productRecommendReduceNum === '': 
        message.error('请填写新人立减金额');
        break;
      case !formData.finished.productRecommendProducts.every((item,index)=>{return item.tabDesc && item.reason && item.fundCode && item.fundTags}): 
        message.error('请填写产品推荐模块产品信息');
        break;
      case formData.finished.newPersonGuidanceTitle === '': 
        message.error('请填写已开户页面新人引导模块标题');
        break;
      case !formData.finished.newPersonGuidanceEntrys.every((item,index)=>{return item.img && item.link}): 
        message.error('请完善已开户页面新人引导模块信息');
        break;
      case formData.notFinished.headerImg === '': 
        message.error('请上传头部背景图');
        break;
      case formData.notFinished.accountOpeningGuidance.img === '': 
        message.error('请上传开户引导图片');
        break;
      case formData.notFinished.accountOpeningGuidance.link === '': 
        message.error('请填写开户引导跳转链接');
        break;
      case formData.notFinished.newPersonGuidanceTitle === '': 
        message.error('请填写未开户页面新人引导模块标题');
        break;
      case !formData.notFinished.newPersonGuidanceEntrys.every((item,index)=>{return item.img && item.link}): 
        message.error('请完善未开户页面新人引导模块信息');
        break;
      default:
        isPass = true;
        break;
    }
    return isPass;
  };
  // 深拷贝对象/数组
  const deepClone = (obj:any) => {
     return JSON.parse(JSON.stringify(obj));
  };

  return (
    <div className={styles['account-opening-result']}>
      <Spin spinning={isListLoading}>
        {/* 新增页面 */}
        <Button type='primary' onClick={()=>{addNewPage()}}>新增页面</Button>
        {/* 列表 */}
        <Table
          style={{marginTop: '20px'}}
          columns={tableColumns}
          dataSource={listData}
          pagination={false}
          rowKey={record => record.pageId}
        />
        {/* 编辑面板 */}
        <Drawer
          className="account-opening-result-drawer"
          title={drawerStatus === 'add' ? '新增' : '编辑'}
          placement="right"
          width="1200"
          maskClosable={false}
          destroyOnClose={true}
          onClose={closeDrawer}
          visible={showDrawer}
        >
          {/* 页面id */}
          <Row gutter={[40,14]}>
            <Col span={12}>
              <div style={{fontWeight: '600'}}><b style={{color: '#fe5d4e'}}>*</b>页面id：</div>
              <Input placeholder='请填入页面id' value={formData.pageId} onChange={(e)=>{changeFormItem('common','pageId',e.target.value)}} disabled={drawerStatus === 'edit'}></Input>
            </Col>
          </Row>
          {/* 备注 */}
          <Row gutter={[40,14]}>
            <Col span={12}>
              <div style={{fontWeight: '600'}}>备注：</div>
              <Input placeholder='请填入备注' value={formData.remarks} onChange={(e)=>{changeFormItem('common','remarks',e.target.value)}}></Input>
            </Col>
          </Row>
          <Divider />
          {/* 已开户未交易页面 */}
          <div>
            <Row gutter={[40,14]} style={{fontWeight: '600', fontSize: '20px'}}><Col>已开户未交易用户</Col></Row>
            {/* 基金奖励模块 */}
            <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col>基金奖励：</Col></Row>
            {/* 基金奖励图片 */}
            <Row gutter={[40,14]}>
              <Col span={8}><b style={{color: '#fe5d4e'}}>* </b>基金奖励图片（尺寸686×446）：</Col>
            </Row>
            <Row gutter={[40,14]}>
              <Col span={8} style={{alignItems: 'flex-end'}}>
                <div className={classnames(styles['fund-reward-img'],'fund-reward-img')}>
                  <img style={{width: '100%',height: '100%',display: formData.finished.fundRewardImg ? '' : 'none'}} src={formData.finished.fundRewardImg} alt=""/>
                </div>
                <Upload
                  customRequest={(options)=>{uploadImg(options,'finished','fundRewardImg')}}
                  showUploadList={false}
                >
                  <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                </Upload>
              </Col>
            </Row>
            {/* 基金奖励跳转链接 */}
            <Row gutter={[40,14]}>
              <Col span={12}>
                <div><b style={{color: '#fe5d4e'}}>*</b>基金奖励跳转链接：</div>
                <Input placeholder='请输入基金奖励跳转链接' value={formData.finished.fundRewardLink} onChange={(e)=>{changeFormItem('finished','fundRewardLink',e.target.value)}}></Input>
              </Col>
            </Row>
            {/* 产品推荐模块 */}
            <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col>产品推荐模块：</Col></Row>
            {/* 模块标题 */}
            <Row gutter={[40,14]}>
              <Col span={12}>
                <div><b style={{color: '#fe5d4e'}}>*</b>产品推荐模块标题：</div>
                <Input placeholder='请输入模块标题' value={formData.finished.productRecommendTitle} onChange={(e)=>{changeFormItem('finished','productRecommendTitle',e.target.value)}}></Input>
              </Col>
            </Row>
            {/* 新人立减金额 */}
            <Row gutter={[40,14]}>
              <Col span={12}>
                <div><b style={{color: '#fe5d4e'}}>*</b>新人立减金额：</div>
                <Input placeholder='请输入新人立减金额' value={formData.finished.productRecommendReduceNum} onChange={(e)=>{changeFormItem('finished','productRecommendReduceNum',e.target.value)}}></Input>
              </Col>
            </Row>
            {
              formData.finished?.productRecommendProducts?.map((item, index)=>{
                return (
                  <div style={{border: '1px solid #d0d0d0',borderRadius: '8px',margin: '20px 0',padding: '20px 10px'}}>
                    {/* tab文案 */}
                    <Row gutter={[40,14]}>
                      <Col span={12}>
                        <div><b style={{color: '#fe5d4e'}}>*</b>tab{index + 1}文案：</div>
                        <Input placeholder='请输入tab文案' value={formData.finished.productRecommendProducts[index].tabDesc} onChange={(e)=>{changeFormItem('finished','productTabDesc',e.target.value, index)}}></Input>
                      </Col>
                    </Row>
                    {/* 推荐理由 */}
                    <Row gutter={[40,14]}>
                    <Col span={24}>
                      <div><b style={{color: '#fe5d4e'}}>*</b>推荐理由：</div>
                      <TextArea autoSize={{minRows: 2, maxRows: 10}} value={formData.finished.productRecommendProducts[index].reason} onChange={(e)=>{changeFormItem('finished','productReason',e.target.value, index)}}/>
                    </Col>
                    </Row>
                    {/* 基金代码 */}
                    <Row gutter={[40,14]}>
                      <Col span={12}>
                          <div><b style={{color: '#fe5d4e'}}>*</b>基金代码：</div>
                          <Input placeholder='请输入基金代码' value={formData.finished.productRecommendProducts[index].fundCode} onChange={(e)=>{changeFormItem('finished','productFundCode',e.target.value, index)}}></Input>
                      </Col>
                    </Row>
                    {/* 基金标签 */}
                    <Row gutter={[40,14]}>
                    <Col span={12}>
                        <div><b style={{color: '#fe5d4e'}}>*</b>基金标签(多个标签用逗号分隔)：</div>
                        <Input placeholder='请输入基金标签' value={formData.finished.productRecommendProducts[index].fundTags} onChange={(e)=>{changeFormItem('finished','productFundTags',e.target.value, index)}}></Input>
                    </Col>
                    </Row>
                    {/* 详情跳转链接 */}
                    <Row gutter={[40,14]}>
                    <Col span={12}>
                        <div>详情跳转链接：</div>
                        <Input placeholder='请输入详情跳转链接' value={formData.finished.productRecommendProducts[index].detailUrl} onChange={(e)=>{changeFormItem('finished','productDetailUrl',e.target.value, index)}}></Input>
                    </Col>
                    </Row>
                  </div>
                )
              })
            }
            {/* 新人引导模块 */}
            <Row gutter={[40,14]} style={{fontWeight: '600'}}>
              <Col>
                <span style={{color: '#f00'}}>*</span>新人引导模块：
                <Icon type="minus-square" style={{cursor:'pointer',marginRight: '10px'}} onClick={()=>{changeEntryNum('finished','reduce')}}/>
                <Icon type="plus-square" style={{cursor:'pointer'}} onClick={()=>{changeEntryNum('finished','add')}}/>
              </Col>
            </Row>
            {/* 新人引导模块标题 */}
            <Row gutter={[40,14]}>
              <Col span={12}>
                  <div><b style={{color: '#fe5d4e'}}>*</b>新人引导模块标题：</div>
                  <Input placeholder='请输入模块标题' value={formData.finished.newPersonGuidanceTitle} onChange={(e)=>{changeFormItem('finished','newPersonGuidanceTitle',e.target.value)}}></Input>
              </Col>
            </Row>
            {
              formData.finished?.newPersonGuidanceEntrys?.map((item, index)=>{
                return (
                  <div style={{border: '1px solid #d0d0d0',borderRadius: '8px',margin: '20px 0',padding: '20px 10px'}}>
                    {/* 新人引导入口图片 */}
                    <Row gutter={[40,14]}>
                      <Col span={8}><b style={{color: '#fe5d4e'}}>* </b>入口{index + 1}片（尺寸686×140）：</Col>
                    </Row>
                    <Row gutter={[40,14]}>
                      <Col span={8} style={{alignItems: 'flex-end'}}>
                        <div className={classnames(styles['guidance-entry-img'],'guidance-entry-img')}>
                            <img style={{width: '100%',height: '100%',display: formData.finished.newPersonGuidanceEntrys[index].img ? '' : 'none'}} src={formData.finished.newPersonGuidanceEntrys[index].img} alt=""/>
                        </div>
                        <Upload
                          customRequest={(options)=>{uploadImg(options,'finished','guidanceImg',index)}}
                          showUploadList={false}
                        >
                          <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                        </Upload>
                      </Col>
                    </Row>
                    {/* 跳转链接 */}
                    <Row gutter={[40,14]}>
                      <Col span={12}>
                        <div><b style={{color: '#fe5d4e'}}>*</b>跳转链接：</div>
                        <Input placeholder='请输入跳转链接' value={formData.finished.newPersonGuidanceEntrys[index].link} onChange={(e)=>{changeFormItem('finished','guidanceLink',e.target.value,index)}}></Input>
                      </Col>
                    </Row>
                  </div>
                )
              })
            }

          </div>
          <Divider />
          {/* 未开户用户页面 */}
          <div>
            <Row gutter={[40,14]} style={{fontWeight: '600', fontSize: '20px'}}><Col>未开户用户</Col></Row>
            {/* 头部背景图 */}
            <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><b style={{color: '#fe5d4e'}}>*</b>头部背景图（尺寸750*898）：</Col></Row>
            <Row gutter={[40,14]}>
              <Col span={8} style={{alignItems: 'flex-end'}}>
                <div className={classnames(styles['header-img'],'header-img')}>
                  <img style={{width: '100%',height: '100%',display: formData.notFinished.headerImg ? '' : 'none'}} src={formData.notFinished.headerImg} alt=""/>
                </div>
                <Upload
                  customRequest={(options)=>{uploadImg(options,'notFinished','headerImg')}}
                  showUploadList={false}
                >
                  <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                </Upload>
              </Col>
            </Row>
            {/* 开户引导模块 */}
            <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><b style={{color: '#fe5d4e'}}>*</b>开户引导模块：</Col></Row>
            {/* 开户引导图片 */}
            <Row gutter={[40,14]}>
              <Col span={8}><b style={{color: '#fe5d4e'}}>* </b>开户引导图片（尺寸686*706）：</Col>
            </Row>
            <Row gutter={[40,14]}>
              <Col span={8} style={{alignItems: 'flex-end'}}>
                <div className={classnames(styles['guidance-KH-img'],'guidance-KH-img')}>
                  <img style={{width: '100%',height: '100%',display: formData.notFinished.accountOpeningGuidance.img ? '' : 'none'}} src={formData.notFinished.accountOpeningGuidance.img} alt=""/>
                </div>
                <Upload
                  customRequest={(options)=>{uploadImg(options,'notFinished','KHGuidanceImg')}}
                  showUploadList={false}
                >
                  <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                </Upload>
              </Col>
            </Row>
            {/* 开户引导跳转链接 */}
            <Row gutter={[40,14]}>
              <Col span={12}>
                <div><b style={{color: '#fe5d4e'}}>*</b>开户引导跳转链接：</div>
                <Input placeholder='请输入开户引导跳转链接' value={formData.notFinished.accountOpeningGuidance.link} onChange={(e)=>{changeFormItem('notFinished','KHGuidanceLink',e.target.value)}}></Input>
              </Col>
            </Row>
             {/* 新人引导模块 */}
             <Row gutter={[40,14]} style={{fontWeight: '600'}}>
              <Col>
                <span style={{color: '#f00'}}>*</span>新人引导模块：
                <Icon type="minus-square" style={{cursor:'pointer',marginRight: '10px'}} onClick={()=>{changeEntryNum('notFinished','reduce')}}/>
                <Icon type="plus-square" style={{cursor:'pointer'}} onClick={()=>{changeEntryNum('notFinished','add')}}/>
              </Col>
            </Row>
            {/* 新人引导模块标题 */}
            <Row gutter={[40,14]}>
              <Col span={12}>
                  <div><b style={{color: '#fe5d4e'}}>*</b>新人引导模块标题：</div>
                  <Input placeholder='请输入模块标题' value={formData.notFinished.newPersonGuidanceTitle} onChange={(e)=>{changeFormItem('notFinished','newPersonGuidanceTitle',e.target.value)}}></Input>
              </Col>
            </Row>
            {
              formData.notFinished?.newPersonGuidanceEntrys?.map((item, index)=>{
                return (
                  <div style={{border: '1px solid #d0d0d0',borderRadius: '8px',margin: '20px 0',padding: '20px 10px'}}>
                    {/* 新人引导入口图片 */}
                    <Row gutter={[40,14]}>
                      <Col span={8}><b style={{color: '#fe5d4e'}}>* </b>入口{index + 1}片（尺寸686×140）：</Col>
                    </Row>
                    <Row gutter={[40,14]}>
                      <Col span={8} style={{alignItems: 'flex-end'}}>
                        <div className={classnames(styles['guidance-entry-img'],'guidance-entry-img')}>
                          <img style={{width: '100%',height: '100%',display: formData.notFinished.newPersonGuidanceEntrys[index].img ? '' : 'none'}} src={formData.notFinished.newPersonGuidanceEntrys[index].img} alt=""/>
                        </div>
                        <Upload
                          customRequest={(options)=>{uploadImg(options,'notFinished','XRGuidanceImg',index)}}
                          showUploadList={false}
                        >
                          <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                        </Upload>
                      </Col>
                    </Row>
                    {/* 跳转链接 */}
                    <Row gutter={[40,14]}>
                      <Col span={12}>
                        <div><b style={{color: '#fe5d4e'}}>*</b>跳转链接：</div>
                        <Input placeholder='请输入跳转链接' value={formData.notFinished.newPersonGuidanceEntrys[index].link} onChange={(e)=>{changeFormItem('notFinished','XRGuidanceLink',e.target.value,index)}}></Input>
                      </Col>
                    </Row>
                  </div>
                )
              })
            }
          </div>
          <Divider />
          {/* 保存 */}
          <Button style={{marginTop:'15px',marginRight: '15px'}} type='primary' loading={isSaveLoading} onClick={()=>{handleSave()}}>保存</Button>
          <Button type="danger" onClick={()=>{closeDrawer()}}>取消</Button>
        </Drawer>
      </Spin>
    </div>
  )
}
