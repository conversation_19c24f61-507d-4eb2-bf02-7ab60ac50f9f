import React, { useState, useEffect, FC,useContext} from 'react';
import classnames from 'classnames';
import { ConfigData } from '../../type';
import { Input, Select, Switch, Radio,AutoComplete } from 'antd';
import InputTitle from '../InputTitle';
import UploadImg from '../UploadImg';
import { mapPropToName } from './helper';
import { TopicFundContext } from '../../context';
const { Search } = Input;

export interface IProps {
  data: ConfigData['sectorAnalyze'];
  onChange: (data: ConfigData['sectorAnalyze']) => void;
  disabled: boolean;
}
const SectorAnalyze: FC<IProps> = ({ data, onChange, disabled }) => {

  const topicFundList=useContext(TopicFundContext);
  const handleChange = (name, value) => {
    onChange({
      ...data,
      [name]: value,
    });
  };
 
  return (
    <div>
      <h3>板块解读</h3>

      {/* <div>
        <InputTitle title={mapPropToName['topicFundName']} />
        <Select
          value={data.topicFundId}
          onChange={v => handleChange('topicFundId', v)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={}
        />
      </div>
      <div>
        <InputTitle title={mapPropToName['assessFundName']} />
        <Select disabled={disabled} style={{ marginBottom: 10, width: 300 }} addonBefore={} />
      </div> */}
      <AutoComplete
        value={data.topicFundId}
        onChange={v => {
          console.log('topicFundId', v)
          handleChange('topicFundId', v)
        }}
        dataSource={topicFundList?.filter(item=>{
          const hasId=item.themeCode.indexOf(data.topicFundId)!==-1;
          const hasName=item.themeName.indexOf(data.topicFundId)!==-1;
          return hasId||hasName
        }).map(item=>{
          return <AutoComplete.Option key={item.themeCode} value={item.themeCode}><span>{item.themeName}({item.themeCode})</span></AutoComplete.Option>
        })}
        optionLabelProp="value"
        disabled={disabled}
        style={{ marginBottom: 10 }}
       
      >
        <Input
         addonBefore={<InputTitle title={mapPropToName['topicFundId']} isRequired={false}/>}
       
      />
      </AutoComplete>
      {/* <Input
        value={data.topicFundId}
        onChange={e => handleChange('topicFundId', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
       
      /> */}
      <Input
        value={data.prosperityId}
        onChange={e => handleChange('prosperityId', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['prosperityId']} isRequired={false}/>}
      />
      <Input
        value={data.assessId}
        onChange={e => handleChange('assessId', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['assessId']} isRequired={false}/>}
        addonAfter={
          <Radio.Group
            disabled={disabled}
            onChange={e => handleChange('indexType', e.target.value)}
            value={data.indexType}
          >
            <Radio value="PE">PE</Radio>
            <Radio value="PB">PB</Radio>
          </Radio.Group>
        }
      />
      {/* <div>
        <InputTitle title={mapPropToName['indexType']} isRequired={false} />
        <Select
          value={data.indexType}
          onChange={v => handleChange('indexType', v)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
        >
          <Option value="PE">PE</Option>
          <Option value="PB">PB</Option>
        </Select>
      </div> */}
      <Input
        value={data.moreLink}
        onChange={e => handleChange('moreLink', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['moreLink']} />}
      />
      {/* <Input
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle  />}
        addonAfter={'*格式（由新功能上线的版本和跳转组成）：version=5.63.01###action=gslist'}
      /> */}

      <div>
        <InputTitle title={mapPropToName['lineChartOpen']} />
        <Switch
          disabled={disabled}
          checked={data.lineChartOpen}
          onChange={v => handleChange('lineChartOpen', v)}
        ></Switch>
      </div>
      <div>
        <InputTitle title={mapPropToName['indexOpen']} />
        <Switch
          disabled={disabled}
          checked={data.indexOpen}
          onChange={v => handleChange('indexOpen', v)}
        ></Switch>
      </div>
    </div>
  );
};
export default SectorAnalyze;
