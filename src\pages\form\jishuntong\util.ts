import { safeValueShow } from '../smsPlatform/fn';
import { ManagerFeeInfoList, SaleInfoList } from './type';

export const generateManagerFeeInfoListInTable = (text: ManagerFeeInfoList[]) => {
  let renderText = '';
  (text || []).forEach((element, index) => {
    renderText += `${index === 0 ? '' : '/'}${safeValueShow(element.fundName)}(${safeValueShow(
      element.fundCode,
    )})${element.ratio * 100}%`;
  });
  return renderText;
};
export const generateSaleInfoListInTable = (text: SaleInfoList[]) => {
  let renderText = '';
  (text || []).forEach((element, index) => {
    renderText += `${index === 0 ? '' : '/'}${safeValueShow(element.saleName)}(${safeValueShow(
      element.divideRatio * 100,
    )}%)`;
  });
  return renderText;
};
export function handleSuccessWithFile(res: any) {
  console.log('qaq222');
  if (res.data.size === 0) {
    return Promise.resolve({
      success: false,
      message: '�ļ�������',
      statusCode: 200,
    });
  }
  const { statusText, status, data } = res;
  let result: resultInterface = {};

  console.log('qaq');
  let reader = new FileReader();
  reader.readAsDataURL(data);
  reader.onload = function(e) {
    try {
      let a: any = document.createElement('a');
      let fileName: string = res.headers['content-disposition'].split('=');
      fileName = decodeURI(fileName[fileName.length - 1]);
      fileName = fileName.replace(/"/g, '');
      console.log('fileName', fileName);
      a.download = fileName;
      a.href = e.target.result;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (e) {
      console.log(e.message);
    }
  };

  return Promise.resolve({
    success: true,
    message: statusText,
    statusCode: status,
    ...result,
  });
}
