/**
 * 黄金宝 - 基础配置 - 天天领黄金 - 推荐基金
 */
import React, { useEffect } from 'react';
import styles from './index.less';
import { Typography, Form, Select, Input, Button } from 'antd';
import { FormComponentProps } from 'antd/es/form';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

type Props = {
  formRef: { current: { fund: { [k: string]: FormComponentProps['form'] } } };
  data: { [k: string]: unknown };
};

//
const RecommendedFundForm: React.FunctionComponent<Props & FormComponentProps> = ({
  form,
  formRef,
  data,
}) => {
  //
  useEffect(() => {
    const id = data._id;

    formRef.current.fund[id] = form;

    return () => {
      delete formRef.current.fund[id];
    };
  }, [/* form,  */ data]);

  // prettier-ignore
  return (
    <Form
      className={ styles['recommended-fund-form'] }
      { ...formItemLayout }
      layout="horizontal"
    >
      <Form.Item label="首购推荐基金">
        {
          form.getFieldDecorator('fundCode', {
            initialValue: data.fundCode,
            rules: [{ required: true, message: '请输入首购推荐基金代码' }],
            normalize: value => value ? value.trim() : value,
          })(
            <Input
              placeholder="请输入首购推荐基金代码"
            />,
          )
        }
      </Form.Item>
      <Form.Item label="首购推荐基金话术" extra="建议不多于15个汉字">
        {
          form.getFieldDecorator('text', {
            initialValue: data.text,
            rules: [{ required: true, message: '请输入首购推荐基金话术' }],
            normalize: value => value ? value.trim() : value,
          })(
            <Input
              placeholder="请输入首购推荐基金话术"
            />,
          )
        }
      </Form.Item>
      <Form.Item label="首购推荐基金标签" extra="多个标签用逗号分隔，建议不多于15个汉字">
        {
          form.getFieldDecorator('label', {
            initialValue: data.label,
            rules: [{ required: true, message: '请输入首购推荐基金标签' }],
            normalize: value => value ? value.trim() : value,
          })(
            <Input
              placeholder="请输入首购推荐基金标签"
            />,
          )
        }
      </Form.Item>
    </Form>
  )
};

export default Form.create({ name: '' })(RecommendedFundForm);
