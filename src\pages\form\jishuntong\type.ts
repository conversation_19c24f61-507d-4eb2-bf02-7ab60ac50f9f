export const strategyMap = {
  '2': '销售',
  '3': '商务',
  '4': '合作机构',
  '1': 'Tob',
};
export const strategyKeyMap = {
  Tob: '1',
  销售: '2',
  商务: '3',
  合作机构: '4',
};
export const operateMap = {
  1: '添加',
  2: '修改',
  3: '删除',
  '-1': '初始化',
};
export enum operateEnum {
  ADD = '1',
  UPDATE = '2',
  DELETE = '3',
}
export const channelMap = {
  o32: 'O32',
  jst: '基顺通',
};
export interface List {
  /** 销售人员id */
  id: number;
  /** 销售人员名字 */
  saleName: string;
  /** 员工号(目前应该没用) */
  workNo: string;
  /** 公司代码 */
  companyCode: string;
  /** 公司名称 */
  companyName: string;
  /** 销售人员电话 */
  telephoneNo: string;
  /** 策略代码 */
  strategyCode: string;
  /** 是否有效 */
  isValid: string;
}

/** @description 接口地址: /report/fofSale/query/list */
export interface salesManQueryProps {
  /** 销售人员列表 */
  list: List[];
  /** 下一次查询需要的最小id */
  minId: number;
}
export interface CompanyList {
  /** 公司代码 */
  companyCode: string;
  /** 公司名称 */
  companyName: string;
}

/** @description 接口地址: /report/fofSale/query/list */
export interface salesManFilterQueryProps {
  /** 策略代码列表 */
  strategyCodeList: string[];
  /** 公司列表 */
  companyList: CompanyList[];
}
export interface SaleInfoList {
  /** 销售人员id */
  saleId: string;
  /** 销售人员名称 */
  saleName: string;
  /** 分成比例 */
  divideRatio: number;
  isValid?: boolean;
}

export interface ManagerFeeInfoList {
  /** 基金代码 */
  fundCode: string;
  /** 基金名称 */
  fundName: string;
  /** 比例 */
  ratio: number;
}

export interface ProductInfoList {
  /** 产品id */
  fofId: string;
  /** 投资账户id */
  custId: string;
  /** 交易账户 */
  transactionAccountId: string;
  /** 投资账户名称 */
  accountName: string;
  /** 投管人名称 */
  investorName: string;
  /** 账户类型 */
  accountType: string;
  /** 账户类型名称 */
  accountTypeName: string;
  /** 托管人名称 */
  bankName: string;
  /** 保有量 */
  holdings: number;
  /** 货币保有量 */
  moneyHoldings: number;
  /** 非货保有量 */
  stockHoldings: number;
  /** 债券保有量 */
  bondFundHoldings: string;
  /** 混合保有量 */
  hybridFundHoldings: string;
  /** 股票保有量 */
  stockFundHoldings: string;
  /** 理财产品保有量 */
  financeHoldings: string;
  /** 机构类型 */
  institutionType: string;
  /** 机构类型名称 */
  institutionTypeName: string;
  /** 投资状态，已投资时{@code true} */
  investment: boolean;
  /** 交易通道代码 */
  tradeChannelCode: string;
  /** 交易通道名称 */
  tradeChannelName: string;
  /** 策略代码 */
  strategyCode: string;
  /** 策略名称 */
  strategyName: string;
  /** 销售人员信息列表 */
  saleInfoList: SaleInfoList[];
  /** 返点比例 */
  returnRatio: number;
  /** 返点比例计算公式 */
  returnRatioExpression: string;
  /** 管理费分成信息 */
  managerFeeInfoList: ManagerFeeInfoList[];
  /** 货基赎回交割日 */
  moneySetDayType: string;
  /** 货基赎回交割日名称 */
  moneySetDayTypeName: string;
  /** 非货赎回交割日 */
  stockSetDayType: string;
  /** 非货赎回交割日名称 */
  stockSetDayTypeName: string;
  /** 超级转换 */
  superConversion: string;
  /** 超级转换名字 */
  superConversionName: string;
  /** 超转差额处理方式 */
  scFlag: string;
  /** 超转差额处理方式名字 */
  scFlagName: string;
  /** 开户时间 */
  openAccountDate: string;
}

export interface Result {
  /** 投管人数量 */
  investorCount: number;
  /** 投资账户数量 */
  accountCount: number;
  /** 保有量 */
  holdings: number;
  /** 非货保有量 */
  stockHoldings: string;
  /** 货币保有量 */
  moneyHoldings: string;
  /** 理财产品保有量(资管产品保有量) */
  financeHoldings: string;
}

/** @description 接口地址: /fof-server/report/fofProduct/query/list */
export interface fofProductProps {
  /** 产品信息列表 */
  productInfoList: ProductInfoList[];
  /** 下一次查询需要用到的最小id */
  minId: number;
  result: Result;
}
export interface StrategyInfoList {
  /** 策略代码 */
  strategyCode: string;
  /** 策略名称 */
  strategyName: string;
}

export interface SaleInfoListZ {
  /** 销售人员名称 */
  saleName: string;
  /** 销售人员id */
  saleId: number;
}

export interface OrganizationInfoList {
  /** 组织id */
  custId: string;
  /** 组织名称 */
  accountName: string;
}

export interface InvestorInfoList {
  /** 客户编号 */
  custId: string;
  /** 客户名称 */
  investorName: string;
}

export interface TradeChannelList {
  /** 交易通道枚举 */
  code: string;
  /** 交易通道名称 */
  name: string;
}

export interface AccountTypeList {
  /** 账户类型代码 */
  code: string;
  /** 账户类型名称 */
  name: string;
}

/** @description 接口地址: /fof-server/report/fofProduct/get/condition */
export interface ClientDatIndicator {
  /** 策略信息列表 */
  strategyInfoList: StrategyInfoList[];
  /** 销售人员信息列表 */
  saleInfoList: SaleInfoListZ[];
  /** 投管人信息列表 */
  accountInfoList: OrganizationInfoList[];
  /** 投管人账户信息 */
  investorInfoList: InvestorInfoList[];
  /** 交易通道列表 */
  tradeChannelList: TradeChannelList[];
  /** 账户类型列表 */
  accountTypeList: AccountTypeList[];
}
export const showFiltersMap = {
  strategy: true,
  sale: true,
  account: true,
  investor: true,
  tradeChannel: true,
  accountType: true,
};
export const maintainFilterMap = {
  strategy: false,
  sale: false,
  account: true,
  investor: true,
  tradeChannel: false,
  accountType: false,
};
export type showFiltersMapProps = {
  strategy: boolean;
  sale: boolean;
  account: boolean;
  investor: boolean;
  tradeChannel: boolean;
  accountType: boolean;
};
/** @description 接口地址: /fof-server/report/fofProduct/get/total-result */
export interface fofProductResult {
  /** 投资账户数量 */
  accountCount: number;
  /** 资管产品保有量 */
  financeHoldings: number;
  /** 投管人数量 */
  investorCount: number;
  /** 保有量 */
  holdings: number;
  /** 非货保有量 */
  stockHoldings: number;
  /** 货币保有量 */
  moneyHoldings: number;
}
