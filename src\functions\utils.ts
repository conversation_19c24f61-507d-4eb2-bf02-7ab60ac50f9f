import pathToRegexp from 'path-to-regexp'

/**
 * @function each
 * @description **each(array, fn)** traverse Array
 * @param {Array} array traverse array
 * @param {Function} handle function
 * @example
 * var arr = [1, 2, 3];
 * each(arr, function (i) {console.log(i)});
 * // 1
 * // 2
 * // 3
 */
export function each(array: any, fn: Function) {
    for (let i = 0, len = array.length; i < len; i++) {
        fn(array[i], i)
    }
}

/**
 * Convert an array to a tree-structured array.
 * @param {array} array     The Array need to Converted.
 * @param {string} id        The alias of the unique ID of the object in the array.
 * @param {string} parentId       The alias of the parent ID of the object in the array.
 * @param {string} children  The alias of children of the object in the array.
 * @return {array} Return a tree-structured array.
 */
export function arrayToTree(
    array: any[],
    id = 'id',
    parentId = 'pid',
    children = 'children'
) {
    const result: any[] = [];
    const hash: any = {};
    const data = _.fn.cloneObjDeep(array)

    data.forEach((item: any, index: number) => {
        hash[data[index][id]] = data[index]
    })

    data.forEach((item: any) => {
        const hashParent = hash[item[parentId]]
        if (hashParent) {
            !hashParent[children] && (hashParent[children] = [])
            hashParent[children].push(item)
        } else {
            result.push(item)
        }
    })
    return result
}


/**
 * Whether the path matches the regexp if the language prefix is ignored, https://github.com/pillarjs/path-to-regexp.
 * @param   {string|regexp|array}     regexp     Specify a string, array of strings, or a regular expression.
 * @param   {string}                  pathname   Specify the pathname to match.
 * @return  {array|null}              Return the result of the match or null.
 */
export function pathMatchRegexp(regexp: any, pathname: string) {
    return pathToRegexp(regexp).exec(pathname)
}

/**
 * In an array of objects, specify an object that traverses the objects whose parent ID matches.
 * @param   {array}     array     The Array need to Converted.
 * @param   {string}    current   Specify the object that needs to be queried.
 * @param   {string}    parentId  The alias of the parent ID of the object in the array.
 * @param   {string}    id        The alias of the unique ID of the object in the array.
 * @return  {array}    Return a key array.
 */
export function queryAncestors(array: any[], current: any, parentId: number | string, id = 'id') {
    const result = [current]
    const hashMap = new Map()
    array.forEach(item => hashMap.set(item[id], item))
  
    const getPath = (current: any) => {
      const currentParentId = hashMap.get(current[id])[parentId]
      if (currentParentId) {
        result.push(hashMap.get(currentParentId))
        getPath(hashMap.get(currentParentId))
      }
    };
  
    getPath(current)
    return result
}
