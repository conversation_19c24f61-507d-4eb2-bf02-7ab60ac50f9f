import React, { useEffect, useState, useRef } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { Button, Form, InputNumber, Row, Col, message } from 'antd';
import { WrappedFormUtils } from 'antd/es/form/Form';
import FormRender from 'form-render/lib/antd';
import api from 'api';
import { abTestForm } from './form';
import styles from './index.less';

const PayRouteAbTest = () => {
  const [valid, setValid] = useState([]);
  const [formData, setData] = useState({});

  const handleSave = () => {
    console.log(valid)
    if (!valid.length) {
      if (formData.mode == '2') {
        if (!formData.contentRules) {
          return message.error('请输入custId匹配规则内容')
        }
        if (!/^[A-Za-z0-9]+([,][A-Za-z0-9]+)*$/.test(formData.contentRules)) {
          return message.error('请输入正确的custId匹配规则内容');
        }
      }
      api.postPayRouteAb({
        value: JSON.stringify(formData)
      }).then((res) => {
        if (res.code === '0000') {
          message.success('保存成功');
        } else {
          message.error('保存失败')
        }
      })
    }
  }

  useEffect(() => {
    api.fetchPayRouteAb().then((res) => {
      if (res.code === '0000') {
        const data = JSON.parse(res.data || '{}');
        setData(data)
      }
    })
  },[])
  return <div>
    <p className={styles['title']}>网页临时abtest配置</p>
    <Button onClick={handleSave}>保存</Button>
    <FormRender 
        formData={formData}
        onChange={setData}
        onValidate={setValid}
        labelWidth={200} displayType={'row'}
        propsSchema={abTestForm}
      />
  </div>
}

type ConfigItem = {
  value: string;
  key: string;
  keyDes?: string;
  scene: 'capitalMethodScoreWeight' | 'capitalMethodExtraScore'
}
const WeightConfig = Form.create()(( { form }: { form: WrappedFormUtils}) => {
  const { setFieldsValue, getFieldDecorator } = form;
  const formValue = useRef({});
  const [configList, setConfigList] = useState<ConfigItem[]>([]);
  const [extraScoreList, setExtraScoreList] = useState<ConfigItem[]>([]);
  useEffect(() => {
    api.getPayRouteConfig({
      scene: ['capitalMethodScoreWeight', 'capitalMethodExtraScore']
    }).then((res) => {
      if (res?.code ==='0000') {
        const $extraScoreList: ConfigItem[] = [];
        const $configList: ConfigItem[] = [];
        res.data.properties.forEach((config) => {
          formValue.current[config.key] = config.value;
          if (config.scene === 'capitalMethodExtraScore') {
            $extraScoreList.push(config);
          } else {
            $configList.push(config);
          }
        })
        unstable_batchedUpdates(() => {
          setConfigList($configList);
          setExtraScoreList($extraScoreList);
        })
      } else {
        message.error('获取最优渠道权重比配置失败')
      }
    })
  }, [])
  useEffect(() => {
    if (configList) {
      setFieldsValue(formValue.current);
    }
  }, [configList]);

  const handleSave = () => {
    form.validateFields((err, data) => {
      if (!err) {
        const payProperties: ConfigItem[] = [];
        configList.forEach(config => {
          payProperties.push({
            scene: config.scene,
            key: config.key,
            value: String(data[config.key])
          })
        })
        extraScoreList.forEach(config => {
          payProperties.push({
            scene: config.scene,
            key: config.key,
            value: String(data[config.key])
          })
        })
        api.savePayRouteConfig({
          payProperties,
          editor: JSON.parse(localStorage.getItem('name') || '')
        }).then(res => {
          if (res?.code === '0000') {
            message.success('保存成功');
          } else {
            message.error(res?.message || '保存失败')
          }
        })
      }
    })
  }
  return <div>
    <p className={styles['title']}>最优渠道权重比配置</p>
    <Button onClick={handleSave}>保存</Button>
    <Form
      className={styles['weight-form']}
      labelCol={{
        span: 8, 
      }} 
      wrapperCol={{
        span: 12, 
      }}
    >
      <Row>
        <Col span={12}>
          <Row>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '10px'}}>影响权重</Col>
            <Col span={12} style={{ paddingLeft: '10px'}}>权重比</Col>
          </Row>
          {
            configList.map((config => {
              return <Form.Item label={config.keyDes} key={config.key}>
                {
                  getFieldDecorator(config.key, {
                    rules: [{ required: true, message: `请输入${config.keyDes}的权重比`}]
                  })(<InputNumber precision={1}/>)
                }
              </Form.Item>
            }))
          }
        </Col>
        <Col span={12}>
        <Row>
          <Col span={8} style={{ textAlign: 'right', paddingRight: '10px'}}>特殊加分项-渠道</Col>
          <Col span={12} style={{ paddingLeft: '10px'}}>分数</Col>
        </Row>
        {
          extraScoreList.map((config => {
            return <Form.Item label={config.keyDes} key={config.key}>
              {
                getFieldDecorator(config.key, {
                  rules: [{ required: true, message: `请输入${config.keyDes}渠道加分`}]
                })(<InputNumber min={0} precision={1}/>)
              }
            </Form.Item>
          }))
        }
        </Col>
      </Row>
    
    
    </Form>
    
  </div>
})

const PayRoute = () => {
  return <>
    <WeightConfig />
    {/* <PayRouteAbTest /> */}
  </>;
}

export default PayRoute;