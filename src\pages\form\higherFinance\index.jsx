import FORM_JSON from './form.json';
import <PERSON>actD<PERSON> from 'react-dom';
import React, { useState, useEffect } from 'react';
import { Button, message, Popconfirm } from  'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';
import uploadImg from './uploadImg'

export default function() {
    const { fetchhigherFinance, posthigherFinance, fetchRedeemConditionField,postVerifyConfig, fetchVerifyConfig} = api;
    const [init, setInit] = useState(true);
    const [formData, setData] = useState({})
    const [valid, setValid] = useState([]);

    useEffect( () => {
        getItem()
    }, [getItem, init])

    const getItem = () => {
        Promise.all([fetchhigherFinance(),fetchVerifyConfig()])
        .then( ([res,res2]) => {
            let _data = FORM_JSON.formData;
            console.log(_data)
            try {
                res = JSON.parse(res.data);
                if(res) {
                    _data = res;
                  
                    res2 && (res.verifyConfig = JSON.parse(res2.data || "{}"));
                    console.log(_data)
                    addEvent()
                }
            } catch(e) {
                console.warn(e)
            }
            setInit(true);
            setData(_data);
        }).catch((e) => {
            message.error(e.message);
        })
    }

    const addEvent = () => {
        let properties = FORM_JSON.propsSchema.properties;
        console.log(properties)
        //给标签id添加回车事件
        for(let item in properties) {
            console.log(item)
            if(item != "talkSwitch") {
                let conditions = properties[item].properties.form.items.properties.conditions,
                    field = conditions.items.properties.field;
                console.log(conditions, field)
                field["ui:options"].onPressEnter = (e) => {
                    conditionField(e, item)
                }
            }
            // //添加基金代码查询回车事件
            // if(item === "productCard") {
            //     let fundCode = properties[item].properties.form.items.properties.fundCode;
            //     console.log(fundCode)
            //     fundCode["ui:options"].onPressEnter = (e) => {
            //         console.log(e.target)
            //     }
            // }
        }
    }

    // 回车回调
    const conditionField = (e, item) => {
        let title = '';
        switch(item) {
            case("bannerConfig"): title = "banner配置"; break;
            case("discussionArea"): title = "讨论区配置"; break;
            case("financeYL"): title = "理财有料"; break;
            case("productCard"): title = "产品卡配置"; break;
        }
        if(!e.currentTarget.value) {
            message.error(`请在${title}输入标签！`)
        } else {
            let tag = e.currentTarget.value;
            fetchRedeemConditionField({
                labelNum: tag
            }).then( data => {
                console.log(data)
                if(data) {
                    let nameId, enumId; //名称和枚举对应的数组编号
                    data = (JSON.parse(data.data)).data;
                    console.log(data)
                    for (let i=0; i< data.head.length; i++) {
                        if (data.head[i].id === 'label_name') {
                            nameId = i;
                        }
                        if (data.head[i].id === 'label_enum') {
                            enumId = i;
                        }
                    }
                    let result = `名称：(${data.body[0][nameId]}); 枚举值：(${data.body[0][enumId]})`;
                    console.log(result);
                    message.success('获取成功');
                    alert(`"${title}"的标签id：${tag}查询结果为：\n${result}`)
                }
            }).catch((e) => {
                message.error("无法获取");
            })
        }
    }

    // 链接去重
    const checkUrl = (str, type) => {
        console.log(str)
        if(!!str) {
            if(type === 'image') {
                let images = str.toString().split(/\.jpg|png|bmp|tif|gif|svg|psd|webp/g);
                return images.length <= 2 ? false : true;
            } else if(type === 'url'){
                let urls = str.toString().split(/https?:\/\//g);
                return urls.length <= 2 ? false : true;
            }
        } else {
            return false;
        }
    }

    const onSubmit = () => {
        if(valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        let _formData = JSON.parse(JSON.stringify(formData));
        
        console.log(_formData);
        
        let images = false,
            urls = false;
        // 链接去重
        for(let item in _formData) {
            if(item != "talkSwitch" ) {
                console.log(_formData[item])
                _formData[item].form && _formData[item].form.map( obj => {
                    console.log("obj", obj)
                    images = obj.imgUrl ? images || checkUrl(obj.imgUrl, 'image') : images;
                    urls = obj.url ? urls || checkUrl(obj.url, 'url') : urls;
                })
                console.log(`图片是否重复：${images}`, `链接是否重复：${urls}`)
            }
        }
        if(!images && !urls) {
            const verifyConfig = _formData.verifyConfig;

            delete _formData.verifyConfig
            postVerifyConfig({
                value:JSON.stringify(verifyConfig)
            })
            posthigherFinance({
                // value: JSON.stringify(FORM_JSON.formData)
                value: JSON.stringify(_formData)
            }).then( (res) => {
                console.log(res)
                try {
                    if(res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('提交成功');
                    }
                } catch(e) {
                    message.error(e.message);
                }
            })
        } else if(images){
            message.error('一个输入框只能输入一张图片');
        } else if(urls){
            message.error('一个输入框只能输入一个跳转链接');
        }
    }

    return (
        <article>
            {
                init ?
                <article>
                    <div>
                        <span>高端理财</span>
                    </div>
                    <FormRender 
                        propsSchema={FORM_JSON.propsSchema}
                        uiSchema={FORM_JSON.uiSchema}
                        onValidate={setValid}
                        formData={formData}
                        onChange={setData}
                        displayType="row"
                        showDescIcon={true}
                        lableWidth={90}
                        widgets={{uploadImg: uploadImg}}
                    />
                    <Popconfirm
                        title={`确定要提交吗？`}
                        onConfirm={onSubmit}
                        okText="是"
                        cancelText="取消"
                    >
                        <Button>提交配置</Button>
                    </Popconfirm>
                </article>
                :
                null
            }
        </article>
    )
}