import React, { useEffect, useState } from 'react';
import { Input } from 'antd';
import styles from './index.less';
import { MailData } from '@/pages/form/mail/interface';
const { TextArea } = Input;
type FormRender = {
  onChange: Function;
  name: string;
  formData: MailData;
};

export default function({ onChange, name, formData }: FormRender) {
  const [textValue, setTextValue] = useState('');
  return (
    <div className={styles['m-text-content']}>
      <TextArea
        value={formData.textContent}
        rows={5}
        maxLength={300}
        onChange={e => {
          onChange(name, e.target.value);
          setTextValue(e.target.value);
        }}
      />
      <span className={styles['m-text-content-limit']}>{`字数 ${formData.textContent?.length ||
        textValue.length}/300`}</span>
      <p className={styles['m-text-content-ps']}>
        PS：前<span>18</span>个字将展现在消息中心的预览中，其它字在预览时为“……”这样的省略号
      </p>
    </div>
  );
}
