/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/
import React, { Fragment } from 'react';
import {Button, Input, Card, Row, message, Popconfirm, Collapse, DatePicker} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import <PERSON>actD<PERSON> from 'react-dom';
import FormR<PERSON> from 'form-render/lib/antd';
import TyjForm from "./tyjForm";

const { fetchTyjList, fetchTyjFile, fetchTyjReceivestate, fetchTyjRegularFile } = api;

@autobind
class tyj extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            activityId: '', 
            receiveActivityId: '',
            activityForms: [],  
            isNew: [],       //是否为新加入的配置   
            startTime: '',
            endTime: '',
            byTimeActivityId: ''
        }
    }

    dataInit () {
        fetchTyjList({
            type: 'query'
        }).then(data => {
            if(data.code === '0000') {
                let tyjData = [];
                let _isNew = [];
                for(let key  in data.data){
                    tyjData.push(data.data[key]);
                    _isNew.push(false);
                }
                console.log(tyjData)
                this.setState({activityForms: tyjData, isNew: _isNew});
            }
        })
    }

    addTyj () {
        let _activityForms = this.state.activityForms;
        _activityForms.push({});
        let _isNew = this.state.isNew;
        _isNew.push(true);
        this.setState({ activityForms: _activityForms, isNew: _isNew });
    }

    handleActivityId (e) {
        let _value = e.target.value;
        this.setState({activityId: _value});
    }

    downloadActivityFile () {
        if (this.state.activityId === '') {
            message.error("请输入活动号");
            return;
        } 
        fetchTyjFile({
            activityId: this.state.activityId,
            responseType: 'blob'
        }).then(res => {
            console.log(res)
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", this.state.activityId + "活动奖励列表.csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    /**
     * 通过时间获取文件
     */
    downloadTyjRegularFile () {
        if (!this.state.startTime || !this.state.endTime) {
            message.error('请填写时间');
            return;
        }
        if (!this.state.byTimeActivityId) {
            message.error('请填写FS');
            return;
        }
        console.log({
            activityId: this.state.byTimeActivityId,
            startDate: this.state.startTime,
            endDate: this.state.endTime,
            responseType: 'blob',
        })
        fetchTyjRegularFile({
            activityId: this.state.byTimeActivityId,
            startDate: this.state.startTime,
            endDate: this.state.endTime,
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", this.state.byTimeActivityId + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    handleByTimeActivityId(e) {
        let _value = e.target.value;
        this.setState({byTimeActivityId: _value});
    }

    handleReceiveActivityId (e) {
        let _value = e.target.value;
        this.setState({receiveActivityId: _value});
    }

    downloadReceiveActivityFile () {
        if (this.state.receiveActivityId === '') {
            message.error("请输入活动号");
            return;
        } 
        fetchTyjReceivestate({
            activityId: this.state.receiveActivityId,
            responseType: 'blob'
        }).then(res => {
            console.log(res)
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", this.state.receiveActivityId + "用户领取状态列表.csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    onchangeDownLoadTime (value) {
        // largeTransferStartTime
        function getTime(Date) {
            let _year = Date.getFullYear();
            let _month = Date.getMonth() + 1;
            _month = _month < 10 ? '0' + _month : _month;
            let _day = Date.getDate();
            _day = _day < 10 ? '0' + _day : _day;
            let _hour = Date.getHours();
            _hour = _hour < 10 ? '0' + _hour : _hour;
            let _minute = Date.getMinutes();
            _minute = _minute < 10 ? '0' + _minute : _minute;
            let _second = Date.getSeconds();
            _second = _second < 10 ? '0' + _second : _second;
            return `${_year}-${_month}-${_day} ${_hour}:${_minute}:${_second}`;
        }
        let _startTime = getTime(value[0]._d);
        
        let _endTime = getTime(value[1]._d);
        console.log(_startTime, _endTime)
        this.setState({
            startTime: _startTime,
            endTime: _endTime
        }, () => {console.log(this.state)})
    }

    componentDidMount () {
        this.dataInit();
    }

    render () {
        return (
          <div className='codeInterface'>
                <header className="g-mb20">
                    <section className="g-mb20">
                        <span style={{width: '200px'}}>下载活动奖励列表:</span>
                        <Input onChange={this.handleActivityId} style={{width: '300px', marginRight: '30px', marginLeft: '30px'}}></Input>
                        <Button type="primary" onClick={this.downloadActivityFile}>下载</Button>
                    </section>
                    <section className="g-mb20">
                        <span style={{width: '200px'}}>用户领取状态列表:</span>
                        <Input onChange={this.handleReceiveActivityId} style={{width: '300px', marginRight: '30px', marginLeft: '30px'}}></Input>
                        <Button type="primary" onClick={this.downloadReceiveActivityFile}>下载</Button>
                    </section>
                    <Row className="g-mb20">
                        <header className="g-mb20">
                            <span>按日期导出体验金提现名单</span>
                        </header>
                        <article style={{marginTop: '20px', marginBottom: '20px'}}>
                            <span style={{width: '50px', display: 'inline-block', marginRight: '30px'}}>时间: </span>
                            <DatePicker.RangePicker 
                            showTime 
                            onChang={(value, dateString) => {
                                console.log(value, dateString)
                            }} 
                            onOk={(value) => {
                                this.onchangeDownLoadTime(value)
                            }}
                                />
                        </article>
                        <section>
                            <span className="g-mb20" style={{width: '50px', display: 'inline-block'}}>活动Id:</span>
                            <Input 
                            onChange={this.handleByTimeActivityId} 
                            style={{width: '300px', marginRight: '30px', marginLeft: '30px'}}
                            ></Input>
                            
                            <Button
                                type="primary" 
                                onClick={this.downloadTyjRegularFile}
                            >
                                下载
                            </Button>
                        </section>
                    </Row>
              </header>
            {this.state.activityForms.map((activity, index) => (
                <section
                key={index}
                style={{width: '800px'}}>
                    <TyjForm 
                    data={activity}
                    check={this.state.isNew[index]}
                    ></TyjForm>
                </section>
            ))}
            <Button
                type="primary" 
                style={{ float: 'left' }}
                onClick={this.addTyj}
                className="g-mt50 g-mb50"
            >
                增加
            </Button>
          </div>
        );
    }
}

export default tyj;