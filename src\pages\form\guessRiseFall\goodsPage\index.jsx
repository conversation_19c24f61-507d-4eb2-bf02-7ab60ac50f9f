import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classnames from 'classnames';
import api from 'api';
import moment from 'moment';
import {Row, Col, Input, Upload, Button, Divider, message, Drawer, Table, Modal, Spin, Select, Radio } from 'antd';

const { Option  } = Select;
const { confirm } = Modal;
const { TextArea } = Input;
message.config({
    duration: 3,// 持续时间
    maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
    top: 70,// 到页面顶部距离
});
const {getGuessRiseFallGoodsList, goodsImgUpload, saveGuessRiseFallGoodsForm, publishGuessRiseFallGoodsList, deleteGuessRiseFallGoods, getGuessRiseFallGoodsDetail, changeGuessRiseFallGoodsStatus} = api;
const FORMDATAMAP = {
    'discount': '优惠券(满减红包和0折卡)',
    'cashOne': '万家现金宝A 1元',
    'cashThree': '万家现金宝A 3元',
    'cashFive': '万家现金宝A 5元',
    'vipAweek': '同花顺金牛会员纯享周卡',
    'vipAmonth': '同花顺金牛会员纯享月卡',
    'vipSweek': '同花顺金牛会员尊享周卡',
    'vipSmonth': '同花顺金牛会员尊享月卡',
    'realProduct': '实物商品(用于展示敬请期待)',
}; // 表单数据映射关系
export default function() {
    const [pageLoading,setPageLoading] = useState(false); // 页面loading
    const [saveBtnLoading,setSaveBtnLoading] = useState(false); // 保存按钮loading
    const [isUploading,setIsUploading] = useState(false); // 上传中loading
    const [listData, setListData] = useState([]);// 任务列表数据
    const [listTotal,setListTotal] = useState(); // 列表总条数
    const [currentPageNum, setCurrentPageNum] = useState(); // 当前页码
    const [addOrRevise,setAddOrRevise] = useState(0); // 新增状态：0；编辑状态：1。
    const [isShowDrawer,setIsShowDrawer] = useState(false); // 是否显示新增/编辑面板
    const [formData,setFormData] = useState({}); // 表单数据
    const pageSize = 10; // 列表页每页数量
    const goodsEssenceList = [
        {
            id: 'discount',
            name: '优惠券(满减红包和0折卡)'
        },
        {
            id: 'cashOne',
            name: '万家现金宝A 1元'
        },
        {
            id: 'cashThree',
            name: '万家现金宝A 3元'
        },
        {
            id: 'cashFive',
            name: '万家现金宝A 5元'
        },
        {
            id: 'vipAweek',
            name: '同花顺金牛会员纯享周卡'
        },
        {
            id: 'vipAmonth',
            name: '同花顺金牛会员纯享月卡'
        },
        {
            id: 'vipSweek',
            name: '同花顺金牛会员尊享周卡'
        },
        {
            id: 'vipSmonth',
            name: '同花顺金牛会员尊享月卡'
        },
        {
            id: 'realProduct',
            name: '实物商品(用于展示敬请期待)'
        },
    ]; // 商品实质列表
    const tableColumns = [
        {
            title: '商品id',
            dataIndex: 'productId',
            key: 'productId',
          },
        {
          title: '商品名称',
          dataIndex: 'productName',
          key: 'productName',
        },
        {
          title: '商品实质',
          dataIndex: 'productEssence',
          key: 'productEssence',
        },
        {
          title: '兑换积分',
          dataIndex: 'exchangePoints',
          key: 'exchangePoints',
        },
        {
          title: '单用户兑换上限',
          dataIndex: 'exchangeLimit',
          key: 'exchangeLimit',
        },
        {
          title: '库存',
          dataIndex: 'stock',
          key: 'stock',
        },
        {
            title: '排序',
            dataIndex: 'productSort',
            key: 'productSort',
        },
        {
          title: '商品状态',
          dataIndex: 'productStatusDesc',
          key: 'productStatusDesc',
        },
        {
            title: '上架或下架',
            key: 'changeStatus',
            render: (row, record, index) => (
                <div>
                      <Button type={record.productStatus !== '1' ? 'primary':'danger'} onClick={()=>{changeGoodsStatus(record)}}>{record.productStatus !== '1' ? '上架':'下架'}</Button>
                </div>
            ),
        },
        // {
        //     title: '操作时间',
        //     dataIndex: 'operateTime',
        //     key: 'operateTime',
        // },
        {
          title: '操作',
          key: 'action',
          render: (row, record, index) => (
              <div>
                    <span style={{color: '#1890FF', cursor: 'pointer', marginRight: '10px'}} onClick={()=>{openEditDrawer(record)}}>编辑</span>
                    <span style={{color: '#1890FF', cursor: 'pointer'}} onClick={() => deleteRow(record)}>删除</span>
              </div>
          ),
        },
    ]; // 列表项
    useEffect(()=>{
        handleGetGoodsList(1);
        initFormData();
    },[]);
    // 获取商品列表
    const handleGetGoodsList = (pageNum) => {
        setPageLoading(true);
        setCurrentPageNum(pageNum);
        getGuessRiseFallGoodsList({},pageNum,pageSize).then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                const data = res.data;
                const _listData = data.pointGuessProductDtoList && data.pointGuessProductDtoList.map((item,index)=>{
                    const _item = {};
                    _item.productId = item.productId;
                    _item.productName = item.productName;
                    _item.productEssence = FORMDATAMAP[item.productEssence];
                    _item.packetId = item.packetId;
                    _item.exchangePoints = `${item.exchangePoints}积分`;
                    _item.exchangeLimit = item.exchangeLimit?.exchangeIsLimit === 'N' ? '不限' : item.exchangeLimit?.exchangeLimitNumber;
                    _item.stock = item.stock?.stockIsLimit === 'N' ? '不限' : item.stock?.stockNumber;
                    _item.productStatus = item.productStatus;
                    _item.productStatusDesc = item.productStatus === '1' ? '在售' : '下架';
                    _item.productSort = item.productSort;
                    _item.operateTime = item.updateTime;
                    _item.useRule = item.useRule;
                    return _item;
                });
                setListTotal(data.size);
                setListData(_listData);
            } else {
                message.error('获取列表失败');
            }
        });
    };
    // 发布
    const onPublish = () => {
        setPageLoading(true);
        publishGuessRiseFallGoodsList().then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                message.success('发布成功');
            } else {
                message.error(res.message);
            }
        });
    };
    // 点击删除操作
    const deleteRow = (record) => {
        confirm({
            title: '提示',
            content: `确定删除该任务？`,
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            centered: 'true',
            onOk() {
                setPageLoading(true);
                deleteGuessRiseFallGoods({productId: record.productId}).then((res)=>{
                    setPageLoading(false);
                    if (res.code === '0000') {
                        handleGetGoodsList(currentPageNum);
                        message.success('删除成功');
                    } else {
                        message.error('删除失败');
                    }
                });
            }
        });
    };
    // 上架或下架
    const changeGoodsStatus = (record)=> {
        setPageLoading(true);
        const productStatus = record.productStatus !== '1' ? '1' : '0';
        changeGuessRiseFallGoodsStatus({},record.productId,productStatus).then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                handleGetGoodsList(currentPageNum);
                message.success('更新商品状态成功');
            }
        });
    };
    // 初始化表单
    const initFormData = () => {
        let _formData = {};
        _formData = {
            productName: '',
            productId: '',
            productEssence: 'discount',
            packetId: '',
            exchangePoints: '',
            exchangeLimit: {
                exchangeIsLimit: 'N',
                exchangeLimitNumber: '',
            },
            stock: {
                stockIsLimit: 'N',
                stockNumber: '0',
                stockAddNumber: ''
            },
            productStatus: '1',
            productMainJpg: '',
            productInfoJpg: '',
            updateTime: '',
            productSort: '',
            useRule: ''
        }
        setFormData(_formData);
    };
    // 列表项发生变化
    const changeFormItem = (type, value) => {
        const _formData = deepClone(formData);
        switch (type) {
            case 'productEssence':
                _formData.productEssence = value;
                _formData.packetId = '';
                break;
            case 'exchangeIsLimit':
                _formData.exchangeLimit.exchangeIsLimit = value;
                break;
            case 'exchangeLimitNumber':
                _formData.exchangeLimit.exchangeLimitNumber = value;
                break;
            case 'stockIsLimit':
                _formData.stock.stockIsLimit = value;
                break;
            case 'stockAddNumber':
                _formData.stock.stockAddNumber = value;
                break;
            default:
                _formData[type] = value;
                break;
        }
        setFormData(_formData);
    };
    // 上传图片
    const uploadImg = (options,type,index)=> {
        if (options.file.size > 10240000) {
            message.error('文件大小不得超过10M');
            return;
        }
        setIsUploading(true);
        message.info('图片上传中');
        let params = new FormData();
        params.append('file', options.file);
        goodsImgUpload(params).then((res)=>{
            if (res.status_code === 0) {
                let _formData = {...formData};
                switch (type) {
                    case 'goodsMajorImg':
                        _formData.productMainJpg = insertStr(res.data,4,'s');
                        break;
                    // case 'goodsDetailImg':
                    //     _formData.productInfoJpg = insertStr(res.data,4,'s');
                    //     break;
                    default:
                        break;
                }
                setFormData(_formData);
                message.success('上传成功');
            } else {
                message.error(res.status_msg);
            }
            setIsUploading(false);
        }).catch(()=>{
            message.error('上传失败');
            setIsUploading(false);
        });
    }
    // 表单校验
    const checkFormData = () => {
        let isPass = false;
        switch (true) {
            case !formData.productName:
                message.error('请填写商品名称');
                break;
            case formData.productEssence === 'discount' && !formData.packetId:
                message.error('请填写红包id');
                break;
            case !formData.exchangePoints:
                message.error('请填写兑换积分');
                break;
            case isNaN(formData.exchangePoints) || parseInt(formData.exchangePoints) <= 0:
                message.error('兑换积分不符合规则');
                break;
            case formData.exchangeLimit?.exchangeIsLimit === 'Y' && !formData.exchangeLimit?.exchangeLimitNumber:
                message.error('请填写单用户兑换上限');
                break;
            case formData.exchangeLimit?.exchangeIsLimit === 'Y' && isNaN(formData.exchangeLimit?.exchangeLimitNumber) || parseInt(formData.exchangeLimit?.exchangeLimitNumber) <= 0:
                message.error('单用户兑换上限不符合规则');
                break;
            case formData.stock?.stockAddNumber && isNaN(formData.stock?.stockAddNumber):
                message.error('新增库存数不符合规则');
                break;
            case !formData.productMainJpg:
                message.error('请上传商品主图');
                break;
            case !formData.productSort:
                message.error('请填写排序');
                break;
            case isNaN(formData.productSort) || parseInt(formData.productSort) < 0:
                message.error('排序不符合规则');
                break;
            case !formData.useRule:
                message.error('请填写使用规则');
                break;
            default:
                isPass = true;
                break;
        }
        return isPass;
    };
    // 保存
    const saveForm = () => {
        if (!checkFormData()) return; 
        const params = deepClone(formData);
        params.updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
        setSaveBtnLoading(true);
        saveGuessRiseFallGoodsForm(params).then((res)=>{
            setSaveBtnLoading(false);
            if (res.code === '0000') {
                closeDrawer();
                if (addOrRevise === 0) {
                    handleGetGoodsList(1);
                } else {
                    handleGetGoodsList(currentPageNum);
                }
                message.success('保存成功');
            } else {
                message.error(res.message);
            }
        });
    };
    // 打开新增抽屉
    const openAddDrawer = () => {
        setAddOrRevise(0);
        initFormData();
        setIsShowDrawer(true);
    };
    // 打开编辑抽屉
    const openEditDrawer = (record) => {
        setPageLoading(true);
        // // 调用接口获取返显数据
        getGuessRiseFallGoodsDetail({productId: record.productId}).then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                let data = res.data;
                let _formData = deepClone(formData);
                _formData.productName = data.productName;
                _formData.productId = data.productId;
                _formData.productEssence = data.productEssence;
                _formData.packetId = data.packetId;
                _formData.exchangePoints = data.exchangePoints;
                _formData.exchangeLimit = deepClone(data.exchangeLimit);
                _formData.stock = deepClone(data.stock);
                _formData.stock.stockAddNumber =  '';
                _formData.productStatus =  data.productStatus;
                _formData.productMainJpg = data.productMainJpg;
                _formData.productInfoJpg = data.productInfoJpg;
                _formData.productSort = data.productSort;
                _formData.useRule = data.useRule;
                setFormData(_formData);
                setAddOrRevise(1);
                setIsShowDrawer(true);
            } else {
                message.error('获取商品信息失败');
            }
        });
    };
    // 关闭新增/编辑抽屉
    const closeDrawer = () => {
        initFormData();
        setIsShowDrawer(false);
    };
     // 深拷贝对象/数组
     const deepClone = (obj) => {
        return JSON.parse(JSON.stringify(obj));
    };
    // 字符串指定位置插入字符
    const insertStr = (soure, start, newStr) =>{   
        return soure.slice(0, start) + newStr + soure.slice(start);
    }
    return (
        <div className={styles['goods-page']}>
            <Spin spinning={pageLoading}>
                <Button type="danger " style={{marginBottom: '20px',marginRight: '20px'}}  onClick={()=>{onPublish()}}>发布</Button>
                <Button type="primary" style={{marginBottom: '20px'}} onClick={()=>{openAddDrawer()}}>新增</Button>
                <Table
                    columns={tableColumns}
                    dataSource={listData}
                    pagination={{ pageSize: pageSize, total: listTotal, current: currentPageNum}}
                    onChange={(pagination)=>{handleGetGoodsList(pagination.current);}}
                    rowKey={record=>record.taskId}
                />
            </Spin>
            <Drawer
                className="goods-page-drawer"
                title={addOrRevise === 0 ? '添加' : '编辑'}
                placement="right"
                width="1300"
                maskClosable={false}
                destroyOnClose={true}
                onClose={closeDrawer}
                visible={isShowDrawer}
            >
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>商品名称：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={8}>
                        <Input placeholder='请填入商品名称' value={formData.productName} onChange={(e)=>{changeFormItem('productName',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>商品实质：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={8}>
                        <Select value={formData.productEssence} onChange={(e) => {changeFormItem('productEssence',e)}} style={{width:'100%'}} disabled={addOrRevise === 1}>
                            {
                                goodsEssenceList && goodsEssenceList.map((item, index) => {
                                    return (
                                        <Option value={item.id} key={index}>{item.name}</Option>
                                    );
                                })
                            }
                        </Select>
                    </Col>
                </Row>
                <Row gutter={[40,14]} style={{display: formData.productEssence === 'discount' ? '' : 'none'}}>
                    <Col span={6}>
                        <div>红包id：</div>
                        <Input value={formData.packetId} onChange={(e)=>{changeFormItem('packetId',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>兑换积分（纯数字且值>0）：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={8}>
                        <Input placeholder='请填入兑换积分' value={formData.exchangePoints} onChange={(e)=>{changeFormItem('exchangePoints',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>单用户兑换上限（纯数字且值>0）：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={6}>
                        <Radio.Group value={formData.exchangeLimit?.exchangeIsLimit} onChange={(e) => {changeFormItem('exchangeIsLimit',e.target.value)}}>
                            <Radio value={'N'}>不限</Radio>
                            <Radio value={'Y'}>限制为</Radio>
                        </Radio.Group>
                        <Input
                            value={formData.exchangeLimit?.exchangeLimitNumber} 
                            onChange={(e)=>{changeFormItem('exchangeLimitNumber',e.target.value)}}
                            disabled={formData.exchangeLimit?.exchangeIsLimit === 'N'}
                        ></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]}>
                    <Col style={{fontWeight: '600'}}><span style={{color: '#f00'}}>*</span>库存：</Col>
                    <Col style={{paddingLeft: 0}}>
                        <Radio.Group value={formData.stock?.stockIsLimit} onChange={(e) => {changeFormItem('stockIsLimit',e.target.value)}}>
                            <Radio value={'N'}>不限</Radio>
                            <Radio value={'Y'}>限制</Radio>
                        </Radio.Group>
                    </Col>
                </Row>
                <Row gutter={[40,14]}><Col>当前库存：{formData.stock?.stockNumber}</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={6}>
                        <div>在原库存上增加库存（可为负数）：</div>
                        <Input value={formData.stock?.stockAddNumber} onChange={(e)=>{changeFormItem('stockAddNumber',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>商品主图（尺寸270*270）：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={8} style={{alignItems: 'flex-end'}}>
                        <div className={classnames(styles['goods-major-img'],'goods-major-img')}>
                            <img style={{width: '100%',height: '100%',display: formData.productMainJpg ? '' : 'none'}} src={formData.productMainJpg} alt=""/>
                        </div>
                        <Upload
                            customRequest={(options)=>{uploadImg(options,'goodsMajorImg')}}
                            showUploadList={false}
                        >
                            <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                        </Upload>
                    </Col>
                </Row>
                <Divider />
                {/* <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col>商品详情图：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={8} style={{alignItems: 'flex-end'}}>
                        <div className={classnames(styles['goods-detail-img'],'goods-detail-img')}>
                            <img style={{width: '100%',height: '100%',display: formData.productInfoJpg ? '' : 'none'}} src={formData.productInfoJpg} alt=""/>
                        </div>
                        <Upload
                            customRequest={(options)=>{uploadImg(options,'goodsDetailImg')}}
                            showUploadList={false}
                        >
                            <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                        </Upload>
                    </Col>
                </Row>
                <Divider /> */}
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>排序：（数字从小到大，专区内从上至下，输入值>=0）</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={4}>
                        <Input value={formData.productSort} onChange={(e)=>{changeFormItem('productSort',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider/>
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>使用规则</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={24}>
                        <TextArea size='default' autoSize={{minRows: 4, maxRows: 10}} value={formData.useRule} onChange={(e)=>{changeFormItem('useRule',e.target.value)}}/>
                    </Col>
                </Row>
                <Row gutter={[40,14]}>
                    <Col span={8}>
                        <Button type="primary" style={{ marginRight: 24 }} loading={saveBtnLoading} onClick={()=>{saveForm()}}>保存</Button>
                        <Button type="danger" onClick={()=>{closeDrawer()}}>取消</Button>
                    </Col>
                </Row>
            </Drawer>
        </div>
    )
}

