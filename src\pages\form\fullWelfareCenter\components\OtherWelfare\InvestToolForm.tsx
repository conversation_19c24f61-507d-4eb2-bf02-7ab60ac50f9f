import React from 'react';
import { Form, Input, But<PERSON>, Card } from 'antd';
import UploadForm from '../Upload';

const InvestToolForm = props => {
  const { getFieldDecorator, toolCardNum, setToolCardNum } = props;
  const toolCardList = [];
  for (let i = 0; i < toolCardNum; i++) {
    const toolCardItem = (
      <Card key={i}>
        <UploadForm
          label="图片"
          fieldLocation={`otherWelfare.investmentTool.cardList[${i}].image`}
          getFieldDecorator={getFieldDecorator}
          required={true}
        />
        <Form.Item label="名称">
          {getFieldDecorator(`otherWelfare.investmentTool.cardList[${i}].name`, {
            rules: [{ required: true, message: '请输入名称' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="简介">
          {getFieldDecorator(`otherWelfare.investmentTool.cardList[${i}].introduction`, {
            rules: [{ required: true, message: '请输入简介' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="价值">
          {getFieldDecorator(`otherWelfare.investmentTool.cardList[${i}].value`, {
            rules: [{ required: true, message: '请输入价值' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="门槛标签">
          {getFieldDecorator(`otherWelfare.investmentTool.cardList[${i}].label`, {
            rules: [{ required: true, message: '请输入门槛标签' }],
          })(<Input />)}
        </Form.Item>
        <Form.Item label="跳转链接">
          {getFieldDecorator(`otherWelfare.investmentTool.cardList[${i}].jumpUrl`, {
            rules: [{ required: true, message: '请输入跳转链接' }],
          })(<Input />)}
        </Form.Item>
      </Card>
    );
    toolCardList.push(toolCardItem);
  }
  return (
    <>
      <Form.Item label="tab名称">
        {getFieldDecorator('otherWelfare.investmentTool.tabName', {
          rules: [{ required: true, message: '请输入tab名称' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="角标">
        {getFieldDecorator('otherWelfare.investmentTool.mark')(<Input />)}
      </Form.Item>
      {toolCardList}
      <Button
        onClick={() => {
          setToolCardNum(toolCardNum + 1);
        }}
      >
        新增
      </Button>
      <Button onClick={() => setToolCardNum(toolCardNum - 1)} disabled={toolCardNum === 0}>
        删除
      </Button>
      <Form.Item label="查看更多跳转链接">
        {getFieldDecorator('otherWelfare.investmentTool.seeMoreUrl')(<Input />)}
      </Form.Item>
    </>
  );
};

export default InvestToolForm;
