{"type": "object", "ui:className": "m-fund-display-form", "properties": {"bsjj": {"title": "博时基金(场外)", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "bsjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "ctzg": {"title": "财通资管（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "ctzg", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "dfhzg": {"title": "东方红资管（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "dfhzg", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "fgjj": {"title": "富国基金（场内）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "fgjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes"]}, "gyrxjj": {"title": "工银瑞信基金（场内）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "gyrxjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes", "fundCodes"]}, "gtjj": {"title": "国泰基金（场内 + 场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "gtjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes", "fundCodes"]}, "hajj": {"title": "华安基金（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "hajj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "htbrjj": {"title": "华泰柏瑞基金（场内）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "htbrjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes"]}, "hxjj": {"title": "华夏基金（场内 + 场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "hxjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes", "fundCodes"]}, "htfjj": {"title": "汇添富基金（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "htfjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "jsjj": {"title": "嘉实基金（场内）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "jsjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes"]}, "jsccjj": {"title": "景顺长城基金（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "jsccjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "nfjj": {"title": "南方基金（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "nfjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "phjj": {"title": "鹏华基金（场内）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "phjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes"]}, "pajj": {"title": "平安基金（场内）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "pajj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes"]}, "thjj": {"title": "天弘基金（场内 + 场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "thjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes", "fundCodes"]}, "wjjj": {"title": "万家基金（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "wjjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "yfdjj": {"title": "易方达基金（场内）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "yfdjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes"]}, "yhjj": {"title": "银华基金（场内 + 场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "yhjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "etfCodes": {"title": "推荐ETF", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["etfCodes", "fundCodes"]}, "zsjj": {"title": "招商基金（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "zsjj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}, "zojj": {"title": "中欧基金（场外）", "type": "object", "ui:className": "m-fund-display-form-item", "properties": {"companyId": {"type": "string", "default": "zojj", "ui:className": "g-form-item-companyId", "ui:widget": "html"}, "fundCodes": {"title": "推荐基金", "type": "string", "pattern": "^[0-9]{6}(,[0-9]{6})*$", "message": {"pattern": "配置多个基金，请用英文逗号隔开"}}}, "required": ["fundCodes"]}}}