/**
 * 黄金宝 - 任务配置 - 任务编辑
 */
import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { Checkbox, Typography, Button, Form, Input, Select, Switch } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import {
  TASK_TYPE,
  TASK_TYPE_TEXT,
  REWARD_TYPE,
  REWARD_TYPE_TEXT,
  TASK_CYCLE,
  TASK_CYCLE_TEXT,
} from '../const';
import { isBuyType, isBrowseType, isShareType, isRewardGram } from '../helper';
import UploadImg from '@/pages/frontend/compoment/uploadImg';

type Props = {
  data: { [k: string]: unknown };
  onBack: () => void;
  onSubmit: (data: { [k: string]: unknown }) => void;
  hasFirstPurchaseTask: boolean;
  hasHighTask: boolean;
  hasSuperTask: boolean;
};

//
const TaskForm: React.FunctionComponent<Props & FormComponentProps> = ({
  form,
  data,
  onBack,
  onSubmit,
  hasFirstPurchaseTask,
  hasHighTask,
  hasSuperTask,
}) => {
  //
  const [type, setType] = useState(data.type);
  const [shareImage, setShareImage] = useState(data.share_image);
  const [rewardType, setRewardType] = useState(data.reward_type);
  const [superTask, setSuperTask] = useState(data.is_super_task);
  const [cycleTask, setCycleTask] = useState(data.is_cycle_task);
  const isBuyTask = isBuyType(type);
  const isBrowseTask = !isBuyTask && isBrowseType(type);
  const isShareTask = !isBuyTask && !isBrowseTask && isShareType(type);
  const isRewardGramTask = isBuyTask && isRewardGram(type);
  const isOnlyBuyTask = isBuyTask && type == TASK_TYPE.BUY;

  const handleChangeType = type => {
    setType(type);

    if (!isBuyType(type) || type != TASK_TYPE.BUY) {
      setRewardType(String(REWARD_TYPE.FIXED_REWARD));
      form.setFieldsValue({
        reward_type: String(REWARD_TYPE.FIXED_REWARD),
        reward_every_full: null,
      });
    }
    if (isRewardGram(type)) {
      setCycleTask(false);
      form.setFieldsValue({ is_cycle_task: false });
    }
  };

  const handleSubmit = () => {
    form.validateFieldsAndScroll((err, values) => {
      if (err) return;

      // 要恢复默认值
      if (!isBuyTask) {
        values.condition_amount = null;
      }
      if (!isBrowseTask) {
        values.condition_time = null;
      }
      if (!isShareTask) {
        values.share_text = values.share_title = values.share_image = values.share_link = null;
      }
      if (!isRewardGramTask) {
        values.reward_grams = null;
      }

      onSubmit({ ...data, ...values, is_super_task: superTask, is_cycle_task: cycleTask });
    });
  };

  const handleReset = () => {
    setType(data.type);
    setShareImage(data.share_image);
    setRewardType(data.reward_type);
    setSuperTask(data.is_super_task);
    setCycleTask(data.is_cycle_task);
    form.setFieldsValue(data);
  };

  useEffect(() => {
    if (shareImage) {
      form.setFields({ share_image: { value: shareImage, errors: [] } });
    } else {
      form.setFields({ share_image: { value: shareImage, errors: [new Error('请上传分享图片')] } });
    }
  }, [shareImage]);

  // prettier-ignore
  return (
    <div className={ styles['task-form'] }>
      <Typography.Title level={ 4 } align="center">任务编辑</Typography.Title>
      <nav className={ styles['task-form-nav'] }>
        <Button type="secondary" onClick={ onBack }>返回列表</Button>
        <Button type="primary" onClick={ handleSubmit }>保存</Button>
        <Button type="secondary" onClick={ handleReset }>重置</Button>
      </nav>
      <main className={ styles['task-form-body'] }>
        <Form>
          <Form.Item label="任务名称">
            {
              form.getFieldDecorator('name', {
                initialValue: data.name,
                rules: [{ required: true, message: '请输入任务名称' }],
                normalize: value => value ? value.trim() : value,
              })(
                <Input
                  placeholder="请输入任务名称"
                />,
              )
            }
          </Form.Item>
          <section className={ styles['task-form-block'] }>
            <Form.Item label="任务类型">
              {
                form.getFieldDecorator('type', {
                  initialValue: String(data.type),
                  rules: [{ required: true, message: '请选择任务类型' }],
                })(
                  <Select onChange={ val => handleChangeType(val) }>
                    {
                      Object.entries(TASK_TYPE_TEXT)
                        .filter(([key, text]) => key == data.type || (key != TASK_TYPE.FIRST_PURCHASE && key != TASK_TYPE.HIGH_END_FINANCIAL_MANAGEMENT) || (key == TASK_TYPE.FIRST_PURCHASE && !hasFirstPurchaseTask) || (key == TASK_TYPE.HIGH_END_FINANCIAL_MANAGEMENT && !hasHighTask))
                        .map(([key, text]) =>
                          <Select.Option key={ key } value={ String(key) }>{ text }</Select.Option>
                        )
                    }
                  </Select>
                )
              }
            </Form.Item>
            {
              (isBuyTask || isBrowseTask) &&
              <Form.Item label={
                (type == TASK_TYPE.HIGH_END_FINANCIAL_MANAGEMENT && "要求金额(元，高端理财请配置为0)")
                || (isBuyTask && "要求金额(元)")
                || (isBrowseTask && "要求时长(秒)")
              }>
                {
                  form.getFieldDecorator(isBuyTask ? 'condition_amount' : 'condition_time', {
                    initialValue: isBuyTask ? data.condition_amount : data.condition_time,
                    rules: [
                      { required: true, message: '请填写任务要求' },
                      type == TASK_TYPE.HIGH_END_FINANCIAL_MANAGEMENT
                        ? { validator: (_, value) => value == 0, message: '请设置为 0' }
                        : { validator: (_, value) => value > 0 && parseInt(value) == parseFloat(value), message: '必须大于 0 且为整数' }
                    ],
                  })(
                    <Input
                      type="number"
                      placeholder="请填写任务要求"
                    />,
                  )
                }
              </Form.Item>
            }
            {
              isShareTask && (<>
                <Form.Item label="分享标题">
                  {
                    form.getFieldDecorator('share_title', {
                      initialValue: data.share_title,
                      rules: [{ required: true, message: '请输入分享标题' }],
                      normalize: value => value ? value.trim() : value,
                    })(
                      <Input
                        placeholder="请输入分享标题"
                      />,
                    )
                  }
                </Form.Item>
                <Form.Item label="分享文案">
                  {
                    form.getFieldDecorator('share_text', {
                      initialValue: data.share_text,
                      rules: [{ required: true, message: '请输入分享文案' }],
                      normalize: value => value ? value.trim() : value,
                    })(
                      <Input
                        placeholder="请输入分享文案"
                      />,
                    )
                  }
                </Form.Item>
                <Form.Item label="分享链接">
                  {
                    form.getFieldDecorator('share_link', {
                      initialValue: data.share_link,
                      rules: [{ required: true, message: '请输入分享链接' }],
                      normalize: value => value ? value.trim() : value,
                    })(
                      <Input
                        placeholder="请输入分享链接"
                      />,
                    )
                  }
                </Form.Item>
                <Form.Item label="分享图片">
                  {
                    form.getFieldDecorator('share_image', {
                      initialValue: data.share_image,
                      rules: [{ required: true, message: '请上传分享图片' }],
                      normalize: value => value ? value.trim() : value,
                    })(
                      <UploadImg
                        handleChange={ (url) => setShareImage(url) }
                        imageUrl={ shareImage }
                        isEdit={ true }
                      />
                    )
                  }
                </Form.Item>
              </>)
            }
          </section>
          <section className={ styles['task-form-block'] }>
            <Form.Item label="是否周期任务">
              {
                form.getFieldDecorator('is_cycle_task', {
                  initialValue: data.is_cycle_task,
                  rules: [{ required: true, message: '请选择是否是周期任务' }],
                })(
                  <Switch
                    disabled={ isRewardGram(type) }
                    checked={ cycleTask }
                    onChange={ (val) => setCycleTask(val) }
                  />
                )
              }
            </Form.Item>
            {
              cycleTask &&
              <Form.Item label="任务周期">
                {
                  form.getFieldDecorator('cycle', {
                    initialValue: String(data.cycle),
                    rules: [{ required: true, message: '请选择任务周期' }],
                  })(
                    <Select>
                      {
                        Object.entries(TASK_CYCLE_TEXT)
                          .map(([key, text]) =>
                            <Select.Option key={ key } value={ String(key) }>{ text }</Select.Option>
                          )
                      }
                    </Select>
                  )
                }
              </Form.Item>
            }
            {
              cycleTask &&
              <Form.Item label="周期内可重复完成次数（0 为无限次，1 为周期内不可重复完成）">
                {
                  form.getFieldDecorator('times_in_cycle', {
                    initialValue: data.times_in_cycle,
                    rules: [{ required: true, message: '请填写周期内可重复完成次数' }, { validator: (_, value) => value >= 0 && parseInt(value) == parseFloat(value), message: '必须大于等于 0 且为整数' }],
                  })(
                    <Input
                      type="number"
                      placeholder="请填写周期内可重复完成次数"
                    />,
                  )
                }
              </Form.Item>
            }
          </section>
          <Form.Item label="是否今日重金悬赏">
            {
              form.getFieldDecorator('is_super_task', {
                initialValue: data.is_super_task,
                rules: [{ required: true, message: '请选择是否是今日重金悬赏任务' }],
              })(
                <Switch
                  disabled={ hasSuperTask && !data.is_super_task }
                  checked={ superTask }
                  onChange={ (val) => setSuperTask(val) }
                />
              )
            }
          </Form.Item>
          <section className={ styles['task-form-block'] }>
            <Form.Item label="任务奖励">
              {
                form.getFieldDecorator('reward_type', {
                  initialValue: String(data.reward_type),
                  rules: [{ required: true, message: '请选择任务奖励' }],
                })(
                  <Select onChange={ val => setRewardType(val) }>
                    {
                      Object.entries(REWARD_TYPE_TEXT).filter(([key, text]) => isOnlyBuyTask || key != REWARD_TYPE.FULL_REWARD).map(([key, text]) =>
                        <Select.Option key={ key } value={ String(key) }>{ text }</Select.Option>
                      )
                    }
                  </Select>
                )
              }
            </Form.Item>
            {
              rewardType == REWARD_TYPE.FULL_REWARD &&
              <Form.Item label="满赠金额(每满/元)">
                {
                  form.getFieldDecorator('reward_every_full', {
                    initialValue: data.reward_every_full,
                    rules: [{ required: true, message: '请输入奖励黄金豆的满赠金额' }, { validator: (_, value) => value > 0 && parseInt(value) == parseFloat(value), message: '必须大于 0 且为整数' }],
                  })(
                    <Input
                      type="number"
                      placeholder="请输入奖励黄金豆的满赠金额"
                    />,
                  )
                }
              </Form.Item>
            }
            {
              !isRewardGramTask &&
              <Form.Item label="奖励黄金豆(颗)">
                {
                  form.getFieldDecorator('reward_beans', {
                    initialValue: data.reward_beans,
                    rules: [{ required: true, message: '请输入奖励黄金豆数目' }, { validator: (_, value) => value > 0 && parseInt(value) == parseFloat(value), message: '必须大于 0 且为整数' }],
                  })(
                    <Input
                      type="number"
                      placeholder="请输入奖励黄金豆数目"
                    />,
                  )
                }
              </Form.Item>
            }
            {
              rewardType == REWARD_TYPE.FIXED_REWARD && isRewardGramTask &&
              <Form.Item label="奖励黄金(克，实际奖励等值黄金豆)">
                {
                  form.getFieldDecorator('reward_grams', {
                    initialValue: data.reward_grams,
                    rules: [{ required: true, message: '请输入奖励黄金克数（实际奖励等值黄金豆）' }, { validator: (_, value) => value > 0, message: '必须大于 0' }],
                  })(
                    <Input
                      type="number"
                      placeholder="请输入奖励黄金克数（实际奖励等值黄金豆）"
                    />,
                  )
                }
              </Form.Item>
            }
          </section>
          <Form.Item label="跳转链接">
            {
              form.getFieldDecorator('link', {
                initialValue: data.link,
                normalize: value => value ? value.trim() : value,
              })(
                <Input
                  placeholder="请输入跳转链接"
                />,
              )
            }
          </Form.Item>
        </Form>
      </main>
    </div>
  )
};

export default Form.create()(TaskForm);
