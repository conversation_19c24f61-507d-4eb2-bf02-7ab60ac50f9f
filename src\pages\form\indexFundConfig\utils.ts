import { ISectorData } from './types';

/**
 * 修改对象数组并保持原有顺序，且不改变原有数组
 * @param arr 需要修改的对象数组
 * @param key 对象数组中需要修改的那个对象的key值
 * @param newObj 对象的修改值
 * @param isEditing 是否为编辑状态
 */
export const getNewObjectArray = (arr: ISectorData[], key: string, savingData: ISectorData) => {
  const index = arr.findIndex(value => value.key === key);
  // 添加
  if (index === -1) {
    if (arr.length === 0) {
      return [{ ...savingData }];
    }
    return [{ ...savingData }, ...JSON.parse(JSON.stringify(arr))];
  }
  // 编辑
  if (index !== -1) {
    if (arr.length === 1) {
      return [{ ...savingData }];
    }
    const arr1 = arr.slice(0, index);
    const arr2 = arr.slice(index + 1);
    return [
      ...JSON.parse(JSON.stringify(arr1)),
      { ...savingData },
      ...JSON.parse(JSON.stringify(arr2)),
    ];
  }
};

// 禁止输入中文（仅支持输入英文字母、数字和符号）
export const checkChinese = (str: string) => {
  let temp = '';
  for (let i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 255) {
      temp += str.charAt(i);
    }
  }
  return temp;
};
