import React, { useState, useEffect } from 'react';
import api from 'api';
import { Button, Select, Pagination, ConfigProvider, message } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import classnames from 'classnames';
import styles from './index.less';
import { FundTableData, GdlcTableData, SelectOption, FiltersData, FiltersMap } from './types';
import { funcBlock, setFiltersStatus, exportTableItem } from './fn';
import ConfigTable from './configTable/index';
import { resolveOnChange } from 'antd/lib/input/Input';

const { Option } = Select;
const { fetchFundConfigCompany, fetchFundConfigCheck } = api;

const selectors: SelectOption[] = [
  {
    key: 'company',
    label: '基金公司',
    options: [],
    placeholder: '请选择基金公司',
    filterOption: (input: string, option: any) => ~option.props.children.indexOf(input),
  },
  {
    key: 'fundType',
    label: '基金类型',
    options: [],
    placeholder: '请选择基金类型',
  },
  {
    key: 'fundStatus',
    label: '基金状态',
    options: [],
    placeholder: '请选择基金状态',
  },
];
const filtersDataMap: FiltersData[] = [{ fundKey: '' }, { fundKey: '' }, { fundKey: '' }];
const companyMap = {
  'fund': [],
  'gs': []
};
const tabs = ['普通基金', '高端理财', '券商理财'];
const tabKeys = ['fund', 'gdlc', 'gsdata'];
let tableIndex = 0; // 用于查询接口尾部添加suffix
let lastTab = 0; // 用于记录上一个tab
let tabChangeFilters = false; // 是否是tab变化改变筛选项
let keywordInput: any; // 关键词检索输入框dom

let _fetchTable: any;

export default function fundConfigCheck() {
  const [init, setInit] = useState(false);
  const [selectorsData, setSelectorsData] = useState<SelectOption[]>(selectors);
  const [filtersData, setFiltersData] = useState<FiltersData>(filtersDataMap[0]);
  const [tab, setTab] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [tableTotal, setTableTotal] = useState(1000);
  const [tableData, setTableData] = useState<FundTableData[] | GdlcTableData[]>([]);

  useEffect(() => {
    return function() {
      setInit(false);
    };
  }, []);
  useEffect(() => {
    if (init === false) {
      initPage();
    }
  }, [init]);
  useEffect(() => {
    if (init) {
      _fetchTable(tabKeys[tab], filtersData, pageNumber, pageSize);
    }
  }, [pageNumber]);
  useEffect(() => {
    if (init) {
      handleTableReset();
    }
  }, [pageSize]);
  useEffect(() => {
    // 设置 选择器选项
    const _selectors: SelectOption[] = setFiltersStatus(selectorsData, tab);
    if ((tab === 2 && lastTab !== 2) || (tab !== 2 && lastTab === 2) || !init) {
      fetchCompany(_selectors);
    } else {
      setSelectorsData(_selectors);
    }
    // 筛选项切换
    tabChangeFilters = init && true;
    filtersDataMap[lastTab] = filtersData;
    setFiltersData(filtersDataMap[tab]);
    keywordInput.value = filtersDataMap[tab].fundKey;
  }, [tab]);
  useEffect(() => {
    if (init && tabChangeFilters) {
      tabChangeFilters = false;
      handleTableReset();
    }
  }, [filtersData]);
  // 初始化
  const initPage = async () => {
    keywordInput = document.querySelector("#input-keyword");
    _fetchTable = funcBlock(
      500,
      () => {},
      (type: string, filtersData: any, pageNumber: number, pageSize: number) =>
        fetchTable(type, filtersData, pageNumber, pageSize),
    );
    await new Promise((resolve, reject) => {
      fetchTable(tabKeys[tab], filtersData, pageNumber, pageSize);
      resolve(true);
    }).then(() => {
      setInit(true);
    })
  };

  // 获取 基金公司
  const fetchCompany = (_selectors: SelectOption[]) => {
    const companyType = tab === 2 ? 'gs' : 'fund';
    let companys: any = companyMap[companyType];
    new Promise(resolve => {
      if (companys.length === 0) {
        fetchFundConfigCompany({}, companyType).then((res: any) => {
          try {
            if (res.status_code === 0) {
              companys = res.data.map((item: any) => {
                if (tab !== 2) {
                  return {
                    value: item.orgid,
                    label: item.shortname,
                  };
                } else {
                  return {
                    value: item.companyId,
                    label: item.companyName,
                  };
                }
              });
            } else {
              message.error(res.status_msg);
            }
            companyMap[companyType] = companys;
            resolve(1);
          } catch (e) {
            console.log(e.message);
          }
        });
      } else {
        resolve(1);
      }
    }).then(() => {
      _selectors.forEach((item: SelectOption) => {
        if (item.key === 'company') {
          item.options = companys;
        }
      });
      setSelectorsData([..._selectors]);
    })
  };
  // 查询表格
  const fetchTable = (type: string, filtersData: any, pageNumber: number, pageSize: number) => {
    const dataToSend = {
      requestDto: JSON.stringify(filtersData),
      offset: (pageNumber - 1) * pageSize,
      limit: pageSize,
    };
    tableIndex++;
    fetchFundConfigCheck(dataToSend, type, tableIndex).then((res: any) => {
      try {
        if (res.status_code === 0) {
          res = res.data;
          if (res.dataList === '[]') {
            res.dataList = [];
          }
          const tableData = res.dataList.map((item: any) => {
            return exportTableItem(item, type);
          });

          setTableTotal(res.size);
          setTableData(tableData);
        } else {
          message.error(res.status_msg);
        }
      } catch (e) {
        console.log(e.message);
      }
    });
  };

  // 筛选条件 控制器
  const handleFilterChange = (
    key: 'fundKey' | 'company' | 'fundType' | 'fundStatus',
    value: string,
  ) => {
    let _filtersData = { ...filtersData };
    _filtersData[key] = value;
    setFiltersData(_filtersData);
  };
  // 查询 控制器
  const handleSearch = () => {
    handleTableReset();
  };
  // 非页码 查询控制器
  const handleTableReset = () => {
    if (pageNumber === 1) {
      _fetchTable(tabKeys[tab], filtersData, pageNumber, pageSize);
    } else {
      setPageNumber(1);
    }
  };
  // tab 控制器
  const handleTab = (index: number) => {
    lastTab = tab;
    setTab(index);
  };
  // 页码 控制器
  const handlePageNum = (current: number, pageSize: number) => {
    setPageNumber(current);
  };
  // 页容量 控制器
  const handlePageSize = (current: number, pageSize: number) => {
    setPageSize(pageSize);
  };

  return (
    <section className={classnames(styles['m-fundConfigCheck'])}>
      <section className={classnames(styles['m-filters'])}>
        <div className={classnames(styles['m-option'])} key="fundKey">
          <span>关键词检索</span>
          <input
            type="text"
            placeholder="请输入基金名称关键词，或是基金ID来查询"
            id="input-keyword"
            className="ant-input"
            onInput={(e: any) => handleFilterChange('fundKey', e.target.value)}
            onKeyDown={(e: any) => {
              if (e.key === 'Enter') handleSearch();
            }}
          />
        </div>
        {selectorsData.map((select: SelectOption, index: number) => {
          return (
            <div className={classnames(styles['m-option'])} key={select.key}>
              <span>{select.label}</span>
              <Select
                value={filtersData[select.key]}
                placeholder={select.placeholder}
                showSearch={!!select.filterOption}
                filterOption={select.filterOption}
                allowClear
                onChange={(value: string) => handleFilterChange(select.key, value)}
              >
                {select.options.map((item: any, index: number) => {
                  return (
                    <Option value={item.value} key={index}>
                      {item.label}
                    </Option>
                  );
                })}
              </Select>
            </div>
          );
        })}
        <Button type="primary" onClick={handleSearch}>
          查询
        </Button>
      </section>
      <section className={classnames(styles['m-tabs'])}>
        {tabs.map((title: string, index: number) => {
          return (
            <div
              className={classnames(styles['m-tab'], styles[`${tab === index ? 'm-now' : ''}`])}
              onClick={() => handleTab(index)}
              key={index}
            >
              <span>{title}</span>
              <div className={classnames(styles['m-underline'])}></div>
            </div>
          );
        })}
        <div className={classnames(styles['m-table-pagination'], styles['m-top'])}>
          <p>
            当前第 <span>{pageNumber}</span>/{Math.ceil(tableTotal / pageSize)} 页
          </p>
          <ConfigProvider locale={zhCN}>
            <Pagination
              className={classnames(styles['m-pagination'])}
              current={pageNumber}
              pageSize={pageSize}
              total={tableTotal}
              showSizeChanger
              pageSizeOptions={['10', '20', '30', '50']}
              onChange={handlePageNum}
              onShowSizeChange={handlePageSize}
            />
          </ConfigProvider>
        </div>
      </section>
      <section className={classnames(styles['m-table'])}>
        <ConfigTable tableData={tableData} type={tabKeys[tab]} />
        <div className={classnames(styles['m-table-pagination'], styles['m-bottom'])}>
          <p>
            当前第 <span>{pageNumber}</span>/{Math.ceil(tableTotal / pageSize)} 页
          </p>
          <ConfigProvider locale={zhCN}>
            <Pagination
              className={classnames(styles['m-pagination'])}
              current={pageNumber}
              pageSize={pageSize}
              total={tableTotal}
              showSizeChanger
              pageSizeOptions={['10', '20', '30', '50']}
              onChange={handlePageNum}
              onShowSizeChange={handlePageSize}
            />
          </ConfigProvider>
        </div>
      </section>
    </section>
  );
}
