import React from 'react';
import { Form, Input, message, Button } from 'antd';
import api from 'api';

const { queryCotaid } = api;

const WeeklyLiveForm = props => {
  const { getFieldDecorator, getFieldValue, setFieldsValue } = props;
  const handleQuery = () => {
    queryCotaid({ themeId: getFieldValue('otherWelfare.weeklyLive.cardId') })
      .then(res => {
        if (res.status_code === 0) {
          setFieldsValue({ 'otherWelfare.weeklyLive.cardKey': res.data });
        } else {
          message.error(res.message);
        }
      })
      .catch(err => {
        message.error(err);
      });
  };
  return (
    <>
      <Form.Item label="tab名称">
        {getFieldDecorator('otherWelfare.weeklyLive.tabName', {
          rules: [{ required: true, message: '请输入tab名称' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="角标">
        {getFieldDecorator('otherWelfare.weeklyLive.mark')(<Input />)}
      </Form.Item>
      <Form.Item label="输入cota平台id">
        {getFieldDecorator('otherWelfare.weeklyLive.cardId', {
          rules: [{ required: true, message: '请输入平台id' }],
        })(<Input />)}
      </Form.Item>
      <Button style={{ float: 'right', zIndex: 99 }} onClick={handleQuery}>
        查询
      </Button>
      <Form.Item label="查询到的id">
        {getFieldDecorator('otherWelfare.weeklyLive.cardKey', {
          rules: [{ required: true, message: '请输入平台id' }],
        })(<Input disabled={true} />)}
      </Form.Item>
      <Form.Item label="查看更多跳转链接">
        {getFieldDecorator('otherWelfare.weeklyLive.seeMoreUrl')(<Input />)}
      </Form.Item>
    </>
  );
};

export default WeeklyLiveForm;
