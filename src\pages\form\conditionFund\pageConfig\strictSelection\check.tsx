import { message } from 'antd';
import { generateId, getRandomId, setRandomId } from '../../script/utils';
export const modelName = 'title';
export function checkForm(formData, index) {
  try {
    if (!formData.title) {
      throw new Error(`请输入标题`);
    }
    if (
      !formData.mainCondition ||
      !formData.mainCondition.normal ||
      !formData.mainCondition.parsed
    ) {
      throw new Error(`请输入核心条件设置`);
    }
    if (!formData.condition || !formData.condition.normal || !formData.condition.parsed) {
      throw new Error('请输入条件设置');
    }
  } catch (e) {
    message.error(`${e.message} (第${index + 1}项)`);
    return false;
  }

  return true;
}

export const fetchData = (item: any) => {
  setRandomId(item.id);
  return {
    id: item.id,
    title: item.title,
    mainCondition: {
      normal: item.mainConditionStr,
      parsed: item.mainConditionParsed,
    },
    condition: {
      normal: item.conditionStr,
      parsed: item.conditionParsed,
    },
  };
};
export const parseData = item => {
  console.log('check Strict', item);
  return {
    id: item.id || generateId('strict-'),
    title: item.title,
    mainConditionStr: item.mainCondition.normal,
    mainConditionParsed: item.mainCondition.parsed,
    conditionStr: item.condition.normal,
    conditionParsed: item.condition.parsed,
  };
};
export const addData = () => {
  return {
    title: '',
    mainCondition: {
      normal: '',
      parsed: [],
    },
    condition: {
      normal: '',
      parsed: [],
    },
  };
};
