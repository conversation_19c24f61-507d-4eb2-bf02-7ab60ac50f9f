import React from 'react';
import { Button } from 'antd';

const UpsAndDowns = props => {
  const { name, feature, tabLocation, setTabLocation } = props;
  const index = tabLocation.indexOf(feature);
  const handleMoveUp = () => {
    const newTabLocation = [...tabLocation];
    newTabLocation[index] = tabLocation[index - 1];
    newTabLocation[index - 1] = tabLocation[index];
    setTabLocation(newTabLocation);
  };
  const handleMoveDown = () => {
    const newTabLocation = [...tabLocation];
    newTabLocation[index] = tabLocation[index + 1];
    newTabLocation[index + 1] = tabLocation[index];
    setTabLocation(newTabLocation);
  };
  return (
    <div>
      <h2>{name}</h2>
      {index === 0 ? (
        <Button type="primary" style={{ float: 'right', zIndex: 99 }} onClick={handleMoveDown}>
          下移
        </Button>
      ) : index === 3 ? (
        <Button
          type="primary"
          style={{ float: 'right', zIndex: 99, marginRight: '15px' }}
          onClick={handleMoveUp}
        >
          上移
        </Button>
      ) : (
        <>
          <Button type="primary" style={{ float: 'right', zIndex: 99 }} onClick={handleMoveDown}>
            下移
          </Button>
          <Button
            type="primary"
            style={{ float: 'right', zIndex: 99, marginRight: '15px' }}
            onClick={handleMoveUp}
          >
            上移
          </Button>
        </>
      )}
    </div>
  );
};

export default UpsAndDowns;
