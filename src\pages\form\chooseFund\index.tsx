import * as React from 'react';
import classNames from 'classnames';
import { Button, message, Popconfirm } from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';
// @ts-ignore
import FROM_JSON from './form';

const { fetchChooseFund, postChooseFund } = api;

const { useState, useEffect } = React;

interface iProps {
  className?: string
}

function fetchChooseFundConfig() {
  return new Promise((resolve, reject) => {
    fetchChooseFund().then((data: any) => {
      if (data.code === '0000') {
        resolve(data.data && JSON.parse(data.data));
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {

      message.info(e.message);
    });
  });
}

function postChooseFundConfig(value: any) {
  const dataToSend = { value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    postChooseFund(dataToSend).then((data: any) => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}

export default function({ className }: iProps) {
  const [formData, setFormData]:any = useState({});
  const [valid, setValid] = useState([]);
  useEffect(() => {
    fetchChooseFundConfig().then((data: any) => {
      if (data) {
        setFormData(data);
      }
    });
  }, []);
  const onSubmit = () => {
    // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
    if (valid.length > 0) {
      message.info(`校验未通过字段： ${valid.toString()}`);
    } else {
      let dataToSend = {
        lists:formData.lists,
        configs:formData.configs,
      };
      postChooseFundConfig(dataToSend).then(() => {
        message.info('编辑成功');
      });
    }
  };
  return (
    <section className={classNames(className)}>
      <FormRender
        propsSchema={FROM_JSON}
        formData={formData}
        onChange={setFormData}
        onValidate={setValid}
      />
      <Popconfirm
        title="确认呢要提交么？如果是修改会覆盖线上配置！"
        onConfirm={onSubmit}
        okText="确定"
        cancelText="取消"
      >
        < Button type={'primary'}>提交</Button>
      </Popconfirm>
    </section>
  );
}
