/*
* kyc卡片
* <AUTHOR>
* @time 2020.03
*/

import React from 'react';
import { Row, Input, Button, Icon, Select, message, Collapse, Popconfirm, DatePicker, Tooltip, Rate } from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import moment from 'moment';
const { fetchKycCardSave, postKycCardSave, postKycCard} = api;
const {Panel} = Collapse;
const {Option} = Select;


const _cate_name = {
    bzsb: "基金排行榜",
    cnxh: "智能优选",

    //这四个cate为lxkj functionType(需要做处理)
    detault: "内容模板默认类型",
    fourmoney: "四笔钱",
    novAdvance: "新手进阶",
    ctyj: "长投赢+",

    lxsp: "老徐实盘",
    gzlc: "工资理财",
    ngxd: "你关心的",
    ccjj: "持仓基金",
    zxzb: "自选周报",
    lcyb: "理财月报",
    jjpz: "基金配置",
    clq: "策略圈",
    gzzshbk: "关注指数和板块",
    rmht:"热门话题",

    hyfxb: "行业风向标",
    mby: "目标赢"
}

const _need_login = {
    "1": '全部可见',
    "2": '无登录记录可见',
    "3": '有登录记录可见',
    "4": '登录中可见',
    "5": '未登录可见',
    "6": '未登录可见（有登录记录）',
}

@autobind 
class kycCard extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            title: "投资风向标",
            cate: "gzlc", //添加的类型
            list: [
            //     {
            //         cate: "novAdvance",
            //         title: "新客专区",
            //         rightJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/xyd/newBoyHome/dist/index.html",
            //         rightVersionControl: "",
            //         buttonTitle: "立即查看立即查看立即查看立即查看",
            //         buttonJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/xyd/newBoyHome/dist/index.html",
            //         buttonVersionControl: "",
            //         needlogin: "1",
            //         startTime: "2020-03-02 10:58:30",
            //         endTime: "",
            //         lxkjTitle: "进阶理财老司机必看进阶理财老司机必看进阶理财老司机必看进阶理财老司机必看",
            //         lxkjContent: "完成新客任务，1得3000元3天体验金1111111",
            //         lxkjJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/xyd/newBoyHome/dist/index.html",
            //         lxkjVersionControl: ""
            //     },
            //     {
            //         cate: "jjpz",
            //         title: "基金配置基金配置基金配置基金配置基金配置",
            //         rightJumpAction: "action=fund,code=003515",
            //         rightVersionControl: "version=5.97.11###action=gslist",
            //         buttonTitle: "国泰利是宝国泰利是宝国泰利是宝",
            //         buttonJumpAction: "action=fund,code=003515",
            //         buttonVersionControl: "version=5.97.11###action=gslist",
            //         needlogin: "1",
            //         startTime: "2020-02-11 09:30:30",
            //         endTime: "",
            //         jjpzContet: "国泰利是宝国泰利是宝国泰利是宝",
            //         sydesc: "七日年化",
            //         syvalue: "{fund:003515,totalnet,hb}",
            //         tags: "收益稳健收益稳健|这是标签这是标签|这个标签不显示"
            //     },
            //     {
            //         cate: "lcyb",
            //         title: "理财月报理财月报理财月报理财月报理财月报",
            //         rightJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/xyd/monthlyProfit/dist/index.html",
            //         rightVersionControl: "version=5.97.11###action=gslist",
            //         buttonTitle: "立即查看",
            //         buttonJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/xyd/monthlyProfit/dist/index.html",
            //         buttonVersionControl: "version=5.97.11###action=gslist",
            //         needlogin: "1",
            //         startTime: "2020-02-11 09:30:30",
            //         endTime: ""
            //     },
            //     {
            //         cate: "zxzb",
            //         title: "自选周报自选周报自选周报自选周报",
            //         rightJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/syh/zxzb/dist",
            //         rightVersionControl: "version=5.97.11###action=gslist",
            //         buttonTitle: "去看看",
            //         buttonJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/syh/zxzb/dist",
            //         buttonVersionControl: "version=5.97.11###action=gslist",
            //         needlogin: "1",
            //         startTime: "2020-02-11 09:30:30",
            //         endTime: ""
            //     },
            //     {
            //         cate: "ctyj",
            //         title: "长投赢+",
            //         rightJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/syh/cty/dist/index.html?groupid=SN000004",
            //         rightVersionControl: "",
            //         buttonTitle: "立即查看立即查看立即查看立即查看立即查看立即查看",
            //         buttonJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/syh/cty/dist/index.html?groupid=SN000004",
            //         buttonVersionControl: "",
            //         needlogin: "1",
            //         startTime: "2020-02-06 16:38:19",
            //         endTime: "",
            //         lxkjTitle: "长投赢长投赢长投赢长投赢长投赢长投赢长投赢长投赢",
            //         lxkjContent: "平时要用|资产保值|稳健理财|稳健理财",
            //         lxkjJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/syh/cty/dist/index.html?groupid=SN000004",
            //         lxkjVersionControl: ""
            //     },
            //     {
            //         cate: "gzlc",
            //         title: "工资理财",
            //         rightJumpAction: "https://trade.5ifund.com:8443/tohangqing/ifundapp_app/public/syh/salaryFinancing/dist/index$5092ff.html",
            //         rightVersionControl: "",
            //         buttonTitle: "立即体验",
            //         buttonJumpAction: "https://trade.5ifund.com:8443/tohangqing/ifundapp_app/public/syh/salaryFinancing/dist/index$5092ff.html",
            //         buttonVersionControl: "",
            //         needlogin: "1",
            //         startTime: "",
            //         endTime: "",
            //         gzlcTitle: "投资更便利",
            //         gzlcContent1: "自动转",
            //         gzlcContent2: "灵活取"
            //     },
            //     {
            //         cate: "ngxd",
            //         title: "你关心的基金",
            //         rightJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/wzy/transferStrategy/dist/index.html",
            //         rightVersionControl: "",
            //         buttonTitle: "看看吧",
            //         buttonJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/xyd/monthlyProfit/dist/index.html",
            //         buttonVersionControl: "version=5.95.01###action=184796",
            //         needlogin: "1",
            //         startTime: "",
            //         endTime: ""
            //     },
            //     {
            //         cate: "lxsp",
            //         title: "老徐实盘",
            //         rightJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         rightVersionControl: "",
            //         buttonTitle: "快去瞧瞧",
            //         buttonJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         buttonVersionControl: "version=5.95.00###https://t.10jqka.com.cn/m/channel/homePage/homepage.html?fid=184796",
            //         needlogin: "1",
            //         startTime: "",
            //         endTime: ""
            //     },
            //     {
            //         cate: "detault",
            //         title: "老徐侃基",
            //         rightJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         rightVersionControl: "",
            //         buttonTitle: "老徐侃基",
            //         buttonJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         buttonVersionControl: "version=5.95.00###https://t.10jqka.com.cn/m/channel/homePage/homepage.html?fid=184796",
            //         needlogin: "1",
            //         startTime: "",
            //         endTime: "",
            //         lxkjTitle: "老徐侃基",
            //         lxkjContent: "平时要用|资产保值|稳健理财|稳健理财",
            //         lxkjJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/syh/cty/dist/index.html?groupid=SN000004",
            //         lxkjVersionControl: ""
            //     },
            //     {
            //         cate: "lxsp",
            //         title: "老徐实盘",
            //         rightJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         rightVersionControl: "",
            //         buttonTitle: "快去瞧瞧",
            //         buttonJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         buttonVersionControl: "version=5.95.00###https://t.10jqka.com.cn/m/channel/homePage/homepage.html?fid=184796",
            //         needlogin: "1",
            //         startTime: "",
            //         endTime: ""
            //     },
            //     {
            //         cate: "fourmoney",
            //         title: "四笔钱",
            //         rightJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         rightVersionControl: "",
            //         buttonTitle: "四笔钱",
            //         buttonJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         buttonVersionControl: "",
            //         needlogin: "1",
            //         startTime: "",
            //         endTime: "",
            //         lxkjTitle: "四笔钱",
            //         lxkjContent: "平时要用|资产保值|稳健理财|稳健理财",
            //         lxkjJumpAction: "https://testfund.10jqka.com.cn/ifundapp_app/public/syh/cty/dist/index.html?groupid=SN000004",
            //         lxkjVersionControl: ""
            //     },
            //     {
            //         cate: "gzlc",
            //         title: "工资理财",
            //         rightJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         rightVersionControl: "",
            //         buttonTitle: "工资理财",
            //         buttonJumpAction: "https://fund.10jqka.com.cn/ifundapp_app/public/whw/masterstock/dist/advertise.html",
            //         buttonVersionControl: "",
            //         needlogin: "1",
            //         startTime: "",
            //         endTime: "",
            //         gzlcTitle: "",
            //         gzlcContent1: "",
            //         gzlcContent2: ""
            //     }
            ]
        }
    }

//cate应该不能修改

    /**
     * 修改属性
     * @param {*} value
     * @param {索引} index 
     * @param {对象key值} key 
     */
    handleValue(value, index, key) {
        let _list = this.state.list;
        _list[index][key] = value;
        this.setState({list: _list}, () => {
            for(let i = 0, _length = _list.length; i < _length; i++) {
                if (_list[i].functionType) {
                    _list[i].cate = _list[i].functionType;
                    delete _list[i].functionType;
                }
            }
            this.setState({list: _list})
        })
    }

    /**
     * 修改cate
     */
    handleListCate(value, index) {
        let _list = this.state.list;
        let _oldCate = _list[index].cate;
        if(_oldCate === 'gzlc') {
            delete _list[index].gzlcTitle;
            delete _list[index].gzlcContent1;
            delete _list[index].gzlcContent2;
        } else if (_oldCate === 'jjpz') {
            delete _list[index].jjpzContet;
            delete _list[index].sydesc;
            delete _list[index].syvalue;
            delete _list[index].tags;
        } else if (_oldCate === 'ctyj' || _oldCate === 'novAdvance' || _oldCate === 'detault' || _oldCate === 'fourmoney' || _oldCate === 'clq') {
            delete _list[index].lxkjTitle;
            delete _list[index].lxkjContent;
            delete _list[index].lxkjJumpAction;
            delete _list[index].lxkjVersionControl;
        } else if (_oldCate === 'rmht') {
            delete _list[index].rmhtTitle;
            delete _list[index].rmhtContent;
            delete _list[index].rmhtJumpAction;
            delete _list[index].rmhtVersionControl;
            delete _list[index].positiveBtn;
            delete _list[index].negativeBtn;
            delete _list[index].code;
            delete _list[index].rmhtId;
        } else if (_oldCate === 'hyfxb') {
            delete _list[index].hyfxbContet;
            delete _list[index].hyfxbStar;
        } else if (_oldCate === 'mby') {
            delete _list[index].mbyContet;
        }
        _list[index].cate = value;
        _list[index].title = _cate_name[value];
        if(value === 'gzlc') {
            Object.assign(_list[index], {
                gzlcTitle: "",
                gzlcContent1: "",
                gzlcContent2: ""
            })
        } else if (value === 'jjpz') {
            Object.assign(_list[index], {
                jjpzContet: "",
                sydesc: "",
                syvalue: "",
                tags: ""
            })
        } else if (value === 'ctyj' || value === 'novAdvance' || value === 'detault' || value === 'fourmoney' || value === 'clq') {
            Object.assign(_list[index], {
                lxkjTitle: "",
                lxkjContent: "",
                lxkjJumpAction: "",
                lxkjVersionControl: ""
            })
        }else if (value === 'rmht') {
            Object.assign(_list[index], {
                rmhtTitle: "",
                rmhtContent: "",
                rmhtJumpAction: "",
                rmhtVersionControl: "",
                positiveBtn:'', //正方话题
                negativeBtn:'', //反方话题
                code:'',
                rmhtId:''
            })
        } else if (value === 'hyfxb') {
            Object.assign(_list[index], {
                hyfxbContet: "",
                hyfxbStar: ""
            })
        } else if (value === 'mby') {
            Object.assign(_list[index], {
                mbyContet: ""
            })
        }
        this.setState({list: _list}, () => {console.log(this.state.list)})
    }

    handleCate(value) {
        this.setState({cate: value}, () => {console.log(this.state.list)})
    }

    /**
     * 添加队列
     */
    addList() {
        let _list = this.state.list;
        let _cate = this.state.cate;
        let _obj = {
            cate: _cate,
            title: _cate_name[_cate],
            rightJumpAction: "",
            rightVersionControl: "",
            buttonTitle: "",
            buttonJumpAction: "",
            buttonVersionControl: "",
            needlogin: "1",
            startTime: "",
            endTime: ""
        }
        if(_cate === 'gzlc') {
            _list.push(
                Object.assign(_obj, {
                    gzlcTitle: "",
                    gzlcContent1: "",
                    gzlcContent2: ""
                })
            )
        } else if (_cate === 'jjpz') {
            _list.push(
                Object.assign(_obj, {
                    jjpzContet: "",
                    sydesc: "",
                    syvalue: "",
                    tags: ""
                })
            )
        } else if (_cate === 'ctyj' || _cate === 'novAdvance' || _cate === 'detault' || _cate === 'fourmoney' || _cate === 'clq') {
            _list.push(
                Object.assign(_obj, {
                    lxkjTitle: "",
                    lxkjContent: "",
                    lxkjJumpAction: "",
                    lxkjVersionControl: ""
                })
            )
        } else if (_cate === 'rmht') {
            _list.push(
                Object.assign(_obj, {
                    rmhtTitle: "",
                    rmhtContent: "",
                    rmhtJumpAction: "",
                    rmhtVersionControl: "",
                    positiveBtn:'', //正方话题
                    negativeBtn:'', //反方话题）
                    code:'',
                    rmhtId:''
                })
            )
        } else if (_cate === 'hyfxb') {
            _list.push(
                Object.assign(_obj, {
                    hyfxbContet: "",
                    hyfxbStar: ""
                })
            )
        } else if (_cate === 'mby') {
            _list.push(
                Object.assign(_obj, {
                    mbyContet: ""
                })
            )
        } else {
            _list.push(_obj);
        }
        this.setState({list: _list}, () => {console.log(this.state.list)})
    }

    /**
     * 删除
     * @param {*} index 
     */
    deleteItem(index) {
        let _list = this.state.list;
        _list.splice(index, 1);
        this.setState({list: _list}, console.log(this.state.list))
    }

    /**
     * 初始化
     */
    init() {
        fetchKycCardSave().then((res) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    let _data = JSON.parse(res.data);
                    for(let i = 0, _length = _data.length; i < _length; i++) {
                        if (_data[i].functionType) {
                            _data[i].cate = _data[i].functionType;
                            delete _data[i].functionType;
                        }
                    }
                    this.setState({list: _data})
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }
    
    /**
     * 保存
     */
    save() {
        let _list = this.state.list;
        postKycCardSave (
            {value: JSON.stringify(_list)}
        ).then ( res => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！')
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    /**
     * 提交
     */
    upload() {
        let _list = this.state.list;
        for (let i = 0; i < _list.length; i++) {
            const item = _list[i];
            if (!item.title) {
                message.error(`请填写${_cate_name[item.cate]}的标题`);
                return;
            }
            if (!item.rightJumpAction && item.cate !== 'cnxh' && item.cate !== 'zxzb') {
                message.error(`请填写${_cate_name[item.cate]}的右侧跳转链接`);
                return;
            } else if (!/http|client/.test(item.rightJumpAction) && item.cate !== 'cnxh' && item.cate !== 'zxzb') {
                message.error(`${_cate_name[item.cate]}的右侧跳转链接格式有误`);
                return;
            }
            if (!item.buttonJumpAction && item.cate !== 'cnxh' && item.cate !== 'zxzb' && item.cate !== 'gzzshbk') {
                message.error(`请填写${_cate_name[item.cate]}的按钮跳转链接`);
                return;
            } else if (!/http|client/.test(item.buttonJumpAction) && item.cate !== 'cnxh' && item.cate !== 'zxzb' && item.cate !== 'gzzshbk') {
                message.error(`${_cate_name[item.cate]}的按钮跳转链接格式有误`);
                return;
            }
            if(item.cate === 'gzlc') {
                if (!item.gzlcTitle) {
                    message.error(`请填写${_cate_name[item.cate]}的工资理财标题`);
                    return;
                }
                if (!item.gzlcContent1) {
                    message.error(`请填写${_cate_name[item.cate]}的工资理财内容1`);
                    return;
                }
                if (!item.gzlcContent2) {
                    message.error(`请填写${_cate_name[item.cate]}的工资理财内容2`);
                    return;
                }
            } else if (item.cate === 'jjpz') {
                if (!item.jjpzContet) {
                    message.error(`请填写${_cate_name[item.cate]}的基金配置内容`);
                    return;
                }
                if (!item.sydesc) {
                    message.error(`请填写${_cate_name[item.cate]}的收益描述`);
                    return;
                }
                if (!item.syvalue) {
                    message.error(`请填写${_cate_name[item.cate]}的收益`);
                    return;
                }
                if (!item.tags) {
                    message.error(`请填写${_cate_name[item.cate]}的标签`);
                    return;
                }
            } else if (item.cate === 'ctyj' || item.cate === 'novAdvance' || item.cate === 'detault' || item.cate === 'fourmoney' || item.cate === 'clq') {
                if (!item.lxkjTitle) {
                    message.error(`请填写${_cate_name[item.cate]}的卡片标题`);
                    return;
                }
            } else if (item.cate === 'hyfxb') {
                if (!item.hyfxbContet) {
                    message.error(`请填写${_cate_name[item.cate]}的推荐名称`);
                    return;
                }
                if (!item.hyfxbStar) {
                    message.error(`请填写${_cate_name[item.cate]}的推荐星级`);
                    return;
                }
            } else if (item.cate === 'mby') {
                if (!item.mbyContet) {
                    message.error(`请填写${_cate_name[item.cate]}的卡片内容`);
                    return;
                }    
            } else if (item.cate === 'rmht'){
                if(item.rmhtTitle.length > 14) {
                    message.error('话题标题长度最大为14');
                }
                if(item.code.length !== 7){
                    message.error('话题Code长度应为7');
                }
                if(item.positiveBtn.length > 2 || item.negativeBtn.length > 2){
                    message.error('正反方按钮长度最大为2');
                }
            }
        }
        postKycCardSave (
            {value: JSON.stringify(_list)}
        ).then ( res => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！')
                    for (let i = 0; i < _list.length; i++) {
                        let _cate = _list[i].cate;
                        if (_cate === 'ctyj' || _cate === 'novAdvance' || _cate === 'detault' || _cate === 'fourmoney' || _cate === 'clq') {
                            _list[i].cate = 'lxkj';
                            _list[i].functionType = _cate;
                        }
                    }
                    console.log(_list)
                    postKycCard (
                        {value: JSON.stringify(_list)}
                    ).then ( res => {
                        try {
                            if (res.code !== '0000') {
                                message.error(res.message);
                            } else {
                                message.success('提交成功！')
                            }
                        } catch (e) {
                            message.error(e.message);
                        }
                    })
                }
            } catch (e) {
                message.error(e.message);
            }
        })
        
    }

    componentDidMount () {
        this.init();
    }

    render () {
        const syvalueStr = "收益格式：{fund:000697,month,fhb}表示基金000697近一月涨幅。收益区间fhb、group(month,tmonth,hyear,year,tyear,annualizedRate);hb(totalnet,net);gs(yearsy);sypz(yearsy);"
        return (
            <article style={{width: 1100}}>
                <Collapse style={{width: 1100, marginBottom: '20px'}}>
                {
                    this.state.list.map( (item, index) => {
                        return (
                            <Panel
                            header={'功能类型：' + _cate_name[item.cate] + '   ' + '标题：' + item.title} 
                            key={index}
                            extra={
                                <Popconfirm
                                placement="rightBottom"
                                title={`你确定要删除${_cate_name[item.cate]}么`}
                                onConfirm={(e) => {
                                    this.deleteItem(index)
                                }}
                                okText="确认"
                                cancelText="取消"
                                >
                                    <Button type="danger" >删除</Button>
                                </Popconfirm>
                            }
                            >
                                <Row style={{ marginBottom: '20px'}}>
                                    功能类型：
                                    <Select
                                    style={{width: '300px'}} 
                                    value={item.cate}
                                    onChange={(value) => {this.handleListCate(value, index)}}
                                    >
                                        {
                                            Object.keys(_cate_name).map((cateKey, cateIndex) => {
                                                return (
                                                    <Option value={cateKey} key={cateKey}>
                                                        {_cate_name[cateKey]}
                                                    </Option>
                                                )
                                            })
                                        }
                                    </Select>
                                </Row>

                                <Row style={{ marginBottom: '20px'}}>
                                    <Input 
                                    style={{width: '800px'}} 
                                    addonBefore="标题:" 
                                    onChange={(e) => {this.handleValue(e.target.value, index, 'title')}}
                                    value={item.title}></Input>
                                </Row>
                                {
                                    item.cate === 'ctyj' || item.cate === 'novAdvance' || item.cate === 'detault' || item.cate === 'fourmoney' || item.cate === 'clq'
                                        ?
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="卡片标题:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'lxkjTitle')}}
                                            value={item.lxkjTitle}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="卡片内容:" 
                                            addonAfter="*多个以'|'分割由(前端处理)"
                                            // suffix={
                                            //     <Tooltip title="*多个以'|'分割由(前端处理)">
                                            //         <Icon type="question-circle" />
                                            //     </Tooltip>
                                            //     }
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'lxkjContent')}}
                                            value={item.lxkjContent}></Input>
                                        </Row>
                                    </section>
                                        :
                                        ''
                                }
                               

                                {
                                    item.cate === 'gzlc'
                                    ?
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="工资理财标题:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'gzlcTitle')}}
                                            value={item.gzlcTitle}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="工资理财内容1:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'gzlcContent1')}}
                                            value={item.gzlcContent1}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="工资理财内容2:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'gzlcContent2')}}
                                            value={item.gzlcContent2}></Input>
                                        </Row>
                                    </section>
                                    :
                                    ''
                                }

                                {
                                    item.cate === 'jjpz'
                                    ?
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="基金配置内容:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'jjpzContet')}}
                                            value={item.jjpzContet}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="收益描述:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'sydesc')}}
                                            value={item.sydesc}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="收益:" 
                                            // suffix={
                                            //     <Tooltip title="{fund:000697,month,fhb}表示基金000697近一月涨幅。收益区间fhb、group(month,tmonth,hyear,year,tyear,annualizedRate);hb(totalnet,net);gs(yearsy);sypz(yearsy);">
                                            //         <Icon type="question-circle" />
                                            //     </Tooltip>
                                            //     }
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'syvalue')}}
                                            value={item.syvalue}></Input>
                                        </Row>
                                        <p style={{width: '745px', marginLeft: '55px'}} >{syvalueStr}</p><br></br>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="标签:" 
                                            addonAfter="*多个以'|'分割由(前端处理)"
                                            // suffix={
                                            //     <Tooltip title="*多个以'|'分割由(前端处理)">
                                            //         <Icon type="question-circle" />
                                            //     </Tooltip>
                                            //     }
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'tags')}}
                                            value={item.tags}></Input>
                                        </Row>
                                    </section>
                                    :
                                    ''
                                }

                                {
                                    item.cate === 'ctyj' || item.cate === 'novAdvance' || item.cate === 'detault' || item.cate === 'fourmoney' || item.cate === 'clq'
                                    ?
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="卡片跳转链接:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'lxkjJumpAction')}}
                                            value={item.lxkjJumpAction}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="卡片跳转链接版本控制:" 
                                            suffix={
                                                <Tooltip title="*格式(由新功能上线的版本和跳转组成)：version=5.63.01###action=gslist">
                                                    <Icon type="question-circle" />
                                                </Tooltip>
                                                }
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'lxkjVersionControl')}}
                                            value={item.lxkjVersionControl}></Input>
                                        </Row>
                                    </section>
                                    :
                                    ''
                                }

                                <Row style={{ marginBottom: '20px'}}>
                                    <Input 
                                    style={{width: '800px'}} 
                                    addonBefore="右侧跳转链接:" 
                                    onChange={(e) => {this.handleValue(e.target.value, index, 'rightJumpAction')}}
                                    value={item.rightJumpAction}></Input>
                                </Row>
                                <Row style={{ marginBottom: '20px'}}>
                                    <Input 
                                    style={{width: '800px'}} 
                                    addonBefore="右侧跳转链接版本控制:" 
                                    suffix={
                                        <Tooltip title="*格式(由新功能上线的版本和跳转组成)：version=5.63.01###action=gslist">
                                            <Icon type="question-circle" />
                                        </Tooltip>
                                        }
                                    onChange={(e) => {this.handleValue(e.target.value, index, 'rightVersionControl')}}
                                    value={item.rightVersionControl}></Input>
                                </Row>

                                {
                                    item.cate === 'rmht'
                                    ?
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="话题标题:" 
                                            placeholder="14字以内"
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'rmhtTitle')}}
                                            value={item.rmhtTitle}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="话题副标题:" 
                                            addonAfter="*多个以'|'分割由(前端处理)"
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'rmhtContent')}}
                                            value={item.rmhtContent}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="话题Code:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'code')}}
                                            value={item.code}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="投票ID:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'rmhtId')}}
                                            value={item.rmhtId}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="正方按钮:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'positiveBtn')}}
                                            value={item.positiveBtn}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="反方按钮:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'negativeBtn')}}
                                            value={item.negativeBtn}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                                <Input 
                                                style={{width: '800px'}} 
                                                addonBefore="投票后按钮:" 
                                                onChange={(e) => {this.handleValue(e.target.value, index, 'buttonTitle')}}
                                                value={item.buttonTitle}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="卡片跳转链接:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'rmhtJumpAction')}}
                                            value={item.rmhtJumpAction}></Input>
                                        </Row>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="卡片跳转链接版本控制:" 
                                            suffix={
                                                <Tooltip title="*格式(由新功能上线的版本和跳转组成)：version=5.63.01###action=gslist">
                                                    <Icon type="question-circle" />
                                                </Tooltip>
                                                }
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'rmhtVersionControl')}}
                                            value={item.rmhtVersionControl}></Input>
                                        </Row>
                                    </section>
                                    :
                                    ''
                                }

                                {
                                    item.cate === 'hyfxb'
                                    ?
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="行业风向标推荐名称:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'hyfxbContet')}}
                                            value={item.hyfxbContet}></Input>
                                        </Row>
                                        {/* <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="行业风向标推荐星级:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'hyfxbStar')}}
                                            value={item.hyfxbStar}></Input>
                                        </Row> */}
                                        <Row>
                                            <span>行业风向标推荐星级:</span>
                                            <Rate 
                                                allowHalf={true}
                                                onChange={(value) => {this.handleValue(value, index, 'hyfxbStar')}}
                                                value={item.hyfxbStar}
                                            />
                                        </Row>
                                    </section>
                                    :
                                    ''
                                }

                                {
                                    item.cate === 'mby'
                                    ?
                                    <section>
                                        <Row style={{ marginBottom: '20px'}}>
                                            <Input 
                                            style={{width: '800px'}} 
                                            addonBefore="目标赢卡片内容:" 
                                            onChange={(e) => {this.handleValue(e.target.value, index, 'mbyContet')}}
                                            value={item.mbyContet}></Input>
                                        </Row>
                                    </section>
                                    :
                                    ''
                                }

                                
                               
                                {
                                    item.cate !== 'rmht' ?
                                        <div>
                                             <Row style={{ marginBottom: '20px'}}>
                                                <Input 
                                                style={{width: '800px'}} 
                                                addonBefore="按钮标题:" 
                                                onChange={(e) => {this.handleValue(e.target.value, index, 'buttonTitle')}}
                                                value={item.buttonTitle}></Input>
                                            </Row>
                                            <Row style={{ marginBottom: '20px'}}>
                                                <Input 
                                                style={{width: '800px'}} 
                                                addonBefore="按钮跳转链接:" 
                                                onChange={(e) => {this.handleValue(e.target.value, index, 'buttonJumpAction')}}
                                                value={item.buttonJumpAction}></Input>
                                             </Row>
                                             <Row style={{ marginBottom: '20px'}}>
                                                <Input 
                                                style={{width: '800px'}} 
                                                addonBefore="按钮跳转链接版本控制:" 
                                                suffix={
                                                    <Tooltip title="*格式(由新功能上线的版本和跳转组成)：version=5.63.01###action=gslist">
                                                        <Icon type="question-circle" />
                                                    </Tooltip>
                                                    }
                                                onChange={(e) => {this.handleValue(e.target.value, index, 'buttonVersionControl')}}
                                                value={item.buttonVersionControl}></Input>
                                            </Row>
                                        </div> : null
                                }
                                

                                <Row style={{ marginBottom: '20px'}}>
                                    是否需要判断登录显示：
                                    <Select
                                        style={{width: '300px'}} 
                                        value={item.needlogin}
                                        onChange={(value) => {this.handleValue(value, index, 'needlogin')}}
                                        >
                                            {
                                                Object.keys(_need_login).map((needLoginKey, needLoginIndex) => {
                                                    return (
                                                        <Option value={needLoginKey} key={needLoginKey}>
                                                            {_need_login[needLoginKey]}
                                                        </Option>
                                                    )
                                                })
                                            }
                                    </Select>
                                </Row>
                                <Row style={{ marginBottom: '20px'}}>
                                    开始时间： 
                                    <DatePicker 
                                    format="YYYY-MM-DD HH:mm:ss"
                                    showTime 
                                    style={{width: '300px',  marginRight: '68px'}} 
                                    onChange={(date, dateString) => {this.handleValue(dateString, index, 'startTime')}}
                                    value={moment(item.startTime, 'YYYY/MM/DD HH:mm:ss')}
                                    />
                                    结束时间：
                                    <DatePicker 
                                    format="YYYY-MM-DD HH:mm:ss"
                                    showTime 
                                    style={{width: '300px'}} 
                                    onChange={(date, dateString) => {this.handleValue(dateString, index, 'endTime')}}
                                    value={moment(item.endTime, 'YYYY/MM/DD HH:mm:ss')}
                                    />
                                </Row>
                            </Panel>
                        )
                    })
                }
                </Collapse>

                <section >
                    <Button 
                    type="primary"
                    onClick={this.save}
                    >保存</Button>

                    <Popconfirm
                    placement="rightBottom"
                    title={`你确定要提交key值么`}
                    onConfirm={this.upload}
                    okText="确认"
                    cancelText="取消"
                    >
                        <Button 
                        type="primary"
                        style={{marginLeft:'955px'}}
                        >提交</Button>
                    </Popconfirm>
                </section>

                <section
                 style={{marginTop: '100px'}}>
                    <Select
                    style={{width: '300px', marginRight: '100px'}} 
                    value={this.state.cate}
                    onChange={(value) => {this.handleCate(value)}}
                    >
                        {
                            Object.keys(_cate_name).map((cateKey, cateIndex) => {
                                return (
                                    <Option value={cateKey} key={cateKey}>
                                        {_cate_name[cateKey]}
                                    </Option>
                                )
                            })
                        }
                    </Select>
                    <Button type="primary" onClick={this.addList}>添加</Button>
                </section>

            </article>
        )
    }
}

export default kycCard;
