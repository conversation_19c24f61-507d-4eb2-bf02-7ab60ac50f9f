export interface OutsideTrackItem {
  // 显示文本
  label: string;
  // 实际值
  value: string;
  // 子选项（可选，用于非叶子节点）
  children?: OutsideTrackItem[];
}

export interface TrackItem {
  trackCode: string;
  trackName: string;
}

export interface IndustryItem {
  // 名称
  name: string;
  // 概述
  summary: string;
}

export interface IndustryDetail {
  // 标题
  title: string;
  // 产业链
  industry: IndustryItem[];
}

export interface IndustryConfig {
  // 产业链开关
  switch: boolean;
  // tab名称
  tab: string;
  // 产业链详情
  detail: {
    // 上游
    upstream?: IndustryDetail;
    // 中游
    midstream?: IndustryDetail;
    // 下游
    downstream?: IndustryDetail;
  };
}

export interface Block {
  // 板块名称
  name: string;
  // 板块代码
  code: string;
  // 板块市场
  market: string;
}

export interface PageConfig {
  // 页面标题
  title: string;
  // 摘要
  digest: string;
  // AB开关
  abTest: boolean;
  // AB用户尾号
  abValue: string;
  // 资讯应用id
  newsAppId: string;
  // 概念板块
  conceptBlock: Block;
  // 产业链
  industryConfig: IndustryConfig;
}

export interface OpportunityItem {
  // 页面ID
  configId?: number;
  // 页面名称
  name: string;
  // 配置分类分组
  group: number;
  //状态，状态0:ai生成中，1:ai生成完成 2:ai生成失败 3:填写完整（可发布）4:已上线 5:已下线
  status: number;
  // 是否使用AI
  useAi?: boolean;
  // 操作人
  operator: string;
  // 创建时间
  ctime: string;
  // 修改时间
  mtime: string;
  data: PageConfig;
}

export interface SelectedETF {
  code: string;
  name: string;
  marketId: string;
  // 比例
  result: string;
  // 自定义简称
  customName?: string;
  // 自定义亮点
  customHighlight?: string;
}

export interface ETFConfigItem {
  // 所属类型
  position: string;
  // ETF赛道
  trackCode: string;
  // 选择的ETF
  selectedETF: SelectedETF[];
  // 选择的产品完整信息（提交时生成）
  selectedProductInfo?: SelectedETF[];
}

export interface FundConfigItem {
  // 所属类型
  position: string;
  // 赛道
  trackCode: string;
  // 选择的ETF
  selectedFund: Omit<SelectedETF, 'marketId'>;
  // 选择的产品完整信息（提交时生成）
  selectedProductInfo?: SelectedETF[];
}

export interface FundConfig {
  // 场内
  inside: ETFConfigItem[];
  // 场外
  outside: FundConfigItem[];
}

// 新增：ETF自定义配置项
export interface ETFCustomItem {
  // 自定义简称
  customName?: string;
  // 自定义亮点
  customHighlight?: string;
}

// 新增：ETF自定义配置映射
export interface ETFCustomConfig {
  [etfCode: string]: ETFCustomItem;
}

// 查询参数接口
export interface QueryParams {
  date?: string;
  name?: string;
  operator?: string;
}

// 分页响应数据类型
export interface PaginationResponse<T> {
  list: T[];
  total: number;
  pageNo: number;
  pageSize: number;
  totalPage: number;
}

// API响应类型
export interface ApiResponse<T> {
  status_code: number;
  status_msg: string;
  data: T;
}

// AI生成参数接口
export interface AiGenerateParams {
  group: number;
  status: number;
  useAi: boolean;
  name: string;
  aiInfo: {
    type: string;
    id: string;
    prompt: string;
  };
  operator: string;
  configId?: number;
}

// AI生成弹窗组件Props接口
export interface AiGenerateModalProps {
  // 是否显示弹窗
  showDialog: boolean;
  // 关闭弹窗回调
  onClose: () => void;
  // AI生成提交回调
  onSubmit: (data: AiGenerateParams) => Promise<void>;
  // 初始数据（可选，用于参数回填）
  initialData?: {
    configId?: number;
    name?: string;
    aiId?: string;
    newsContent?: string;
    targetIndustry?: string;
    initialIndustryChain?: string;
  };
}

// AI生成API响应接口
export interface AiGenerateResponse {
  // 生成的配置ID
  configId: number;
  // 生成状态
  status: number;
  // 页面名称
  name: string;
}
