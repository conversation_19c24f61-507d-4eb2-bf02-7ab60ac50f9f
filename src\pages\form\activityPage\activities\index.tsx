import React, {useEffect, useState} from 'react'
import api from 'api';
import Activity from './activity'
import {Button,message, Card, Collapse, Modal, DatePicker, Input} from 'antd';

const {
    fetchActivityList,
    updateActivity,
    fetchFundAwardRegister
} = api

interface activityItem {
    activityId?: string
    activityName: string
    startDate: string
    endDate: string
    status: string
    source: string
    remark: string
}

export default function () {

    const [activityId, setActivityId] = useState('') //下载活动Id
    const [startTime, setStartTime] = useState('') //开始时间
    const [endTime, setEndTime] = useState('') //结束时间
    const [activities, setActivities] = useState([] as activityItem[])
    const [isShowAdd, setIsShowAdd] = useState(false)

    useEffect(() => {
        _.fundLoading()
        fetchActivityList().then((data: any) => {
            _.hideFundLoading()
            if (data.code !== '0000') return message.error(data.message)
            data = data.data
            let _activities: activityItem[] = []
            for (let key in data) {
                _activities.push(data[key])
            }
            _activities = _activities.sort((a: any, b: any) =>(b.activityId - a.activityId))
            setActivities(_activities)
        }).catch((e: Error) => {
            _.hideFundLoading()
            message.error(e.message)
        })
    }, [])

    function showAdd() {
        setIsShowAdd(true)
    }

    function onchangeDownLoadTime (value: any) {
        // largeTransferStartTime
        function getTime(Date: Date) {
            let _year = Date.getFullYear();
            let _month: number | string = Date.getMonth() + 1;
            _month = _month < 10 ? '0' + _month : _month;
            let _day: number | string = Date.getDate();
            _day = _day < 10 ? '0' + _day : _day;
            let _hour: number | string = Date.getHours();
            _hour = _hour < 10 ? '0' + _hour : _hour;
            let _minute: number | string = Date.getMinutes();
            _minute = _minute < 10 ? '0' + _minute : _minute;
            let _second: number | string = Date.getSeconds();
            _second = _second < 10 ? '0' + _second : _second;
            return `${_year}-${_month}-${_day} ${_hour}:${_minute}:${_second}`;
        }
        let _startTime = getTime(value[0]._d);
        let _endTime = getTime(value[1]._d);
        setStartTime(_startTime)
        setEndTime(_endTime)
    }

    function downLoadFundActivity () {
        if (!activityId) {
            message.error('请填写活动ID');
            return;
        }
        fetchFundAwardRegister({},
            activityId,
            `&startDate=${startTime}&endDate=${endTime}`,
            {
            responseType: 'blob'
        }).then((data: any) => {
            if (data.data && data.data.size === 0) message.error('下载失败，请查看参数是否正确或稍后再试')
        })
    }

    return (
        <div>
            <Card
                style={{width: '1000px', marginTop: '10px'}}
                title={'基金奖励导出'}
            >
                <div>
                    <div style={{marginTop: '20px', marginBottom: '20px'}}>
                        <DatePicker.RangePicker showTime onOk={onchangeDownLoadTime} onChange={onchangeDownLoadTime}/>
                    </div>
                    <div>
                        <Input 
                        addonBefore="活动Id"
                        value={activityId} 
                        onChange={(e) => { setActivityId(e.target.value)}} style={{width: 400}}></Input>
                    </div>
                    
                    <Button
                        type="primary" 
                        style={{ float: 'right' }}
                        onClick={downLoadFundActivity}
                    >
                        下载
                    </Button>
                </div>
            </Card>
            {
                activities.map((activity: activityItem, index: number) => {
                    return (
                        <Collapse key={index} style={{width: 1000, marginTop: '10px'}}>
                            <Collapse.Panel
                            header={<div className="u-l-middle">
                                <span>{`活动id:${activity.activityId}`}</span>
                                <span className="g-ml30">{`活动名称:${activity.activityName}`}</span>
                                <span className="g-ml30">{`活动负责人:${activity.source}`}</span>
                            </div>}
                            key="1">
                                <Activity 
                                activity = {activity}
                                setIsShowAdd = {() => {}}
                                />
                            </Collapse.Panel>
                        </Collapse>
                    )
                })
            }
            <Button type="primary" style={{marginTop: '20px'}} onClick={showAdd}>增加活动</Button>

            <Modal title="增加活动" visible={isShowAdd} footer={null} closable={false} width={800}>
                <Activity 
                    isAdd = {true}
                    setIsShowAdd = {setIsShowAdd}
                />
            </Modal>
        </div>
    )
}