import React, { useState, useEffect } from 'react';

import { Input } from 'antd';

interface dataProps {
  onChange: any;
  tabData: any;
}

export default function(props: dataProps) {
  const { onChange, tabData } = props;

  const changeInput = (type: string, value: string) => {
		let _changeData = {
			tab1Name: tabData.tab1Name,
			tab1Desc: tabData.tab1Desc,
			tab2Name: tabData.tab2Name,
			tab2Desc: tabData.tab2Desc
		}
    switch (type) {
      case 'name1':
				_changeData.tab1Name = value;
        break;
      case 'desc1':
				_changeData.tab1Desc = value;
        break;
      case 'name2':
				_changeData.tab2Name = value;
        break;
      case 'desc2':
				_changeData.tab2Desc = value;
        break;
      default:
        break;
    }
		onChange(_changeData);
  };

  return (
    <article>
      <div>
        <span>
          <b style={{ color: '#fe5d4e' }}>* </b>系列1名称
        </span>
        <Input
          style={{ width: 200, marginLeft: 10 }}
          value={tabData.tab1Name}
          onChange={e => {
            changeInput('name1', e.target.value);
          }}
          placeholder="最多填入四个字"
					maxLength={4}
        />
      </div>
      <div style={{ marginTop: 20 }}>
        <span>
          <b style={{ color: '#fe5d4e' }}>* </b>系列1文案
        </span>
        <Input
          style={{ width: 500, marginLeft: 10 }}
          value={tabData.tab1Desc}
          onChange={e => {
            changeInput('desc1', e.target.value);
          }}
        />
      </div>
      <div style={{ marginTop: 20 }}>
        <span>
          <b style={{ color: '#fe5d4e' }}>* </b>系列2名称
        </span>
        <Input
          style={{ width: 200, marginLeft: 10 }}
          value={tabData.tab2Name}
          onChange={e => {
            changeInput('name2', e.target.value);
          }}
          placeholder="最多填入四个字"
					maxLength={4}
        />
      </div>
      <div style={{ marginTop: 20 }}>
        <span>
          <b style={{ color: '#fe5d4e' }}>* </b>系列2文案
        </span>
        <Input
          style={{ width: 500, marginLeft: 10 }}
          value={tabData.tab2Desc}
          onChange={e => {
            changeInput('desc2', e.target.value);
          }}
        />
      </div>
    </article>
  );
}
