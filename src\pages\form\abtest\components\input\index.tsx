import React from 'react';
import { Input } from 'antd';
import styles from './index.less';

export function InputLeft({ value, onChange, name }: any) {
  return (
    <div>
      <Input
        className={styles['input-left']}
        value={value}
        onChange={e => onChange(name, e.target.value)}
      />
    </div>
  );
}

export function InputRight({ value, onChange, name }: any) {
  return (
    <div style={{ display: 'flex' }}>
      <strong>~</strong>
      <Input
        className={styles['input-right']}
        value={value}
        onChange={e => onChange(name, e.target.value)}
      />
    </div>
  );
}

export function HtmlText({ value }: any) {
  return (
    <div className={styles['html-text']}>
      <span>{`${value}`}</span>
    </div>
  );
}

export function HeaderNote({ value }: any) {
  return (
    <div className={styles['header-note']}>
      <span>{`${value}`}</span>
    </div>
  );
}
