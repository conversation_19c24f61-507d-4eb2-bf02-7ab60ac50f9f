import React, { useEffect, useState } from "react";
import api from 'api'
import { Table, Divider, Button, Popconfirm, Modal, Input, message, Cascader, Drawer } from 'antd';
import './index.less'
import ImgUpload from '../../../frontend/compoment/uploadImg'
import WrappedInvestForm from './investDrawer';

const { postLazyfundcompanyTactics, postLazyCatAllConfig, postHomeConfig } = api;

export default function () {
    const [typeData, setTypeData] = useState([]);
    const [fundcompanyTactics, setFundcompanyTactics] = useState([]);
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);
    const [loading, setLoading] = useState(true);
    const [visibleType, setVisibleType] = useState(false);
    const [editVisibleType, setEditVisibleType] = useState(false);
    const [visibleProduct, setVisibleProduct] = useState(false);
    const [pictureUrl, setPictureUrl] = useState('');
    const [addTypeInput, setAddTypeInput] = useState('');
    const [addAbbrInput, setAddAbbrInput] = useState('');
    const [addDescInput, setAddDescInput] = useState('');
    const [editPictureUrl, setEditPictureUrl] = useState('');
    const [editAddTypeInput, setEditAddTypeInput] = useState('');
    const [editAbbrInput, setEditAbbrInput] = useState('');
    const [editDescInput, setEditDescInput] = useState('');
    const [strategyoptions, setStrategyoptions] = useState([]);
    const [fundCompanyOption, setFundCompanyOption] = useState({});
    const [strategyOption, setStrategyOption] = useState({});
    const [pIndex, setPindex] = useState(0);
    const [strategyEdit, setStrategyEdit] = useState(false); 
    const [currentStrategyData, setCurrentStrategyData] = useState({});
    const [currentIndex, setCurrentIndex] = useState(null);
    const [currentSubIndex, setCurrentSubIndex] = useState(null);
    useEffect(() => {
        postLazyfundcompanyTactics().then(data => {
            if(data.code == '0000') {
                let dataSource = data.data;
                setFundcompanyTactics(dataSource);
            } else {
                message.error(data.success);
            }
        });

        postLazyCatAllConfig({
            type: 'query'
        }).then(data => {
            setLoading(false);
            let dataSource = data.data;
            dataSource[0].test = 1;
            setTypeData(dataSource);
        });
    }, []);

    // 子表格配置项
    const expandedRowRender = (mainRecord, index, indent, expanded) => {
        const data = mainRecord.typeRelationList;
        const columns = [
            {
                title: '基金公司',
                dataIndex: 'taName',
                width: '30%',
            },
            {
                title: '投顾策略名称',
                dataIndex: 'investConsultTacticsName',
                width: '30%',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                render: (text, minorRecord, subIndex) => (
                    <span>
                        <a onClick={() => { handleProductEdit(index, subIndex, minorRecord) }}>编辑</a>
                        <Divider type="vertical" />
                        <a onClick={() => { handleDeleteProduct(index, subIndex) }}>删除</a>
                    </span>
                ),
            },
        ];

        return <Table columns={columns} dataSource={data} pagination={false} />;
    };

    // 主表格列配置项
    const columns = [
        {
            title: '类型名称',
            dataIndex: 'typeName',
            width: '20%',
        },
        {
            title: '类型简称(埋点用)',
            dataIndex: 'typeAbbr',
            width: '10%',
        },
        {
            title: '配置理念图片',
            dataIndex: 'pictureUrl',
            width: '30%',
        },
        {
            title: '描述',
            dataIndex: 'description',
            width: '15%',
        },
        {
            title: '操作',
            render: (text, record, index) => (
                <span>
                    <a onClick={() => { handleEdit(record, index) }}>编辑</a>
                    <Divider type="vertical" />
                    <a onClick={() => handleDeleteType(index)}>删除</a>
                    <Divider type="vertical" />
                    <a onClick={() => handleAddProduct(record, index)}>新增产品</a>
                </span>
            ),
        },
    ];

    // 编辑处理函数
    const handleEdit = (record, index) => {
        setPindex(index);
        setEditVisibleType(true);
        setEditPictureUrl(record.pictureUrl);
        setEditAddTypeInput(record.typeName);
        setEditDescInput(record.description);
        setEditAbbrInput(record.typeAbbr);
    }

    // 添加类型函数
    const handleAddType = () => {
        setVisibleType(true);
    }

    // 删除类型处理函数
    const handleDeleteType = index => {
        let dataSource = [...typeData];
        dataSource.splice(index, 1);
        setTypeData(dataSource);
    };
    const handleProductEdit = (index, subIndex, minorRecord) => {
        setCurrentIndex(index);
        setCurrentSubIndex(subIndex);
        setCurrentStrategyData(minorRecord);
        setStrategyEdit(true);
    }
    const onEditClose = () => {
        setStrategyEdit(false);
    }
    const handleStrategyData = (data) => {
        let dataSource = [...typeData];
        dataSource[currentIndex].typeRelationList[currentSubIndex] = data;
        console.log(dataSource);
        setTypeData(dataSource);
    }
    // 删除类型产品处理函数
    const handleDeleteProduct = (index, subIndex) => {
        let dataSource = [...typeData];
        let list = dataSource[index].typeRelationList;
        list.splice(subIndex, 1);
        dataSource[index].typeRelationList = list;
        setTypeData(dataSource);
    }
    // 子表格展开处理函数
    const handleExpaned = (expanded, record) => {
        if (expanded) {
            setExpandedRowKeys([record.typeName]);
        } else {
            setExpandedRowKeys([]);
        }
    }

    // 新增产品处理函数
    const handleAddProduct = (record, index) => {
        setPindex(index);
        setVisibleProduct(true);
        setExpandedRowKeys([record.typeName]);
    }
    // 表格数据提交处理函数
    const handleSubmit = () => {
        console.log(typeData);
        postLazyCatAllConfig({
            type: 'update',
            value: JSON.stringify(typeData)
        }).then(data => {
            console.log('data', data);
            if(data.code == '0000') {
                message.success('提交成功', () => {
                  window.location.reload(true);
                });
            } else {
                message.warning(data.message, () => {
                  window.location.reload(true);
                });
            }
        })
    }

    // 新增类型配置理念图片处理函数
    const handlePictureConfig = (url) => {
        setPictureUrl(url);
    }

    const handleEditPictureConfig = (url) => {
        setEditPictureUrl(url);
    }
    // 取消新增类型处理函数
    const handleAddTypeCancel = () => {
        setVisibleType(false);
    }
    const handleEditAddTypeCancel = () => {
        setEditVisibleType(false);
    }
    // 保存新增类型处理函数
    const handleAddTypeOk = () => {
        const dataSource = typeData ? typeData : [];
        const newData = {
            typeId: '',
            typeName: addTypeInput,
            pictureUrl: pictureUrl,
            description: addDescInput,
            typeAbbr: addAbbrInput,
            typeRelationList: []
        };
        if (!newData.typeName) {
            message.warning('有必填项未填写');
        } else {
            setTypeData([...dataSource, newData]);
            setVisibleType(false);
        }
    }
    const handleEditAddTypeOk = () => {
        let dataSource = [...typeData];
        dataSource[pIndex].typeName = editAddTypeInput;
        dataSource[pIndex].pictureUrl = editPictureUrl;
        dataSource[pIndex].description = editDescInput;
        dataSource[pIndex].typeAbbr = editAbbrInput;
        setTypeData(dataSource);
        setEditVisibleType(false);
    }
    // 保存新增产品处理函数
    const handleAddProductOk = () => {
        let dataSource = [...typeData];
        let data = { ...fundCompanyOption, ...strategyOption };
        let temp = dataSource[pIndex].typeRelationList.filter(item => {
            return item.consultTacticsId == strategyOption.consultTacticsId;
        })
        if (temp.length > 0) {
            message.warning('该投顾策略名称已添加');
        } else {
            dataSource[pIndex].typeRelationList.push(data);
            setTypeData(dataSource);
            setVisibleProduct(false);
        }
    }
    const handleAddTypeInput = (e) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            setAddTypeInput(e.target.value);
        }
    }
    const handleAddAbbrInput = (e) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            setAddAbbrInput(e.target.value);
        }
    }
    const handleAddDescInput = (e) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            setAddDescInput(e.target.value);
        }
    }
    const handleEditAddTypeInput = (e) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            setEditAddTypeInput(e.target.value);
        }
    }

    const handleEditDescInput = (e) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            setEditDescInput(e.target.value);
        }
    }

    const handleEditAbbrInput = (e) => {
        if (!e.target.value) {
            e.target.focus();
            message.warning('该项为必填项');
        } else {
            setEditAbbrInput(e.target.value);
        }
    }


    const handleAddProductCancel = () => {
        setVisibleProduct(false);
    }
    const handlefundComponeyChange = (value, selectedOptions) => {
        let temp = {};
        temp.taCode = selectedOptions[0].taCode;
        temp.taName = selectedOptions[0].taName;
        setFundCompanyOption(temp);
        let data = [...fundcompanyTactics];
        let dataSource;
        data.map(item => {
            if (value[0] == item.taCode) {
                dataSource = item.consultTacticsList;
            }
        });
        setStrategyoptions(dataSource);
    }

    const handleStrategyChange = (value, selectedOptions) => {
        setStrategyOption(selectedOptions[0]);
    }
    return (
        <section>
            <section style={{ display: 'flex', marginBottom: '20px', alignItems: 'center' }}>
        <Button
  
          type="link"
          onClick={() => {
            window.location.hash = '/form/touguManage/home';
          }}
        >
          投顾管家主页配置
        </Button>
        <Button
          style={{ marginLeft: '120px' }}
          type="link"
          onClick={() => {
            window.location.hash = '/form/touguManage/investAdviser';
          }}
        >
          投顾安管家投顾产品配置
        </Button>
     
      </section>
            <section className="strategy-header">
                <span className="strategy-type-title">策略类型配置</span>
                <Button
                    type="primary"
                    onClick={handleAddType}
                >新增类型</Button>
            </section>
            <Table
                loading={loading}
                className="components-table-demo-nested"
                columns={columns}
                expandedRowRender={expandedRowRender}
                dataSource={typeData}
                pagination={false}
                onExpand={handleExpaned}
                rowKey={record => record.typeName}
                expandedRowKeys={expandedRowKeys}
            />
            <section className="strategy-footer">
                <Popconfirm
                    title="确定要提交吗？"
                    onConfirm={handleSubmit}
                    okText="确定"
                    cancelText="取消"
                >
                    <Button
                        type="primary"
                    >提交</Button>
                </Popconfirm>
            </section>
            {/* 新增类型 */}
            {
                visibleType ?
                    <Modal
                        visible={visibleType}
                        closable={false}
                        onOk={handleAddTypeOk}
                        onCancel={handleAddTypeCancel}
                        okText="保存"
                        cancelText="取消"
                    >
                        <section className="strategy-addType">
                            <span>类型名称：</span>
                            <Input
                                style={{ width: '300px' }}
                                placeholder="请输入类型名称"
                                allowClear={true}
                                onPressEnter={handleAddTypeInput}
                                onBlur={handleAddTypeInput}
                            ></Input>
                            <span className="warning">*</span>
                        </section>
                        <section className="strategy-addType">
                            <span>类型简称(埋点用)：</span>
                            <Input
                                style={{ width: '300px' }}
                                placeholder="请输入类型简称"
                                allowClear={true}
                                onPressEnter={handleAddAbbrInput}
                                onBlur={handleAddAbbrInput}
                            ></Input>
                            <span className="warning">*</span>
                        </section>
                        <section className="strategy-addType">
                            <span>描述：</span>
                            <Input
                                style={{ width: '300px' }}
                                placeholder="请输入描述"
                                allowClear={true}
                                onPressEnter={handleAddDescInput}
                                onBlur={handleAddDescInput}
                            ></Input>
                            <span className="warning">*</span>
                        </section>
                        <section className="strategy-picture">
                            <span>配置理念图片：</span>
                            <ImgUpload
                                isEdit={true}
                                handleChange={handlePictureConfig}
                                size={['750px*495px']}
                            ></ImgUpload>
                        </section>
                    </Modal>
                    : ''
            }
            {/* 编辑 */}
            {
                editVisibleType ?
                    <Modal
                        visible={editVisibleType}
                        closable={false}
                        onOk={handleEditAddTypeOk}
                        onCancel={handleEditAddTypeCancel}
                        okText="保存"
                        cancelText="取消"
                    >
                        <section className="strategy-addType">
                            <span>类型名称：</span>
                            <Input
                                defaultValue={editAddTypeInput}
                                style={{ width: '300px' }}
                                placeholder="请输入类型名称"
                                allowClear={true}
                                onPressEnter={handleEditAddTypeInput}
                                onBlur={handleEditAddTypeInput}
                            ></Input>
                            <span className="warning">*</span>
                        </section>
                        <section className="strategy-addType">
                            <span>类型简称(埋点用)：</span>
                            <Input
                                defaultValue={editAbbrInput}
                                style={{ width: '300px' }}
                                placeholder="请输入类型名称"
                                allowClear={true}
                                onPressEnter={handleEditAbbrInput}
                                onBlur={handleEditAbbrInput}
                            ></Input>
                            <span className="warning">*</span>
                        </section>
                        <section className="strategy-addType">
                            <span>描述：</span>
                            <Input
                                defaultValue={editDescInput}
                                style={{ width: '300px' }}
                                placeholder="请输入描述"
                                allowClear={true}
                                onPressEnter={handleEditDescInput}
                                onBlur={handleEditDescInput}
                            ></Input>
                            <span className="warning">*</span>
                        </section>
                        <section className="strategy-picture">
                            <span>配置理念图片：</span>
                            <ImgUpload
                                imageUrl={editPictureUrl}
                                isEdit={true}
                                handleChange={handleEditPictureConfig}
                                size={['750px*495px']}
                            ></ImgUpload>
                        </section>
                    </Modal>
                    : ''
            }
            {/* 新增产品 */}
            {
                visibleProduct ?
                    <Modal
                        visible={visibleProduct}
                        closable={false}
                        onOk={handleAddProductOk}
                        onCancel={handleAddProductCancel}
                        okText="保存"
                        cancelText="取消"
                    >
                        <section className="strategy-addType">
                            <Cascader
                                allowClear={false}
                                style={{ marginRight: '20px' }}
                                fieldNames={{ label: 'taName', value: 'taCode' }}
                                options={fundcompanyTactics}
                                onChange={handlefundComponeyChange}
                                placeholder="请选择基金公司"
                            />
                            <Cascader
                                allowClear={false}
                                fieldNames={{ label: 'investConsultTacticsName', value: 'consultTacticsId' }}
                                options={strategyoptions}
                                onChange={handleStrategyChange}
                                placeholder="请选择投顾策略名称"
                            />
                        </section>
                    </Modal>
                    : ''
            }
            
            { strategyEdit && (
                <Drawer
                    width={1000}
                    title="投顾策略类型配置"
                    placement="right"
                    closable
                    onClose={onEditClose}
                    visible={strategyEdit}
                    >
                    { <WrappedInvestForm handleData={handleStrategyData} currentData={currentStrategyData} onEditClose={onEditClose}></WrappedInvestForm> }
                </Drawer>
            ) }
        </section>
    );
}
