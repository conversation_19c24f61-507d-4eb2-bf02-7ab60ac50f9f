{"type": "object", "required": ["name", "imgUrl", "jumpUrl"], "properties": {"name": {"title": "活动名称", "type": "string", "ui:options": {}}, "imgUrl": {"title": "活动图片", "type": "string", "ui:widget": "uploadImg"}, "jumpUrl": {"title": "跳转链接", "type": "string", "ui:options": {}}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime"}, "mutex": {"title": "互斥情况", "type": "string", "enum": ["0", "1"], "enumNames": ["不互斥", "互斥"], "ui:widget": "radio"}}}