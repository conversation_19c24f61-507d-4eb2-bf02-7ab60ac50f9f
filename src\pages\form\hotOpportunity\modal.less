.industry-item-group {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  background-color: #fafafa;
  position: relative;
  
  &:hover {
    border-color: #40a9ff;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.industry-item-group-header {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  font-weight: 500;
}

// AI推荐提示相关样式
.aiRecommendMessage {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.4;
  
  &.success {
    color: #52c41a; // 成功匹配的绿色
  }
  
  &.warning {
    color: #faad14; // 未匹配的警告橙色
  }
}

// 产业链动态配置相关样式
.industry-chain-section {
  margin-top: 16px;
}

.industry-chain-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin-bottom: 16px;
  background-color: #fafafa;
  
  &:hover {
    border-color: #40a9ff;
  }
}

.industry-chain-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #d9d9d9;
  border-radius: 6px 6px 0 0;
}

.industry-chain-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.industry-chain-content {
  padding: 16px;
}

.add-industry-chain-section {
  padding: 16px;
  background-color: #f8f9fa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  text-align: center;
  margin-bottom: 16px;
  
  &:hover {
    border-color: #40a9ff;
    background-color: #f0f8ff;
  }
}

.empty-industry-chain-tip {
  padding: 24px;
  text-align: center;
  color: #999;
  font-style: italic;
  background-color: #f8f9fa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

// 预览功能相关样式
.preview-section {
  margin-top: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  
  :global(.content-preview) {
    .preview-title {
      font-size: 13px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px solid #e8e8e8;
    }
    
    .preview-content {
      line-height: 1.6;
      color: #333;
      margin-bottom: 8px;
      min-height: 20px;
      padding: 8px 0;
      
      &.empty {
        color: #999;
        font-style: italic;
      }
      
      // 超链接样式
      a {
        color: #1890ff;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
        
        &:visited {
          color: #722ed1;
        }
      }
    }
    
    .preview-placeholder {
      color: #999;
      font-style: italic;
    }
    
    .preview-help {
      font-size: 11px;
      color: #999;
      padding-top: 6px;
      border-top: 1px dashed #d9d9d9;
    }
  }
} 