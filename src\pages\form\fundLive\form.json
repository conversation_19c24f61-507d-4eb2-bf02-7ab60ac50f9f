{"propsSchema": {"type": "object", "properties": {"topRecommended": {"title": "头部推荐位", "type": "array", "maxItems": 3, "items": {"type": "object", "properties": {"fundCompany": {"title": "基金公司", "type": "string", "ui:width": "40%"}, "headUrl": {"title": "基金公司头像地址", "type": "string", "ui:width": "60%", "format": "image", "description": "请配置https地址", "pattern": "^https://.+jpg|png|bmp|tif|gif|svg|psd|webp$"}, "liveTheme": {"title": "直播主题", "type": "string", "ui:width": "60%"}, "liveCover": {"title": "直播封面", "type": "string", "format": "image", "description": "请配置https地址", "pattern": "^https://.+jpg|png|bmp|tif|gif|svg|psd|webp$"}, "liveTime": {"title": "直播时间", "type": "range", "format": "dateTime", "ui:width": 500, "ui:options": {"placeholder": ["开始时间", "结束时间"]}}, "liveAddress": {"title": "直播地址", "type": "string", "pattern": "^http(|s)?://[^\n ，]*$"}}, "required": ["fundCompany", "headUrl", "liveTheme", "liveCover", "liveTime", "liveAddress"]}}, "productList": {"title": "产品列表页", "type": "array", "items": {"type": "object", "properties": {"fundCompany": {"title": "基金公司", "type": "string", "ui:width": "40%"}, "liveGuest": {"title": "直播嘉宾", "type": "string", "ui:width": "40%"}, "headUrl": {"title": "基金公司头像地址", "type": "string", "ui:width": "60%", "format": "image", "description": "请配置https地址", "pattern": "^https://(.+)jpg|png|bmp|tif|gif|svg|psd|webp$"}, "liveTheme": {"title": "直播主题", "type": "string", "ui:width": "70%"}, "liveCover": {"title": "直播封面", "type": "string", "format": "image", "description": "请配置https地址", "pattern": "^https://.+jpg|png|bmp|tif|gif|svg|psd|webp$"}, "liveTime": {"title": "直播时间", "type": "range", "format": "dateTime", "ui:width": 500, "ui:options": {"placeholder": ["开始时间", "结束时间"]}}, "liveAddress": {"title": "直播地址", "type": "string", "pattern": "^http(|s)?://[^\n ，]*$"}}, "required": ["fundCompany", "liveGuest", "headUrl", "liveTheme", "liveCover", "liveTime", "liveAddress"]}}}}, "formData": {"topRecommended": [], "productList": []}}