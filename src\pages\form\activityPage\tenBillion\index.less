:global {
    .ten-billion-drawer {
        .ant-col {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: nowrap;
            div {
                white-space: nowrap;
            }
            .head-thumbnail {
                width: 180px;
                height: 270px;
                margin-right: 20px;
                border: 1px dashed #cccccc;
                border-radius: 4px;
                display: inline-block;
                vertical-align: bottom;
            }
        }
    }
}
.m-drag {
    height: 100%;
    width: 1400px;
    overflow: hidden;
  }
  .tag {
    margin: 3px;
    font-size: 13px;
    border: 1px dashed #cccccc;
    border-radius: 4px;
    // padding: 0 8px;
    line-height: 30px;
    color: #666666;
    background: rgba(255, 255, 255, 0.7);
    
  }
  .m-row{
        padding: 5px;
        width: 100%;
        .m-header {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            flex-wrap: nowrap;
        }
        .m-title {
            font-weight: 600;
        }
        .m-delete {
            color: #f00;
            text-align: end;
            cursor: pointer;
        }
        .m-add {
            color: #1890ff;
            text-align: end;
            cursor: pointer;
            font-weight: 400;
        }
        .thumbnail {
            width: 180px;
            height: 80px;
            margin-right: 20px;
            border: 1px dashed #cccccc;
            border-radius: 4px;
            display: inline-block;
            vertical-align: bottom;
        }
  }