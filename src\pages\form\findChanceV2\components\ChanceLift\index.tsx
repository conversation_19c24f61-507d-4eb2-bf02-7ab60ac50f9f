import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ChanceLift, ConfigData, HotSpot, ChangeLiftType, ChanceLiftType } from '../../type';
import { Card, DatePicker, Select, Input, Button, Popconfirm, message } from 'antd';
import InputTitle from '../InputTitle';
import { chanceLiftHelper } from './helper';
import UploadImg from '../UploadImg';
import api from 'api';
import useDebounce from '@/hooks/useDebounce';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import DragAndSortWrapper from '../DragAndSortWrapper';
const { fetchFundNameByCode } = api;

export interface IProps {
  data: ConfigData['chanceLift'];
  onChange: (data: ConfigData['chanceLift']) => void;
  disabled: boolean;
}
const ChanceLift: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleAdd = (type: ChanceLiftType) => {
    onChange([chanceLiftHelper.addArrayData(type), ...(data || [])]);
  };
  const handleChange = (index, name, value) => {
    const newData = [...data];
    newData[index][name] = value;
    onChange(newData);
  };
  const handleDel = index => {
    const newData = [...data];
    newData.splice(index, 1);
    onChange(newData);
  };
  /**
   * 获取基金名称
   */
  const getFundName = (code: string, key: string, index?: number) => {
    handleChange(index, key, '');
    if (code === '') return;
    code = code.trim();
    fetchFundNameByCode({
      fundCode: code,
    })
      .then((data: any) => {
        if (data.code === '0000') {
          if (data.data) {
            let name = data.data.name;
            handleChange(index, key, name);
          } else {
            message.error('该基金不存在，请重新输入');
          }
        } else {
          message.error('调用基金详情接口失败，请稍后再试');
        }
      })
      .catch(() => {
        message.error('调用基金详情接口失败，请稍后再试');
      });
  };

  const changePosition = (dragIndex, hoverIndex) => {
    let dragItem = data[dragIndex];
    let hoverItem = data[hoverIndex];
    if (!dragItem || !hoverItem) {
      return;
    }
    //hoverIndex后面的都往后移动一位

    let newData = [...data];
    //先去掉在数组中被拖动的数据
    newData.splice(dragIndex, 1, undefined);

    //获取放置位置以及后面的数据
    const postfixArr = newData.slice(hoverIndex) || [];
    const prefixArr = newData.slice(0, hoverIndex + 1) || [];
    prefixArr[hoverIndex] = dragItem;
    const changeIndexKeys = [];

    newData = [...prefixArr, ...postfixArr]
      .filter(item => Boolean(item))
      .map((item, index) => {
        if (item.index !== index) {
          item.index = index;
          changeIndexKeys.push(item.id);
        }
        return item;
      });

    //   dragItem={...dragItem,index:hoverIndex};
    //   hoverItem={...hoverItem,index:dragIndex};

    //   const newData=[...configDataList];
    //   newData[hoverIndex]=dragItem
    //   newData[dragIndex]=hoverItem

    onChange(newData);
  };

  return (
    <div>
      {/* <h3>基金推荐</h3> */}
      <DndProvider backend={HTML5Backend}>
        {data?.map((item, index) => {
          let ch = null;
          if (item.$type === ChanceLiftType.STREAM) {
            ch = (
              <>
                <Input
                  maxLength={20}
                  value={item.recommendWords}
                  onChange={e => handleChange(index, 'recommendWords', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['recommendWords']}
                    />
                  }
                />
                <Input
                  value={item.title}
                  onChange={e => handleChange(index, 'title', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['title']} />
                  }
                />
                {/* <Input
                  value={item.organization}
                  onChange={e => handleChange(index, 'organization', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={<InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['organization']} />}
                /> */}
                <Input
                  value={item.streamId}
                  onChange={e => handleChange(index, 'streamId', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['streamId']} />
                  }
                />

                <Input
                  value={item.link}
                  onChange={e => handleChange(index, 'link', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['link']} />
                  }
                />

                <UploadImg
                  value={item.cover}
                  disabled={disabled}
                  onChange={v => handleChange(index, 'cover', v)}
                  title={chanceLiftHelper.mapPropToName(item.$type)['cover']}
                  isRequired
                  size={['149*84']}
                ></UploadImg>
                <Popconfirm
                  title={'请确认是否删除'}
                  disabled={disabled}
                  okText={'确认'}
                  cancelText={'取消'}
                  onConfirm={() => handleDel(index)}
                >
                  <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                    删除
                  </Button>
                </Popconfirm>
              </>
            );
          } else {
            ch = (
              <>
                <Input
                  maxLength={20}
                  value={item.recommendWords}
                  onChange={e => handleChange(index, 'recommendWords', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['recommendWords']}
                    />
                  }
                />
                <Input
                  value={item.fundCode}
                  onBlur={e => {
                    getFundName(e.target.value, 'fundName', index);
                  }}
                  onChange={e => {
                    handleChange(index, 'fundCode', e.target.value);
                  }}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['fundCode']} />
                  }
                />
                <Input
                  value={item.fundName}
                  onChange={e => handleChange(index, 'fundName', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['fundName']} />
                  }
                />
                <Input
                  value={item.fundDetailUrl}
                  onChange={e => handleChange(index, 'fundDetailUrl', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['fundDetailUrl']}
                      isRequired={false}
                    />
                  }
                />
                <Input
                  value={item.fundDetailVersion}
                  onChange={e => handleChange(index, 'fundDetailVersion', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['fundDetailVersion']}
                      isRequired={false}
                    />
                  }
                />
                <div>
                  <InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['profitArea']} />
                  <Select
                    value={item.profitArea}
                    onChange={v => handleChange(index, 'profitArea', v)}
                    disabled={disabled}
                    style={{ marginBottom: 10, width: 300 }}
                  >
                    <Option value="month">近1月</Option>
                    <Option value="tmonth">近3月</Option>
                    <Option value="hyear">近6月</Option>
                    <Option value="year">近1年</Option>
                    <Option value="tyear">近3年</Option>
                    <Option value="fyear">近5年</Option>
                    <Option value="nowyear">今年来</Option>
                    <Option value="now">成立以来</Option>
                  </Select>
                </div>
                <Input
                  value={item.heavyPositionStock}
                  onChange={e => handleChange(index, 'heavyPositionStock', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['heavyPositionStock']}
                    />
                  }
                />
                <Input
                  value={item.buttonText}
                  onChange={e => handleChange(index, 'buttonText', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['buttonText']}
                      isRequired={false}
                    />
                  }
                />
                <Input
                  value={item.link}
                  onChange={e => handleChange(index, 'link', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['link']}
                      isRequired={false}
                    />
                  }
                />
                <Input
                  value={item.linkVersion}
                  onChange={e => handleChange(index, 'linkVersion', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['linkVersion']}
                      isRequired={false}
                    />
                  }
                />
                <div>
                  <InputTitle title={chanceLiftHelper.mapPropToName(item.$type)['showType']} />
                  <Select
                    value={item.showType}
                    onChange={v => handleChange(index, 'showType', v)}
                    disabled={disabled}
                    style={{ marginBottom: 10, width: 300 }}
                  >
                    <Option value="asset">持仓占比</Option>
                    <Option value="profit">收益区间</Option>
                  </Select>
                </div>
                <Input
                  value={item.articleContent}
                  onChange={e => handleChange(index, 'articleContent', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['articleContent']}
                      isRequired={false}
                    />
                  }
                />
                <Input
                  value={item.articleLink}
                  onChange={e => handleChange(index, 'articleLink', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['articleLink']}
                      isRequired={false}
                    />
                  }
                />

                <Input
                  value={item.articleCreator}
                  onChange={e => handleChange(index, 'articleCreator', e.target.value)}
                  disabled={disabled}
                  style={{ marginBottom: 10, width: 300 }}
                  addonBefore={
                    <InputTitle
                      title={chanceLiftHelper.mapPropToName(item.$type)['articleCreator']}
                      isRequired={false}
                    />
                  }
                />

                <UploadImg
                  disabled={disabled}
                  value={item.articleCreatorAvatar}
                  onChange={v => handleChange(index, 'articleCreatorAvatar', v)}
                  title={chanceLiftHelper.mapPropToName(item.$type)['articleCreatorAvatar']}
                  isRequired
                  size={['18*18']}
                ></UploadImg>
                <Popconfirm
                  title={'请确认是否删除'}
                  okText={'确认'}
                  disabled={disabled}
                  cancelText={'取消'}
                  onConfirm={() => handleDel(index)}
                >
                  <Button disabled={disabled} style={{ marginTop: '4px' }} size="small">
                    删除
                  </Button>
                </Popconfirm>
              </>
            );
          }
          return (
            <Card
              key={index}
              style={{ width: '520px', display: 'inline-block', verticalAlign: 'top' }}
            >
              <DragAndSortWrapper
                changePosition={changePosition}
                id={index}
                key={index}
                index={index}
              >
                <Button disabled={disabled} style={{ marginBottom: '12px' }} type="dashed">
                  拖拽移动
                </Button>
              </DragAndSortWrapper>

              {ch}
            </Card>
          );
        })}
      </DndProvider>
      <div>
        <Button
          disabled={disabled}
          onClick={() => {
            handleAdd(ChanceLiftType.FUND);
          }}
          style={{ marginTop: '4px' }}
          size="small"
        >
          新增相关基金
        </Button>
        <Button
          disabled={disabled}
          onClick={() => {
            handleAdd(ChanceLiftType.STREAM);
          }}
          style={{ marginTop: '4px' }}
          size="small"
        >
          新增直播卡片
        </Button>
      </div>
    </div>
  );
};
export default ChanceLift;
