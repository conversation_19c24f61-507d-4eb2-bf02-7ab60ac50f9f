import React, { useEffect, useState } from 'react';
import { Button, message, Tabs, Input } from 'antd';
const { TabPane } = Tabs;
import api from 'api';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
const propsSchema = FORM_CONFIG.propsSchema;
const topSchema = FORM_CONFIG.topSchema;
const schema = FORM_CONFIG.schema;

const { getCircle, postCircle, getCircleOrder, postCircleOrder } = api; 

// tab顺序列表
interface order {
  tabName: string,
  key: string
};

// tab内容
interface data {
  tabName: string,
  key: string,
  content: content[]
};


// form render可编辑栏 
interface content {
  circleId: string,
  circleName: string,
  jumpAction: string,
  tab: Array <string>
}

interface circle {
  data: data[],
  circleId: string[]
}

export default function () {
  const [activeKey, setActiveKey] = useState('1');
  const [data, setData] = useState<data[]>([]);
  const [order, setOrder] = useState<order[]>([]);

  window.copy = (list: content[], index: number) => {
    const item = list[index];
    let _data: data[] = data;
    _data[0].content.push(item);
    setData(_data)
    console.log(_data)
    return list;
  };

  useEffect(() => {
    getCircle().then((res: any) => {
      if(res.code === '0000') {
          let data = JSON.parse(res.data) && JSON.parse(res.data).data || [];
          setData(data)
      } else {
        message.error(res.status_msg)
      }
    });
    getCircleOrder().then((res: any) => {
      if(res.code === '0000') {
          let data = JSON.parse(res.data) || [];
          // console.log(data)
          propsSchema.items.properties.tab.enum = data && data.map((item: order) => item.tabName)
          propsSchema.items.properties.tab.enumNames = data && data.map((item: order) => item.tabName)
          topSchema.items.properties.tab.enum = data && data.map((item: order) => item.tabName)
          topSchema.items.properties.tab.enumNames = data && data.map((item: order) => item.tabName)
          setOrder(data)
      } else {
        message.error(res.status_msg)
      }
    })
  }, [])

  const postOrder = () => {
    for (let item of order) {
      if (!item.tabName) {
        message.error("请填写tab名称")
        return
      }
      if (!item.key) {
        message.error("请填写key值")
        return
      }
    }
    postCircleOrder({value:JSON.stringify(order)}).then((res: any)=>{
      try {
          if (res.code === '0000') {
              message.success("保存成功");
              setTimeout(() => {
                  window.location.reload();
              }, 1000);
          } else {
              message.error(res.message);
          }
      } catch (e) {
          message.error(e.message);
      }
    })
    console.log(order, data)
    let _order = JSON.parse(JSON.stringify(order));
    for (let i = 0; i < _order.length; i++) {
      for (let j= 0; j < data.length; j++) {
        if (_order[i].key === data[j].key) {
          _order[i].content = data[j].content
        }
      }
    }
    let _data: circle = {circleId: [], data: []};
    let _array = [];
    _data.data = _order;
    for (let item of data) {
      if (item.content) {
        for (let content of item.content) {
          _array.push(content.circleId)
        }
      }
    }
    _data.circleId = _array;
    console.log(_order)
    postCircle({value:JSON.stringify(_data)}).then((res: any)=>{
      try {
          if (res.code === '0000') {
              message.success("保存成功");
              setTimeout(() => {
                  window.location.reload();
              }, 1000);
          } else {
              message.error(res.message);
          }
      } catch (e) {
          message.error(e.message);
      }
    })
  }

  const handleTabChange = (formData: any) => {
    // let _data: any = [];
    // for (let item of formData) {
    //   for (let content of data) {
    //     if (item.tabName === content.tabName) {
    //       _data.push(content);
    //     }
    //   }
    // }
    // setData(_data)
    console.log('tab', formData)
    setOrder(formData)
  }

  const onChange = (activeKey: string) => {
    setActiveKey(activeKey)
  };

  const handleChange = (formData: content[], index: number) => {
    console.log(formData);
    let _data: data[] = JSON.parse(JSON.stringify(data));
    _data[index].content = formData;
    setData(_data);
  }

  const post = () => {
    console.log(JSON.stringify(data))
    let _data: circle = {circleId: [], data: []};
    let _array = [];
    _data.data = data;
    for (let item of data) {
      if (item.content) {
        for (let content of item.content) {
          if (!content.circleId) {
            message.error("请填写圈子id")
            return 
          } else if (!content.circleName) {
            message.error("请填写圈子名称")
            return 
          } else if (!content.jumpAction) {
            message.error("请填写跳转链接")
            return 
          } else if (content.tab.length === 0) {
            message.error("请填写圈子分类")
            return 
          }
          _array.push(content.circleId)
        }
      }
    }
    _data.circleId = _array;
    console.log(_data)
    postCircle({value:JSON.stringify(_data)}).then((res: any)=>{
      try {
          if (res.code === '0000') {
              message.success("保存成功");
              setTimeout(() => {
                  window.location.reload();
              }, 1000);
          } else {
              message.error(res.message);
          }
      } catch (e) {
          message.error(e.message);
      }
    })
  }

  return (
    <>
      <FormRender
        propsSchema={schema}
        formData={order}
        displayType='row'
        labelWidth='120px'
        onChange={handleTabChange}
        onValidate={() => {}}
        showDescIcon
      ></FormRender>
      <Button className={'g-mb40'} onClick={postOrder} type="primary">修改Tab（编辑完tab请先点此按钮）</Button>

      <Tabs
        onChange={onChange}
        activeKey={activeKey}
      >
        {data.map((item, index) => {
          // console.log(item)
          console.log(item, item.content)
          return (
            <TabPane tab={item.tabName} key={item.key}>
              <FormRender
                propsSchema={index === 0 ? topSchema : propsSchema}
                formData={item.content}
                displayType='row'
                labelWidth={120}
                onChange={(formData: content[]) => handleChange(formData, index)}
                onValidate={() => {}}
              ></FormRender>
            </TabPane>
          )
        })}
      </Tabs>
      <Button onClick={post} type="primary">提交</Button>
    </>
  );
}