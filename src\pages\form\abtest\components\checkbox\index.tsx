import React, { useEffect, useState } from 'react';
import { Checkbox } from 'antd';
import styles from '@/pages/frontend/frontend.less';
// 多选框配置
const platformOptions = [
  { label: '三星', value: 'samsungmarket' },
  { label: '魅族', value: 'meizu' },
  { label: '淘宝', value: 'taobao' },
  { label: '百度', value: 'baidu' },
  { label: '360', value: '360zhushou' },
  { label: '安智', value: 'anzhi' },
  { label: '小米', value: 'xiaomi' },
  { label: '腾讯', value: 'tencent' },
  { label: '木蚂蚁', value: 'mumayi' },
  { label: '应用汇', value: 'appchina' },
  { label: '搜狗', value: 'sougou' },
  { label: '智汇云', value: 'hicloud' },
  { label: '联想乐商店', value: 'lenovole' },
  { label: 'OPPO商店', value: 'oppomarket' },
  { label: 'VIVO商店', value: 'vivo' },
  { label: '金立', value: 'gionee' },
  { label: 'PUSH', value: 'thspush' },
  { label: '资讯', value: 'thszx' },
  { label: '应用推荐', value: 'thxtj' },
  { label: '同花顺渠道', value: 'mhexin' },
  { label: '消息中心', value: 'zixunzhengwen' },
  { label: 'WEB', value: 'thsmobile' },
  { label: '神马搜索', value: 'smss' },
  { label: '下载中心', value: 'thsdownload' },
  { label: '行情客户端', value: 'hqkhd' },
  { label: '自更新', value: 'thsgx' },
];
// 应用市场信息
const marketAllData = [
  'samsungmarket',
  'meizu',
  'taobao',
  'baidu',
  '360zhushou',
  'anzhi',
  'xiaomi',
  'tencent',
  'mumayi',
  'appchina',
  'sougou',
  'hicloud',
  'lenovole',
  'oppomarket',
  'vivo',
  'gionee',
];
// 自身渠道信息
const channelAllData = [
  'thspush',
  'thszx',
  'thxtj',
  'mhexin',
  'zixunzhengwen',
  'thsmobile',
  'smss',
  'thsdownload',
  'hqkhd',
  'thsgx',
];

export default function({ value, onChange, name }: any) {
  /*
    @description Form-Render 自定义组件回调
    @params name 自定义组件在Form-Render scheam中的命名
    @params onChange 自定义组件在Form-Render 中可以改变数据映射的回调函数
    @params value 自定义组件此次表单输入的值
  */
  const [platform, setPlatform] = useState<string[]>();
  // 控制全选样式
  const [checkAllPlatform, setCheckAllPlatform] = useState(false);
  // 控制半全选样式
  const [indeterminatePlat, setiPlat] = useState(false);
  // 控制快速选择应用市场样式
  const [checkMarketPlatform, setCheckMarketPlatform] = useState(false);
  // 控制快速选择自身渠道样式
  const [checkChannelPlatform, setCheckChannelPlatform] = useState(false);
  // 多选框内容改变
  const onChangePlatform = (platform: string[]) => {
    let channelArr = [];
    let marketArr = [];

    platform.map(item => {
      if (marketAllData.includes(item)) {
        marketArr.push(1);
      }
    });
    platform.map(item => {
      if (channelAllData.includes(item)) {
        channelArr.push(1);
      }
    });

    setiPlat(!!platform.length && platform.length < platformOptions.length);
    setCheckAllPlatform(platform.length === platformOptions.length);
    setCheckMarketPlatform(marketArr.length === 16);
    setCheckChannelPlatform(channelArr.length === 10);
    setPlatform(platform);
  };
  // 点选全选
  const onCheckAllChangePlatform = (e: any) => {
    let _getAll: string[] = platformOptions.map((item, index) => item.value);
    setiPlat(false);
    setCheckAllPlatform(e.target.checked);
    setPlatform(e.target.checked ? _getAll : []);
    if (!e.target.checked) {
      setCheckMarketPlatform(false);
      setCheckChannelPlatform(false);
    }
  };
  // 点选快速选择应用市场
  const onCheckMarketChangePlatform = (e: any) => {
    let data: any = platform;
    let arr = [];

    if (e.target.checked) {
      data = data.concat(marketAllData);
      setPlatform(data);
    } else {
      for (let val of data) {
        !marketAllData.includes(val) ? arr.push(val) : '';
      }
      setPlatform(arr);
    }

    setCheckMarketPlatform(e.target.checked);
  };
  // 点选快速选择自身渠道
  const onCheckChannelChangePlatform = (e: any) => {
    let data: any = platform;
    let arr = [];

    if (e.target.checked) {
      data = data.concat(channelAllData);
      setPlatform(data);
    } else {
      for (let val of data) {
        !channelAllData.includes(val) ? arr.push(val) : '';
      }
      setPlatform(arr);
    }

    setCheckChannelPlatform(e.target.checked);
  };
  // 编辑页初始化
  useEffect(() => {
    value.length > 0 ? setPlatform(value) : '';
    onChangePlatform(value);
  }, []);
  // 控制全选时快速选择按钮不会跟着一起高亮
  useEffect(() => {
    if (checkAllPlatform === true) {
      setCheckMarketPlatform(false);
      setCheckChannelPlatform(false);
    }
  }, [checkAllPlatform]);
  // 解决当以快速选择的方式使多选框为空时此时并不会触发onChangePlatform,所以需要用监听器手动处理全选框样式
  useEffect(() => {
    if (platform) {
      platform.length === 0 ? setiPlat(false) : '';
    }
  }, [platform]);

  return (
    <section className={styles['m-config']}>
      <p className={styles['m-card-label']}>
        <span style={{ color: 'red', fontSize: '18px', marginRight: '5px' }}>*</span>应用市场:
      </p>
      <Checkbox
        indeterminate={indeterminatePlat}
        onChange={e => {
          if (e.target.checked) {
            let allArr: string[] = platformOptions.map((item, index) => item.value);
            onChange(name, allArr);
          }
          if (!e.target.checked) {
            onChange(name, []);
          }

          onCheckAllChangePlatform(e);
        }}
        checked={checkAllPlatform}
      >
        全选
      </Checkbox>
      <Checkbox
        onChange={e => {
          if (e.target.checked) {
            onChange(name, marketAllData);
          }
          if (!e.target.checked) {
            let arr = value;
            arr = arr.filter((item: any) => {
              return channelAllData.includes(item);
            });
            onChange(name, arr);
          }
          onCheckMarketChangePlatform(e);
        }}
        checked={checkMarketPlatform}
      >
        快速选择应用市场
      </Checkbox>
      <Checkbox
        onChange={e => {
          if (e.target.checked) {
            onChange(name, channelAllData);
          }
          if (!e.target.checked) {
            let arr = value;
            arr = arr.filter((item: any) => {
              return marketAllData.includes(item);
            });
            onChange(name, arr);
          }
          onCheckChannelChangePlatform(e);
        }}
        checked={checkChannelPlatform}
      >
        快速选择自身渠道
      </Checkbox>
      <br />
      <Checkbox.Group
        options={platformOptions}
        onChange={(platform: any) => {
          onChangePlatform(platform);
          onChange(name, platform);
        }}
        value={platform}
        style={{ marginLeft: '110px', marginBottom: '20px' }}
      />
      <br />
    </section>
  );
}
