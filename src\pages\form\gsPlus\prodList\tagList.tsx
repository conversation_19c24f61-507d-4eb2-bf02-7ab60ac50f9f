import React, { useState, useEffect } from 'react';
import { Card, But<PERSON>, List, Popconfirm, Modal, Input, message, Col, Row, Checkbox } from 'antd';

interface PRODITEM {
    showType: string;
    fundCode: string;
    fundName: string;
    id?: string;
    recommendText: string;
    tags?: string[];
    tab?: string[];
    jumpUrl?: string;
}

interface dataProps {
    prodTag: string[];
    handleTag: any;
    prodList: PRODITEM[];
    saveData: any;
}

export default function (props: dataProps) {

    const { prodTag, handleTag, prodList, saveData } = props;

    const [ModalShow, set_ModalShow] = useState(false); // 新增标签的弹窗
    const [newTag, set_newTag] = useState(''); // 新增标签  
    const [editingItem, set_editingItem] = useState('');

    /**
     * 添加标签
     */
    const addTag = () => {
        if (!newTag) {
            message.error('请填写标签');
            return;
        }
				if (newTag.length > 5 || newTag.length < 3) {
					message.error('标签文案需在3-5个字之内');
          return;
				}
        if (prodTag.includes(newTag)) {
            message.error('标签重复');
            return;
        }
        handleTag('add', newTag);
        set_ModalShow(false);
        set_newTag('');
    }

    /**
     * 勾选标签
     * @param checked 
     * @param index 
     */
    const chooseTag = (checked: boolean, index: number) => {
        let _prodList = [...prodList];
        if (checked) {
            _prodList[index].tags?.push(editingItem);
        } else {
            if (_prodList[index].tags) {
                let _i = _prodList[index].tags?.indexOf(editingItem);
                if (_i !== undefined && _i !== -1) {
                    _prodList[index].tags?.splice(_i, 1);
                }
            }
        }
        saveData(_prodList);
    }

    return (
        <article>
            <Row gutter={16}>
                <Col span={8}>
                    <Card style={{ width: 400 }}>
                        <List
                            bordered
                        >
                            {
                                prodTag.map((item: string, index: number) => (
                                    <List.Item
                                        actions={[
                                            <Button type="link" key="list-loadmore-edit" onClick={() => {set_editingItem(item)}}>编辑</Button>,
                                            <Popconfirm
                                                title="确定删除吗？"
                                                onConfirm={() => { if (editingItem === item) {set_editingItem('')} handleTag('delete', item, index) }}
                                                okText="确定"
                                                cancelText="取消"
                                            >
                                                <Button type="link" style={{ color: '#fe5d4e' }} key="list-loadmore-delete">删除</Button>
                                            </Popconfirm>
                                        ]}
                                        key={item}
                                    >{item}</List.Item>
                                ))
                            }
                            {
                                prodTag && prodTag.length === 0 ?
                                <p style={{ marginLeft: 10, marginTop: 10, marginBottom: 10, color: '#ccc' }}>暂无标签</p> : null
                            }
                        </List>
                        <Button
                            style={{ marginTop: 10 }}
                            type="primary"
                            disabled={prodTag.length >= 8}
                            onClick={() => { set_ModalShow(true) }}
                        >新增</Button>
                    </Card>
                </Col>
                <Col span={8}>
                    {
                        editingItem ?
                        <Card title={editingItem} style={{ width: 400 }}>
                            <Row>
                                {
                                    prodList.map((item, index) => (
                                        <Col span={16}>
                                            <Checkbox value={item.fundCode} onChange={(e) => {chooseTag(e.target.checked, index)}} checked={item.tags?.includes(editingItem)} key={item.fundCode}>{item.fundName}</Checkbox>
                                        </Col>
                                    ))
                                }
                                {
                                    prodList && prodList.length === 0 ?
                                    <p style={{ marginLeft: 10, marginTop: 10, marginBottom: 10, color: '#ccc' }}>暂无产品</p> : null
                                }
                            </Row>
                        </Card> : null
                    }
                </Col>
            </Row>
            <Modal
                title="新增标签"
                visible={ModalShow}
                onOk={addTag}
                onCancel={() => { set_ModalShow(false) }}
                cancelText="取消"
                okText="新增"
                closable={false}
            >
                <Input
                    style={{ width: 200 }}
                    value={newTag}
                    onChange={(e) => { set_newTag(e.target.value) }}
                />
            </Modal>
        </article>
    )
}