import React, { useState, useEffect } from 'react';
import {
  Table,
  Modal,
  Form,
  Switch,
  Input,
  Select,
  Button,
  Divider,
  Icon,
  Popconfirm,
  message,
} from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import moment from 'moment';
import store from 'store';
import api from 'api';

import { IFormData, IRequestData, ITableItem, IResponse } from './type';
import { transformTableDataToFormData, transformFormDataToReqData, generateUniqueId } from './utils';
import { SORT_MAP, INITIAL_MAP, FILTER_MAP, SUCCESS_CODE, FUNDTYPE_MAP } from './const';
import styles from './index.less';

const { Option } = Select;
const { fetchFundRankList, postFundRankList } = api;

const RankListTable = (props: FormComponentProps) => {
  const { form } = props;
  const { getFieldDecorator, setFieldsValue, resetFields, getFieldValue } = form;

  const [dataSource, setDataSource] = useState<ITableItem[]>([]);
  const [enableLabel, setEnableLabel] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [filterCount, setFilterCount] = useState(0);
  // 新增基金类型和成立年限
  const [hasFundType, setHasFundType] = useState(false);
  const [hasYears, setHasYears] = useState(false);
  const [showSecondInput, setShowSecondInput] = useState(true);

  const [editingKey, setEditingKey] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const checkInputSize = (_, value: string, callback) => {
    if (value && Number(getFieldValue('yearsStartValue')) >= Number(getFieldValue('yearsEndValue'))) {
      callback('开始年限只能小于结束年限');
    } else {
      callback();
    }
  };

  useEffect(() => {
    fetchFundRankList().then((res: IResponse) => {
      if (res.code !== SUCCESS_CODE) {
        message.error(res.message)
      } else {
        const data: IRequestData = JSON.parse(res.data || '{}');
        if (data?.rankList.length) {
          data.rankList.forEach(item => {
            if (item.emptyData === undefined) {
              item.emptyData = null
            }
          })
        }
        setDataSource(data?.rankList || []);
        setEnableLabel(data?.zeroTag === '1' ? true : false)
        setIsLoading(false)
      }
    });
  }, []);

  const rankColumns = [
    {
      title: '是否上线',
      dataIndex: 'online',
      key: 'online',
      render: (text: '0' | '1') => {
        return (
          <Icon
            type={text === '1' ? 'check-circle' : 'close-circle'}
            style={{ fontSize: '20px', color: text === '1' ? '#52c41a' : 'red' }}
          />
        );
      },
      width: '5%',
    },
    {
      title: '榜单名称',
      dataIndex: 'name',
      key: 'name',
      width: '8%',
    },
    {
      title: '榜单顺序',
      key: 'order',
      render: (_1: ITableItem, _2: ITableItem, index: number) => {
        if (dataSource.length <= 2) {
          return '默认固定'
        } else if (index === 0) {
          return '默认固定'
        } else if (index === 1) {
          return (
            <div>
              <a onClick={() => handleDown(index)}>下移</a>
            </div>
          );
        } else if (index === dataSource.length - 1) {
          return (
            <div>
              <a onClick={() => handleUp(index)}>上移</a>
            </div>
          );
        } else {
          return (
            <div>
              <a onClick={() => handleUp(index)}>上移</a>
              &nbsp; &nbsp;
              <a onClick={() => handleDown(index)}>下移</a>
            </div>
          );
        }
      },
      width: '7%',
    },
    {
      title: '初始排序规则',
      key: 'sortRule',
      render: (dataItem: ITableItem) => {
        return `${SORT_MAP.get(dataItem.sortType)}  ${dataItem.sort === 'ASC' ? '升序' : '降序'}`
      },
    },
    {
      title: '初始位置',
      dataIndex: 'initialField',
      key: 'initialField',
      render: (text: string) => {
        return INITIAL_MAP.get(text)
      },
      width: '10%',
    },
    {
      title: '上榜筛选条件',
      key: 'filterList',
      render: (dataItem: ITableItem) => {
        if (dataItem.filterList.length) {
          return dataItem.filterList.map((item, index) => {
            // 基金类型、成立年限、普通筛选类型
            if (item.filterField === 'l2code') {
              return <div key={index}>
                基金类型为：{item.filterValue.split(',').map(valueItem => FUNDTYPE_MAP.get(valueItem)).join('、')}
              </div>
            }
            if (item.filterField === 'nowDayAmount') {
              if (item.filterSymbol === 'GREATER') {
                return <div key={index}>
                  成立年限为：大于{Number(item.filterValue) / 365}年
                </div>
              }
              if (item.filterSymbol === 'BETWEEN') {
                return <div key={index}>
                  成立年限区间为：{Number(item.filterValue.split(',')[0]) / 365} — {Number(item.filterValue.split(',')[1]) / 365} 年
                </div>
              }
            }
            if (item.filterField === 'rsi') {
              return <div key={index}>
                波段值{item.filterSymbol === 'GREATER' ? '大于' : '小于'}{Number(item.filterValue) * 100}
              </div>
            }
            return (
              <div key={index}>
                {FILTER_MAP.get(item.filterField) + (item.filterSymbol === 'GREATER' ? '大于' : '小于') + item.filterValue + '%'}
              </div>
            )
          }
          )
        }
        return '无额外条件'
      },
      width: '11%',
    },
    {
      title: '理财楼层文案',
      dataIndex: 'financialFloor',
      key: 'financialFloor',
    },
    {
      title: '推荐标签',
      dataIndex: 'recommend',
      key: 'recommend',
      render: (text: '0' | '1') => {
        return (
          <Icon
            type={text === '1' ? 'check-circle' : 'close-circle'}
            style={{ fontSize: '20px', color: text === '1' ? '#52c41a' : 'red' }}
          />
        );
      },
      width: '5%',
    },
    {
      title: '运营文字链',
      key: 'operations',
      render: (dataItem: ITableItem) => {
        return (
          <>
            <span>是否启用</span>
            &nbsp;&nbsp;&nbsp;
            <Icon
              type={dataItem.operation === '1' ? 'check-circle' : 'close-circle'}
              style={{ fontSize: '16px', color: dataItem.operation === '1' ? '#52c41a' : 'red' }}
            />
            <div>文案：{dataItem.operationText}</div>
            <div>链接：{dataItem.operationLink}</div>
          </>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (dataItem: ITableItem, _: ITableItem, index: number) => {
        return (
          <>
            <a onClick={() => handleEdit(dataItem, index)}>编辑</a>
            &nbsp;&nbsp;
            {
              index === 0 ? null : <Popconfirm
                title="确定要删除这个榜单吗？"
                okText="是"
                cancelText="否"
                onConfirm={() => handleDelete(dataItem.key)}
              >
                <a>删除</a>
              </Popconfirm>
            }
          </>
        );
      },
      width: '7%',
    },
    {
      title: '最后编辑人/时间',
      key: 'lastEditInfo',
      render: (dataItem: ITableItem) => {
        return (
          <>
            <div>{dataItem.lastEditor}</div>
            <div>{dataItem.editorTime}</div>
          </>
        );
      },
    },
  ];
  // 过滤条件可能有多个，需要灵活控制长度
  const filterList = [];
  for (let i = 0; i < filterCount; i++) {
    filterList.push(
      <Form.Item label={`普通筛选条件${i + 1}`} key={i} required>
        <Form.Item style={{ margin: '0' }}>
          {getFieldDecorator('filterField' + i, {
            rules: [{ required: true, message: '请输入普通筛选条件' }]
          })(
            <Select placeholder="数据项">
              {Array.from(FILTER_MAP.entries()).map(([key, value]) => {
                return <Option value={key} key={key}>{value}</Option>
              })}
            </Select>,
          )}
        </Form.Item>
        <Form.Item style={{ margin: '0' }}>
          {getFieldDecorator('filterSymbol' + i, {
            rules: [{ required: true, message: '请输入普通筛选条件' }]
          })(
            <Select placeholder="比较项">
              <Option value="GREATER">大于</Option>
              <Option value="LESS">小于</Option>
            </Select>
          )}
        </Form.Item>
        <Form.Item style={{ margin: '0' }}>
          <span>
            {getFieldDecorator('filterValue' + i, {
              rules: [{ required: true, message: '请输入普通筛选条件' }]
            })(
              <Input type='number' className={styles['short-input']} />
            )} %
          </span>
        </Form.Item>
      </Form.Item>,
    );
  }
  // 是否有基金类型和成立年限选项
  if (hasFundType) {
    filterList.push(
      <Form.Item label='基金类型筛选条件' key={'fundType'} required>
        <Form.Item style={{ margin: '0' }}>
          {getFieldDecorator('fundTypeValue', {
            rules: [{ required: true, message: '请选择基金类型' }]
          })(
            <Select
              mode="multiple"
              style={{ width: '100%' }}
              placeholder="请选择基金类型（可多选）"
            >
              {Array.from(FUNDTYPE_MAP.entries()).map(([key, value]) => {
                return <Option value={key} key={key}>{value}</Option>
              })}
            </Select>
          )}
        </Form.Item>
      </Form.Item>,)
  }
  if (hasYears) {
    filterList.push(
      <Form.Item label='成立年限筛选条件' key='years' required>
        <Form.Item style={{ margin: '0' }}>
          {getFieldDecorator('yearsSymbol', {
            rules: [{ required: true, message: '请输入年限' }]
          })(
            <Select placeholder="比较项" onChange={(value) => setShowSecondInput(value === 'BETWEEN')}>
              <Option value="GREATER">大于</Option>
              <Option value="BETWEEN">区间</Option>
            </Select>
          )}
        </Form.Item>
        <Form.Item style={{ margin: '0', display: 'inline-block' }}>
          <span>
            {getFieldDecorator('yearsStartValue', {
              rules: [
                { required: true, message: '请输入年限' },
                { pattern: /^[0-9]+$/, message: '年限必须为非负整数' },
              ]
            })(
              <Input type='number' className={styles['short-input']} />
            )}年&nbsp;
          </span>
        </Form.Item>
        <Form.Item style={{ margin: '0', display: 'inline-block' }}>
          {
            showSecondInput ?
              <span>
                —&nbsp;{getFieldDecorator('yearsEndValue', {
                  rules: [
                    { required: true, message: '请输入年限' },
                    { pattern: /^[0-9]+$/, message: '年限必须为非负整数' },
                    { validator: checkInputSize }
                  ]
                })(
                  <Input type='number' className={styles['short-input']} />
                )}年
              </span> : null
          }
        </Form.Item>
      </Form.Item>
    )
  }
  // 上移和下移功能
  const handleUp = (index: number) => {
    const newDataSource = [
      ...dataSource.slice(0, index - 1),
      dataSource[index],
      dataSource[index - 1],
      ...dataSource.slice(index + 1),
    ];
    const reqData: IRequestData = {
      zeroTag: enableLabel ? '1' : '0',
      rankList: newDataSource
    }
    postFundRankList({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
      if (res.code === SUCCESS_CODE) {
        setDataSource(newDataSource);
      } else {
        message.error(res.message);
      }
    });
  };
  const handleDown = (index: number) => {
    const newDataSource = [
      ...dataSource.slice(0, index),
      dataSource[index + 1],
      dataSource[index],
      ...dataSource.slice(index + 2),
    ];
    const reqData: IRequestData = {
      zeroTag: enableLabel ? '1' : '0',
      rankList: newDataSource,
    }
    postFundRankList({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
      if (res.code === SUCCESS_CODE) {
        setDataSource(newDataSource)
      } else {
        message.error(res.message)
      }
    })
  };
  // 0折宣传标签开关
  const handleSwitchSubmit = (checked: boolean) => {
    const reqData: IRequestData = {
      zeroTag: checked ? '1' : '0',
      rankList: dataSource
    }
    postFundRankList({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
      if (res.code !== SUCCESS_CODE) {
        message.error(res.message)
      } else {
        setEnableLabel(checked)
      }
    })
  };
  // 点击编辑，保存当前的key，弹出弹窗，并初始化表单字段
  const handleEdit = (dataItem: ITableItem, index: number) => {
    const commonFilterArr = dataItem.filterList.filter(
      item => item.filterField !== 'l2code' && item.filterField !== 'nowDayAmount'
    )
    const fundTypeFilterArr = dataItem.filterList.filter(item => item.filterField === 'l2code')
    const yearsFilterArr = dataItem.filterList.filter(item => item.filterField === 'nowDayAmount')
    setHasFundType(fundTypeFilterArr.length !== 0)
    setHasYears(yearsFilterArr.length !== 0)
    if (yearsFilterArr[0]?.filterSymbol === 'GREATER') {
      setShowSecondInput(false)
    } else if (yearsFilterArr[0]?.filterSymbol === 'BETWEEN') {
      setShowSecondInput(true)
    }
    setEditingKey(dataItem.key)
    setShowModal(true);
    setFilterCount(commonFilterArr.length)
    // 为了让表单渲染完之后再设置数据
    setTimeout(() => setFieldsValue(transformTableDataToFormData(dataItem, index)), 0)
  };
  // 删除某一行的表格数据
  const handleDelete = (key: string) => {
    const newDataSource = dataSource.filter(item => item.key !== key);
    const reqData: IRequestData = {
      zeroTag: enableLabel ? '1' : '0',
      rankList: newDataSource,
    }
    postFundRankList({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
      if (res?.code === SUCCESS_CODE) {
        message.success('删除成功')
        setDataSource(newDataSource);
      } else {
        message.error(res.message)
      }
    })
  };
  // 提交表格
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const editor = store.get('name')
    const editTime = moment().format('YYYY-MM-DD HH:mm:ss');
    form.validateFields((err, value: IFormData) => {
      if (!err) {
        const reqData = transformFormDataToReqData(value, dataSource, enableLabel, editingKey, editor, editTime)
        postFundRankList({ value: JSON.stringify(reqData) }).then((res: IResponse) => {
          if (res.code === SUCCESS_CODE) {
            setShowModal(false)
            setDataSource(reqData.rankList)
            message.success('修改成功')
          } else {
            message.error('修改数据错误')
          }
        })
      } else {
        message.error('请输入完整数据')
      }
    });
  };
  // 新增榜单
  const handleAdd = () => {
    let id = generateUniqueId(dataSource)
    setEditingKey(id);
    setFilterCount(0);
    setHasFundType(false);
    setHasYears(false);
    setShowModal(true);
    resetFields();
  };
  return (
    <div>
      <div className={styles['enable-label']}>
        {!isLoading ? <div>
          <span>0折宣传标签是否启用</span>
          &nbsp;&nbsp;&nbsp;
          <Switch checked={enableLabel} onChange={handleSwitchSubmit} />
        </div> : null}
      </div>
      <div className={styles['table-title']}>
        榜单配置
        <Button className={styles['add-button']} onClick={handleAdd} type="primary">
          新增榜单
        </Button>
      </div>
      <Table columns={rankColumns} dataSource={dataSource} bordered pagination={false} scroll={{ y: 500 }}></Table>
      <Modal
        visible={showModal}
        onCancel={() => setShowModal(false)}
        footer={null}
      >
        <Form onSubmit={handleSubmit}>
          <Form.Item>
            <span>是否上线</span>
            &nbsp;&nbsp;&nbsp;
            {getFieldDecorator('online', {
              initialValue: true,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
          <Divider />
          <Form.Item label="榜单名称">
            {getFieldDecorator('name', {
              rules: [{ required: true, message: '请输入榜单名称' }]
            })(<Input />)}
          </Form.Item>
          <Divider />
          <Form.Item label="初始排序规则" required>
            <Form.Item style={{ margin: '0' }}>
              {getFieldDecorator('sortType', {
                rules: [{ required: true, message: '请选择排序规则' }]
              })(
                <Select placeholder="数据项">
                  {Array.from(SORT_MAP.entries()).map(([key, value]) => {
                    return <Option value={key} key={key}>{value}</Option>
                  })}
                </Select>,
              )}
            </Form.Item>
            <Form.Item style={{ margin: '0' }}>
              {getFieldDecorator('sort', {
                rules: [{ required: true, message: '请选择排序规则' }]
              })(
                <Select placeholder="排序规则">
                  <Option value="ASC">升序</Option>
                  <Option value="DESC">降序</Option>
                </Select>,
              )}
            </Form.Item>
          </Form.Item>
          <Divider />
          <Form.Item label="初始位置">
            {getFieldDecorator('initialField', {
              rules: [{ required: true, message: '请选择初始位置' }]
            })(
              // 具体选项待定
              <Select placeholder="数据项">
                {Array.from(INITIAL_MAP.entries()).map(([key, value]) => {
                  return <Option value={key} key={key}>{value}</Option>
                })}
              </Select>,
            )}
          </Form.Item>
          <Divider />
          {/* 过滤条件 */}
          {filterList}
          {filterCount === 0 && !hasFundType && !hasYears ? <div style={{ marginBottom: '20px', fontWeight: 'bold' }}>暂无筛选条件</div> : null}
          <div>
            <Button onClick={() => setFilterCount(filterCount + 1)}>新增普通条件</Button>
            <Button disabled={hasFundType} onClick={() => setHasFundType(true)}>新增基金类型条件</Button>
            <Button disabled={hasYears} onClick={() => setHasYears(true)}>新增成立年限条件</Button>
          </div>
          <div>
            <Button disabled={filterCount < 1} onClick={() => setFilterCount(filterCount - 1)}>删除普通条件</Button>
            <Button disabled={!hasFundType} onClick={() => setHasFundType(false)}>删除基金类型条件</Button>
            <Button disabled={!hasYears} onClick={() => setHasYears(false)}>删除成立年限条件</Button>
          </div>
          <Divider />
          <Form.Item label="理财楼层文案">
            {getFieldDecorator('financialFloor', {
              rules: [{ required: true, message: '请输入理财楼层文案' }]
            })(<Input placeholder="不超过15字" maxLength={15} />)}
          </Form.Item>
          <Divider />
          <Form.Item>
            <span>推荐标签</span>
            &nbsp;&nbsp;&nbsp;
            {getFieldDecorator('recommend', {
              initialValue: true,
              valuePropName: 'checked',
            })(<Switch />)}
          </Form.Item>
          <Divider />
          <Form.Item label="运营文字链">
            <Form.Item style={{ margin: '0' }}>
              <span>是否启用</span>
              &nbsp;&nbsp;&nbsp;
              {getFieldDecorator('operation', {
                initialValue: true,
                valuePropName: 'checked',
              })(<Switch />)}
            </Form.Item>
            <Form.Item style={{ margin: '0' }}>
              <span>文案</span>
              {getFieldDecorator('operationText')(<Input />)}
            </Form.Item>
            <Form.Item style={{ margin: '0' }}>
              <span>链接</span>
              {getFieldDecorator('operationLink')(<Input />)}
            </Form.Item>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

const WrappedRankListTable = Form.create({ name: 'rankList' })(RankListTable);

export default WrappedRankListTable;
