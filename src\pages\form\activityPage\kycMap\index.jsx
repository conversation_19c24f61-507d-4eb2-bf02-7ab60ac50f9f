import React, { useState, useEffect } from 'react';
import { Button, message } from 'antd';
import api from 'api';
import PacketFrom from './kycMapForm';

const { fetchKycMapList, postKycMap } = api;


function queryKycMapList() {
  const dataToSend = { type: 'query' };
  return new Promise((resolve, reject) => {
    fetchKycMapList(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data.data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

function deleteKcyMap(value) {
  if (!value) message.info('缺少必要信息');
  const dataToSend = { type: 'delete', value };
  return new Promise((resolve, reject) => {
    postKycMap(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data.data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

function addOrEditKcyMap(value) {
  const dataToSend = { type: 'insert', value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    postKycMap(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

export default function KycMap() {
  const [kycList, setKycList] = useState([]); // kyc列表

  useEffect(() => {
    queryKycMapList().then(data => {
      const _kycList = Object.keys(data).map(key => data[key]);
      setKycList(_kycList);
    });
  }, []);

  function addActivityListItem() {
    const _activityList = JSON.parse(JSON.stringify(kycList));
    _activityList.push({});
    setKycList(_activityList);
  }

  function submitPacketForm(data) {
    addOrEditKcyMap(data).then(data => {
      message.info('操作成功');
    });
  }

  return (
    <div style={{ padding: 30 }}>
      {
        kycList.length ? kycList.map((formData, index) =>
            <PacketFrom submit={submitPacketForm} data={formData} key={index}/>,
          ) :
          <p className={'f-tc'}>暂无KYC配置数据</p>
      }
      <Button type={'primary'} onClick={addActivityListItem}>新增</Button>
    </div>
  );
}
