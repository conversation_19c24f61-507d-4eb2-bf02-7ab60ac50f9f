import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ConfigData } from '../../type';
import { Input, Switch } from 'antd';
import InputTitle from '../InputTitle';
import UploadImg from '../UploadImg';
import { mapPropToName } from './helper';

export interface IProps {
  data: ConfigData['screenStream'];
  onChange: (data: ConfigData['screenStream']) => void;
  disabled: boolean;
}
const ScreemStream: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleChange = (name, value) => {
    onChange({
      ...data,
      [name]: value,
    });
  };
  return (
    <div>
      <h3>
        首屏直播卡{' '}
        <Switch
          disabled={disabled}
          checked={data.open}
          onChange={v => handleChange('open', v)}
        ></Switch>
      </h3>
      <Input
        value={data.streamId}
        onChange={e => handleChange('streamId', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['streamId']} />}
      />
      <Input
        value={data.link}
        onChange={e => handleChange('link', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['link']} />}
      />

      <UploadImg
        disabled={disabled}
        value={data.cover}
        onChange={v => handleChange('cover', v)}
        title={mapPropToName['cover']}
        isRequired
        size={['343*190']}
      ></UploadImg>
    </div>
  );
};
export default ScreemStream;
