export type HotSpot = {
  title: string; //标题
  time: string; //时间点
  link: string; //链接

  content: string; //摘要内容
  tags: string; //机会标签
  analyze: string; //热点剖析
};

export type ScreenStream = {
  open: boolean; //是否开启
  link: string; //直播链接
  streamId: string; //直播id
  cover: string; //直播间图片
};

export type SectorAnalyze = {
  topicFundId: string; //主题选基id
  //topicFundName: string;
  // assessFundId: string; //景气度估值id
  // assessFundName: string;
  prosperityId: string; //景气度id
  assessId: string; //估值id
  indexType: string; //PE-PB
  moreLink: string; //板块跳转链接
  lineChartOpen: boolean;
  indexOpen: boolean;
};

export type ManagerAnalyze = {
  id: string; //id
  name: string; //名称
  tags: string; //标签
  intro: string; //简介
  content: { title: string; value: string }[]; //解读内容
  avatar: string; //头像
};
export type FundRecommend = {
  recommendWords: string; //一句话推荐
  fundCode: string; //基金代码
  fundName: string; //基金名称
  profitArea: string; //收益区间
  heavyPositionStock: string; //重仓股票
  buttonText: string; //按钮内容
  link: string; //跳转链接
  linkVersion: string; //跳转链接客户版本号
  articleContent: string; //文章内容
  articleLink: string; //文章链接
  fundDetailUrl: string; // 详情跳转链接
  fundDetailVersion: string; // 详情跳转链接所需客户版本号
  articleCreator: string; //文章作者
  articleCreatorAvatar: string; //头像
};
export type StreamCard = {
  recommendWords: string; //一句话推荐
  title: string; //直播标题
  //organization: string; //直播机构
  link: string; //链接
  streamId: string; //直播id
  cover: string; //图片
};
export type Banner = {
  open: boolean; //是否开启
  img: string; //图片
  name: string; //活动名称
  link: string; //跳转链接

  startTime: string; //开始时间
  endTime: string; //结束时间
};
export type Infomation = {
  id: string;
  type: string;
  name: string; //文章标题
  from: string; //文章来源
  //readAmount: string; //阅读量
  createTime: string; //发文时间
  cover: string; //封面图
  link: string; //文章链接
};
export enum ChanceLiftType {
  FUND = 'fund',
  STREAM = 'stream',
}

export enum Info {
  ZIXUN = '0',
  STREAM = '4',
  STREAM_SINGLE = '5',
  HOTCOMMENT = '6',
  ASKANS = '8',
  TOUGULONG = '10',
  TOUGUSHORT = '11',
  GONGGAO = '12',
  YANBAO = '13',
  SERVER_BIJI = '16',
  SERVER_STREAM = '17',
  TOPIC = '18',
  // TOPIC_2 = 19,
  LEARN_CLASS = '19',
  LEARN_SECTION = '20',
  ASKANS_NEW = '21',
  LUYAN = '22',
  VIDEO = '23',
  FORUM = '24',
}
export const InfoNameMap = {
  [Info.ZIXUN]: '资讯',
  [Info.STREAM]: '直播',
  [Info.STREAM_SINGLE]: '直播单条内容',
  [Info.HOTCOMMENT]: '热评',
  [Info.ASKANS]: '问答',
  [Info.TOUGULONG]: '同顺号长文',
  [Info.TOUGUSHORT]: '投顾短文',
  [Info.GONGGAO]: '公告',
  [Info.YANBAO]: '研报',
  [Info.SERVER_BIJI]: '服务包笔记',
  [Info.SERVER_STREAM]: '服务包视频直播',
  [Info.TOPIC]: '话题',
  [Info.LEARN_CLASS]: '学投资-套课（课程）',
  [Info.LEARN_SECTION]: '学投资-章节',
  [Info.ASKANS_NEW]: '问答新版回答',
  [Info.ASKANS_NEW]: '问答新版回答',
  [Info.LUYAN]: '路演',
  [Info.VIDEO]: '视频',
  [Info.FORUM]: '用户发帖',
};
export type ChanceLift = FundRecommend &
  StreamCard & {
    $type: ChangeLiftType;
  };
export type ConfigData = {
  id: string; //当前配置项的id
  index: string; //当前配置项的顺序索引
  sectorTitle: string; //机会板块名称
  chanceTitle: string; //最新机会
  hotLogic: string; //热点逻辑
  detailLink: string; //全部事件跳转链接
  topTag: string; //顶部标签
  tabTag: string; //tab标签
  marker: string; //角标
  markerStartTime: string; //角标开始时间
  markerEndTime: string; //角标结束时间
  hotspots: HotSpot[]; //重磅热点
  screenStream: ScreenStream; //首屏直播卡
  sectorAnalyze: SectorAnalyze; //板块解读
  managerAnalyze: ManagerAnalyze; //基金经理解读
  chanceLift: ChanceLift[];
  fundRecommend: FundRecommend[]; //机会顺风车-基金推荐
  streamCard: StreamCard[]; //机会顺风车-直播卡片
  banner: Banner; //活动banner
  infomation: Infomation[]; //相关资讯
};
