import React from 'react';
import ImgUpload from '../uploadImg/index.jsx';
interface dataProps {
  name: string;
  onChange: Function;
  value: string;
  readonly: boolean;
}

function WrapperUploadImg(props: dataProps) {
  return (
    <div style={{ width: 514 }}>
      <ImgUpload
        handleChange={(value: any) => props.onChange(props.name, value)}
        imageUrl={props.value}
        isEdit={!props.readonly}
        title=""
        // size={['60*60']}
      />
    </div>
  );
}

export default WrapperUploadImg;
