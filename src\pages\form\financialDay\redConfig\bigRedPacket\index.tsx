import React, { useEffect, useState } from 'react';
import { Button, Collapse, message, Tag } from 'antd';
import CardMessage from './myCard';
import classnames from 'classnames';
import styles from '../index.less';
import { iBigRed } from './redPacket';

const { Panel } = Collapse;

export interface iBigData {
    date: string | null;
    cards: iBigRed[];
    key: string;
}
interface iFixedStrategyProps {
    handleData: (data: iBigData[]) => void;
    bigData: iBigData[];
    isModify: boolean;
    handleModify: (flag: boolean) => void;
}
function StrictElection({handleData, bigData, isModify, handleModify}: iFixedStrategyProps) {
    const newAdd = () => {
        let arr = [...bigData];
        let obj: any = {
            date: null,
            cards: [],
            key: +new Date() + '',
        }
        arr.push(obj);
        handleData(arr);
    }
    const handleDelete = (key: number) => {
        if (!isModify) handleModify(true);
        let arr = [...bigData];
        arr.splice(Number(key), 1);
        handleData(arr);
    }
    const modifyStrictElectionData = (data: iBigData, num: number) => {
        if (!isModify) handleModify(true);
        let arr = [...bigData];
        arr[num] = data;
        handleData(arr);
    }
    const panelHeader = (obj: iBigData) => {
        return (
            <div className={classnames(styles['m-panel-header'], 'u-flex')}>
                <span style={{marginRight: 40}}>{obj.date}</span>
            </div>
        )
    }
    return <div style={{marginTop: 40}}>
        <h1 className="g-fs28 f-bold">大额红包配置</h1>
        <Collapse>
            {
                bigData?.map((item: iBigData, index: number) => {
                    return (
                        <Panel 
                            header={panelHeader(item)} 
                            key={item.key}
                        >
                            <CardMessage currentData={item} handleData={(data) => modifyStrictElectionData(data, index)} handleDelete={() => handleDelete(index)}/>
                        </Panel>
                    )
                })
            }
            
        </Collapse>
        <div style={{marginTop: 20}}>
          <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
        </div>
    </div>
}
export default React.memo(StrictElection);
