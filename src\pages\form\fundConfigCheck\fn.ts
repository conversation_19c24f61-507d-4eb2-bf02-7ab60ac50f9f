import { FundTableData, GdlcTableData, GsdataTableDate } from './types';

// 查询项 配置
const fundStatusOptions = [
  [
    { value: '0', label: '交易' },
    { value: '1', label: '发行' },
    { value: '2', label: '发行成功' },
    { value: '3', label: '发行失败' },
    { value: '4', label: '停止交易' },
    { value: '5', label: '停止申购' },
    { value: '6', label: '停止赎回' },
    { value: '7', label: '权益登记' },
    { value: '8', label: '红利发放' },
    { value: '9', label: '基金封闭' },
    { value: 'a', label: '基金终止' },
  ],
  [
    { value: '1', label: '预约' },
    { value: '2', label: '在售' },
    { value: '3', label: '售罄' },
    { value: '5', label: '开售提醒' },
    { value: '6', label: '即将开售' },
    { value: '7', label: '封闭期' },
  ],
  [
    { value: '1', label: '火爆预售中' },
    { value: '2', label: '火爆发售中' },
    { value: '3', label: '已售罄' },
  ]
]
const fundTypeOptions = [
  [
    { value: 'hhx', label: '混合' },
    { value: 'gpx', label: '股票' },
    { value: 'zqx', label: '债券' },
    { value: 'hbx', label: '货币' },
    { value: 'other', label: '其他' },
  ],
  [
    { value: 'specialBankAccount', label: '专户' },
    { value: 'smallset', label: '小集合' },
  ],
  [
    { value: '证券公司集合理财产品', label: '证券公司集合理财产品' }
  ]
]

export const funcBlock = function(intervalTime: any, f1: any, f2: any) {
  let lastFunc = 0;
  let timer: any;
  // 连续执行结束，重置lastFunc，等待新的首次执行
  const funcEnd: any = function() {
    f2 && f2(...arguments);
    lastFunc = 0;
  };
  // 首次执行和连续执行调用。
  return function() {
    if (lastFunc !== 0) {
      // 连续执行需清理定时器
      clearTimeout(timer);
    }
    lastFunc = new Date().getTime();
    f1 && f1(...arguments);
    // 每次执行，启用定时器，定时器若执行，判断为连续执行结束。
    timer = setTimeout(
      () => {
        clearTimeout(timer);
        funcEnd(...arguments);
      },
      intervalTime,
      ...arguments,
    );
  };
};
// 设置 筛选项配置
export const setFiltersStatus = (selectors: any[], tab: number) => {
  const _selectors = [...selectors];
  _selectors.forEach((item: any) => {
    if (item.key === 'fundStatus') {
      item.options = fundStatusOptions[tab];
    } else if (item.key === 'fundType') {
      item.options = fundTypeOptions[tab];
    }
  });
  return _selectors;
};
export const exportTableItem = (item: any, type: string) => {
  switch (type) {
    case 'fund': {
      const tableItem: FundTableData = {
        fundName: { name: item.name, code: item.code },
        companyAndManager: { admin: item.admin, manager: item.manager },
        tradeStatus: item.tradeStatus,
        fundType: item.type,
        soldTime: { start: item.rgstart, end: item.rgend },
        fundAmount: item.asset,
        riskLevel: item.levelOfRisk,
        verifyStatus: item.verifyStatus,
        rgRates: item.rgRates,
        sgRates: item.sgRates,
        shRates: item.shRates,
        discount: { wallet: item.walletDiscount, card: item.cardDiscount },
      };
      return tableItem;
    }
    case 'gdlc': {
      const tableItem: GdlcTableData = {
        fundName: item.fundname,
        fundCode: item.fundcode,
        syvalue: item.syvalue,
        sydesc: item.sydesc,
        manageRate: item.tradeRule?.manageRate,
        tradeFeeRatio: item.tradeRule?.tradeFeeRatio,
        redeemRate: item.tradeRule?.redeemRate,
        openPeriod: { start: item.openstartday, end: item.openendday },
        deadline: item.deadline,
        risklevel: item.risklevel
      }
      return tableItem;
    }
    case 'gsdata': {
      const tableItem: GsdataTableDate = {
        fundName: item.fundname,
        fundCode: item.fundcode,
        deadline: item.deadline,
        yearsy: item.yearsy,
        openPeriod: { start: item.openstartday, end: item.openendday },
        transactioncfmdate: item.transactioncfmdate,
        redeemPeriod: { start: item.maturitydate, end: item.endmaturitydate },
        arrivaldate: item.arrivaldate,
        limitation: item.limitation,
        isqs: item.isqs,
        risklevel: item.risklevel
      }
      return tableItem;
    }
  }
}