import React, { Component } from 'react'
import classnames from 'classnames'
import styles from './index.less'

interface PageProps {
    className?: string;
    children?: any;
    inner: boolean;
}

const Page: React.FC = (props: PageProps) => {
    const { 
        className, 
        children, 
        inner = true 
    } = props;
    return (
        <div
            className={classnames(
                className, 
                {
                    [styles.contentInner]: inner,
                }
            )}
            s-bd="1"
            s-bg="1"
        >
            {children}
        </div>
    )
};

export default Page

