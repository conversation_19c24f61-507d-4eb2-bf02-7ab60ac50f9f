/*
* 四笔钱配置
* <AUTHOR>
* @time 2019.12
*/


import React, { Fragment } from 'react';
import { Tabs, Input, Button, Alert, Card, Radio, Select, Row, Col, Tooltip, Icon, message, Switch, Popconfirm} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';

var moment = require('moment');
moment().format();

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

const {fetchRedeemConditionField, postFourmoneyTemporary, fetchFourmoneyTemporary, postFourmoney, postOnemoney} = api;

@autobind
class ifundMoneyK extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            psyyRef: null,
            zcbzRef: null,
            tzwlRef: null,
            bxbzRef: null,
            tabName: ['平时要用', '资产保值', '投资未来', '保险保障'],
            title: '', // 首页上方大标题
            list: [], // 四笔钱
        };
    }
    savePsyyRef = (ref) => {
        this.setState({
            psyyRef: ref
        })
    }
    saveZcbzRef = (ref) => {
        this.setState({
            zcbzRef: ref
        })
    }
    saveTzwlRef = (ref) => {
        this.setState({
            tzwlRef: ref
        })
    }
    saveBxbzRef = (ref) => {
        this.setState({
            bxbzRef: ref
        })
    }

    // 获取保存临时数据
    fetchData = () => {
        fetchFourmoneyTemporary().then(data => {
            data = JSON.parse(data.data);
            if (data) {
                setTimeout(() => {
                    this.loadData(data);
                }, 0)
            }
        })
    }

    // 上传保存临时数据
    submitData = () => {
        let _list = [];
        _list.push(this.state.psyyRef.submit());
        _list.push(this.state.zcbzRef.submit());
        _list.push(this.state.tzwlRef.submit());
        _list.push(this.state.bxbzRef.submit());
        let _submitData = {
            title: this.state.title,
            list: _list
        }
        for(let i = 0; i < _list.length; i++) {
            if (_list[i].tags === '') {
                _list[i].tags = [];
            } else {
                _list[i].tags = _list[i].tags.split('|');
            }
            for (let j = 0; j < _list[i].itemList.length; j++) {
                if (_list[i].itemList[j].fundList.length === 0) {
                    message.error('不允许配置空方案，方案下必须配置产品。')
                    return;
                }
            }
        }
        postFourmoneyTemporary({
            value: JSON.stringify(_submitData)
        }).then((res) => {
            if (res.code === '0000') {
                message.success('保存成功');
                window.location.reload();
            } else {
                message.error(res.message);
            }
        }).catch((e = Error) => {
            message.error(e.message);
        })
    }

    // 发布数据
    publishData = () => {
        fetchFourmoneyTemporary().then(data => {
            data = JSON.parse(data.data);
            // 发布所有配置
            postFourmoney({
                value: JSON.stringify(data)
            }).then((res) => {
                if (res.code === '0000') {
                    message.success('发布四笔钱首页配置成功');
                } else {
                    message.error(res.message);
                }
            }).catch((e = Error) => {
                message.error(e.message);
            })
            let _oneMoney = [];
            for (let i = 0; i < data.list.length; i++) {
                _oneMoney.push({
                    type: data.list[i].type,
                    value: data.list[i]
                })
            }
            // console.log(_oneMoney);
            for (let i = 0; i < _oneMoney.length; i++) {
                postOnemoney({
                    value: JSON.stringify(_oneMoney[i].value)
                }, `${_oneMoney[i].type}`).then((res) => {
                    if (res.code === '0000') {
                        message.success(`发布${_oneMoney[i].type}二级页配置成功`);
                    } else {
                        message.error(`${_oneMoney[i].type}: ${res.message}`);
                    }
                }).catch((e = Error) => {
                    message.error(e.message);
                })
            }
        }).catch((e = Error) => {
            message.error(e.message);
        })
    }
    // 装填数据
    loadData = (data) => {
        this.setState({
            title: data.title
        });
        let _psyy = {},
            _zcbz = {},
            _tzwl = {},
            _bxbz = {};
        for (let i = 0; i < data.list.length; i++) {
            let _type = data.list[i].type;
            if (_type === 'psyy') {
                _psyy = data.list[i];
            } else if (_type === 'zcbz') {
                _zcbz = data.list[i];
            } else if (_type === 'tzwl') {
                _tzwl = data.list[i];
            } else if (_type === 'bxbz') {
                _bxbz = data.list[i];
            }
        }
        this.state.psyyRef.loadData(_psyy);
        this.state.zcbzRef.loadData(_zcbz);
        this.state.tzwlRef.loadData(_tzwl);
        this.state.bxbzRef.loadData(_bxbz);
    }

    // 双向绑定输入框
    handleTitleChange = e => {
        this.setState({
            title: e.target.value
        })
    }

    componentDidMount() {
        this.fetchData();
    }

    render() {
        const {tabName, title} = this.state;
        return (
            <article>
                <Input 
                    addonBefore="标题*"
                    placeholder="首页上方大标题"
                    style={{width: '400px', marginRight: '20px'}}
                    value={title}
                    onChange={this.handleTitleChange}
                    onBlur={cantBeEmpty}
                />
                <Popconfirm
                    title="将发布保存后的数据，请确认无误。"
                    okText="发布"
                    cancelText="取消"
                    onConfirm={this.publishData}
                    onCancel={() => {}}
                    placement="bottom"
                >
                    <Button type="danger">发布</Button>
                </Popconfirm>
                <Tabs defaultActiveKey="1">
                    <TabPane tab={tabName[0]} key="1" forceRender={true}>
                        <MoneyForm 
                            type='psyy'
                            onRef={this.savePsyyRef}
                        />
                    </TabPane>
                    <TabPane tab={tabName[1]} key="2" forceRender={true}>
                        <MoneyForm 
                            type='zcbz'
                            onRef={this.saveZcbzRef}
                        />
                    </TabPane>
                    <TabPane tab={tabName[2]} key="3" forceRender={true}>
                        <MoneyForm 
                            type='tzwl'
                            onRef={this.saveTzwlRef}
                        />
                    </TabPane>
                    <TabPane tab={tabName[3]} key="4" forceRender={true}>
                        <MoneyForm 
                            type='bxbz'
                            onRef={this.saveBxbzRef}
                        />
                    </TabPane>
                </Tabs>
                <section
                    style={{width: '600px', textAlign: 'center', marginTop: '30px'}}
                >
                    <Button type="primary" onClick={this.submitData}>保存</Button>
                </section>
            </article>
        );
    }
}

// Component
// 配置面板
class MoneyForm extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            plan: [], // 方案
            data: {
                typeNameHome: '', // 首页-名称
                descHome: '', // 首页-介绍
                startRate: '', // 首页-特色-起始
                endRate: '', // 首页-特色-结束
                tags: '', // 首页-特色-自定义
                sydesc: '', // 首页-特色描述
                buttonTitle: '', // 首页-按钮文案
                buttonJumpAction: '', // 首页-按钮链接
                baseNum: '', // 首页-购买总人数基数
                recommendTag: '1', // 首页-推荐标签是否显示 0-显示 1-隐藏
                typeNameDetail: '', // 二级页-名称
                descDetail: '', // 二级页-介绍
                topicCode: '', // 二级页-讨论区id
            },
            valueType: '1', // 1-区间 2-自定义
        }
    }
    // 上传配置
    submit = () => {
        let _plan = this.state.plan;
        let _planData = [];
        for (let i = 0; i < _plan.length; i++) {
            _planData.push(_plan[i].ref.submit());
        }
        let _startRate = '',
            _endRate = '',
            _tags = this.state.data.tags;
        if (_tags instanceof Array) {
            _tags = _tags.join('|');
        }
        if (this.state.valueType === '1') {
            // 区间
            _startRate = this.state.data.startRate;
            _endRate = this.state.data.endRate;
            _tags = "";
        }
        //  else if (this.state.valueType === '2') {
        //     // 自定义
        //     _tags = _tags;
        // }
        let _data = this.state.data;
        _data.startRate = _startRate;
        _data.endRate = _endRate;
        _data.tags = _tags;
        _data.type = this.props.type;
        _data.itemList = _planData;
        return _data;
    }
    // 装填数据
    loadData = (data) => {
        let _data = {
            typeNameHome:  data.typeNameHome,
            descHome: data.descHome,
            startRate: data.startRate,
            endRate: data.endRate,
            tags: data.tags.join('|'),
            sydesc: data.sydesc,
            buttonTitle: data.buttonTitle,
            buttonJumpAction: data.buttonJumpAction,
            baseNum: data.baseNum,
            recommendTag: data.recommendTag,
            typeNameDetail: data.typeNameDetail,
            descDetail: data.descDetail,
            topicCode: data.topicCode
        }
        let _valueType = '1';
        if (_data.tags !== '') {
            _valueType = '2';
        }
        let _plan = this.state.plan;
        for (let i = 0; i < data.itemList.length; i++) {
            _plan.push({
                randomKey: ('' + Math.random()).slice(-8),
                name: data.itemList[i].name,
                desc: data.itemList[i].desc,
                relation: data.itemList[i].relation,
                thirdType: data.itemList[i].thirdType || '',
                conditions: data.itemList[i].conditions,
                buttonTitle: data.itemList[i].buttonTitle,
                buttonJumpAction: data.itemList[i].buttonJumpAction,
                cardJumpAction: data.itemList[i].cardJumpAction,
                fundList: data.itemList[i].fundList
            });
        }
        this.setState({
            data: _data,
            plan: _plan,
            valueType: _valueType
        })
    }
    // 增加方案卡片
    addPlanPlane = () => {
        let _plan = this.state.plan;
        _plan.push({
            randomKey: ('' + Math.random()).slice(-8),
            name: '',
            desc: '',
            relation: 'or',
            thirdType: '',
            conditions: [],
            holdday: '',
            buttonTitle: '',
            buttonJumpAction: '',
            cardJumpAction: '',
            fundList: []
        });
        this.setState({
            plan: _plan
        })
    }
    // 删除方案卡片
    deletePlanPlane = (count) => {
        let _plan = this.state.plan;
        _plan.splice(count - 1, 1);
        this.setState({
            plan: _plan
        })
    }
    // 绑定 ref
    saveRef = (ref, index) => {
        let _plan = this.state.plan;
        _plan[index].ref = ref;
        this.setState({
            plan: _plan
        })
    }
    // 双向绑定输入框
    handleTypeNameHomeChange = e => {
        let _data = this.state.data;
        _data.typeNameHome = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleDescHomeChange = e => {
        let _data = this.state.data;
        _data.descHome = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleSydescChange = e => {
        let _data = this.state.data;
        _data.sydesc = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleButtonTitleChange = e => {
        let _data = this.state.data;
        _data.buttonTitle = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleButtonJumpActionChange = e => {
        let _data = this.state.data;
        _data.buttonJumpAction = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleTypeNameDetailChange = e => {
        let _data = this.state.data;
        _data.typeNameDetail = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleDescDetailChange = e => {
        let _data = this.state.data;
        _data.descDetail = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleTopicCodeChange = e => {
        let _data = this.state.data;
        _data.topicCode = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleTagsChange = e => {
        let _data = this.state.data;
        _data.tags = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleStartRateChange = e => {
        let _data = this.state.data;
        _data.startRate = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleEndRateChange = e => {
        let _data = this.state.data;
        _data.endRate = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleValueTypeChange = e => {
        this.setState({
            valueType: e.target.value
        })
    }
    onRecommendTagChange = checked => {
        let _data = this.state.data;
        _data.recommendTag = checked ? '0' : '1';
        this.setState({
            data: _data
        })
    }
    handleBaseNumChange = e => {
        let _data = this.state.data;
        _data.baseNum = e.target.value;
        this.setState({
            data: _data
        })
    }

    componentDidMount() {
        this.props.onRef(this)
    }
    render () {
        const {plan, data, valueType} = this.state;
        return (
            <section>
                <div>
                    <Alert 
                        message="首页"
                        type="info"
                        showIcon
                        style={{width: '600px', marginBottom: '10px'}} 
                    />
                    <div style={{width: '600px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="名称*"
                            value={data.typeNameHome}
                            onChange={this.handleTypeNameHomeChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '600px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="介绍*"
                            value={data.descHome}
                            onChange={this.handleDescHomeChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{marginBottom: '5px'}}>
                        <span s-word="1" style={{marginRight: '10px'}}>特色*</span>
                        <Radio.Group onChange={this.handleValueTypeChange} value={valueType}>
                            <Radio value={'1'}>
                                区间
                                {valueType === '1' ? 
                                <Fragment>
                                    <Input
                                        style={{width: '80px', marginLeft: '10px', marginRight: '4px'}}
                                        value={data.startRate}
                                        onChange={this.handleStartRateChange}
                                        onBlur={cantBeEmpty}
                                    />% ~ 
                                    <Input
                                        style={{width: '80px', marginLeft: '10px', marginRight: '4px'}}
                                        value={data.endRate}
                                        onChange={this.handleEndRateChange}
                                    />%
                                </Fragment> :
                                null}
                            </Radio>
                            <Radio value={'2'}>
                                <span style={{marginRight: '5px'}}>自定义</span>
                                
                                {valueType === '2' ?
                                <Fragment>
                                    <Tooltip
                                        title="可填多个，中间用|分隔"
                                    >
                                        <Icon type="question-circle" />
                                    </Tooltip>
                                    <Input
                                        value={data.tags}
                                        onChange={this.handleTagsChange}
                                        style={{width: '200px', marginLeft: '10px'}}
                                        onBlur={cantBeEmpty}
                                    />
                                </Fragment>
                                 :
                                null}
                            </Radio>
                        </Radio.Group>
                    </div>
                    <div style={{width: '600px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="特色描述*"
                            value={data.sydesc}
                            onChange={this.handleSydescChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '600px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="按钮文案*"
                            style={{width: '200px'}}
                            value={data.buttonTitle}
                            onChange={this.handleButtonTitleChange}
                            onBlur={cantBeEmpty}
                        />
                        <Input 
                            addonBefore="按钮链接*"
                            style={{width: '370px', marginLeft: "30px"}}
                            value={data.buttonJumpAction}
                            onChange={this.handleButtonJumpActionChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{marginBottom: '5px'}}>
                        <Input 
                            style={{width: '300px', marginRight: '10px'}}
                            addonBefore="购买总人数基数*"
                            value={data.baseNum}
                            onChange={this.handleBaseNumChange}
                            onBlur={cantBeEmpty}
                        />
                        <span s-word="1" style={{display: 'inline-block', paddingTop: '2px'}}>推荐标签</span>
                        <Switch 
                            style={{marginLeft: '10px'}}
                            checked={data.recommendTag === '0' ? true : false}
                            onChange={this.onRecommendTagChange} 
                        />
                    </div>
                    <Alert 
                        message="二级页" 
                        type="info" 
                        showIcon
                        style={{width: '600px', marginBottom: '10px', marginTop: '20px'}} 
                    />
                    <div style={{width: '600px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="名称*"
                            value={data.typeNameDetail}
                            onChange={this.handleTypeNameDetailChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '600px', marginBottom: '5px'}}>
                        <Row>
                            <Col span={2}>
                                <p s-word="1" style={{textAlign: 'center'}}>介绍</p>
                            </Col>
                            <Col span={22}>
                                <TextArea 
                                    autoSize={{minRows: 2, maxRows: 6}}
                                    value={data.descDetail}
                                    onChange={this.handleDescDetailChange}
                                    onBlur={cantBeEmpty}
                                    maxLength={80}
                                />
                            </Col>
                        </Row>
                    </div>
                    <div style={{width: '600px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="讨论区id*"
                            value={data.topicCode}
                            onChange={this.handleTopicCodeChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                </div>
                {
                    plan.length === 0 ?
                    <p style={{width: '600px', textAlign: 'center', marginTop: '20px', marginBottom: '20px'}}>尚未配置方案</p> :
                    null
                }
                {
                    plan.map((item, index) => (
                        <PlanPlane 
                            key={item.randomKey}
                            data={item}
                            count={index + 1}
                            deletePlanPlane={this.deletePlanPlane}
                            onRef={(ref) => {this.saveRef(ref, index)}}
                        />
                    ))
                }
                <div style={{width: '600px', marginTop: '10px', textAlign: 'center'}}>
                    <Button onClick={this.addPlanPlane} type="primary">添加方案</Button>
                </div>
            </section>
        )
    }
    
}

// 方案面板
class PlanPlane extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            product: [], // 产品列表
            data: {
                name: '', // 名称
                desc: '', // 描述
                buttonTitle: '', // 按钮文案
                buttonJumpAction: '', // 按钮链接
                cardJumpAction: '', // 卡片其他区域连接
                relation: 'or',
                thirdType: ''
            },
            conditions: [], // 规则标签
        }
    }
    submit = () => {
        let _conditions = this.state.conditions;
        let _conditionsData = [];
        for (let i = 0; i < _conditions.length; i++) {
            _conditionsData.push(_conditions[i].ref.submit());
        }
        let _product = this.state.product;
        let _productData = [];
        for (let i = 0; i < _product.length; i++) {
            _productData.push(_product[i].ref.submit());
        }
        let _data = this.state.data;
        _data.conditions = _conditionsData;
        _data.fundList = _productData;
        return _data;
    }
    // 添加产品卡片
    addProductPlane = () => {
        let _product = this.state.product;
        _product.push({
            randomKey: ('' + Math.random()).slice(-8),
            productType: 'fund', // 产品类型 fund普通基金 group组合
            valueType: '2', // 特色 1、取值 2、配置
            productCode: '',
            productName: '',
            syvalue: '',
            sydesc: '',
            selectValue: 'week',
            holddaydesc: '',
            holdday: ''
        });
        this.setState({
            product: _product
        })
    }
    // 删除产品卡片
    deleteProductPlane = (count) => {
        let _product = this.state.product;
        _product.splice(count - 1, 1);
        this.setState({
            product: _product
        })
    }
    // 添加规则标签
    addConditionsPlane = () => {
        let _conditions = this.state.conditions;
        _conditions.push({
            randomKey: ('' + Math.random()).slice(-8),
            field: '',
            function: 'equal',
            params: []
        });
        this.setState({
            conditions: _conditions
        })
    }
    // 删除规则标签
    deleteConditionsPlane = (count) => {
        let _conditions = this.state.conditions;
        _conditions.splice(count - 1, 1);
        this.setState({
            conditions: _conditions
        })
    }
    // 绑定 ref
    saveConditionsRef = (ref, index) => {
        let _conditions = this.state.conditions;
        _conditions[index].ref = ref;
        this.setState({
            conditions: _conditions
        })
    }
    saveProductRef = (ref, index) => {
        let _product = this.state.product;
        _product[index].ref = ref;
        this.setState({
            product: _product
        })
    }
    // 双向绑定输入框
    handleNameChange = e => {
        let _data = this.state.data;
        _data.name = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleDescChange = e => {
        let _data = this.state.data;
        _data.desc = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleButtonTitleChange = e => {
        let _data = this.state.data;
        _data.buttonTitle = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleButtonJumpActionChange = e => {
        let _data = this.state.data;
        _data.buttonJumpAction = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleCardJumpActionChange = e => {
        let _data = this.state.data;
        _data.cardJumpAction = e.target.value;
        this.setState({
            data: _data
        })
    }
    handleSelectValueChange = value => {
        let _data = this.state.data;
        _data.relation = value;
        this.setState({
            data: _data
        })
    }
    handleThirdTypeChange = value => {
        let _data = this.state.data;
        _data.thirdType = value;
        this.setState({
            data: _data
        })
    }

    componentDidMount() {
        this.props.onRef(this)
        const _propData = this.props.data;
        let _data = {
            name: _propData.name,
            desc: _propData.desc,
            holdday: _propData.holdday,
            buttonTitle: _propData.buttonTitle,
            buttonJumpAction: _propData.buttonJumpAction,
            cardJumpAction: _propData.cardJumpAction,
            relation: _propData.relation,
            thirdType: _propData.thirdType
        };
        let _product = [];
        for (let i = 0; i < _propData.fundList.length; i++) {
            _product.push({
                randomKey: ('' + Math.random()).slice(-8),
                productCode: _propData.fundList[i].productCode,
                productName: _propData.fundList[i].productName,
				productType: _propData.fundList[i].productType,
				valueType: _propData.fundList[i].valueType,
				syvalue: _propData.fundList[i].syvalue,
                sydesc: _propData.fundList[i].sydesc,
                holddaydesc: _propData.fundList[i].holddaydesc,
                holdday: _propData.fundList[i].holdday
            });
        }
        let _conditions = [];
        for (let i = 0; i < _propData.conditions.length; i++) {
            _conditions.push({
                randomKey: ('' + Math.random()).slice(-8),
                field: _propData.conditions[i].field,
				function: _propData.conditions[i].function,
				params: _propData.conditions[i].params
            })
        }
        this.setState({
            product: _product,
            data: _data,
            conditions: _conditions
        })
    }
    render() {
        const {product, data, conditions} = this.state;
        const {count, deletePlanPlane} = this.props;
        return (
            <section>
                <Card
                    size="small"
                    title={`方案${count}`}
                    style={{width: '600px', marginTop: '10px'}}
                    extra={<Button onClick={() => {deletePlanPlane(count)}} type="danger" size="small">删除</Button>}
                >
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="名称*"
                            value={data.name}
                            onChange={this.handleNameChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="描述*"
                            value={data.desc}
                            onChange={this.handleDescChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="按钮文案*"
                            value={data.buttonTitle}
                            onChange={this.handleButtonTitleChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore={
                                <Fragment>
                                    <span style={{marginRight: 5}}>按钮链接</span>
                                    <Tooltip
                                        title="如需跳转到三级页，直接不填此栏，并选择三级页样式"
                                    >
                                        <Icon type="question-circle" />
                                    </Tooltip>
                                </Fragment>
                            }
                            value={data.buttonJumpAction}
                            onChange={this.handleButtonJumpActionChange}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore={
                                <Fragment>
                                    <span style={{marginRight: 5}}>卡片其他区域链接</span>
                                    <Tooltip
                                        title="如需跳转到三级页，直接不填此栏，并选择三级页样式"
                                    >
                                        <Icon type="question-circle" />
                                    </Tooltip>
                                </Fragment>
                            }
                            value={data.cardJumpAction}
                            onChange={this.handleCardJumpActionChange}
                        />
                    </div>
                    {
                        (data.cardJumpAction === "" || data.buttonJumpAction === "") ?
                        <div style={{}}>
                            <span>三级页开关和样式控制</span>
                            <Select value={data.thirdType} style={{ width: '80%'}} onChange={this.handleThirdTypeChange}>
                                <Option value="">不跳任何页面</Option>
                                <Option value="zcbz">跳资产保值三级页</Option>
                                <Option value="tzwl">跳投资未来三级页</Option>
                                <Option value="bxbz">跳保险保障三级页</Option>
                            </Select>
                        </div> : null
                    }
                    <div style={{marginBottom: '5px'}}>
                        <span style={{marginRight: '10px'}}>标签规则</span>
                        <Select value={data.relation} style={{ width: '70px', marginLeft: 10 }} onChange={this.handleSelectValueChange}>
                            <Option value="or">或</Option>
                            <Option value="and">与</Option>
                        </Select>
                    </div>
                    <div style={{width: '560px', marginBottom: '5px'}}>
                        <Row>
                            <Col span={6}>
                                <span style={{marginRight: '10px'}}>标签id</span>
                                <Tooltip
                                    title="所使用标签需联系叶志飞（<EMAIL>）加入到标签接口中"
                                >
                                    <Icon type="question-circle" />
                                </Tooltip>
                            </Col>
                            <Col span={7}>逻辑</Col>
                            <Col span={8}>
                                <span style={{marginRight: '10px'}}>值</span>
                                <Tooltip
                                    title="可输入多个，中间用英文逗号隔开"
                                >
                                    <Icon type="question-circle" />
                                </Tooltip>
                            </Col>
                            <Col span={3} style={{textAlign: 'center'}}>
                                <Button onClick={this.addConditionsPlane} type="primary" size="small">添加</Button>
                            </Col>
                        </Row>
                        {
                            conditions.length === 0 ?
                            <p style={{textAlign: 'center', marginTop: '20px', marginBottom: '20px'}}>尚未设置标签规则</p> :
                            null
                        }
                        {
                            conditions.map((item, index) => (
                                <ConditionsPlan 
                                    key={item.randomKey}
                                    count={index + 1}
                                    data={item}
                                    deleteConditionsPlane={this.deleteConditionsPlane}
                                    onRef={(ref) => {this.saveConditionsRef(ref, index)}}
                                />
                            ))
                        }
                    </div>
                    <section style={{width: '100%', height: '1px', backgroundColor: '#e2e2e2', marginTop: '5px', marginBottom: '5px'}}></section>
                    {
                        product.length === 0 ?
                        <p style={{textAlign: 'center', marginTop: '20px', marginBottom: '20px'}}>尚未配置产品</p> :
                        null
                    }
                    {
                        product.map((item, index) => (
                            <ProductPlane 
                                key={item.randomKey}
                                count={index + 1}
                                data={item}
                                deleteProductPlane={this.deleteProductPlane}
                                onRef={(ref) => {this.saveProductRef(ref, index)}}
                            />
                        ))
                    }
                    <div style={{width: '500px', marginTop: '10px', textAlign: 'center'}}>
                        <Button onClick={this.addProductPlane} type="primary">添加产品</Button>
                    </div>
                </Card>
            </section>
        )
    }
}

// 标签规则面板
class ConditionsPlan extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            _field: '', // 标签id
            _function: 'equal', // 逻辑
            _params: '', // 值 - 上传时，需处理成数组，逗号隔开
            fieldContent: '点击获取详细内容', // 标签id具体内容
        }
    }

    submit = () => {
        let {_field, _function, _params} = this.state;
        if (_params === '') {
            _params = [];
        } else {
            _params = _params.split(',');
        }
        return {
            field: _field,
            function: _function,
            params: _params
        }
    }

    // 查询标签id具体内容
    searchFieldContent = () => {
        const {_field, fieldContent} = this.state;
        if (_field === '') {
            return message.error('请输入标签id')
        }
        if (fieldContent !== '点击获取详细内容') {return;}
        fetchRedeemConditionField({
            labelNum: _field
        }).then((data) => {
            if (data.data) {
                let _data = JSON.parse(data.data).data;
                let _nameIndex = 0,
                    _enumIndex = 0;
                for (let i = 0; i < _data.head.length; i++) {
                    if (_data.head[i].id === 'label_name') {
                        _nameIndex = i
                    }
                    if (_data.head[i].id === 'label_enum') {
                        _enumIndex = i;
                    }
                }
                let _result = '';
                if (_data.body[0]) {
                    _result = `名称：${_data.body[0][_nameIndex]}；枚举值：${_data.body[0][_enumIndex]}`
                } else {
                    _result = '名称：；枚举值：'
                }
                this.setState({
                    fieldContent: _result
                })
            }
        })
    }

    // 双向绑定输入框
    handleFieldChange = e => {
        this.setState({
            _field: e.target.value
        })
    }
    handleParamsChange = e => {
        this.setState({
            _params: e.target.value
        })
    }
    handleSelectValueChange = value => {
        this.setState({
            _function: value
        })
    }

    componentDidMount() {
        this.props.onRef(this);
        let _propData = this.props.data;
        this.setState({
            _field: _propData.field, // 标签id
            _function: _propData.function, // 逻辑
            _params: _propData.params.join(','),
        })
    }

    render () {
        const {_field, _params, _function, fieldContent} = this.state;
        const {deleteConditionsPlane, count} = this.props;
        return (
            <section style={{marginTop: '10px'}}>
                <Row>
                    <Col span={6}>
                        <Input
                            style={{width: '70%', marginRight: '10px'}}
                            value={_field}
                            onChange={this.handleFieldChange}
                            onBlur={cantBeEmpty}
                        />
                        <Tooltip
                            title={fieldContent}
                        >
                            <Icon type="eye" theme="filled" onClick={this.searchFieldContent} />
                        </Tooltip>
                    </Col>
                    <Col span={7}>
                        <Select value={_function} style={{ width: '80%'}} onChange={this.handleSelectValueChange}>
                            <Option value="equal">等于</Option>
                            <Option value="notEqual">不等于</Option>
                            <Option value="in">字符串包含</Option>
                            <Option value="notIn">字符串不包含</Option>
                            <Option value="isSet">有值</Option>
                            <Option value="notSet">没值</Option>
                            <Option value="less">小于</Option>
                            <Option value="greater">大于</Option>
                            <Option value="greaterEqual">大于等于</Option>
                            <Option value="lessEqual">小于等于</Option>
                        </Select>
                    </Col>
                    <Col span={8}>
                        <Input 
                            style={{width: '80%'}}
                            value={_params}
                            onChange={this.handleParamsChange}
                            onBlur={cantBeEmpty}
                        />
                    </Col>
                    <Col span={3} style={{textAlign: 'center', lineHeight: '32px'}}>
                        <Icon onClick={() => {deleteConditionsPlane(count)}} type="delete" theme="filled" />
                    </Col>
                </Row>
            </section>
        )
    }
}

// 产品面板
class ProductPlane extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            productType: 'fund', // 产品类型 fund普通基金 group组合 fixedincome 券商理财
            valueType: '2', // 特色 1、取值 2、配置
            productCode: '', // 产品代码
            productName: '', // 产品名称
            syvalue: '',
            sydesc: '',
            selectValue: 'week',
            holdday: '',
            holddaydesc: ''
        }
    }
    submit = () => {
        let {productType, valueType, productCode, syvalue, sydesc, selectValue, holddaydesc, holdday, productName} = this.state;
        let _syvalue = '';
        if (valueType === '1') {
            _syvalue = selectValue
        } else if (valueType === '2') {
            _syvalue = syvalue
        }
        return {
            productType: productType,
            valueType: valueType,
            productCode: productCode,
            productName: productName,
            syvalue: _syvalue,
            sydesc: sydesc,
            holddaydesc: holddaydesc,
            holdday: holdday
        }
    }

    // 双向绑定
    handleProductTypeChange = e => {
        this.setState({
            productType: e.target.value,
        });
    }
    handleValueTypeChange = e => {
        this.setState({
            valueType: e.target.value,
        });
    }
    handleSelectValueChange = value => {
        this.setState({
            selectValue: value,
        });
    }
    handleProductCodeChange = e => {
        this.setState({
            productCode: e.target.value,
        });
    }
    handleProductNameChange = e => {
        this.setState({
            productName: e.target.value
        })
    }
    handleSyvalueChange = e => {
        this.setState({
            syvalue: e.target.value,
        });
    }
    handleSydescChange = e => {
        this.setState({
            sydesc: e.target.value,
        });
    }
    handleHolddayChange = e => {
        this.setState({
            holdday: e.target.value,
        });
    }
    handleHolddayDescChange = e => {
        this.setState({
            holddaydesc: e.target.value,
        });
    }

    componentDidMount() {
        this.props.onRef(this);
        const _propData = this.props.data;
        let _syvalue = '',
            _selectValue = 'week';
        if (_propData.valueType === '1') {
            _selectValue = _propData.syvalue;
        } else if (_propData.valueType === '2') {
            _syvalue = _propData.syvalue;
        }
        this.setState({
            productType: _propData.productType, // 产品类型 fund普通基金 group组合 fixedincome 券商
            valueType: _propData.valueType, // 特色 1、取值 2、配置
            productCode: _propData.productCode,
            productName: _propData.productName,
            syvalue: _syvalue,
            sydesc: _propData.sydesc,
            selectValue: _selectValue,
            holdday: _propData.holdday,
            holddaydesc: _propData.holddaydesc
        })
    }

    render () {
        const {productType, valueType, productCode, sydesc, syvalue, selectValue, holddaydesc, holdday, productName} = this.state;
        const {count, deleteProductPlane} = this.props;
        return (
            <section>
                <Card
                    size="small"
                    title={`产品${count}`}
                    style={{width: '560px', marginTop: '10px'}}
                    extra={<Button onClick={() => {deleteProductPlane(count)}} type="danger" size="small">删除</Button>}
                >
                    <div style={{marginBottom: '5px'}}>
                        <span style={{marginRight: '10px'}}>产品类型</span>
                        <Radio.Group onChange={this.handleProductTypeChange} value={productType}>
                            <Radio value={'fund'}>普通基金</Radio>
                            <Radio value={'group'}>组合</Radio>
                            <Radio value={'fixedincome'}>券商理财</Radio>
                        </Radio.Group>
                    </div>
                    <div style={{marginBottom: '5px'}}>
                        <span style={{marginRight: '10px'}}>特色*</span>
                        <Radio.Group onChange={this.handleValueTypeChange} value={valueType}>
                            <Radio value={'2'}>
                                自定义
                                {valueType === '2' ? 
                                <Input
                                    value={syvalue}
                                    onChange={this.handleSyvalueChange}
                                    style={{ width: '100px', marginLeft: '10px' }} 
                                    onBlur={cantBeEmpty}
                                /> :
                                 null}
                            </Radio>
                            <Radio value={'1'}>
                                <span>取值</span>
                                {valueType === '1' ? 
                                <Fragment>
                                <Select value={selectValue} style={{ width: '240px', marginLeft: 10 }} onChange={this.handleSelectValueChange}>
                                    <Option value="week">近一周收益-week</Option>
                                    <Option value="month">近一月收益-month</Option>
                                    <Option value="tmonth">近三月收益-tmonth</Option>
                                    <Option value="hyear">近半年收益-hyear</Option>
                                    <Option value="year">近一年收益-year</Option>
                                    <Option value="now">成立以来收益-now</Option>
                                    <Option value="nhsy">7日年化收益-nhsy</Option>
                                    <Option value="annualizedRate">成立以来年化-annualizedRate</Option>
                                </Select>
                                {
                                    productType === 'fixedincome' ?
                                    <span>无需选择，默认年化收益基准</span> : null
                                }
                                </Fragment>
                                : null}
                            </Radio>
                        </Radio.Group>
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="特色描述*"
                            value={sydesc}
                            onChange={this.handleSydescChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="产品ID*"
                            value={productCode}
                            onChange={this.handleProductCodeChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="产品名称*"
                            value={productName}
                            onChange={this.handleProductNameChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="建议持有期限*"
                            value={holdday}
                            onChange={this.handleHolddayChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                    <div style={{width: '500px', marginBottom: '5px'}}>
                        <Input 
                            addonBefore="建议持有期限描述*"
                            value={holddaydesc}
                            onChange={this.handleHolddayDescChange}
                            onBlur={cantBeEmpty}
                        />
                    </div>
                </Card>
            </section>
        )
    }
}

function cantBeEmpty (e, notice = '请填写完整！', flag = true) {
    if (flag && e.target.value === '') {
        message.error(notice);
    }
}

export default ifundMoneyK;