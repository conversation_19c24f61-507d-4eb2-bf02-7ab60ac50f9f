import React, { useEffect, useState } from 'react';
import { Table, But<PERSON>, Popconfirm, Drawer, Input, message } from 'antd';
interface dataProps {
    flag?: number,
    fundCode?: string,
    fundName?: string,
    name?: string,
    applyArea?: string,
    pe?: string,
    pb?: string,
}
interface rankProps {
    flag?: number,
    fundCode?: string,
    fundName?: string,
    name?: string,
    type?: string,
    introduce?: string,
    innerFund?: string,
    innerName?: string,
    innerText?: string,
    outterFund?: string,
    outterName?: string,
    outterText?: string,
    applyArea?: string,
    pe?: string,
    pb?: string,
}
interface iProps {
    saveRecommend: Function,
    data: Array<dataProps>
    rankData: Array<rankProps>
}

export default function ({ saveRecommend, data, rankData }: iProps) {
    const [list, setList] = useState<Array<dataProps>>([])
    const [addStrategy, setAdd] = useState<string>('')
    useEffect(() => {
        console.log('recommend', data)
        // if (data.length === 0) return
        setList(data)
    }, [data])

    const columns = [
        {
            title: '指数代码',
            dataIndex: 'fundCode',
            key: 'fundCode',
            editable: true,

        },
        {
            title: '指数名称',
            dataIndex: 'fundName',
            key: 'fundName',
        },
        {
            title: '展示名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '适用指标',
            dataIndex: 'applyArea',
            key: 'applyArea',
        },
        {
            title: 'PE',
            dataIndex: 'pe',
            key: 'pe',
        },
        {
            title: 'PB',
            dataIndex: 'pb',
            key: 'pb',
        },
        {
            title: 'operation',
            dataIndex: 'operation',
            render: (text: any, record: any) =>
                <div>
                    <Popconfirm title="确定删除?" onConfirm={() => deleteItem(record.fundCode)}>
                        <Button type="danger" ghost>删除</Button>
                    </Popconfirm>


                </div>
        },

    ]
    function addItem() {
        let isExist = list.find(val => val.fundCode === addStrategy)
        if(isExist) {
            message.error(`${addStrategy}已存在`)
            return
        }
        let target:any = rankData.find(val => val.fundCode === addStrategy)
        console.log('target', target)
        if (!target) {
            message.error(`找不到${addStrategy}`)
        } else {
            let _temp: dataProps = {
                flag: new Date().getTime(),
                fundCode: target.fundCode,
                fundName: target.fundName,
                name: target.name,
                applyArea: target.applyArea,
                pe: target.pe,
                pb: target.pb,
            }
            const _list: Array<dataProps> = [...list]
            _list.push(_temp)
            // setList(_list)
            saveRecommend(_list)
        }


    }
    function deleteItem(fundCode: any) {
        const _list = [...list]
        // setList(_list.filter(item => item.flag !== flag))
        console.log('delete', _list)
        saveRecommend(_list.filter(item => item.fundCode !== fundCode))
    }

    function handleChange(params: string) {
        setAdd(params)
    }
    return <div>
        <p className={'g-fs30'}>近期推荐配置</p>
        <Input
            onChange={(e) => { handleChange(e.target.value) }}
            value={addStrategy || ''}
            style={{ width: 200 }}
            placeholder="请输入要增加的指数代码"
        />
        <Button onClick={addItem} type="primary" style={{ margin: 16 }}>
            增加
        </Button>
        <Table
            columns={columns}
            dataSource={list}
        >

        </Table>


    </div>

}