import React, { useState, useEffect, FC } from 'react';

import { Icon, Modal, Input, Popover, message, Tag } from 'antd';
import styles from './index.less'
import api from "api";
import searchImage from "./images/conditionSearch.JPEG"
import resultImage from "./images/conditionResult.JPEG"
const {getWCParseQuestion} = api
const { TextArea } = Input;
export interface IProps {
  name:string;
  onChange: Function;
  value: {
    normal: string;
    parsed: any;
  };
  readonly: boolean;
}
const ConditionParser: FC<IProps> = ({ value, name,onChange, readonly,disabled }) => {
  const [modalShow, setModalShow] = useState(false);
  const [modalStr, setModalStr] = useState('');
  const [loading, setLoading] = useState(false);
 
  useEffect(() => {
    if (modalShow) {
      //显示时默认展示normal值
      setModalStr(value.normal);
    }
  }, [modalShow]);

  const handleParse = async () => {
    if(!modalStr){
      message.warn("请输入问句内容")
      return ;
    }
    try {
      setLoading(true);
      const res = await getWCParseQuestion({sentence:modalStr})
      if(res.status_code!==0){
        throw new Error(status_msg);
      }
      onChange(name,{
        normal: modalStr,
        parsed:res.data||[],
      });
      setModalShow(false);
    } catch (e) {
      
      message.error(e.message||'转换失败');
    } finally {
      setLoading(false);
    }
  };
  return (
    <div style={{
      opacity:readonly?0.5:1,
      pointerEvents:readonly?"none":"initial"
    }}>
      <span className={styles['tag-container']}>
        {value?.parsed?.map(item=>{
          return <Tag>{String(item)}</Tag>
        })}
      </span>
      <span
        style={{ cursor: 'pointer' }}
        onClick={() => {
          setModalShow(true);
        }}
      >
        <Icon type="form" />
        <span>修改</span>
      </span>
      <span style={{ marginLeft: '12px' }}>
        <Popover
          content={
            <>
              <p>
                请使用条件选基功能配置选基条件，复制问财解析后的条件问句，粘贴进条件设置框以完成配置
              </p>
              <img style={{height:"400px",marginRight:"12px"}} src={searchImage}/>
              <img  style={{height:"400px"}} src={resultImage}/>
            </>
          }
        >
          <Icon type="question-circle" />
        </Popover>
      </span>
      <Modal
        confirmLoading={loading}
        title={'请输入问句'}
        visible={modalShow}
        onCancel={() => setModalShow(false)}
        onOk={() => {handleParse()}}
      >
        <TextArea
          value={modalStr}
          onChange={e => {
            setModalStr(e.target.value);
          }}
        />
      </Modal>
    </div>
  );
};
export default ConditionParser;
