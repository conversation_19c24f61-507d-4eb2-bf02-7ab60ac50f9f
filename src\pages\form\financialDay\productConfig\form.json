{"propsSchema": {"type": "object", "properties": {"productInfo": {"title": "产品配置", "type": "array", "items": {"type": "object", "properties": {"fundCode": {"title": "产品代码", "type": "string", "ui:width": "40%"}, "fundName": {"title": "产品名称", "type": "string", "ui:width": "40%"}, "label1": {"title": "文案1", "type": "string", "ui:width": "40%"}, "label2": {"title": "文案2", "type": "string", "ui:width": "40%"}, "jumpUrl": {"title": "跳转链接", "type": "string", "ui:width": "60%"}}, "required": ["fundCode", "fundName"]}}, "fundType": {"type": "array", "widget": "checkboxes", "items": {"type": "string"}, "enum": ["hb", "gp", "zq", "hh"], "enumNames": ["货币型", "股票型", "债券型", "混合型"]}}}, "formData": {"productInfo": [], "fundType": []}}