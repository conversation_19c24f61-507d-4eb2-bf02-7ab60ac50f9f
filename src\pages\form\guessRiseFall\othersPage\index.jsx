import React, { useState, useEffect } from 'react';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import 'moment/locale/zh-cn';
import styles from './index.less';
import classnames from 'classnames';
import api from 'api';
import moment from 'moment';
import {Row, Col, Input, Button, Divider, message, Drawer, Table, Modal, Spin, DatePicker,ConfigProvider, Upload,Icon } from 'antd';

const { confirm } = Modal;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
message.config({
    duration: 3,// 持续时间
    maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
    top: 70,// 到页面顶部距离
});
const {getGuessRiseFallFeedbackList, downloadGuessRiseFallFeedbackList, othersPageImgUpload, saveOthersConfig, getOthersConfig} = api;
export default function() {
    const [pageLoading,setPageLoading] = useState(false); // 页面loading
    const [saveBtnLoading,setSaveBtnLoading] = useState(false); // 保存按钮loading
    const [listData, setListData] = useState(null); // 反馈列表数据
    const [listTotal,setListTotal] = useState(0); // 列表总条数
    const [currentPageNum, setCurrentPageNum] = useState(); // 当前页码
    const [startDate, setStartDate] = useState(moment('00:00:00', 'HH:mm:ss')); // 开始日期
    const [endDate, setEndDate] = useState(moment('23:59:59', 'HH:mm:ss')); // 结束日期
    const [userId, setUserId] = useState(''); // userId
    const [formData,setFormData] = useState({}); // 表单数据
    const [isShowDrawer,setIsShowDrawer] = useState(false); // 是否显示杂项配置抽屉
    const [isUploading,setIsUploading] = useState(false); // 上传中loading
    const pageSize = 20; // 列表页每页数量
    const tableColumns = [
        {
            title: '序号',
            dataIndex: 'messageId',
            key: 'messageId',
            width: 100,
          },
        {
          title: '提交时间',
          dataIndex: 'submitTime',
          key: 'submitTime',
          width: 200,
        },
        {
            title: '手炒id',
            dataIndex: 'userId',
            key: 'userId',
            width: 200,
          },
        {
          title: '反馈内容',
          dataIndex: 'message',
          key: 'message',
        },
    ]; // 列表项

    useEffect(() => {
        handleGetTaskList(1);
        initFormData();
    }, []);
    // 修改日期范围
    const changeDateRange = (e) => {
        setStartDate(e[0]);
        setEndDate(e[1]);
    };
    // 获取任务列表
    const handleGetTaskList = (pageNum) => {
        const params = {
            pageNum: pageNum,
            pageSize: pageSize,
            userId: userId,
            startDate: startDate ? moment(startDate).valueOf()/1000 : 0,
            endDate: endDate ? moment(endDate).valueOf()/1000 : 0
        }
        setPageLoading(true);
        getGuessRiseFallFeedbackList(params).then((res)=>{
            setPageLoading(false);
            if (res?.code === '0000') {
                setListData(res?.data?.pointGuessMessageDtoList);
                setListTotal(res?.data?.size);
            }
        });
        setCurrentPageNum(pageNum);
    };
    // 下载反馈列表
    const handleDownloadFeedback = () => {
        const params = {
            userId: userId,
            startDate: startDate ? moment(startDate).valueOf()/1000 : 0,
            endDate: endDate ? moment(endDate).valueOf()/1000 : 0
        }
        downloadGuessRiseFallFeedbackList({},`userId=${params.userId}&startDate=${params.startDate}&endDate=${params.endDate}`,'',{ responseType: 'blob' }).then((res)=>{
        }).catch(()=>{
            message.error('下载失败');
        });
    }
    // 初始化表单
    const initFormData = () => {
        let _formData = {};
        _formData = {
            backGroundUrl: '',
            bannerImg: '',
            signReward: [''],
            playRule: '',
            guessRule: '',
            coinRule: ''
        }
        setFormData(_formData);
    };
    // 上传图片
    const uploadImg = (options,type,index)=> {
        if (options.file.size > 5120000) {
            message.error('文件大小不得超过5M');
            return;
        }
        setIsUploading(true);
        message.info('图片上传中');
        let params = new FormData();
        params.append('file', options.file);
        othersPageImgUpload(params).then((res)=>{
            if (res.status_code === 0) {
                let _formData = {...formData};
                switch (type) {
                    case 'bgImg':
                        _formData.backGroundUrl = insertStr(res.data,4,'s');
                        break;
                    case 'bannerImg':
                        _formData.bannerImg = insertStr(res.data,4,'s');
                        break;
                    default:
                        break;
                }
                setFormData(_formData);
                message.success('上传成功');
            } else {
                message.error(res.status_msg);
            }
            setIsUploading(false);
        }).catch(()=>{
            message.error('上传失败');
            setIsUploading(false);
        });
    }
    // 增加或减少签到奖励天数
    const changeRewardDays = (type)=>{
        const _formData = {...formData};
        switch (type) {
            case 'add':
                _formData.signReward.push('');
                break;
            case 'reduce':
                if (_formData.signReward.length > 1) {
                    _formData.signReward.pop();
                } else {
                    message.error('签到奖励最少为1天');
                }
                break;
            default:
                break;
        }
        setFormData(_formData);
    }
    // 列表项发生变化
    const changeFormItem = (type, value, index) => {
        const _formData = deepClone(formData);
        switch (type) {
            case 'signReward':
                _formData.signReward[index] = value;
                break;
            default:
                _formData[type] = value;
                break;
        }
        setFormData(_formData);
    };
    // 打开抽屉
    const openDrawer = (record) => {
        setPageLoading(true);
        // 调用接口获取返显数据
        getOthersConfig().then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                let _formData = JSON.parse(res.data) || formData;
                setFormData(_formData);
                setIsShowDrawer(true);
            } else {
                message.error('获取信息失败');
            }
        });
    };
    // 表单校验
    const checkFormData = () => {
        let isPass = false;
        switch (true) {
            case !formData.backGroundUrl:
                message.error('请上传活动背景图');
                break;
            case !formData.bannerImg:
                message.error('请上传活动banner');
                break;
            case !formData.signReward.every((item,index)=>{return item}):
                message.error('填写签到奖励');
                break;
            case !formData.playRule:
                message.error('请填写玩法介绍');
                break;
            case !formData.guessRule:
                message.error('请填写竞猜规则');
                break;
            case !formData.coinRule:
                message.error('请填写金币使用规则');
                break;
            default:
                isPass = true;
                break;
        }
        return isPass;
    };
    // 保存
    const saveForm = () => {
        if (checkFormData()) {
            confirm({
                title: '提示',
                content: '确认修改活动配置吗',
                okText: "确定",
                centered: true,
                cancelText: "取消",
                onOk() {
                    setSaveBtnLoading(true);
                    saveOthersConfig({
                        value: JSON.stringify(formData)
                    }).then((res)=>{
                        setSaveBtnLoading(false);
                        if (res.code !== '0000') {
                            message.error(res.msg);
                        } else {
                            message.success('保存成功！');
                            closeDrawer();
                        }
                    }).catch((e) => {
                        setSaveBtnLoading(false);
                        message.error('保存失败');
                    });
                }
            });
        }
    };
    // 关闭新增/编辑抽屉
    const closeDrawer = () => {
        initFormData();
        setIsShowDrawer(false);
    };
    // 深拷贝对象/数组
    const deepClone = (obj) => {
        return JSON.parse(JSON.stringify(obj));
    };
    // 字符串指定位置插入字符
    const insertStr = (soure, start, newStr) =>{   
        return soure.slice(0, start) + newStr + soure.slice(start);
    }
    return (
        <div className={styles['task-page']}>
            <Spin spinning={pageLoading}>
                <div style={{marginBottom: '20px'}}>活动负责人：zhengzekai。<br/>活动链接：client.html?action=ymtz^webid=2997^title=猜涨跌^titleColorOpacity=ffffff^titleColor=323232^backColorOpacity=ffffff^backColor=323232^titleBarColor=ffffff^url=https://fund.10jqka.com.cn/scym_scsy/public/m/guessRiseFall/dist/index.html#/</div>
                <Button type="primary" style={{marginBottom: '20px'}} onClick={()=>openDrawer()}>活动杂项配置</Button>
                <div gutter={[40,14]} style={{fontSize: '20px', fontWeight:600,marginBottom: '10px'}}>用户反馈</div>
                <div className={styles['header']} style={{marginBottom: '20px',display: 'flex', flexDirection: 'row',justifyContent: 'space-between',whiteSpace: 'nowrap'}}>
                    <div>
                        <ConfigProvider locale={zh_CN}>
                            日期：
                            <RangePicker 
                                showToday={false} 
                                style={{width:'50%',marginRight: '20px'}}
                                value={[startDate,endDate]}
                                onChange={(e)=>{changeDateRange(e)}}
                                showTime={{
                                    // defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
                                }}
                            />
                        </ConfigProvider>
                        <div style={{display: 'inline-block'}}>
                            userId：
                            <Input
                                value={userId}
                                onChange={(e)=>{setUserId(e.target.value)}}
                                style={{display: 'inline-block',width: '200px',marginRight: '20px'}}
                            />
                        </div>
                        <Button type="primary" onClick={()=>handleGetTaskList(1)}>查询</Button>
                    </div>
                    <Button type="primary" onClick={()=>{handleDownloadFeedback()}}>表格下载</Button>
                </div>
                <Table
                    columns={tableColumns}
                    dataSource={listData}
                    pagination={{ pageSize: pageSize, total: listTotal, current: currentPageNum}}
                    onChange={(pagination)=>{handleGetTaskList(pagination.current);}}
                    rowKey={record=>record.taskId}
                />
                <Drawer
                    className="others-page-drawer"
                    title='杂项配置（保存即影响线上用户 谨慎配置）'
                    placement="right"
                    width="1300"
                    maskClosable={false}
                    destroyOnClose={true}
                    onClose={closeDrawer}
                    visible={isShowDrawer}
                >
                    <Row gutter={[40,14]} style={{fontWeight: '600'}}> <Col span={8}><span style={{color: '#f00'}}>*</span>活动背景图（尺寸375*465）：</Col></Row>
                    <Row gutter={[40,14]}>
                        <Col span={8} style={{alignItems: 'flex-end'}}>
                            <div className={classnames(styles['activity-bg-img'],'activity-bg-img')}>
                                <img style={{width: '100%',height: '100%',display: formData.backGroundUrl ? '' : 'none'}} src={formData.backGroundUrl} alt=""/>
                            </div>
                            <Upload
                                customRequest={(options)=>{uploadImg(options,'bgImg')}}
                                showUploadList={false}
                            >
                                <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                            </Upload>
                        </Col>
                    </Row>
                    <Divider />
                    <Row gutter={[40,14]} style={{fontWeight: '600'}}> <Col span={8}><span style={{color: '#f00'}}>*</span>活动banner（尺寸343*90）：</Col></Row>
                    <Row gutter={[40,14]}>
                        <Col span={8} style={{alignItems: 'flex-end'}}>
                            <div className={classnames(styles['banner-img'],'banner-img')}>
                                <img style={{width: '100%',height: '100%',display: formData.bannerImg ? '' : 'none'}} src={formData.bannerImg} alt=""/>
                            </div>
                            <Upload
                                customRequest={(options)=>{uploadImg(options,'bannerImg')}}
                                showUploadList={false}
                            >
                                <Button style={{marginRight: '20px'}} disabled={isUploading}>上传</Button>
                            </Upload>
                        </Col>
                    </Row>
                    <Divider />
                    <Row gutter={[40,14]} style={{fontWeight: '600'}}>
                        <Col span={8}>
                            <span style={{color: '#f00'}}>*</span>签到奖励：
                            <Icon type="plus-square" style={{cursor:'pointer',marginRight: '10px'}} onClick={()=>{changeRewardDays('add')}}/>
                            <Icon type="minus-square" style={{cursor:'pointer'}} onClick={()=>{changeRewardDays('reduce')}}/>
                        </Col>
                    </Row>
                    <Row gutter={[40,14]}>
                        {
                            formData.signReward && formData.signReward.map((item,index)=>{
                                return (
                                    <Col span={3} key={index}>
                                        <div>{`第${index+1}天：`}</div>
                                        <Input value={formData.signReward[index]} onChange={(e)=>{changeFormItem('signReward',e.target.value,index)}}/>
                                    </Col> 
                                )
                            })
                        }
                    </Row>
                    <Divider />
                    <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col span={8}><span style={{color: '#f00'}}>*</span>活动规则：</Col></Row>
                    <Row gutter={[40,14]}>
                        <Col span={24}>
                            <div>玩法介绍：</div>
                            <TextArea size='default' autoSize={{minRows: 2, maxRows: 10}} value={formData.playRule} onChange={(e)=>{changeFormItem('playRule',e.target.value)}}/>
                        </Col> 
                    </Row>
                    <Row gutter={[40,14]}>
                        <Col span={24}>
                            <div>竞猜规则：</div>
                            <TextArea size='default' autoSize={{minRows: 2, maxRows: 10}} value={formData.guessRule} onChange={(e)=>{changeFormItem('guessRule',e.target.value)}}/>
                        </Col> 
                    </Row>
                    <Row gutter={[40,14]}>
                        <Col span={24}>
                            <div>金币使用规则：</div>
                            <TextArea size='default' autoSize={{minRows: 2, maxRows: 10}} value={formData.coinRule} onChange={(e)=>{changeFormItem('coinRule',e.target.value)}}/>
                        </Col> 
                    </Row>
                    <Row gutter={[40,14]}>
                        <Col span={8}>
                            <Button type="primary" style={{ marginRight: 24 }} loading={saveBtnLoading} onClick={()=>{saveForm()}}>保存</Button>
                            <Button type="danger" onClick={()=>{closeDrawer()}}>取消</Button>
                        </Col>
                    </Row>
                    <Divider />
                </Drawer>
            </Spin>
        </div>
    )
}

