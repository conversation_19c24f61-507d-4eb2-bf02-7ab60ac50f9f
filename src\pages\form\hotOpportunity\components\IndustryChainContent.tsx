import React, { useState } from 'react';
import { Form, Input, Button, Icon } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import { useFieldArray } from '../hooks';
import ContentPreview from './ContentPreview';
import styles from '../modal.less';

// 产业链配置
const industryChainConfig = {
  upstream: { title: '上游产业链', placeholder: '请输入上游产业链标题' },
  midstream: { title: '中游产业链', placeholder: '请输入中游产业链标题' },
  downstream: { title: '下游产业链', placeholder: '请输入下游产业链标题' },
} as const;

interface IndustryChainContentProps {
  type: 'upstream' | 'midstream' | 'downstream';
  form: FormComponentProps['form'];
  initialLength?: number;
  formData?: any;
}

const IndustryChainContent: React.FC<IndustryChainContentProps> = ({
  type,
  form,
  initialLength = 0,
  formData,
}) => {
  const { getFieldDecorator, getFieldValue } = form;
  const config = industryChainConfig[type];
  const fieldPath = `data.industryConfig.detail.${type}`;

  // 预览状态管理 - 记录每个小标题组的预览展开状态
  const [previewStates, setPreviewStates] = useState<{ [key: number]: boolean }>({});

  // 计算数组初始长度
  const calculateInitialLength = (): number => {
    // 优先使用formData中的实际数组长度
    if (formData?.data?.industryConfig?.detail?.[type]?.industry) {
      return formData.data.industryConfig.detail[type].industry.length;
    }
    // 如果没有formData，使用传入的initialLength
    return initialLength;
  };

  const arrayManager = useFieldArray({ arrNum: calculateInitialLength() });

  // 切换预览状态
  const togglePreview = (key: number) => {
    setPreviewStates(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <>
      <Form.Item label="标题">
        {getFieldDecorator(`${fieldPath}.title`, {rules: [{required: true, message: '请输入标题'}]})(<Input placeholder={config.placeholder} />)}
      </Form.Item>

      {arrayManager?.arr?.map((key: number, index: number) => {
        const summaryValue = getFieldValue(`${fieldPath}.industry[${key}].summary`) || '';
        const isPreviewVisible = previewStates[key] || false;
        
        return (
          <div className={styles['industry-item-group']} key={key}>
            <div className={styles['industry-item-group-header']}>
              小标题组 {index + 1}
              <div style={{ float: 'right' }}>
                <Button
                  type="link"
                  size="small"
                  onClick={() => togglePreview(key)}
                  style={{ marginRight: 8 }}
                >
                  <Icon type={isPreviewVisible ? 'eye-invisible' : 'eye'} />
                  {isPreviewVisible ? '隐藏预览' : '显示预览'}
                </Button>
                <Button
                  type="link"
                  size="small"
                  style={{ color: '#ff4d4f' }}
                  onClick={() => arrayManager.onRemove(key)}
                >
                  删除
                </Button>
              </div>
            </div>
            <Form.Item label="小标题">
              {getFieldDecorator(`${fieldPath}.industry[${key}].name`, {rules: [{required: true, message: '请输入小标题'}]})(
                <Input placeholder="请输入标题" />,
              )}
            </Form.Item>
            <Form.Item label="内容">
              {getFieldDecorator(`${fieldPath}.industry[${key}].summary`, {rules: [{required: true, message: '请输入内容'}]})(
                <Input.TextArea 
                  placeholder="请输入内容，支持超链接格式：{文字-链接}" 
                  rows={4}
                />,
              )}
            </Form.Item>
            
            {/* 预览区域 */}
            {isPreviewVisible && (
              <div className={styles['preview-section']}>
                <ContentPreview
                  content={summaryValue}
                  title="内容预览"
                  showValidation={true}
                />
              </div>
            )}
          </div>
        );
      })}

      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          size="large"
          style={{ marginBottom: 8 }}
          onClick={() => arrayManager.onAdd()}
          disabled={arrayManager?.arr?.length >= 3}
        >
          {arrayManager?.arr?.length >= 3 ? '已达上限(3个)' : '新增小标题'}
        </Button>
      </div>
    </>
  );
};

export default IndustryChainContent; 