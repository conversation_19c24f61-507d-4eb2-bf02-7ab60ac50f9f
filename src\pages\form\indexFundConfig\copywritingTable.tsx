import React, { useState, useEffect } from 'react';
import { Form, Table, Button, Modal, Input, message } from 'antd';
import { ColumnProps } from 'antd/lib/table';

import api from 'api';
import { ICopywritingData } from './types'
import styles from './index.less'

const { fetchIndexBuyingCopywriting, postIndexBuyingCopywriting } = api;

const CopyWritingTable = (props) => {
  const { getFieldDecorator, resetFields, validateFields } = props.form

  const [sectorData, setSectorData] = useState([
    { key: 'inside', type: 'ETF', tag: '', text: '' },
    { key: 'outside', type: '场外基金', tag: '', text: '' },
  ]);
  const [showForm, setShowForm] = useState(false);
  const [isEditingInside, setIsEditingInside] = useState(false)

  // 发送请求初始化sectorData;
  useEffect(() => {
    fetchIndexBuyingCopywriting()
      .then(res => {
        if (res.code !== '0000') {
          throw new Error(res?.message);
        }
        const data = JSON.parse(res.data)
        setSectorData([
          {key: 'inside', type: 'ETF', tag: data[0].tag, text: data[0].text},
          {key: 'outside', type: '场外基金', tag: data[1].tag, text: JSON.parse(res.data)[1].text}
        ])
      })
      .catch(err => {
        console.log(err.message);
        message.error(err.message);
      });
  }, []);

  const handleEdit = (text, record: ICopywritingData) => {
    if (record.key === 'inside') {
      setIsEditingInside(true)
    } else if (record.key === 'outside') {
      setIsEditingInside(false)
    }
    setShowForm(true);
  }
  const handleCancel = () => {
    setShowForm(false);
    resetFields()
  };
  const handleSubmit = (e) => {
    e.preventDefault()
    validateFields((err, values) => {
      if(!err) {
        postIndexBuyingCopywriting({
          value: JSON.stringify(
            isEditingInside ? [{ ...values, type: 'inside', key: 'inside' }, { ...sectorData[1], type: 'outside', key: 'outside' }] 
            : [{ ...sectorData[0], type: 'inside', key: 'inside' }, { ...values, type: 'outside', key: 'outside' }]
          )
        })
        .then(res => {
          if (res.code !== '0000') {
            throw new Error(res?.message)
          }
          setShowForm(false)
          resetFields()
          if(isEditingInside){
            setSectorData([{...values, key: 'inside', type: 'ETF'},{...sectorData[1]}])
          } else {
            setSectorData([{...sectorData[0]}, {...values, key: 'outside', type: '场外基金'}])
          }
          message.success('修改成功')
        })
        .catch(err => {
          message.error(err.message)
        })
      }
    })
  };

  const columns: ColumnProps<ICopywritingData>[] = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '标签',
      dataIndex: 'tag',
      key: 'tag',
    },
    {
      title: '文案',
      dataIndex: 'text',
      key: 'text',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      render: (text, record: ICopywritingData) => {
        return (
          <a
            onClick={() => {handleEdit(text, record)}}
          >
            编辑
          </a>
        );
      },
    },
  ];

  return (<>
    <Table columns={columns} dataSource={sectorData} bordered pagination={false}></Table>
    <Modal visible={showForm} 
           title={`请输入${ isEditingInside ? 'ETF' : '场外基金' }的标签和文案`}
           onCancel={handleCancel} footer={null}
    >
      <Form onSubmit={handleSubmit}>
        <Form.Item>
          <span>标签</span>
          {
            getFieldDecorator('tag')(
              <Input placeholder='最多输入5个字符' maxLength={5}/>
            )
          }
        </Form.Item>
        <Form.Item>
          <span>文案</span>
          {getFieldDecorator('text')(
            <Input placeholder='最多输入10个字符' maxLength={10}/>
          )}
        </Form.Item>
        <Form.Item>
          <div>
            <Button type='primary' htmlType='submit' className={styles['submit-btn']}>
              确定
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  </>)
};

const WrappedCopyWritingTable = Form.create({ name: 'copywriting_submit'})(CopyWritingTable)
export default WrappedCopyWritingTable;
