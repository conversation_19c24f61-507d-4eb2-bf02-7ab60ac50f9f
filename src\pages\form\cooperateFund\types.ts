export interface ContentProps {
  fundCode: string,
  subMarket: string,
  simpleFundName: string,
  expSecurityName: string,
  jumpCode: string,
  nickNameList: string[],
  isCooperate: CooperateStatus,
}

export enum CooperateStatus {
  Yes = '1',
  No = '0'
}

export enum EditContentType {
  Add,
  Edit
}

export type columnsProps = {
  title: string,
  dataIndex?: string,
  key?: string,
  width?: string,
  render?: Function
}

export type ReducerState = {
  search: string;
  subMarket: string;
  isCooperate: string;
}

export type ReducerAction = {
  type: string;
  payload?: string;
}

export type SearchReducer = React.Reducer<Partial<ReducerState>, ReducerAction>