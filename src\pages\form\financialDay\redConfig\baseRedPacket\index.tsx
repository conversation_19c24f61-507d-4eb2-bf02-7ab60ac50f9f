import React, { useState } from 'react';
import {But<PERSON>, Popconfirm, Drawer, Table, Tag } from 'antd';
import WrappedInvestDrawer from './investDrawer';

export interface iBaseData {
  date: string,
  activityId: string,
  redPaperId: string,
  threshold: string,
  minusMoney: string,
  status: string,
  key?: string;
}

interface iProps {
  handleData: (data: iBaseData[]) => void;
  baseData: iBaseData[];
  isModify: boolean;
  handleModify: (flag: boolean) => void;
}
function PlanInvestment({handleData, baseData, isModify, handleModify}: iProps) {
    const [edit, setEdit] = useState(0); // 1 新增 2 编辑
    const [currentData, setCurrentData] = useState<iBaseData>({
      date: '',
      activityId: '',
      threshold: '',
      redPaperId: '',
      minusMoney: '',
      status: ''
    });

    const columns = [
      {
        title: '日期',
        dataIndex: 'date',
        key: 'date',
      },
      {
        title: '活动ID',
        dataIndex: 'activityId',
        key: 'activityId',
      },
      {
        title: '红包ID',
        dataIndex: 'redPaperId',
        key: 'redPaperId',
      },
      {
        title: '门槛',
        dataIndex: 'threshold',
        key: 'threshold',
      },
      {
        title: '满减金额',
        dataIndex: 'minusMoney',
        key: 'minusMoney',
      },
      {
        title: '红包状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => {
          return (
          <span>{status === '2' ? '已抢完' : status === '0' ? '可领取' : '--'}</span>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text: any, record: iBaseData) => {
          let num: any = record?.key;
          return (
            <>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => handleEdit(record)}>编辑</Button>
              <Popconfirm title="确定要删除吗?" onConfirm={() => handleDelete(num)}>
                <Button type="danger">删除</Button>
              </Popconfirm>
            </>
          )
        }
      },
    ];

    const handleEdit = (val: iBaseData) => {
      setCurrentData(val);
      setEdit(2);
    }
    const handleDelete = (num: string) => {
      if (!isModify) handleModify(true);
      let arr = baseData && JSON.parse(JSON.stringify(baseData));
      arr.splice(Number(num), 1);
      arr?.forEach((item: iBaseData, index: number) => {
        item.key = index.toString();
      })
      handleData(arr);
    }
    const modifyPlanData = (data: iBaseData) => {
      if (!isModify) handleModify(true);
      let arr = baseData && JSON.parse(JSON.stringify(baseData));
      if (data?.key) {
        let index: any = data.key;
        arr[index] = data;
      } else {
        arr.push(data);
      }
      arr?.forEach((item: iBaseData, index: number) => {
        item.key = index.toString();
      })
      handleData(arr);
    }
    const onEditClose = () => {
      setEdit(0);
    }
    const newAdd = () => {
      setCurrentData({
        date: '',
        activityId: '',
        redPaperId: '',
        threshold: '10000',
        minusMoney: '20',
        status: '0'
      });
      setEdit(1);
    }
    return (
      <div style={{marginTop: 40}}>
        <h1 className="g-fs28 f-bold">基础红包配置</h1>
        <Table columns={columns} dataSource={baseData}></Table>
        <div style={{marginTop: 20}}>
          <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
        </div>
        <Drawer
          width={1000}
          title="新增红包配置"
          placement="right"
          closable
          onClose={onEditClose}
          visible={Boolean(edit)}
          >
          { Boolean(edit) && <WrappedInvestDrawer currentData={currentData} onEditClose={onEditClose} handleData={modifyPlanData}></WrappedInvestDrawer> }
        </Drawer>
      </div>
    )
}

export default React.memo(PlanInvestment);