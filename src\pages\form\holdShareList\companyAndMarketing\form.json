{"schema": {"type": "object", "properties": {"companyData": {"type": "object", "properties": {"label": {"title": "展示标签", "ui:options": {"placeholder": "最多6个汉字"}, "type": "string", "ui:disabled": "{{formData.config.disabledForm === true}}", "ui:width": "60%"}, "title": {"title": "标题", "ui:options": {"placeholder": "超过20个字的部分可能会被省略"}, "type": "string", "ui:disabled": "{{formData.config.disabledForm === true}}", "ui:width": "60%"}, "url": {"title": "跳转链接", "ui:options": {"placeholder": "必须是http或者https链接"}, "pattern": "^http(|s)?://[^\n ，]*$", "message": {"pattern": "必须是http或者https链接"}, "type": "string", "ui:disabled": "{{formData.config.disabledForm === true}}", "ui:width": "100%"}, "startTime": {"title": "起始时间", "type": "string", "format": "dateTime", "ui:disabled": "{{formData.config.disabledForm === true}}", "ui:width": "50%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:disabled": "{{formData.config.disabledForm === true}}", "ui:width": "50%"}, "type": {"title": "推送至", "type": "string", "enum": ["0", "1"], "enumNames": ["基金", "财富有伴"], "ui:disabled": "{{formData.config.disabledForm === true}}", "ui:width": "60%"}, "dst": {"title": "基金代码", "type": "string", "ui:options": {"placeholder": "请填写基金代码，多个基金用英文逗号区分"}, "pattern": "^[0-9,]+$", "message": {"pattern": "请填写基金代码，多个基金用英文逗号区分"}, "ui:width": "80%", "ui:disabled": "{{formData.config.disabledForm === true || rootValue.type !== '0'}}", "ui:hidden": "{{rootValue.type !== '0'}}"}, "push": {"title": "是否push", "type": "string", "enum": ["1", "0"], "ui:readonly": "{{rootValue.type !== '0' || formData.config.disabledForm === true}}", "enumNames": ["是", "否"], "ui:width": "60%"}, "pushTime": {"title": "push时间", "type": "string", "ui:width": "60%", "format": "dateTime", "ui:disabled": "{{formData.config.disabledForm === true || rootValue.push !== '1'}}", "ui:hidden": "{{rootValue.push !== '1'}}"}}, "required": ["label", "title", "url", "startTime", "endTime", "type", "dst", "push", "pushTime"]}, "config": {"disabledForm": {"ui:hidden": true}}}}, "formData": {"companyData": {"label": "", "title": "", "url": "", "startTime": "", "endTime": "", "push": "", "type": ""}, "config": {"disabledForm": false}}}