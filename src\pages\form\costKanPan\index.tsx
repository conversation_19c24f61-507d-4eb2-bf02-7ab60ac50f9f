import FORM_JSON from './form.json';
import <PERSON>act<PERSON><PERSON> from 'react-dom';
import React, { useState, useEffect } from 'react';
import {<PERSON><PERSON>, Card, Row, message, Popconfirm, Select, Collapse} from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';

export default function () {
    const { fetchCostKanPanSave, postCostKanPanSave, postCostKanPan } = api;

    const [init, setInit] = useState(false);
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);

    useEffect( () => {
        getItem();
    }, [init]);

    const getItem = () => {
        fetchCostKanPanSave().then((res: any) => {
            let _data = FORM_JSON.formData
            try {
                res = JSON.parse(res.data);
                if (res) {
                    _data = res.formData
                }
            } catch (e) {
                console.warn(e)
            }
            setInit(true);
            setData(_data);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }

    const save = () => {
        postCostKanPanSave ({
            value: JSON.stringify({formData})
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！')
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    const onSubmit = () => {
        if (valid.length > 0) {
            console.log(formData)
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }

        let _formData: any = formData;
        postCostKanPanSave ({
            value: JSON.stringify({formData: _formData})
        }).then( (res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('保存成功！');
                    _formData = _formData.data;
                    for(let i: number = 0, _length = _formData.length; i < _length; i++) {
                        postCostKanPan ({
                            value: JSON.stringify({formData: _formData[i]})
                        },  _formData[i].thscode).then( (res: any) => {
                            try {
                                if (res.code !== '0000') {
                                    message.error(res.message);
                                } else {
                                    message.success('发布成功！')
                                }
                            } catch (e) {
                                message.error(e.message);
                            }
                        })
                    }
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }

    return (
        <article> 
            {
                init ?
                <article>
                    <FormRender
                        propsSchema={FORM_JSON.propsSchema}
                        uiSchema={FORM_JSON.uiSchema}
                        onValidate={setValid}
                        formData={formData}
                        onChange={setData}
                        displayType="row"
                        showDescIcon={true}
                        column={2}
                    />
                    <Button
                        type="primary" 
                        onClick={save}
                        style={{marginRight: '300px'}}
                    >
                        保存
                    </Button>


                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交么'}
                        onConfirm={onSubmit}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button
                            type="danger" 
                        >
                            提交修改
                        </Button>
                    </Popconfirm>
                </article>
                :
                ''
            }
        </article>
    )
}