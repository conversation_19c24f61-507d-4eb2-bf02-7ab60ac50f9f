import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import styles from './index.less';
import { Button, Form, message, Select, Table } from 'antd';
import {
  FormToProtoList,
  ProtoType,
  STRATEGYTable,
  TableDataConfigAdapter,
  TableToConfig,
  TableToConfigSingle,
  TATable,
  TGProtoConfigKey,
} from './utiles';
import api from 'api';
import TGSTConfig from './components/tgSTConfig';
import TGProtoConfig from './components/tgProtoConfig';
import TGStrategyConfig from './components/tgStrategyConfig';
import TGStrategyAlterConfig from './components/tgStrategyAlterConfig';
import TgAppropriatenessRuleConfig from './components/tgAppropriatenessRuleConfig';
import TgInfoConfig from './components/tgCopyInfoConfig';
import TgPerformanceConfig from './components/tgPerformanceConfig';
const { Option } = Select;
const { postTGConfigList, getTGConfig, postTGConfig } = api;
export interface IProps {}
const TGProtocol: FC<IProps> = () => {
  const [dataSource, setDataSource] = useState<TATable[]>([]);
  const [taEditShow, setTaEditShow] = useState(false);
  const [taSTEditShow, setTaSTEditShow] = useState(false);
  // 适当性规则
  const [taArEditShow, setTaArEditShow] = useState(false);
  // logo及文案
  const [copyInfoEditShow, setCopyInfoEditShow] = useState(false);
  const [performanceEditShow, setPerformanceEditShow] = useState(false);
  const [strategyEditShow, setStrategyEditShow] = useState(false);
  const [strategyAlterEditShow, setStrategyAlterEditShow] = useState(false);
  const [currentTA, setCurrentTA] = useState<TATable | null>(null);
  const [currentStrategy, setCurrentStrategy] = useState<STRATEGYTable | null>(null);
  const [selectTa,setSelectTa]=useState(undefined);
  const [loading,setLoading]=useState(false)
  const taOptions=dataSource?.map(ta=>{
    return {label:ta.taName,value:ta.taCode}
  })||[]
  const initData = () => {
    setLoading(true)
    Promise.all([postTGConfigList(), getTGConfig({ key: TGProtoConfigKey })])
      .then(([tgListRes, tgConfigRes]) => {
        if (!tgListRes || !tgConfigRes) {
          throw new Error('获取数据失败');
        }
        if (tgListRes.code !== '0000') {
          throw new Error(tgListRes.message);
        }
        if (tgConfigRes.code !== '0000') {
          throw new Error(tgConfigRes.message);
        }
        const models = tgListRes.data;
        const configMap = tgConfigRes.data;
        const tableData = TableDataConfigAdapter(models, configMap);
        setDataSource(tableData);
      })
      .catch(e => {
        console.log(e.message);
        message.error(e.message);
      }).finally(()=>{
        setLoading(false)
      });
  };
  const onSave = (data: TATable) => {
    const config = TableToConfigSingle(data);
    console.log(config);
    setLoading(true);
    postTGConfig({ key: TGProtoConfigKey, propName: config.taCode, value: JSON.stringify(config) }).then(res=>{
      if(res.code!=='0000'){
        throw new Error(res.message);
      }
      message.success('提交配置成功')
    }).catch(e=>{
      console.log(e.message);
        message.error(e.message);
    }).finally(()=>{
      setLoading(false);
    });
  };
  useEffect(() => {
    initData();
  }, []);
  const appropriatenessRuleMap = {
    strong: '强',
    weak: '弱'
  }
  const columns = [
    { title: 'taCode', dataIndex: 'taCode', key: 'taCode' },
    { title: '投顾机构', dataIndex: 'taName', key: 'taName' },
    {
      title: '适当性匹配规则',
      dataIndex: 'appropriatenessRule',
      key: 'appropriatenessRule',
      render: (text) =>  {
        return (appropriatenessRuleMap[text] || '');
      }
    },
    {
      title: '操作',
      key: 'operate',
      render: record => {
        return (
          <>
            <a className={styles['protocol-operator']} onClick={() => handleTAEdit(record)}>
              投顾协议
            </a>
            |
            <a className={styles['protocol-operator']} onClick={() => handleTASTEdit(record)}>
              小目标协议
            </a>
            |
            <a className={styles['protocol-operator']} onClick={() => handleTAArEdit(record)}>
              适当性匹配规则
            </a>
            |
            <a className={styles['protocol-operator']} onClick={() => handleTAInfoEdit(record)}>
              logo及文案
            </a>
          </>
        );
      },
    },
  ];
  const expandedRowRender = record => {
    const expandColumns = [
      { title: '投顾策略id', dataIndex: 'consultTacticsId', key: 'consultTacticsId' },
      { title: '策略机构', dataIndex: 'taName', key: 'taName' },
      { title: '策略名称', dataIndex: 'investConsultTacticsName', key: 'investConsultTacticsName' },
      {
        title: '操作',
        key: 'operate',
        render: record => {
          return (
            <>
              <a
                className={styles['protocol-operator']}
                onClick={() => handleTAStrategyEdit(record)}
              >
                策略协议
              </a>
              |
              <a
                className={styles['protocol-operator']}
                onClick={() => handleTAStrategyAlterEdit(record)}
              >
                基金备选库
              </a>
              |
              <a
                className={styles['protocol-operator']}
                onClick={() => handlePerformanceEdit(record)}
              >
                业绩表现
              </a>
            </>
          );
        },
      },
    ];
    return (
      <Table
        rowKey={'consultTacticsId'}
        columns={expandColumns}
        dataSource={record.strategyList}
        pagination={false}
      />
    );
  };
  const handleTAEdit = record => {
    setTaEditShow(true);
    setCurrentTA(record);
  };
  const handleTASave = data => {
    const protos = FormToProtoList(data);
    if (!currentTA.share) {
      currentTA.share = {
        protos,
      };
    } else {
      currentTA.share.protos = protos;
    }
    const taIndex=dataSource.findIndex(ta=>ta.taCode===currentTA?.taCode);
    const newTa={ ...currentTA };
     dataSource[taIndex]=newTa;
    const newData = [...dataSource];
    setDataSource(newData);
    handleTAClose();
    onSave(newTa);
  };
  const handleTAClose = () => {
    setTaEditShow(false);
    setCurrentTA(null);
  };
  // 适当性规则-start
  const handleTAArEdit = record => {
    setTaArEditShow(true);
    setCurrentTA(record);
  }
  const handleTAArSave = data => {
    const taIndex=dataSource.findIndex(ta=>ta.taCode===currentTA?.taCode);
    const newTa = {
      ...currentTA,
      appropriatenessRule: data.appropriatenessRule,
    };
     dataSource[taIndex]=newTa;
    const newData = [...dataSource];
    setDataSource(newData);
    handleTAArClose();
    onSave(newTa);
  }
  const handleTAArClose = () => {
    setTaArEditShow(false);
    setCurrentTA(null);
  };
  // 适当性规则-end
  // 配置logo及文案-start
  const handleTAInfoEdit = record => {
    setCopyInfoEditShow(true);
    setCurrentTA(record);
  }
  const handleInfoSave = data => {
    const taIndex=dataSource.findIndex(ta=>ta.taCode===currentTA?.taCode);
    const newTa = {
      ...currentTA,
      logoCopyInfo: data
    };
     dataSource[taIndex]=newTa;
    const newData = [...dataSource];
    setDataSource(newData);
    handleInfoClose();
    onSave(newTa);
  }
  const handleInfoClose = () => {
    setCopyInfoEditShow(false);
    setCurrentTA(null);
  };
  // 配置logo及文案-end
  const handleTASTEdit = record => {
    setTaSTEditShow(true);
    setCurrentTA(record);
  };
  const handleTASTSave = (data) => {
    const stProto = FormToProtoList(data);
    if (!currentTA.share) {
      currentTA.share = {
        stProto,
      };
    } else {
      currentTA.share.stProto = stProto;
    }
    const taIndex=dataSource.findIndex(ta=>ta.taCode===currentTA?.taCode);
    const newTa={ ...currentTA };
    dataSource[taIndex]=newTa;
    const newData = [...dataSource];
    setDataSource(newData);
    handleTASTClose();
    onSave(newTa);
  };
  const handleTASTClose = () => {
    setTaSTEditShow(false);
    setCurrentTA(null);
  };
  const handleTAStrategyEdit = record => {
    setStrategyEditShow(true);
    setCurrentStrategy(record);
  };
  const handleTAStrategySave = (data) => {
    if(!currentStrategy){
      return;
    }
    const ta=dataSource.find(item=>item.taCode===currentStrategy.taCode);
    if(!ta){
      return;
    }
    const protos=data.list.map(item=>{
      return {
        name:item.protoName,
        link:item.protoLink,
        type:ProtoType.STRATEGY
      }
    })
    if(!currentStrategy.strategy){
      currentStrategy.strategy={
        investConsultTacticsName:currentStrategy.investConsultTacticsName,
        consultTacticsId:currentStrategy.consultTacticsId,
        protos,
        fundAlterPool:null
      }
    }else{
      currentStrategy.strategy.protos=protos;
    }
    const strategyIndex=ta.strategyList?.findIndex(tas=>tas.consultTacticsId===currentStrategy.consultTacticsId);
    ta.strategyList[strategyIndex]={...currentStrategy};
    const newData = [...dataSource];
    setDataSource(newData);
    handleTAStrategyClose();
    onSave({...ta})
  };
  const handleTAStrategyClose = () => {
    setStrategyEditShow(false);
    setCurrentStrategy(null);
  };

  const handleTAStrategyAlterEdit = record => {
    setStrategyAlterEditShow(true);
    setCurrentStrategy(record);
  };
  const handleTAStrategyAlterSave = (data) => {
    if(!currentStrategy){
      return;
    }
    const ta=dataSource.find(item=>item.taCode===currentStrategy.taCode);
    if(!ta){
      return;
    }
    // const protos=data.list.map(item=>{
    //   return {
    //     name:item.protoName,
    //     link:item.protoLink,
    //     type:ProtoType.STRATEGY
    //   }
    // })
    const proto={
      name:data.protoName,
      link:data.protoLink,
      type:ProtoType.ALTER
    }
    if(!currentStrategy.strategy){
      currentStrategy.strategy={
        investConsultTacticsName:currentStrategy.investConsultTacticsName,
        consultTacticsId:currentStrategy.consultTacticsId,
        protos:[],
        fundAlterPool:proto
      }
    }else{
      currentStrategy.strategy.fundAlterPool=proto;
    }
    const strategyIndex=ta.strategyList?.findIndex(tas=>tas.consultTacticsId===currentStrategy.consultTacticsId);
    ta.strategyList[strategyIndex]={...currentStrategy};
    const newData = [...dataSource];
    setDataSource(newData);
    handleTAStrategyAlterClose();
    onSave({...ta})
    
  };
  const handleTAStrategyAlterClose = () => {
    setStrategyAlterEditShow(false);
    setCurrentStrategy(null);
  };
  /** 业绩表现 */
  const handlePerformanceEdit = record => {
    setPerformanceEditShow(true);
    setCurrentStrategy(record);
  }
  const handlePerformanceSave = data => {
    if(!currentStrategy){
      return;
    }
    const ta=dataSource.find(item=>item.taCode===currentStrategy.taCode);
    if(!ta){
      return;
    }
    if(!currentStrategy.strategy){
      currentStrategy.strategy={
        investConsultTacticsName:currentStrategy.investConsultTacticsName,
        consultTacticsId:currentStrategy.consultTacticsId,
        protos:[],
        showPerformance: data.showPerformance
      }
    }else{
      currentStrategy.strategy.showPerformance = data.showPerformance;
    }
    const strategyIndex=ta.strategyList?.findIndex(tas=>tas.consultTacticsId===currentStrategy.consultTacticsId);
    ta.strategyList[strategyIndex]={...currentStrategy};
    const newData = [...dataSource];
    setDataSource(newData);
    handlePerformanceClose();
    onSave({...ta})
  }
  const handlePerformanceClose = () => {
    setPerformanceEditShow(false);
    setCurrentStrategy(null);
  }
  /** 业绩表现end */
  const getFilterDataSource=()=>{
    if(!selectTa){
      return dataSource
    }
    return dataSource.filter((ta)=>ta.taCode===selectTa)
  }
  return (
    <section>
      <h2>投顾策略协议配置</h2>
      <div>
        <span>投顾机构</span>
        <Select  allowClear placeholder={'全部'} style={{ width: '200px', marginLeft: '12px' }} value={selectTa} onChange={v=>setSelectTa(v)}>
          {taOptions.map(opt=>{
            return <Option key={opt.value} value={opt.value}>{opt.label}</Option>
          })}
        </Select>
      </div>
      <Table
        loading={loading}
        rowKey={'taCode'}
        dataSource={getFilterDataSource()}
        columns={columns}
        expandedRowRender={expandedRowRender}
        pagination={false}
       
      />
      <TGSTConfig
        data={currentTA}
        visible={taSTEditShow}
        onSubmit={handleTASTSave}
        onClose={handleTASTClose}
      />
      {/* 适当性匹配 */}
      <TgAppropriatenessRuleConfig
        data={currentTA}
        visible={taArEditShow}
        onSubmit={handleTAArSave}
        onClose={handleTAArClose}
      />
      {/* 配置logo及文案 */}
      <TgInfoConfig
        data={currentTA}
        visible={copyInfoEditShow}
        onSubmit={handleInfoSave}
        onClose={handleInfoClose}
      />
      <TGProtoConfig
        data={currentTA}
        visible={taEditShow}
        onSubmit={handleTASave}
        onClose={handleTAClose}
      />
      <TGStrategyConfig
        visible={strategyEditShow}
        data={currentStrategy}
        onSubmit={handleTAStrategySave}
        onClose={handleTAStrategyClose}
      />
      <TGStrategyAlterConfig
        visible={strategyAlterEditShow}
        data={currentStrategy}
        onSubmit={handleTAStrategyAlterSave}
        onClose={handleTAStrategyAlterClose}
      />
      {/* 业绩表现是否展示 */}
      <TgPerformanceConfig
        visible={performanceEditShow}
        data={currentStrategy}
        onSubmit={handlePerformanceSave}
        onClose={handlePerformanceClose}
      />
    </section>
  );
};
export default TGProtocol;
