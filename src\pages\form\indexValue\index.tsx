import React, { useEffect, useState } from 'react';
import { <PERSON>, But<PERSON>, Popconfirm, message } from 'antd';
import Recommend from './recommend'
import Rank from './rank'
import api from 'api';
import initData from './data'



const { fetchMarketEvaluate, postMarketEvaluate } = api;


export default function () {
    const [rankData, setRank] = useState([]) //排行数据
    const [recommendData, setRecommend] = useState([]) //推荐数据
    const [init, setInit] = useState(false);
    useEffect(() => {

        fetchMarketEvaluate().then((res: any) => {
            try {
                let data = JSON.parse(res.data);
                if (data.notFirst) {
                    setRank(data.rank)
                    setRecommend(data.recommend)
                } else {
                    console.log(initData)
                    let _data: any = [...initData]
                    setRank(_data)
                    setRecommend([])
                    
                }

            } catch (e) {
                console.warn(e)
            }
            setInit(true)
        })
    }, [])
    function commit() {
        let rank = rankData
        let recommend = recommendData
        let _data = { rank, recommend, notFirst:true}
        console.log('_data', _data)
        postMarketEvaluate({
            value: JSON.stringify(_data)
        }).then((res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('提交成功！');
                }
            } catch (e) {
                message.error(e.message);
            }
        })

    }
    //排行触发保存的回调
    function saveRank(list: any) {
        console.log('rank', list)
        setRank(list)
        // commit(list, recommendData)
    }

    //推荐触发保存的回调
    function saveRecommend(list: any) {
        console.log('recommend', list)
        setRecommend(list)
        // commit(rankData, list)
    }
    if (!init) return '加载中'
    return <div>
         <Button onClick={commit} type="primary" style={{ margin: 16 }}>
            保存
        </Button>
        <Rank saveRank={saveRank} data={rankData}></Rank>
        <Recommend saveRecommend={saveRecommend} data={recommendData} rankData={rankData}></Recommend>
        {/* <Testt></Testt> */}
    </div>

}