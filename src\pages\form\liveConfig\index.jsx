 /*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React, { Fragment } from 'react';
import {Button, Select, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import FormRender from 'form-render/lib/antd';
import schema from './form.json';

const { fetchTemporaryLiveConfig, postTemporaryLiveConfig, fetchLiveConfig, postLiveConfig } = api;

@autobind
class liveConfig extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            formData: schema.formData || {},
            chosenItem: 0
        };
    }

    onChange = formData => {
        this.setState({ formData }, () => {console.log(this.state)});
    }
    
    dataInit () {
        this.setState({formData: schema.formData});
        fetchTemporaryLiveConfig().then(data => {
            if (data.data) {
                data = JSON.parse(data.data);
                // data.radioList[0].liveInfo.managerName = ''
                // data.radioList[0].liveInfo.subTitle = ''
                // data.radioList[1].liveInfo.managerName = ''
                // data.radioList[1].liveInfo.subTitle = ''
                // data.radioList[2].liveInfo.managerName = ''
                // data.radioList[2].liveInfo.subTitle = ''
                // data.radioList[3].liveInfo.managerName = ''
                // data.radioList[3].liveInfo.subTitle = ''
                // data.videoList[0].liveInfo.managerName = ''
                // data.videoList[0].liveInfo.subTitle = ''
                this.setState({formData: data});
            }
        })
    }
    
    saveForm () {
        let _formData = this.state.formData;
        postTemporaryLiveConfig ({
            value: JSON.stringify(_formData)
        }).then(data => {
            if (data) {
                message.success('保存成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
  }

    updateForm () {
        let _formData = this.state.formData;
        let _list = [..._formData.radioList, ..._formData.videoList]
        for(let i = 0, length = _list.length; i < length; i++) {
            for(let j = 0, length = _list.length; j < length; j++) {
                if (_list[i].statId === _list[j].statId && i !== j) {
                    message.error('埋点输入重复请检查')
                    return
                }
            }
        }
        postTemporaryLiveConfig ({
            value: JSON.stringify(_formData)
        }).then(data => {
            if (data) {
                message.success('保存成功');
                postLiveConfig ({
                    value: JSON.stringify(_formData)
                }).then(data => {
                    if (data) {
                        message.success('修改成功');
                        window.location.reload();
                    } else {
                        message.error(data.message);
                    }
                })
            } else {
                message.error(data.message);
            }
        })
    }

    /**
     * 添加直播到回播
     */
    addVideoList(index) {
        let item = JSON.parse(JSON.stringify(this.state.formData.radioList[index]))
        delete item.beforeStartStr
        delete item.numberObj
        delete item.isJumpSdk
        delete item.sid
        delete item.fid
        let _formData = JSON.parse(JSON.stringify(this.state.formData))
        _formData.radioList.splice(index, 1)
        _formData.videoList.push(item)
        this.setState({formData: _formData})
    }

    componentDidMount () {
        this.dataInit();
        // this.newBoyDataInit();
    }

    render () {
        return (
            <section className="codeInterface" style={{ width: '800px' }}>

                <FormRender
                    propsSchema={schema.propsSchema}
                    formData={this.state.formData}
                    onChange={this.onChange}
                    displayType="row"
                    showDescIcon={true}
                />

                <div className="u-l-middle g-mb20">
                    <span className="u-block_il g-mr20">请选择想要下拉的直播</span>
                    <Select style={{width: 300, marginLeft: 20, marginRight: 20}} 
                        onChange={(value) => {
                            this.setState({chosenItem: value})
                            console.log(value)
                    }}>
                        {
                            this.state.formData.radioList.map((item, index) => {
                                return (
                                    <Select.Option value={index} key={index} >
                                        {item.title}
                                    </Select.Option>
                                )
                            })
                        }
                    </Select>
                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要添加么'}
                        onConfirm={() => {this.addVideoList(this.state.chosenItem)}}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button type="primary">
                            添加直播到回播
                        </Button>
                    </Popconfirm>
                </div>

                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={this.updateForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button type="danger">
                        提交修改
                    </Button>
                </Popconfirm>

                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={this.saveForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="primary" 
                        style={{ marginLeft: '600px' }}
                    >
                        保存
                    </Button>
                </Popconfirm>

            </section>
        )
    }
}

export default liveConfig;