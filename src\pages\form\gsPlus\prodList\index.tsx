import React, { useState, useEffect } from 'react';
import api from 'api';
import { Button, message, Collapse, Divider, PageHeader, Input, Popconfirm, Spin } from 'antd';

import ProdList from './prodList';
import TagList from './tagList';
import TabList from './tabList';

import ShowProdList from './showProdList';

const { Panel } = Collapse;

interface PRODITEM {
  showType: string;
  fundCode: string;
  fundName: string;
  id?: string;
  recommendText: string;
  tags?: string[];
  tab?: string[];
  jumpUrl?: string;
}

export default function() {
  const { fetchGsPlusProdList, postGsPlusProdList } = api;

  const [prodList, set_prodList] = useState<PRODITEM[]>([]); // 全部产品列表
  const [prodRecommendText, set_prodRecommendText] = useState(''); // 智选文案
  const [prodTag, set_prodTag] = useState<string[]>([]); // 全部产品标签
  const [prodShowBox, set_prodShowBox] = useState<any[]>([]); // 产品展示区
  const [tabData, set_tabData] = useState<any>({});

  const [init, set_init] = useState(false);

  /**
   * 获取配置信息
   */
  const fetchData = () => {
    fetchGsPlusProdList()
      .then((res: any) => {
        try {
          res = JSON.parse(res.data);
          if (res) {
            let _prodList = res.prodList,
              _prodTag = res.prodTag,
              _prodShowBox = res.prodShowBox,
              _prodRecommendText = res.prodRecommendText;
            set_prodList(_prodList);
            set_prodTag(_prodTag);
            set_prodShowBox(_prodShowBox);
            set_prodRecommendText(_prodRecommendText);
          }
        } catch (e) {
          console.warn(e);
        }
        set_init(true);
      })
      .catch((e: Error) => {
        message.error(e.message);
      });
  };

  /**
   * 上传配置信息
   */
  const postData = () => {
    if (!prodRecommendText) {
      message.error('请填写智选文案');
      return;
    }
    if (!tabData.tab1Name || !tabData.tab1Desc || !tabData.tab2Name || !tabData.tab2Desc) {
      message.error('请填写系列文案');
      return;
    }
    if (!prodList.length) {
      message.error('全部产品为空');
      return;
    }
    if (prodShowBox[0] && !prodShowBox[0].prod.length) {
      message.error('系列1产品为空');
      return;
    }
    if (prodShowBox[1] && !prodShowBox[1].prod.length) {
      message.error('系列2产品为空');
      return;
    }
    let _prodShowBox = [...prodShowBox];
    if (_prodShowBox[0]) {
      _prodShowBox[0].tabName = tabData.tab1Name;
      _prodShowBox[0].tabDesc = tabData.tab1Desc;
    }
    if (_prodShowBox[1]) {
      _prodShowBox[1].tabName = tabData.tab2Name;
      _prodShowBox[1].tabDesc = tabData.tab2Desc;
    }
    let _postData = {
      prodShowBox: _prodShowBox,
      prodTag,
      prodRecommendText,
      prodList,
    };
    postGsPlusProdList({
      value: JSON.stringify(_postData),
    }).then((res: any) => {
      try {
        if (res.code !== '0000') {
          message.error(res.message);
        } else {
          message.success('发布成功！');
          setTimeout(() => {
            location.reload();
          }, 500);
        }
      } catch (e) {
        message.error(e.message);
      }
    });
  };

  /**
   * 新增或删除标签 - 标签配置
   * @param tag
   * @param index
   * @param type
   */
  const handleTag = (type: string, tag: string, index: number) => {
    let _prodTag = [...prodTag];
    if (type === 'add') {
      _prodTag.push(tag);
    } else if (type === 'delete') {
      _prodTag.splice(index, 1);
      // 要把产品里这个tag全删除
      let _prodList = [...prodList];
      for (let i = 0; i < _prodList.length; i++) {
        if (_prodList[i].tags) {
          let _i = _prodList[i].tags?.indexOf(tag);
          if (_i !== undefined && _i !== -1) {
            _prodList[i].tags?.splice(_i, 1);
          }
        }
      }
      set_prodList(_prodList);
    }
    set_prodTag(_prodTag);
  };

  /**
   * 删除产品 - 全部产品列表
   * @param index
   */
  const deleteProd = (index: number) => {
    let _prodList = [...prodList];
    _prodList.splice(index, 1);
    set_prodList(_prodList);
  };

  /**
   * 完成编辑产品 - 全部产品列表
   * @param data
   */
  const saveProdData = (data: any, index: number) => {
    let _prodList = [...prodList];
    _prodList[index] = data;
    set_prodList(_prodList);
  };

  /**
   * 新增产品 - 全部产品列表
   * @param data
   */
  const addProdData = (data: any) => {
    let _prodList = [...prodList];
    for (let i = 0; i < _prodList.length; i++) {
      if (_prodList[i].fundCode === data.fundCode) {
        message.error('基金代码重复，添加失败');
        return;
      }
    }
    _prodList.push(data);
    set_prodList(_prodList);
  };

  /**
   * 改变产品状态 - 全部产品列表
   * @param index
   */
  const changeProdStatus = (index: number) => {
    let _prodList = [...prodList];
    _prodList[index].showType = _prodList[index].showType === '0' ? '1' : '0';
    set_prodList(_prodList);
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (prodShowBox.length === 2) {
      let _tabData = {
        tab1Name: prodShowBox[0].tabName,
        tab1Desc: prodShowBox[0].tabDesc,
        tab2Name: prodShowBox[1].tabName,
        tab2Desc: prodShowBox[1].tabDesc,
      };
      set_tabData(_tabData);
    }
  }, [prodShowBox]);

  return (
    <Spin size="large" spinning={!init}>
      <article>
        <PageHeader title="产品展示区配置" />
        <TabList onChange={set_tabData} tabData={tabData} />
        <ShowProdList prodList={prodList} prodShowBox={prodShowBox} saveData={set_prodShowBox} />
        <Divider />
        <PageHeader title="全部产品列表配置" />
        <div style={{ marginBottom: 10 }}>
          <span>
            <b style={{ color: '#fe5d4e' }}>* </b> 智选文案
          </span>
          <Input
            style={{ width: 300, marginLeft: 10 }}
            value={prodRecommendText}
            onChange={e => {
              set_prodRecommendText(e.target.value);
            }}
          />
        </div>
        <Collapse defaultActiveKey="tagList">
          <Panel header="标签配置(上限8个)" key="tagList">
            <TagList
              prodTag={prodTag}
              prodList={prodList}
              handleTag={handleTag}
              saveData={set_prodList}
            />
          </Panel>
        </Collapse>
        <Collapse style={{ marginTop: 10 }} defaultActiveKey="prodList">
          <Panel header="全部产品列表" key="prodList">
            <ProdList
              prodList={prodList}
              prodTag={prodTag}
              deleteProd={deleteProd}
              saveData={saveProdData}
              addProdData={addProdData}
              changeStatus={changeProdStatus}
            />
          </Panel>
        </Collapse>
        <Popconfirm
          title="发布后将修改线上配置"
          onConfirm={postData}
          okText="确定"
          cancelText="取消"
        >
          <Button type="primary" style={{ marginTop: 20 }}>
            发布
          </Button>
        </Popconfirm>
      </article>
    </Spin>
  );
}
