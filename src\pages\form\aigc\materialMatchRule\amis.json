{"type": "page", "title": "个基匹配关系表", "body": [{"type": "crud", "syncLocation": false, "api": {"url": "/aigc/audit/match/info/page", "method": "post", "data": {"userTag": "${userTag|default:undefined}", "fundType": "${fundType|default:undefined}", "typePriority": "${typePriority|default:undefined}", "minModifyTime": "${dateRange|split|nth: 0|default:undefined}", "maxModifyTime": "${dateRange|split|nth: 1|default:undefined}", "offSet": "${current}", "pageSize": "${size}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.aigcMatchInfoList || []\r\n  }\r\n}"}, "bulkActions": [], "footerToolbar": ["switch-per-page", "pagination", "statistics"], "columnsTogglable": false, "keepItemSelectionOnPageChange": false, "checkOnItemClick": true, "alwaysShowPagination": true, "name": "crud_materialMatchRule", "headerToolbar": [], "className": "text-lg", "filterTogglable": true, "pageField": "current", "perPageField": "size", "showPerPage": true, "showPageInput": true, "perPageAvailable": [40, 100, 200, 500], "perPage": 40, "filter": {"title": "", "body": [{"type": "input-text", "name": "userTag", "label": "用户标签", "clearable": true, "clearValueOnEmpty": true, "size": "sm", "required": false}, {"type": "input-text", "name": "fundType", "label": "推荐基金分类", "clearable": true, "clearValueOnEmpty": true, "size": "sm", "required": false}, {"type": "select", "name": "typePriority", "label": "优先级", "clearable": true, "size": "xs", "options": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}, {"label": "9", "value": "9"}, {"label": "10", "value": "10"}], "multiple": false, "required": false}, {"type": "input-datetime-range", "name": "date<PERSON><PERSON><PERSON>", "label": "更新时间", "format": "YYYY-MM-DD HH:mm:ss", "placeholder": "-", "className": "text-lg", "clearable": true, "size": "md"}, {"type": "submit", "label": "查询", "level": "primary"}, {"type": "reset", "label": "重置", "level": "primary"}, {"type": "action", "label": "导出模版", "actionType": "download", "api": {"url": "/aigc/audit/download/excel", "method": "get", "data": {"type": "matchInfo"}}}, {"type": "button", "label": "批量上传", "onEvent": {"click": {"actions": [{"dialog": {"type": "dialog", "title": "批量上传", "body": [{"type": "form", "title": "表单", "reload": "crud_materialMatchRule", "body": [{"type": "input-file", "name": "if_fundMatchRuleFile", "label": "上传Excel", "accept": "*", "drag": true, "receiver": "/aigc/audit/upload/match/excel", "fileField": "multipartFile", "hideUploadButton": true, "autoUpload": false}], "wrapWithPanel": false}], "showCloseButton": true, "showErrorMsg": false, "showLoading": true, "closeOnEsc": true, "id": "u:788364bf06fc", "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"componentId": "u:788364bf06fc", "groupType": "component", "actionType": "confirm"}]}}, "level": "primary"}]}, "actionType": "dialog"}]}}, "level": "enhance"}], "actions": [], "wrapWithPanel": true}, "columns": [{"name": "id", "label": "ID", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "userTag", "label": "用户标签", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "fundType", "label": "推荐基金分类", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "typePriority", "label": "优先级", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "modifyTime", "label": "更新时间", "type": "text", "placeholder": "-", "className": "text-lg"}]}]}