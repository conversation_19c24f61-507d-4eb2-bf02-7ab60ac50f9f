/**
 * 黄金宝 - 基础配置 - banner 表单
 */
import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { Form, Input } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import UploadImg from '@/pages/frontend/compoment/uploadImg';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

type Props = {
  formRef: { current: { banner: { [k: string]: FormComponentProps['form'] } } };
  data: { [k: string]: unknown };
};

//
const AdBannerForm: React.FunctionComponent<Props & FormComponentProps> = ({
  form,
  formRef,
  data,
}) => {
  //
  const [image, setImage] = useState(data.image);

  const handleImageChange = (imageUrl: string) => {
    setImage(imageUrl);
  };

  useEffect(() => {
    const id = data._id;

    formRef.current.banner[id] = form;

    return () => {
      delete formRef.current.banner[id];
    };
  }, [/* form,  */ data]);

  useEffect(() => {
    if (image) {
      form.setFields({ image: { value: image, errors: [] } });
    } else {
      form.setFields({ image: { value: image, errors: [new Error('请上传 banner 的图片')] } });
    }
  }, [image]);

  // prettier-ignore
  return (
    <Form
      className={ styles['ad-banner-form'] }
      layout="horizontal"
      { ...formItemLayout }
    >
      <Form.Item label="banner 名称">
        {
          form.getFieldDecorator('name', {
            initialValue: data.name,
            rules: [{ required: true, message: '请输入 banner 的名称' }],
            normalize: value => value ? value.trim() : value,
          })(
            <Input
              placeholder="请输入 banner 的名称"
            />,
          )
        }
      </Form.Item>
      <Form.Item label="banner 链接">
        {
          form.getFieldDecorator('link', {
            initialValue: data.link,
            rules: [{ required: true, message: '请输入 banner 的链接' }],
            normalize: value => value ? value.trim() : value,
          })(
            <Input
              placeholder="请输入 banner 的链接"
            />,
          )
        }
      </Form.Item>
      <Form.Item label="banner 图片(686×160)">
        {
          form.getFieldDecorator('image', {
            initialValue: data.image,
            rules: [{ required: true, message: '请上传 banner 的图片' }],
            normalize: value => value ? value.trim() : value,
          })(
            <UploadImg
              handleChange={ handleImageChange }
              imageUrl={ image }
              isEdit={ true }
            />
          )
        }
      </Form.Item>
    </Form>
  )
};

export default Form.create({ name: 'ad-banner' })(AdBannerForm);
