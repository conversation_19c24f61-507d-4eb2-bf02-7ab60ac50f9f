import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classnames from 'classnames';
import api from 'api';
import { <PERSON><PERSON>, Popconfirm, Row, Col, Select, message } from 'antd';
import { TabItem } from '../type';
import { postJson } from '@/functions/request';
const { fetchFeedTab, postFeedTab, fetchFeedTagList } = api;
const { Option } = Select;
type changeType = 'tag'|'tabName';
const TABNAME_LENGTH = 7;
const tagMap: any = {};

export default function FeedTab () {
  const [formData, setFormData] = useState<TabItem[]>([]);
  const [tagList, setTagList] = useState<string[]>([]);

  useEffect(() => {
    fetchFeedTabForm();
    fetchTagList();
  }, [])

  // 获取表单
  const fetchFeedTabForm = () => {
    fetchFeedTab().then((res: any) => {
      if (res?.code === '0000') {
        res = JSON.parse(res?.data || '');
        setFormData(res);
      } else {
        message.error(res?.message);
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error(e.message);
    })
  }
  // 提交表单
  const postFeedTabForm = () => {
    const checked = checkoutForm();
    if (checked) {
      postFeedTab({
        value: JSON.stringify(formData)
      }).then((res: any) => {
        if (res?.code === '0000') {
          message.success('提交成功');
        } else {
          message.error('提交失败');
        }
      }).catch((e: Error) => {
        console.log(e.message);
        message.error(e.message);
      })
    }
  }
  // 获取tag列表
  const fetchTagList = () => {
    fetchFeedTagList().then((res: any) => {
      if (res?.status_code === 0) {
        res = res.data;
        const _tagList = res.map((item: any) => {
          return item.resultPageTag;
        })
        setTagList(_tagList);
      } else {
        message.error(res?.status_msg);
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error(e.message);
    })
  }
  // 检查 填入项
  const checkoutForm = () => {
    // 遍历表单数据
    let checked = true;
    for (let i = 0; i < formData.length; i++) {
      const row = formData[i];
      if (!row.tag || !row.tabName || row.tabName.length > TABNAME_LENGTH ) {
        checked = false;
        message.warning('请正确填写各字段！');
        break;
      }
    }
    return checked;
  }

  // 选择器选项
  const tagOption = () => {
    return tagList.map((tag, index) => (
      <Option key={index} value={tag}>{tag}</Option>
    ))
  }
  // tag行组件
  const itemRow = ({tag, tabName}: TabItem, index: number) => {
    if (tag) tagMap[tag] = tabName;
    const required = !(tabName && tabName?.length <= TABNAME_LENGTH);
    return (
      <Row key={index} gutter={[32, 16]} className={styles['m-tab-row']}>
        <Col span={5}>
          <Select
            value={tag}
            className={classnames('m-select', tag ? '' : 'm-required')}
            showSearch
            placeholder="请选择基金池tag"
            // onSearch={handleSearch}
            onChange={(value: any) => {handleChange('tag', value, index)}}
          >
            {tagOption()}
          </Select>
        </Col>
        <Col span={3}>
          <div className={`m-value-item m-type-title${required ? ' m-required' : ''}`}>
            <input
              type="text"
              className={classnames('m-input', required ? 'm-required' : '')}
              value={tabName}
              onChange={() => {}}
              onInput={(e: any) => handleChange('tabName', e.target.value, index)}
            />
          </div>
        </Col>
        <Col span={2}>
          <Popconfirm title="确认删除吗？" onConfirm={() => handleDel(index)}>
            <Button type="danger">删除</Button>
          </Popconfirm>
        </Col>
      </Row>
    )
  }

  // 修改配置项
  const handleChange = (type: changeType, value: string, index: number) => {
    console.log('tagMap', tagMap)
    if (!tagMap[value]) {
      // 格式校验
      const reg = /[ ]/g;
      if (value && reg.test(value)) return;
      const _formData = JSON.parse(JSON.stringify(formData));
      _formData[index][type] = value;
      setFormData(_formData);
    } else {
      message.warning('已存在该tag，不可重复配置！');
    }
  }
  // 新增配置项
  const hanldeAdd = () => {
    const _formData = JSON.parse(JSON.stringify(formData));
    _formData.push({
      tag: '',
      tabName: ''
    });
    setFormData(_formData);
  }
  // 删除配置项
  const handleDel = (index: number) => {
    const _formData: TabItem[] = JSON.parse(JSON.stringify(formData));
    // 修改已填tagMap
    const _tag = _formData[index].tag;
    delete tagMap[_tag];
    _formData.splice(index, 1);
    setFormData(_formData);
  }
  // 提交 控制器
  const handleSubmit = () => {
    postFeedTabForm();
  }

  return (
    <section className={styles['m-feed-tab']}>
      <p>精选基金池配置项</p>
      <section className={styles['m-feed-list']}>
        <Row gutter={[32, 16]}>
          <Col span={5}>
            基金池标题名称
          </Col>
          <Col span={3}>
            tab名称
          </Col>
        </Row>
        {formData.map((row, index: number) => itemRow(row, index))}
      </section>
      <section className={styles['m-feed-option']}>
        <Button type="primary" onClick={hanldeAdd}>新增</Button>
        <Button type="primary" onClick={handleSubmit}>提交</Button>
      </section>
    </section>
  )
}