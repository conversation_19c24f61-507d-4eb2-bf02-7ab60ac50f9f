.crossArea{
  //width:800px;
  height: 1000px;
  overflow: auto;
}

.left {
  border: 1px solid #E9E9E9;
  border-radius: 4px;
  padding-bottom: 50px;

}
.right {
  border: 1px solid #E9E9E9;
  border-radius: 4px;
  padding-bottom: 50px;


}
.tag {
  width:1000px;
  position: relative;
  margin: 5px;
  font-size: 13px;
  border: 1px dashed #9cc6f3;
  border-radius: 4px;
  padding: 0 8px;
  line-height: 30px;
  color: #666666;
  background: rgba(255, 255, 255, 0.7);
}
.delete {
  position: absolute;
  top: -1px;
  right: -1px;
  width: 16px;
  height: 16px;
  cursor: pointer;
  user-drag: none;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}
.rowStyle {
  width: 1000px;
}
