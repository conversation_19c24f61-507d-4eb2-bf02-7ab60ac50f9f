import {
    Form,
    Input,
    Button,
    message,
    Popconfirm,
    DatePicker
  } from 'antd';
import React, { useEffect, useState } from 'react';
import { iBigData } from './index';
import { iBigRed } from './redPacket';
import styles from '../index.less';
import RedPacket from './redPacket';
import moment from 'moment';

interface StrategyCardProps {
    currentData: iBigData;
    handleData: (data: iBigData) => void;
    handleDelete: () => void;
}
function CardMessage({currentData, handleData, handleDelete}: StrategyCardProps) {
    const [isEdit, setIsEdit] = useState(false);
    const [cardData, setCardData] = useState<iBigRed[]>([]);
    const [chooseDate, setChooseDate] = useState(null);
    useEffect(() => {   
        let data = currentData && JSON.parse(JSON.stringify(currentData));
        setCardData(data?.cards ?? []);
        setChooseDate(data?.date ?? null)
    }, [currentData])
    const handleCardData = (data: iBigRed[]) => {
        setCardData(data);
    }
    const handleSubmit = () => { 
        if (isEdit) {
            handleData({
                ...currentData,
                date: chooseDate,
                cards: [...cardData]
            });
        }
        setIsEdit(!isEdit);
    };
    const handleDate = (date: any, dateString: any) => {
        setChooseDate(dateString);
    }
    return (
        <div className={styles['m-card']}>
            <div className={styles['m-header']}>
                <Button className={styles['m-button']} onClick={handleSubmit}>{isEdit === true? '保存' : '编辑'}</Button>
                <Popconfirm
                    title="确定删除?"
                    onConfirm={handleDelete}
                    okText="是"
                    cancelText="否"
                >
                    <Button type="danger" className={styles['m-button']}> 删除</Button>
                </Popconfirm>
            </div>
            <div>
                <span style={{marginRight: 100}}>日期</span>
                <DatePicker value={chooseDate ? moment(chooseDate) : null} onChange={handleDate} disabled={!isEdit}></DatePicker>
            </div>
            <RedPacket isEdit={isEdit} cardData={cardData} handleData={handleCardData}></RedPacket>
        </div>
    )
}

export default CardMessage;