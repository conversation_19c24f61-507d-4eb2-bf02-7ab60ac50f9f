import React, { useState, useEffect } from 'react';
import { message, Select } from 'antd';
import api from 'api';
import { UGroupItem, UGroupItemInterface, MailData } from '@/pages/form/mail/interface';

const { postMailUserGroup } = api;
const { Option } = Select;

export default function({
  name,
  onChange,
  formData,
}: {
  name: string;
  onChange: Function;
  formData: MailData;
}) {
  // 控制用户群体列表
  const [uGroupList, setUGroupList] = useState<UGroupItem[]>();
  // 用户群体默认值
  const [uGroupValue, setUGroupValue] = useState<string>('请选择');
  useEffect(() => {
    setUGroupValue('请选择');
    onChange(name, '');
  }, []);
  // 用户类型变化触发用户群体变化
  useEffect(() => {
    postMailUserGroup({ userType: formData.SendPushInfo?.pushUserType })
      .then((res: UGroupItemInterface) => {
        if (res.code === '0000') {
          res.data && setUGroupList(res.data);
          if (uGroupList && uGroupList.length > 0) {
            setUGroupValue('请选择');
            onChange(name, '');
          }
        } else {
          message.error(res.message);
        }
      })
      .catch((err: unknown) => {
        console.warn(err);
        message.error('网络请求错误，请稍后再试');
      });
  }, [formData.SendPushInfo?.pushUserType]);
  const handleChange = (taskId: string) => {
    setUGroupValue(taskId);
    onChange(name, taskId);
  };
  // 渲染用户群体列表
  const getUGroupList = () => {
    if (uGroupList) {
      const _uGroupList = uGroupList.map((uGroupItem: UGroupItem) => (
        <Option value={uGroupItem.taskId} key={uGroupItem.taskId}>
          {uGroupItem.description}
        </Option>
      ));
      return (
        <Select value={uGroupValue} style={{ minWidth: '200px' }} onChange={handleChange}>
          {_uGroupList}
        </Select>
      );
    }
    return;
  };
  return <>{uGroupList && getUGroupList()}</>;
}
