import React, { useState, useEffect } from 'react';
import api from 'api';
import {
  Button,
  message,
  Table,
  Row,
  Col,
  Input,
  Select,
  Modal,
  Switch,
  PageHeader,
  Divider,
  InputNumber,
  Popconfirm,
} from 'antd';
import classNames from 'classnames';
import Search from 'antd/lib/input/Search';
import PlateBind from './plateBind';
import { SideMap } from './type';

const { Option } = Select;
const { postConsultFundRelationV2, getConsultFundRelationV2, cosultList, fundSnsInfo } = api;
const numFormat = (num: number | string) => {
  const res = num.toString().replace(/\d+/, function(n) {
    // 先提取整数部分
    return n.replace(/(\d)(?=(\d{3})+$)/g, function($1) {
      return $1 + ',';
    });
  });
  return res;
};

interface IFundItem {
  type?: string;
  fundName?: string;
  marketId?: string;
  fundCode?: string;
}

interface iFundRelation {
  fundRate?: number; // 投资占比阈值，小数
  scale?: number; // 基金规模阈值，单位：千万
  manualCover?: boolean;
  fundCover?: boolean;
  themeRate?: string;
  stockRate?: string;
  fundMap?: Record<string, Omit<IFundItem, 'fundCode'>>;
}

export default function() {
  const [kvData, setKvDate] = useState<iFundRelation>();

  const [scaleDisabled, setScaleDisabled] = useState(false);
  const [scale, setScale] = useState(0);
  const [fundDisabled, setFundDisabled] = useState(false);
  const [fundRate, setFundRate] = useState(0);
  const [itcProfitList, setItcProfitList] = useState<any[]>([]);
  const [otcProfitList, setOtcProfitList] = useState<any[]>([]);
  const [nowEditKuanJi, setNowEditKuanJi] = useState<IFundItem>({});
  const [kuanJiModalShow, setKuanJiModalShow] = useState<boolean>(false);
  const [switchDisable, setSwitchDisabled] = useState(true);
  const [isKuanJiAdd, setIsKuanJiAdd] = useState(false);

  useEffect(() => {
    getFundRelation();
  }, []);

  const getFundSnsInfo = (params: { fundCode?: string; type?: string }) => {
    return fundSnsInfo(params).then((data: { status_code: number; data: IFundItem }) => {
      if (data.status_code === 0) {
        return data.data;
      }
      return;
    }).catch((error: any) => {
      console.log(error);
    });
  }

  const handleFundInfo = ({ fundCode = nowEditKuanJi.fundCode, type = nowEditKuanJi.type }) => {
    if (!fundCode || !type) {
      return;
    }
    return getFundSnsInfo({ fundCode, type }).then((data?: IFundItem) => {
      if (data) {
        const { fundName, marketId } = data;
        if (!fundName || !marketId) {
          message.error('基金ID 或 类型 输入错误');
          return;
        }
        setNowEditKuanJi(prev => ({
          ...prev,
          fundName,
          marketId
        }));
      }
    });
  }

  const getFundRelation = () => {
    getConsultFundRelationV2().then((res: { code: string; data: string }) => {
      if (res.code === '0000') {
        const data: iFundRelation = JSON.parse(res.data) || {};
        data?.scale ? setScale(data?.scale) : setScale(0);
        data?.fundRate ? setFundRate(data?.fundRate * 100) : setFundRate(0);
        setKvDate(data);
      }
    });
  };

  /**保存规模 */
  const saveScale = () => {
    const data = { ...kvData, scale: scale };
    setKvDate(data);
    postConsultFundRelationV2({ value: JSON.stringify(data) }).then((res: any) => {
      if (res.code === '0000') {
        message.success('保存成功');
        return;
      }
      message.error('保存失败');
    });
  };
  /**保存基金筛选阈值 */
  const saveFundRate = () => {
    const data = { ...kvData, fundRate: fundRate / 100 };
    setKvDate(data);
    postConsultFundRelationV2({ value: JSON.stringify(data) }).then((res: any) => {
      if (res.code === '0000') {
        message.success('保存成功');
        return;
      }
      message.error('保存失败');
    });
  };

  /**处理搜索回调 */
  const onSearch = (val: string) => {
    val = `${val}.TI`;
    cosultList({ indexId: val }).then((res: any) => {
      console.log('indexId', res);
      const itc = [];
      const otc = [];
      const itcTag = ['20', '36', 'UJSJ', 'UJZJ'];
      res?.data?.length > 0 &&
        res?.data?.forEach(item => {
          if (itcTag.includes(item.fundSubMarket)) {
            itc.push(item);
          } else {
            otc.push(item);
          }
        });
      const temp = itc.sort((a, b) => {
        if (Number(a.fundRate) > Number(b.fundRate)) {
          return 1;
        } else if (Number(a.fundRate) === Number(b.fundRate)) {
          return Number(b.scale) - Number(a.scale);
        } else return 0;
      });
      otc.sort((a, b) => {
        if (Number(a.fundRate) > Number(b.fundRate)) {
          return 1;
        } else if (Number(a.fundRate) === Number(b.fundRate)) {
          return Number(b.scale) - Number(a.scale);
        } else return 0;
      });
      setItcProfitList(itc);
      setOtcProfitList(otc);
    });
  };
  /**处理搜索关联关系回调 */
  const onSearchBond = (val: string) => {
    // val = `${val}.TI`;
    // const findItem = kvData.fundMap.find(e => {
    //   return e.marketId === val;
    // });
    const findItem = kvData.fundMap[val];
    if (findItem) {
      const { type, marketId, fundName } = findItem;
      Modal.confirm({
        title: '查询到此项',
        content: `基金Id:${val}已关联 类型:${SideMap[type]},基金名称:${fundName},市场id:${marketId}`,
        okText: '确认',
        cancelButtonProps: { style: { display: 'none' } },
      });
    } else {
      Modal.confirm({
        title: '未查询到此项',
        content: ``,
        okText: '确认',
        cancelButtonProps: { style: { display: 'none' } },
      });
    }
  };

  const fundMapAdapter = fundMap => {
    return Object.keys(fundMap || {})?.map((fundCode, ind) => {
      const { type, fundName, marketId } = fundMap[fundCode];
      return { index: ind, fundCode, type, fundName, marketId };
    });
  };

  const kuanJiPoolColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
    },
    {
      title: '基金id',
      dataIndex: 'fundCode',
      key: 'fundCode',
    },
    {
      title: '类型',
      key: 'type',
      dataIndex: 'type',
      render: item => {
        return <span>{SideMap[item]}</span>;
      },
    },
    {
      title: '基金名称',
      dataIndex: 'fundName',
    },
    {
      title: '市场id',
      dataIndex: 'marketId',
    },
    {
      title: '操作',
      key: 'button',
      render: (item: any, record, index) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                setKuanJiModalShow(true);
                setNowEditKuanJi({ ...record, index: index });
                setIsKuanJiAdd(false);
              }}
            >
              修改
            </Button>
            <Popconfirm
              title="确认删除?"
              onConfirm={() => {
                const kvMap = { ...kvData?.fundMap };
                delete kvMap[record.fundCode];
                const data = { ...kvData, fundMap: kvMap };
                console.log('确认删除', kvMap, record, data);
                setKvDate(data);
                postConsultFundRelationV2({ value: JSON.stringify(data) }).then((res: any) => {
                  if (res.code === '0000') {
                    message.success('保存成功');
                    return;
                  }
                  message.error('保存失败');
                });
              }}
            >
              <Button type="danger">删除</Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  const cosultListColumns = [
    {
      title: '基金名称',
      dataIndex: 'fundName',
      width: '30%',
    },
    {
      title: '基金交易代码',
      dataIndex: 'tradeCode',
    },
    {
      title: '基金占比',
      dataIndex: 'fundRate',
    },
    {
      title: '基金规模',
      dataIndex: 'scale',
      render: text => `${numFormat(text)}元`,
    },
  ];

  return (
    <div>
      <PlateBind></PlateBind>
      <Divider></Divider>
      <section>
        <PageHeader style={{ width: '80%' }} title={`2.基金筛选阈值`}></PageHeader>
        <Row
          className={classNames('g-mb30')}
          style={{ width: '80%', marginLeft: '24px' }}
          type="flex"
          justify={'space-between'}
        >
          <Col style={{}} span={6}>
            <span>规模≥</span>
            <InputNumber
              value={scale}
              onChange={e => setScale(Number(e))}
              style={{ width: '100px', marginLeft: '20px' }}
              disabled={!scaleDisabled}
            />
            <span>千万</span>
          </Col>
          <Col
            span={4}
            style={{ display: 'flex', justifyContent: 'flex-end', marginRight: '48px' }}
          >
            <Button
              type="primary"
              onClick={() => {
                setScaleDisabled(true);
              }}
            >
              修改
            </Button>
            <Button
              type="primary"
              onClick={() => {
                saveScale();
                setScaleDisabled(false);
              }}
              className={'g-ml20'}
            >
              保存
            </Button>
          </Col>
        </Row>

        <Row
          className={classNames('g-mb30')}
          style={{ width: '80%', marginLeft: '24px' }}
          type="flex"
          justify={'space-between'}
        >
          <Col span={6}>
            <span>投资占比≥</span>
            <InputNumber
              value={fundRate}
              onChange={e => setFundRate(Number(e))}
              style={{ width: '100px', marginLeft: '20px' }}
              disabled={!fundDisabled}
              defaultValue={fundRate}
            />
            <span>%</span>
          </Col>
          <Col
            span={4}
            style={{ display: 'flex', justifyContent: 'flex-end', marginRight: '48px' }}
          >
            <Button
              type="primary"
              onClick={() => {
                setFundDisabled(true);
              }}
              className={'g-ml200'}
            >
              {' '}
              修改
            </Button>
            <Button
              type="primary"
              onClick={() => {
                saveFundRate();
              }}
              className={'g-ml20'}
            >
              {' '}
              保存
            </Button>
          </Col>
        </Row>
      </section>
      <Divider></Divider>
      <section>
        <PageHeader
          style={{ width: '80%' }}
          title={`3.行业和概念基金池排序结果查询`}
          extra={
            <Search
              placeholder="输入行业概念代码"
              allowClear
              enterButton="查询"
              onSearch={onSearch}
            />
          }
        ></PageHeader>
        <Row type="flex" justify={'space-between'}>
          <Col span={10}>
            <div>场内</div>
            <Table
              style={{
                height: 300,
                overflow: 'auto',
              }}
              columns={cosultListColumns}
              dataSource={itcProfitList}
            ></Table>
          </Col>
          <Col span={10} style={{ marginLeft: '48px' }}>
            <div>场外</div>
            <Table
              style={{
                height: 300,
                overflow: 'auto',
              }}
              columns={cosultListColumns}
              dataSource={otcProfitList}
            ></Table>
          </Col>
        </Row>
      </section>
      <Divider></Divider>
      <section>
        <PageHeader
          style={{ width: '80%' }}
          title={`4.兜底宽基产品池`}
          extra={
            <div style={{ display: 'flex', flexDirection: 'row' }}>
              <Button
                type="primary"
                style={{ marginRight: 10 }}
                onClick={() => {
                  setKuanJiModalShow(true);
                  setNowEditKuanJi({});
                  setIsKuanJiAdd(true);
                }}
              >
                新增
              </Button>
              <Search
                placeholder="查询关联关系"
                allowClear
                enterButton="查询"
                style={{ marginLeft: '24px' }}
                onSearch={onSearchBond}
              />
              <Button
                onClick={() => {
                  getFundSnsInfo({ fundCode: '000001', type: 'outside' }).then((res?: IFundItem) => {
                    if (res) {
                      const { fundCode, type, fundName, marketId } = res;
                      const data = { ...kvData, fundMap: {
                        [fundCode]: {
                          type,
                          fundName,
                          marketId,
                        },
                      } };
                      postConsultFundRelationV2({ value: JSON.stringify(data) }).then((res: any) => {
                        if (res.code === '0000') {
                          message.success('初始化成功');
                          return;
                        }
                        message.error('初始化失败');
                      });
                      return;
                    }
                    message.error('初始化失败');
                  });
                }}
              >
                初始化
              </Button>
            </div>
          }
        />
        <Modal
          title={`兜底宽基产品池编辑`}
          visible={kuanJiModalShow}
          onOk={() => {
            if (!nowEditKuanJi.fundCode) {
              message.error('基金ID不能为空');
              return;
            }

            if (!nowEditKuanJi.type) {
              message.error('类型不能为空');
              return;
            }

            if (!nowEditKuanJi.fundName) {
              message.error('基金ID 或 类型 输入错误');
              return;
            }

            const { type, fundName, marketId, fundCode } = nowEditKuanJi as any;

            const before = {
              ...kvData?.fundMap,
              [fundCode]: {
                type,
                fundName,
                marketId,
              },
            };
            const data = { ...kvData, fundMap: before };
            setKvDate(data);
            postConsultFundRelationV2({ value: JSON.stringify(data) }).then((res: any) => {
              if (res.code === '0000') {
                message.success('保存成功');
                return;
              }
              message.error('保存失败');
            });
            setKuanJiModalShow(false);
          }}
          onCancel={() => {
            setKuanJiModalShow(false);
          }}
        >
          <Input
            value={nowEditKuanJi?.fundCode}
            disabled={!isKuanJiAdd}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>基金ID：</span>}
            onChange={e => {
              e.persist();
              setNowEditKuanJi(prev => {
                return {
                  ...prev,
                  fundCode: e?.target?.value,
                };
              });
              handleFundInfo({ fundCode: e?.target?.value });
            }}
          />
          <Col>
            <span>类型：</span>
            <Select
              placeholder="类型"
              style={{ marginBottom: 10, width: 300 }}
              value={nowEditKuanJi?.type}
              onChange={val => {
                setNowEditKuanJi(prev => {
                  return {
                    ...prev,
                    type: val as string,
                  };
                });
                handleFundInfo({ type: val });
              }}
            >
              {Object.keys(SideMap).map((sKey, index) => {
                return (
                  <Option value={sKey} key={sKey}>
                    {SideMap[sKey]}
                  </Option>
                );
              })}
            </Select>
          </Col>
          <Input
            value={nowEditKuanJi?.fundName}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>基金名称：</span>}
            disabled
          />
          <Input
            value={nowEditKuanJi?.marketId}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>市场ID：</span>}
            disabled
          />
        </Modal>
        <Table
          columns={kuanJiPoolColumns}
          dataSource={fundMapAdapter(kvData?.fundMap)}
          pagination={{ pageSize: 10 }}
          rowKey={record => record.index}
        ></Table>
      </section>
      <section>
        <PageHeader
          style={{ width: '80%' }}
          title={`5.开关及兜底配置`}
          extra={
            <div>
              <Button
                type="primary"
                onClick={() => {
                  setSwitchDisabled(false);
                }}
                className={'g-ml20'}
              >
                修改
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setSwitchDisabled(true);
                  postConsultFundRelationV2({ value: JSON.stringify(kvData) }).then((res: any) => {
                    if (res.code === '0000') {
                      message.success('保存成功');
                      return;
                    }
                    message.error('保存失败');
                  });
                }}
                className={'g-ml20'}
              >
                保存
              </Button>
            </div>
          }
        />
        <div style={{ marginBottom: 10, width: 300 }}>
          <span>图谱兜底：</span>
          <Switch
            checked={kvData?.manualCover}
            onChange={checkS => {
              setKvDate({ ...kvData, manualCover: checkS });
            }}
            disabled={switchDisable}
          />
        </div>
        <div style={{ marginBottom: 10, width: 300 }}>
          <span>宽基兜底：</span>
          <Switch
            checked={kvData?.fundCover}
            onChange={checkS => {
              setKvDate({ ...kvData, fundCover: checkS });
            }}
            disabled={switchDisable}
          />
        </div>
        <Input
          value={kvData?.stockRate}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<span>个股阈值：</span>}
          disabled={switchDisable}
          type="number"
          onChange={e => {
            if (+e.target.value > 1) {
              message.error('不能大于1');
              return;
            }
            setKvDate({ ...kvData, stockRate: e.target.value });
          }}
        />
        <div style={{ marginBottom: 10, width: 300 }}>
          <Input
            value={kvData?.themeRate}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>板块阈值：</span>}
            onChange={e => {
              if (+e.target.value > 1) {
                message.error('不能大于1');
                return;
              }
              setKvDate({ ...kvData, themeRate: e.target.value });
            }}
            type="number"
            disabled={switchDisable}
          ></Input>
        </div>
      </section>
    </div>
  );
}
