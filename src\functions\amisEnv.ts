import amisEnvInit from '@king-fisher/amis-adapter';
import { apiPrefix } from 'config';
import store from 'store';

// 环境变量配置
const amisEnv = function() {
  // 初始化默认请求头
  const initHeaders = () => {
    let defaultRequestHeaders = {};
    // 用户token
    let token = store.get('user_token');
    token && (defaultRequestHeaders['Authentication'] = token);
    // 用户name
    let userName = store.get('name');
    userName && (defaultRequestHeaders['user'] = encodeURIComponent(userName));
    return defaultRequestHeaders;
  };
  // 自定义env配置
  const customEnvConfig = {
    statusName: 'status_code',
    statusSuccessCode: 0,
    msgName: 'status_msg',
    apiPrefix: ~location.host.indexOf('localhost') ? '/api' : apiPrefix,
    defaultRequestHeaders: initHeaders(),
    errorCallBack: function(e: any) {
      if (e.statusCode === 401) location.hash = '#/login';
    },
  };
  return amisEnvInit(customEnvConfig);
};

export default amisEnv;
