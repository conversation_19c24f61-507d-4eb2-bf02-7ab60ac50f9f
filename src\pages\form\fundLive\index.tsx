import React, { useState, useEffect } from 'react';
import Form<PERSON><PERSON> from 'form-render/lib/antd';
import api from 'api';
import { But<PERSON>, Popconfirm, message } from 'antd';
import FORM_JSON from './form.json';

const { fetchFundLive, postFundLive } = api;

export default function () {
    const [init, setInit] = useState(false);
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);

    useEffect(() => {
        let dataJSON = FORM_JSON;
        fetchFundLive().then((res: any) => {
            let { code, data } = res;
            if ( code === '0000' &&　data ) {
                data = JSON.parse(data);
                dataJSON.formData = {
                    topRecommended: data.topRecommended,
                    productList: data.productList,
                }
            }
            setInit(true);
            setData(dataJSON.formData);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, []);

    const updateForm = () => {
        // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
        if (valid.length > 0) {
            alert(`校验未通过字段：${valid.toString()}`);
        } else {
            postFundLive({
                value: JSON.stringify(formData),
            }).then((res: any) => {
                if (res.code !== '0000') {
                    message.error(res.msg);
                } else {
                    message.success('更新成功！');
                }
            }).catch((e: Error) => {
                message.error(e.message);
            });
        } 
    };
    if (!init) return '加载中';
    return (
        <div style={{padding: 60}}>
            <header style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>
                    <span style={{ marginRight: 30 }}>推荐位封面图：630*350</span>
                    <span>列表封面图：298*260</span>
                </div>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要更新么'}
                    onConfirm={updateForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="primary" 
                    >
                        更新
                    </Button>
                </Popconfirm>
                
            </header>
            <FormRender 
                propsSchema={FORM_JSON.propsSchema}
                formData={formData}
                onChange={setData}
                onValidate={setValid}
                displayType="row"
                showDescIcon={true}
            />
        </div>
    )
}