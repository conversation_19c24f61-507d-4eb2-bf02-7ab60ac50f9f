import React, { useState, useEffect } from 'react';
import api from 'api';
import { But<PERSON>, Popconfirm, message } from 'antd';
import BaseRedPacket from './baseRedPacket';
import BigRedPacket from './bigRedPacket';
const { fetchFinancialDayRed, postFinancialDayRed } = api;

export default function () {
    const [init, setInit] = useState(false);
    const [isModify, setModify] = useState(false);
    const [baseData, setBaseData] = useState<any[]>([]);
    const [bigData, setBigData] = useState<any[]>([]);
    const [allData, setAllData] = useState({});
    useEffect(() => {
        fetchFinancialDayRed().then((res: any) => {
            let { code, data } = res;
            if ( code === '0000' &&　data ) {
                data = JSON.parse(data);
                if (data) {
                    data.baseData = data?.baseData?.sort((a: any, b: any) => {
                        return a?.date === b?.date ? 0 : a?.date < b?.date ? 1 : -1;
                    });

                    setBaseData(data?.baseData ?? []);
                    setBigData(data?.bigData ?? []);
                    setAllData(data)
                }
            }
            setInit(true);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, []);

    const saveForm = () => {
        postFinancialDayRed({
            value: JSON.stringify(allData),
        }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.msg);
            } else {
                message.success('保存成功！');
                setTimeout(() => {
                    window.location.reload();
                }, 1000)
            }
        }).catch((e: Error) => {
            message.error(e.message);
        });
        
    };
    const handleModify = (flag: boolean) => {
        setModify(flag)
    }
    const handleBaseData = (data: any) => {
        setBaseData(data);
        let obj = {
          ...allData,
          baseData: data
        }
        setAllData({...obj})
    }
    const handleBigData = (data: any) => {
        setBigData(data);
        let obj = {
          ...allData,
          bigData: data
        }
        setAllData({...obj})
    }
    
    if (!init) return '加载中';
    return (
        <div style={{padding: 40}}>
            <BaseRedPacket isModify={isModify} handleModify={handleModify} baseData={baseData} handleData={handleBaseData}></BaseRedPacket>
            <BigRedPacket isModify={isModify} handleModify={handleModify} bigData={bigData} handleData={handleBigData}></BigRedPacket>
            <footer style={{ display: 'flex', justifyContent: 'space-between' }} className="g-mt40">
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={saveForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="primary" 
                        disabled={!isModify}
                    >
                        保存
                    </Button>
                </Popconfirm>
            </footer>
        </div>
    )
}