import React, { useState } from 'react';
import { Button, Collapse, message, Tag } from 'antd';
import WrappedFundConfig from './myCard';
import classnames from 'classnames';
import styles from '../index.less';

const { Panel } = Collapse;

export interface iFundConfigData {
    rankType: string;
    paperWork?: string;
    key: string;
}
interface iFundConfigProps {
    handleData: (data: iFundConfigData[]) => void;
    fundConfigData: iFundConfigData[];
    isModify: boolean;
    handleModify: (flag: boolean) => void;
}
const TypeMap = {
    rqbd: '人气之选',
    syph: '业绩领跑',
    tsyx: '同顺严选',
    rqdtb: '人气定投榜',
}
function FundConfig({handleData, fundConfigData, handleModify, isModify}: iFundConfigProps) {
    const newAdd = () => {
        let arr = [...fundConfigData];
        let obj = {
            rankType: 'rqbd',
            paperWork: '',
            key: +new Date() + '',
        }
        arr.push(obj);
        handleData(arr);
    }
    const handleDelete = (key: number) => {
        if (!isModify) handleModify(true);
        let arr = [...fundConfigData];
        arr.splice(Number(key), 1);
        handleData(arr);
        console.log(arr);
    }
    const modifyFundConfigData = (data: iFundConfigData, num: number) => {
        if (!isModify) handleModify(true);
        let arr = [...fundConfigData];
        arr[num] = data;
        handleData(arr);
    }
    const panelHeader = (obj: iFundConfigData, num: number) => {
        return (
            <div className={classnames(styles['m-panel-header1'], 'u-j-middle')}>
                <span style={{marginRight: 40}}>{Number(num) + 1}</span>
                { obj.rankType && <span>榜单类型: {TypeMap[obj.rankType]}</span> }
                { obj.paperWork && <span>运营文案: {obj.paperWork}</span> }
            </div>
        )
    }
    return <div style={{marginTop: 40}}>
        <h1 className="g-fs28 f-bold">适合定投的基金：</h1>
        <Collapse>
            {
                fundConfigData?.map((item: iFundConfigData, index: number) => {
                    return (
                        <Panel 
                            header={panelHeader(item, index)} 
                            key={item.key}
                        >
                            <WrappedFundConfig currentData={item} handleData={(data) => modifyFundConfigData(data, index)} handleDelete={() => handleDelete(index)}/>
                        </Panel>
                    )
                })
            }
            
        </Collapse>
        <div style={{marginTop: 20}}>
          <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
        </div>
    </div>
}
export default React.memo(FundConfig);
