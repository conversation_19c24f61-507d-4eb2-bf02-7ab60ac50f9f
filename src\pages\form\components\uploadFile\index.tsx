import React from 'react';
import api from 'api';
import { Upload, message, Button, Row, Col, Modal } from 'antd';
import { toast } from 'utils/message';
import './index.less';

const { newCommonUpload } = api;

interface iProps {
  text: string;
  callback?: (name: string, size: number, url: string) => void;
  beforeUpload?: () => Boolean;
}

export default function({ text, callback, beforeUpload }: iProps) {
  function customRequest(file: any) {
    _.fundLoading();
    let params = new FormData();
    params.append('file', file.file);
    newCommonUpload(params)
      .then(res => {
        _.hideFundLoading();
        if (res.status_code === 0) {
          let _url = ''
          if(res.data.includes('http')) {
            //兼容以前
            _url = res.data.replace('http://', 'https://')
          } else {
            //区分正式环境、测试环境
            if(window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')) {
              _url = 'https://testo.thsi.cn/' + res.data
            } else {
              _url = 'https://o.thsi.cn/' + res.data
            }
            
          }
          callback &&
            callback(file.file.name, file.file.size, _url);
        } else {
          message.error('上传失败');
        }
      })
      .catch(e => {
        message.error('上传失败 请检查文件大小 最大100M');
      });
  }

  // function beforeUpload() {}

  return (
    <div>
      <Upload
        listType="picture-card"
        className="avatar-uploader"
        showUploadList={false}
        customRequest={customRequest}
        beforeUpload={beforeUpload}
        disabled={false}
      >
        <div style={{ textAlign: 'left' }}>
          <Button type="primary" disabled={false}>
            {text}
          </Button>
        </div>
      </Upload>
    </div>
  );
}
