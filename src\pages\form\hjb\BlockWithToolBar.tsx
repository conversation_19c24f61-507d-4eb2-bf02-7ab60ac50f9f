/**
 * 带工具条的块
 */
import React, { useState } from 'react';
import styles from './block-with-toolbar.less';
import clsx from 'classnames';
import { Button, Badge, Modal } from 'antd';

type Props = {
  className: string;
  children: React.ReactNode[];
  number: number;
  onAppend: (number: number) => void;
  onRemove: (number: number) => void;
};

//
const BlockWithToolBar: React.FunctionComponent<Props> = ({
  className,
  children,
  number,
  onAppend,
  onRemove,
}) => {
  //
  const [removeModal, setRemoveModal] = useState(false);

  // prettier-ignore
  return (<>
    <section className={ clsx(className, styles['block']) }>
      { children }
      <aside className={ styles['toolbar'] }>
        <Badge
          className={ styles['badge'] }
          count={ number }
          style={{ backgroundColor: '#fff', color: '#999', boxShadow: '0 0 0 1px #d9d9d9 inset' }}
        />
        <Button.Group className={ styles['btn-group'] }>
          <Button size="small" type="primary" icon="plus" onClick={ () => onAppend(number) } disabled={ !onAppend } />
          <Button size="small" type="primary" icon="delete" onClick={ () => setRemoveModal(true) } disabled={ !onRemove } />
        </Button.Group>
      </aside>
    </section>
    <Modal
      title={ '删除确认' }
      visible={ removeModal }
      okText={ '确认' }
      cancelText={ '取消' }
      onOk={ () => onRemove(number) }
      onCancel={ () => setRemoveModal(false) }
    >
      <p>确认要删除该配置吗？</p>
    </Modal>
  </>)
};

export default BlockWithToolBar;
