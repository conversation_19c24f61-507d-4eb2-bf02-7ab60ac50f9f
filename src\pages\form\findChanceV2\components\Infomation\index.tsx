import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ConfigData, HotSpot, Info, InfoNameMap } from '../../type';
import { Card, DatePicker, Input, Button, Popconfirm, Select } from 'antd';
import InputTitle from '../InputTitle';
import { informationHelper, mapPropToName } from './helper';
import UploadImg from '../UploadImg';
import moment from 'moment';

export interface IProps {
  data: ConfigData['hotspots'];
  onChange: (data: ConfigData['hotspots']) => void;
  disabled: boolean;
}
const Infomation: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleAdd = () => {
    onChange([informationHelper.addArrayData(), ...data]);
  };
  const handleChange = (index, name, value) => {
    const newData = [...data];
    newData[index][name] = value;
    onChange(newData);
  };
  const handleDel = index => {
    const newData = [...data];
    newData.splice(index, 1);
    onChange(newData);
  };

  
  const changePosition=(dragIndex,hoverIndex)=>{
    let dragItem = data[dragIndex];
    let hoverItem = data[hoverIndex];
    if (!dragItem || !hoverItem) {
      return;
    }
    //hoverIndex后面的都往后移动一位

    let newData = [...data];
    //先去掉在数组中被拖动的数据
    newData.splice(dragIndex, 1, undefined);

    //获取放置位置以及后面的数据
    const postfixArr = newData.slice(hoverIndex) || [];
    const prefixArr = newData.slice(0, hoverIndex + 1) || [];
    prefixArr[hoverIndex] = dragItem;
    const changeIndexKeys = [];
    
    newData = [...prefixArr, ...postfixArr]
      .filter(item => Boolean(item))
      .map((item, index) => {
        if (item.index !== index) {
          item.index = index;
          changeIndexKeys.push(item.id);
        }
        return item;
      });

    //   dragItem={...dragItem,index:hoverIndex};
    //   hoverItem={...hoverItem,index:dragIndex};

    //   const newData=[...configDataList];
    //   newData[hoverIndex]=dragItem
    //   newData[dragIndex]=hoverItem

    onChange(newData);
  }
  return (
    
    <div>
      <h3>相关资讯</h3>
      {data?.map((item,index)=>{
        return <Card style={{ width: '350px',display:"inline-block" }}>
          <Input
        value={item.id}
              onChange={e => handleChange(index, 'id', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={mapPropToName['id']} />}
        />
        
        <div>
              <InputTitle title={mapPropToName['type']} />
              <Select
                value={item.type}
                onChange={v => handleChange(index, 'type', v)}
                disabled={disabled}
                style={{ marginBottom: 10, width: 300 }}
              >
                <Option value={Info.ZIXUN}>{InfoNameMap[Info.ZIXUN]}</Option>
                <Option value={Info.TOUGULONG}>{InfoNameMap[Info.TOUGULONG]}</Option>
              </Select>
            
        </div>
        <Input
        value={item.name}
              onChange={e => handleChange(index, 'name', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={mapPropToName['name']} />}
        />
        <Input
                value={item.from}
                onChange={e => handleChange(index, 'from', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={mapPropToName['from']} isRequired={false}/>}
        />
        {/* <Input
          value={item.readAmount}
          onChange={e => handleChange(index, 'readAmount', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={mapPropToName['readAmount']} isRequired={false}/>}
        /> */}
        <div style={{ marginBottom: 10, width: 300 }}>
          <InputTitle title={mapPropToName['createTime']} isRequired={false}/>
          <DatePicker
           value={item.createTime ? moment(item.createTime) : ''}
           onChange={(date, dateString) => handleChange(index,'createTime', dateString)}
          showTime disabled={disabled} placeholder={'请选择日期'} />
        </div>
        <Input
         value={item.cover}
         onChange={e => handleChange(index, 'cover', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={mapPropToName['cover']} isRequired={false}/>}
        />
        <Input
        value={item.link}
        onChange={e => handleChange(index, 'link', e.target.value)}
          disabled={disabled}
          style={{ marginBottom: 10, width: 300 }}
          addonBefore={<InputTitle title={mapPropToName['link']}  />}
        />
        
        <div style={{ marginTop: '4px' }}>
        <Popconfirm disabled={disabled} onConfirm={()=>handleDel(index)} title={'请确认是否删除'} okText={'确认'} cancelText={'取消'}>
          <Button disabled={disabled} style={{ marginRight: '4px' }} size="small">
            下架
          </Button>
        </Popconfirm>
        <Button onClick={()=>changePosition(index,0)} disabled={disabled} style={{ marginRight: '4px' }} size="small">
            置顶
        </Button>
        </div>
        {/* <UploadImg
          title={mapPropToName['articleCreatorAvatar']}
          isRequired
          size={['60 * 60']}
        ></UploadImg> */}
      </Card>
      })}

      <div>
      <Button disabled={disabled}  onClick={handleAdd} style={{ marginTop: '4px' }} size="small">
        新增相关资讯
      </Button>
      </div>
    </div>
  );
};
export default Infomation;
