{"type": "page", "title": "权益包配置", "body": [{"type": "crud", "syncLocation": false, "api": {"method": "get", "url": "/common_config/kv_data_get?key=equityPackage", "adaptor": "console.log(payload); if (payload.code === '0000') { payload.status = 0; payload.data = {items: JSON.parse(payload.data), allItems: JSON.parse(payload.data)} } return payload"}, "columns": [{"name": "label", "label": "名称", "type": "text", "id": "u:52bd73634fa9"}, {"name": "type", "label": "类型", "type": "text", "id": "u:544d1a77be05"}, {"type": "operation", "label": "操作", "buttons": [{"label": "删除", "type": "button", "actionType": "ajax", "level": "danger", "hiddenOn": "this.type === '权益'", "confirmText": "确认要删除？", "api": {"method": "post", "url": "/common_config/kv_data_save", "data": {"allItems": "${allItems}", "id": "${id}", "label": "${label}"}, "dataType": "form", "adaptor": "console.log(payload); if (payload.code === '0000') { payload.status = 0; payload.message = '删除成功' } return payload", "requestAdaptor": "const { allItems, label, id } = api.data;\rallItems.splice(allItems.findIndex(item => item.id === id), 1)\r\n\r\napi.data = {\r\n  key: \"equityPackage\",\r\n  value: JSON.stringify(allItems)\r\n}\r\nreturn api;"}}, {"label": "编辑", "type": "button", "actionType": "dialog", "hiddenOn": "this.type === '权益'", "level": "primary", "dialog": {"title": "编辑", "body": [{"type": "form", "data": {"&": "$$"}, "rules": [{"rule": "data.label === data.newLabel || allItems.findIndex(item => item.label === data.newLabel) === -1", "message": "权益包名称重复！"}], "api": {"method": "post", "url": "/common_config/kv_data_save", "data": {"items": "${allItems}", "tree": "${tree}", "label": "${label}", "newLabel": "${newLabel}", "id": "${id}"}, "dataType": "form", "adaptor": "if (payload.code === '0000') {\r\n  response.status = 0;\r\n  response.msg = \"编辑成功\"\r\n}\r\nreturn response", "requestAdaptor": "let { items, label, tree, newLabel, id } = api.data;\r\nconst oldItem = items.find((item) => item.id === id);\r\noldItem.label = newLabel;\r\noldItem.children = [];\r\ntree.forEach(item => {\r\n  oldItem.children.push({\r\n    label: item.label,\r\n    value: item.value,\r\n    type: '权益'\r\n  })\r\n})\r\nchildren = oldItem;\r\nlabel=newLabel\r\napi.data = {\r\n  key: \"equityPackage\",\r\n  value: JSON.stringify(items)\r\n}\r\nreturn api;\r\n"}, "body": [{"type": "input-text", "name": "new<PERSON>abel", "label": "权益包名称", "trimContents": true, "value": "${label}", "id": "u:6b57e6f3fe1a", "required": true}, {"type": "container", "id": "u:b7d63c740db3", "body": [{"type": "input-tree", "label": "权益选择", "initiallyOpen": false, "unfoldedLevel": 2, "name": "tree", "id": "u:f4dcef2ffb33", "multiple": true, "enableNodePath": false, "hideRoot": true, "showIcon": true, "autoCheckChildren": true, "onlyChildren": true, "joinValues": false, "extractValue": false, "value": "${children}", "source": {"url": "${domain}/marketing/user/level/rights/page", "method": "get", "requestAdaptor": "", "adaptor": "let res = [\r\n  {\r\n    label: \"全选\",\r\n    value: \"selectAll\",\r\n    children: []\r\n  }\r\n]\r\nconsole.log(\"payload = \", payload)\r\nconst options = []\r\nfor (const key in payload.data.configMap) {\r\n  const optionGroup = {\r\n    label: payload.data.configMap[key][0].rightsType,\r\n    value: key,\r\n    children: []\r\n  };\r\n\r\n  payload.data.configMap[key].forEach(item => {\r\n    optionGroup.children.push({\r\n      label: item.rightsName,\r\n      value: item.rightsId\r\n    });\r\n  });\r\n\r\n  options.push(optionGroup);\r\n}\r\nres[0].children = [...options];\r\npayload.data = res;\r\nconsole.log(\"payload = \", payload.data)\r\nreturn payload", "messages": {}}, "required": true, "onEvent": {}}], "label": "权益选择"}], "id": "u:cf91fe59685f", "actions": [{"type": "submit", "label": "提交", "primary": true}]}], "id": "u:5f95a7075fc8", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:0984a4115acb"}, {"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:5fb1c481957e"}]}, "id": "u:1d604e11611d"}], "id": "u:35cd3ab88de0"}], "bulkActions": [], "itemActions": [], "id": "u:a9851943e1ae", "headerToolbar": [{"label": "新增", "type": "button", "actionType": "dialog", "level": "primary", "dialog": {"title": "新增", "body": [{"type": "form", "data": {"&": "$$"}, "rules": [{"rule": "allItems.findIndex(item => item.label === data.label) === -1", "message": "权益包名称重复！"}], "api": {"method": "post", "url": "/common_config/kv_data_save", "data": {"items": "${allItems}", "tree": "${tree}", "label": "${label}"}, "dataType": "form", "sendOn": "this.items.findIndex(item => item.label === label) === -1", "adaptor": "console.log(payload); if (payload.code === '0000') { payload.status = 0; payload.message = '新增成功' } return payload", "requestAdaptor": "const { items, tree, label } = api.data;\r\nlet id;\r\nif (items.length === 0) {\r\n    id = 0;\r\n} else {\r\n    id = items[items.length - 1].id + 1;\r\n}\r\nconst obj = {\r\n    id,\r\n    label,\r\n    type: \"权益包\",\r\n    children: [],\r\n};\r\n\r\ntree.forEach((item) => {\r\n    item.type = \"权益\";\r\n    obj.children.push(item);\r\n});\r\n\r\nitems.push(obj);\r\napi.data = {\r\n    key: \"equityPackage\",\r\n    value: JSON.stringify(items)\r\n}\r\nreturn api;"}, "body": [{"type": "input-text", "trimContents": true, "name": "label", "label": "权益包名称", "id": "u:6b57e6f3fe1a", "required": true}, {"type": "container", "id": "u:b7d63c740db3", "body": [{"type": "input-tree", "label": "权益选择", "initiallyOpen": false, "unfoldedLevel": 2, "name": "tree", "id": "u:f4dcef2ffb33", "multiple": true, "enableNodePath": false, "hideRoot": true, "showIcon": true, "autoCheckChildren": true, "onlyChildren": true, "joinValues": false, "extractValue": false, "source": {"url": "${domain}/marketing/user/level/rights/page", "method": "get", "requestAdaptor": "", "adaptor": "let res = [\r\n  {\r\n    label: \"全选\",\r\n    value: \"selectAll\",\r\n    children: []\r\n  }\r\n]\r\nconsole.log(\"payload = \", payload)\r\nconst options = []\r\nfor (const key in payload.data.configMap) {\r\n  const optionGroup = {\r\n    label: payload.data.configMap[key][0].rightsType,\r\n    value: key,\r\n    children: []\r\n  };\r\n\r\n  payload.data.configMap[key].forEach(item => {\r\n    optionGroup.children.push({\r\n      label: item.rightsName,\r\n      value: item.rightsId\r\n    });\r\n  });\r\n\r\n  options.push(optionGroup);\r\n}\r\nres[0].children = [...options];\r\npayload.data = res;\r\nconsole.log(\"payload = \", payload.data)\r\nreturn payload", "messages": {}}, "required": true, "onEvent": {}}], "label": "权益选择"}], "id": "u:cf91fe59685f", "actions": [{"type": "submit", "label": "提交", "primary": true}]}], "id": "u:5f95a7075fc8", "actions": [{"type": "button", "actionType": "cancel", "label": "取消", "id": "u:0984a4115acb"}, {"type": "button", "actionType": "confirm", "label": "确定", "primary": true, "id": "u:5fb1c481957e"}]}, "id": "u:1d604e11611d"}, "bulkActions"]}], "id": "u:c2f77cbfbb0f"}