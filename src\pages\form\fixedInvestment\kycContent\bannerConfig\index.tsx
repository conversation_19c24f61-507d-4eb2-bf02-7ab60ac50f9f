import React, { useState } from 'react';
import { Button, Collapse, message, Tag } from 'antd';
import WrappedBannerCard from './myCard';
import classnames from 'classnames';
import styles from '../index.less';

const { Panel } = Collapse;

type ikycData = {
    xsdtargetText: string,
    xsdbtnTitle: string,
    zcxrtargetText: string,
    zcxrbtnTitle: string,
    zclrtargetText: string,
    zclrbtnTitle: string,
    zrztargetText: string,
    zrzbtnTitle: string,
    txztargetText: string,
    txzbtnTitle: string,
}
type ifundData = {
    cardTitle: string,
    contentTitle: string,
    btnText: string,
    jumpUrl: string,
    checkType: 'text' | 'product',
    fundText?: string,
    productId?: string,
    productName?: string,
    rate?: number;
}
export interface iBannerConfigData {
    functionType: string;
    kycData?: ikycData,
    fundData?: ifundData
    key: string;
}
interface iBannerConfigProps {
    handleData: (data: iBannerConfigData[]) => void;
    bannerConfigData: iBannerConfigData[];
    isModify: boolean;
    handleModify: (flag: boolean) => void;
}
const functionText = {
    kyc: 'KYC定投方案',
    jjtj: '基金推荐'
}
function BannerConfig({handleData, bannerConfigData, isModify, handleModify}: iBannerConfigProps) {
    const newAdd = () => {
        let arr = [...bannerConfigData];
        let obj: any = {
            functionType: 'jjtj',
            kycData: {},
            fundData: {
                checkType: 'text',
                rate: 0
            },
            key: +new Date() + '',
        }
        arr.push(obj);
        handleData(arr);
    }
    const handleDelete = (key: number) => {
        if (!isModify) handleModify(true);
        let arr = [...bannerConfigData];
        arr.splice(Number(key), 1);
        handleData(arr);
    }
    const modifyBannerConfigData = (data: iBannerConfigData, num: number) => {
        if (!isModify) handleModify(true);
        let arr = [...bannerConfigData];
        arr[num] = data;
        handleData(arr);
    }
    const panelHeader = (obj: iBannerConfigData) => {
        return (
            <div className={classnames(styles['m-panel-header'])}>
                { obj.functionType && <span style={{marginRight: 40}}>功能类型: {functionText[obj.functionType]}</span> }
                { obj.fundData?.cardTitle && <span>标题: {obj.fundData.cardTitle}</span> }
            </div>
        )
    }
    const goUp = (e: any, item: iBannerConfigData, index: number) => {
        e.stopPropagation();

        if (index === 0) {
            message.info('已在最上方')
            return
        }
        let arr = [...bannerConfigData];
        arr.splice(index, 1);
        arr.splice(index - 1, 0, item);
        handleData(arr);
        if (!isModify) handleModify(true);
    }
    const goDown = (e: any, item: iBannerConfigData, index: number) => {
        e.stopPropagation();
        if (index === bannerConfigData?.length - 1) {
            message.info('已在最下方')
            return
        }
        let arr = [...bannerConfigData];
        arr.splice(index, 1);
        arr.splice(index + 1, 0, item);
        handleData(arr);
        if (!isModify) handleModify(true);
    }
    return <div style={{marginTop: 40}}>
        <h1 className="g-fs28 f-bold">Banner位：</h1>
        <Collapse>
            {
                bannerConfigData?.map((item: iBannerConfigData, index: number) => {
                    return (
                        <Panel 
                            header={panelHeader(item)} 
                            key={item.key}
                            extra={<div style={{marginTop: -5}}><Button onClick={(e) => { goUp(e, item, index) }}>上移</Button><Button onClick={(e) => { goDown(e, item, index) }}>下移</Button></div>}
                        >
                            <WrappedBannerCard index={index} allData={bannerConfigData} currentData={item} handleData={(data) => modifyBannerConfigData(data, index)} handleDelete={() => handleDelete(index)}/>
                        </Panel>
                    )
                })
            }
            
        </Collapse>
        <div style={{marginTop: 20}}>
          <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
        </div>
    </div>
}
export default React.memo(BannerConfig);
