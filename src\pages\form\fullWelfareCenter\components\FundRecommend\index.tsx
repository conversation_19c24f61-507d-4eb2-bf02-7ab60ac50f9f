import React from 'react';
import { Form, Input, Radio, Select } from 'antd';
import FundManagerForm from './FundManagerForm';
import FundTrendForm from './FundTrendForm';
import NoFundTrendForm from './NoFundTrendForm';
import { INCOME_RANGE_MAP } from '../../const';

const { Option } = Select;

const FundRecommendForm = props => {
  const { getFieldDecorator, isNew, setIsNew, isShowFundTrend, setIsShowFundTrend } = props;

  return (
    <div>
      <h2>主推基金</h2>
      <Form.Item label="主标题">
        {getFieldDecorator('fundRecommend.mainFund.mainTitle', {
          rules: [{ required: true, message: '请输入主标题' }],
        })(<Input />)}
      </Form.Item>
      <div>
        7-13,FE2032;1-6,FE2032（表明第7到13个字采用FE2032色值，支持多个文字色值不同，以英文分号为间隔，优先采用FE2032）
      </div>
      <Form.Item label="主标题色值设置">
        {getFieldDecorator('fundRecommend.mainFund.colorValueSetting')(<Input />)}
      </Form.Item>
      <Form.Item label="推荐标签">
        {getFieldDecorator('fundRecommend.mainFund.recommendTag', {
          rules: [{ required: true, message: '请输入推荐标签' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="推荐逻辑">
        {getFieldDecorator('fundRecommend.mainFund.recommendLogic', {
          rules: [{ required: true, message: '请输入推荐逻辑' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="是否是新发基金">
        {getFieldDecorator('fundRecommend.mainFund.isNew', {
          rules: [{ required: true, message: '请选择是否是新发基金' }],
        })(
          <Radio.Group onChange={e => setIsNew(e.target.value === '1')}>
            <Radio value="1">是</Radio>
            <Radio value="0">否</Radio>
          </Radio.Group>,
        )}
      </Form.Item>
      {isNew ? (
        <FundManagerForm getFieldDecorator={getFieldDecorator} />
      ) : (
        <>
          <Form.Item label="是否展示基金走势">
            {getFieldDecorator('fundRecommend.mainFund.isShowFundTrend', {
              rules: [{ required: true, message: '请选择是否展示基金走势' }],
            })(
              <Radio.Group onChange={e => setIsShowFundTrend(e.target.value === '1')}>
                <Radio value="1">是</Radio>
                <Radio value="0">否</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          {isShowFundTrend ? (
            <FundTrendForm getFieldDecorator={getFieldDecorator} />
          ) : (
            <NoFundTrendForm getFieldDecorator={getFieldDecorator} />
          )}
        </>
      )}
      <Form.Item label="跳转链接">
        {getFieldDecorator('fundRecommend.mainFund.jumpUrl')(<Input />)}
      </Form.Item>
      <Form.Item label="购买按钮文描">
        {getFieldDecorator('fundRecommend.mainFund.buyButton', {
          rules: [{ required: true, message: '请输入购买按钮文描' }],
        })(<Input />)}
      </Form.Item>
      <h2>次基金1</h2>
      <Form.Item label="主标题">
        {getFieldDecorator('fundRecommend.subFund[0].mainTitle', {
          rules: [{ required: true, message: '请输入主标题' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="基金代码">
        {getFieldDecorator('fundRecommend.subFund[0].fundCode', {
          rules: [{ required: true, message: '请输入主标题' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="收益区间选择">
        {getFieldDecorator('fundRecommend.subFund[0].incomeRange', {
          rules: [{ required: true, message: '请输入收益区间' }],
        })(
          <Select>
            {Array.from(INCOME_RANGE_MAP.entries()).map(([key, value]) => {
              return (
                <Option value={key} key={key}>
                  {value}
                </Option>
              );
            })}
          </Select>,
        )}
      </Form.Item>
      <Form.Item label="跳转链接">
        {getFieldDecorator('fundRecommend.subFund[0].jumpUrl')(<Input />)}
      </Form.Item>
      <h2>次基金2</h2>
      <Form.Item label="主标题">
        {getFieldDecorator('fundRecommend.subFund[1].mainTitle', {
          rules: [{ required: true, message: '请输入主标题' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="基金代码">
        {getFieldDecorator('fundRecommend.subFund[1].fundCode', {
          rules: [{ required: true, message: '请输入主标题' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="收益区间选择">
        {getFieldDecorator('fundRecommend.subFund[1].incomeRange', {
          rules: [{ required: true, message: '请输入收益区间' }],
        })(
          <Select>
            {Array.from(INCOME_RANGE_MAP.entries()).map(([key, value]) => {
              return (
                <Option value={key} key={key}>
                  {value}
                </Option>
              );
            })}
          </Select>,
        )}
      </Form.Item>
      <Form.Item label="跳转链接">
        {getFieldDecorator('fundRecommend.subFund[1].jumpUrl')(<Input />)}
      </Form.Item>
      <h2>查看更多基金</h2>
      <Form.Item label="跳转链接">
        {getFieldDecorator('fundRecommend.seeMoreUrl', {
          rules: [{ required: true, message: '请输入跳转链接' }],
        })(<Input />)}
      </Form.Item>
    </div>
  );
};

export default FundRecommendForm;
