{"type": "page", "title": "文本审核", "body": [{"type": "crud", "syncLocation": false, "api": {"url": "/aigc/audit/page", "method": "post", "data": {"userLabel": "${userTag|default:undefined}", "industryLabel": "${contentWord|default:undefined}", "auditStatus": "${auditStatus|default:undefined}", "urlContent": "${title|default:undefined}", "fileType": "0", "minModifyTime": "${dateRange|split|nth: 0|default:undefined}", "maxModifyTime": "${dateRange|split|nth: 1|default:undefined}", "offSet": "${currentPage}", "pageSize": "${pageSize}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.aigcAuditInfoList || []\r\n  }\r\n}"}, "headerToolbar": ["bulkActions"], "bulkActions": [{"label": "批量通过", "actionType": "ajax", "api": {"url": "/aigc/audit/approve", "sendOn": "${LENGTH(ARRAYMAP(ARRAYFILTER(items, item => item.auditStatus != 1), item => item.id)) > 0}", "method": "post", "data": {"approve": "1", "idList": "${ARRAYMAP(ARRAYFILTER(items, item => item.auditStatus != 1), item => item.id)}"}}}, {"label": "批量不通过", "actionType": "ajax", "api": {"url": "/aigc/audit/approve", "sendOn": "${LENGTH(ARRAYMAP(ARRAYFILTER(items, item => (item.auditStatus != 1 && item.auditStatus != 2)), item => item.id)) > 0}", "method": "post", "data": {"approve": "2", "idList": "${ARRAYMAP(ARRAYFILTER(items, item => (item.auditStatus != 1 && item.auditStatus != 2)), item => item.id)}"}}}, {"label": "批量删除", "actionType": "ajax", "api": {"url": "/aigc/audit/delete", "method": "post", "data": {"idList": "${ARRAYMAP(items, item => item.id)}"}}}], "footerToolbar": ["switch-per-page", "pagination", "statistics"], "columnsTogglable": false, "keepItemSelectionOnPageChange": false, "checkOnItemClick": true, "alwaysShowPagination": true, "name": "crud_materialContent", "className": "text-lg", "filterTogglable": true, "pageField": "currentPage", "perPageField": "pageSize", "showPerPage": true, "showPageInput": true, "perPageAvailable": [40, 100, 200, 500], "perPage": 40, "filter": {"title": "", "body": [{"type": "input-text", "name": "title", "label": "标题", "clearable": true, "clearValueOnEmpty": true, "size": "sm", "required": false}, {"type": "select", "name": "userTag", "label": "文本使用词（用户侧）", "clearable": true, "clearValueOnEmpty": true, "size": "md", "required": false, "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "return {\r\n  status: 0,\r\n  msg: '', data: payload.data.contentUserList\r\n}", "cache": 2000}}, {"type": "select", "name": "contentWord", "label": "文本使用词（基金侧）", "clearable": true, "clearValueOnEmpty": true, "size": "md", "required": false, "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "return {\r\n  status: 0,\r\n  msg: '', data: payload.data.contentFundList\r\n}", "cache": 2000}}, {"type": "select", "label": "状态", "name": "auditStatus", "multiple": false, "options": [{"label": "全部", "value": ""}, {"label": "待审核", "value": "0"}, {"label": "审核通过", "value": "1"}, {"label": "审核不通过", "value": "2"}], "value": "", "clearValueOnEmpty": true, "size": "sm"}, {"type": "input-datetime-range", "name": "date<PERSON><PERSON><PERSON>", "label": "入库时间", "format": "YYYY-MM-DD HH:mm:ss", "placeholder": "-", "className": "text-lg", "clearable": true, "size": "md"}, {"type": "submit", "label": "查询", "level": "primary"}, {"type": "reset", "label": "重置", "level": "primary"}, {"type": "action", "label": "导出模版", "actionType": "download", "api": {"url": "/aigc/audit/download/excel", "method": "get", "data": {"type": "content"}}}, {"type": "button", "label": "批量上传", "onEvent": {"click": {"actions": [{"dialog": {"type": "dialog", "title": "批量上传", "body": [{"type": "form", "title": "表单", "reload": "crud_materialContent", "api": {"url": "/aigc/audit/upload/content/excel", "method": "post", "adaptor": "return {\r\n  status: payload.status_code, \r\n  msg: `上传成功，失败${payload.data.failCount}条`\r\n};"}, "body": [{"type": "input-file", "name": "multipartFile", "label": "上传Excel", "accept": "*", "drag": true, "asBlob": true, "hideUploadButton": true, "autoUpload": false}], "wrapWithPanel": false}], "showCloseButton": true, "showErrorMsg": false, "showLoading": true, "closeOnEsc": true, "id": "u:788364bf06fc", "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"componentId": "u:788364bf06fc", "groupType": "component", "actionType": "confirm"}]}}, "level": "primary"}]}, "actionType": "dialog"}]}}, "level": "enhance"}], "actions": [], "wrapWithPanel": true}, "columns": [{"name": "id", "label": "ID", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "标题", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "userLabel", "label": "文本使用词（用户侧）", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "industryLabel", "label": "文本使用词（基金侧）", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "fileVersion", "label": "优先级", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "auditStatus", "label": "审核状态", "type": "mapping", "map": {"0": "<span class='label label-warning'>待审核</span>", "1": "<span class='label label-success'>审核通过</span>", "2": "<span class='label label-default'>审核不通过</span>"}, "placeholder": "-", "className": "text-lg"}, {"name": "modifyTime", "label": "入库时间", "type": "text", "placeholder": "-", "className": "text-lg"}, {"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "通过", "actionType": "ajax", "level": "link", "className": "text-info", "disabledOn": "auditStatus == 1", "api": {"url": "/aigc/audit/approve", "method": "post", "data": {"approve": "1", "idList": ["${id}"]}}}, {"type": "button", "label": "不通过", "actionType": "ajax", "level": "link", "className": "text-info", "disabledOn": "auditStatus == 1 || auditStatus == 2", "api": {"url": "/aigc/audit/approve", "method": "post", "data": {"approve": "2", "idList": ["${id}"]}}}, {"type": "button", "label": "删除", "actionType": "ajax", "level": "link", "className": "text-info", "api": {"url": "/aigc/audit/delete", "method": "post", "data": {"idList": ["${id}"]}}, "reload": "crud_materialContent"}]}]}]}