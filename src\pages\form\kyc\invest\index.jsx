
import React, { useEffect, useState } from 'react';
import { Button, message} from 'antd';
import api from 'api';
import FORM_CONFIG from './form.json';
import FormRender from "form-render/lib/antd";
import styles from '../style.less';

const {fetchKycInvest,postKycInvest} = api;
export default function () {

  const [init, setInit] = useState(false);
  const [formData,setFormState] = useState({});
  const [formConfig, setFormConfig] = useState(FORM_CONFIG.propsSchema);
  const [uiSchema, setUiSchema] = useState({});
  const [valid, setValid] = useState([]);
  useEffect(() => {
    fetchKycInvest().then((res) => {
      try {
        res = JSON.parse(res.data);
        console.log(res);
        if (res) {
          setFormState(res)
        }

      } catch (e) {
        console.warn(e)
      }
      setInit(true)
    })
  },[init])



  let onSubmit = () => {
    if (valid.length > 0) {
      message.error(`校验未通过字段：${valid.toString()}`);
    }else {
      let _formData = formData;
      postKycInvest({
        value:JSON.stringify(_formData)
      }).then((res)=>{
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success('提交成功！');
          }
        } catch (e) {
          message.error(e.message);
        }
      })
    }

  }

  if (!init) return '加载中'
  return(
    <div className={styles['kycForm']}>
      {console.log(formData,'11')}
      <FormRender
        propsSchema={formConfig}
        displayType='row'
        uiSchema={uiSchema}
        formData={formData}
        onChange={setFormState}
        onValidate={setValid}
      />
      <Button type="primary" onClick={onSubmit}>提交</Button>
    </div>

  )

}

