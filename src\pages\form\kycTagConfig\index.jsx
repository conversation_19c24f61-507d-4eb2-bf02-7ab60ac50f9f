
import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>confirm, message, Form, Input, Select, Row } from 'antd'
import { post } from 'functions/request'
import api from 'api'
const { Option } = Select;
const { fetchCBAS } = api;

export default function () {
    const [logicOptions, setLogicOptions] = useState([])
    const [tags, setTags] = useState([])
    const [curTags, setCurTags] = useState([])
    useEffect(() => {
        setLogicOptions([{ key: "equal", label: "等于" },
        { key: "notEqual", label: "不等于" },
        { key: "in", label: "字符串包含" },
        { key: "notIn", label: "字符串不包含" },
        { key: "isSet", label: "有值" },
        { key: "notSet", label: "没值" },
        { key: "greater", label: "大于" },
        { key: "less", label: "小于" },
        { key: "greaterEqual", label: "大于等于" },
        { key: "lessEqual", label: "小于等于" }
        ])
    }, [])
    const [formData, setFormData] = useState([])
    useEffect(() => {
        doRequest("query", {}).then(data => {
            if (data && data.code === "0000") {
                const keys = Object.keys(data.data);
                setFormData(keys.map(key => data.data[key]))
            }

        })
        fetchCBAS({}).then((data) => {
            if (data && data.code === "0000") {
                setTags(data.data)
                setCurTags(data.data)
            }
        })
    }, [])


    const save = (item, outerIndex) => {
        const { kycs } = item;
        if (!kycs || kycs.length === 0) {
            message.error("标签组至少包含一行")
            return;
        }
        for (let kyc of kycs) {
            if (!kyc.label) {
                message.error("kyc标签不能为空")
                return;
            }
            if (kyc.logic === "isSet" || kyc.logic === "notSet") {
                kyc.value = ""
            }
        }

        doRequest("insert", item).then(data => {
            if (data.code === "0000") {
                const kycGroupId = data.kycGroupId;
                if (kycGroupId) {
                    const _formData = formData.slice();
                    _formData[outerIndex].kycGroupId = kycGroupId;
                    setFormData(_formData)
                }
                message.success("保存成功")
            }

        })
    }
    const addGroup = () => {
        const _formData = formData.slice();
        _formData.push({
            "kycGroupId": "",
            "kycs": [
                {
                    "label": "",
                    "logic": "",
                    "value": ""
                }
            ]
        })
        setFormData(_formData)
    }
    const deleteRow = (outerIndex) => {
        const _formData = formData.slice();
        const data = _formData[outerIndex];
        if (data.kycGroupId) {
            doRequest("delete", _formData[outerIndex]).then(data => {
                if (data.code === "0000") {
                    _formData.splice(outerIndex, 1);
                    setFormData(_formData)
                    message.success("删除成功")
                }

            })
        }
        else {
            _formData.splice(outerIndex, 1);
            setFormData(_formData)
        }

    }
    const deleteTag = (outerIndex, innerIndex) => {
        const _formData = formData.slice();
        _formData[outerIndex].kycs.splice(innerIndex, 1);
        setFormData(_formData)

    }
    const onValuesChange = (values, outerIndex, innerIndex) => {
        console.log(values)
        if (values.label) {
            values.value = ""
        }
        if (values.logic === "isSet" || values.logic === "notSet") {
            values.value = ""
        }
        const _formData = formData.slice();
        Object.assign(_formData[outerIndex].kycs[innerIndex], values)
        console.log(JSON.stringify(_formData[outerIndex].kycs[innerIndex]))
        setFormData(_formData)
    }
    const add = (outerIndex) => {
        const _formData = formData.slice();
        _formData[outerIndex].kycs.push(
            {
                "label": "",
                "logic": "",
                "value": ""
            }
        )
        setFormData(_formData)
    }
    const formItemLayout = {
        labelCol: {
            xs: { span: 24 },
            sm: { span: 4 },
        },
        wrapperCol: {
            xs: { span: 24 },
            sm: { span: 18 },
        },
    };

    const doRequest =async (type, item) => {

        let url = "/yytjapi/config/activity/kycgroup18599/enter"
        if (window.location.hostname === "localhost") {
            url = "http://febs.5ifund.com:8080/yytjapi/config/activity/kycgroup18599/enter";
        }

        return post(url, { type: type, value: JSON.stringify(item) })
        // .then((data) => {
        //     console.log(data)
        //     if (!data ||data.code !== "0000") {
        //         message.error("出错")
        //     }
        // })
      

    }
    const getValueTags = (item, outerIndex, innerIndex) => {

        const label_code = item.label;
        const val = item.value;
        const tagData = tags.filter(tag => tag.label_code === label_code);
        let disable = false;
        if (item.logic === "isSet" || item.logic === "notSet") {
            disable = true
        }
        if (!tagData[0]) {
            return <Input onChange={(e) => onValuesChange({ value: e.target.value }, outerIndex, innerIndex)} defaultValue={val} disabled={disable}></Input>
        }
        const { is_enum, enum_value, data_type } = tagData[0];


        if (is_enum === "1") {//枚举值
            const values = enum_value.split(",");
            const prop = { onChange: (e) => onValuesChange({ value: e }, outerIndex, innerIndex), defaultValue: val }
            if (values.indexOf(val) === -1) {
                prop.defaultValue = ""
            }
            return <Select {...prop} value={val} disabled={disable}>
                {values.map(item => {
                    return <Option value={item}>{item}</Option>
                })}
            </Select>
        }
        else {
            return <Input onChange={(e) => onValuesChange({ value: e.target.value }, outerIndex, innerIndex)} value={val} defaultValue={val} disabled={disable}></Input>
        }

    }
    const handleSearch = (value) => {
        console.log(value)
        if (value) {
            const _tags = tags.filter(item =>  (item.label_name + item.label_code).indexOf(value) > -1)
            console.log(_tags)
            setCurTags(_tags)
        } else {
            setCurTags(tags)
        }
    }
    return <>
        <Button type="primary" onClick={addGroup}>新增分组</Button>
        {formData.map((row, outerIndex) => {
            const { kycs } = row
            //(values) => { onValuesChange(values, row) }

            return <Form key={outerIndex} style={{ border: "1px solid #f0f0f0", marginTop: "20px" }} {...formItemLayout}>
                <Form.Item label="kyc标签分组id" labelCol={{ span: 2 }} name="kycGroupId" >
                    <Input value={row.kycGroupId} disabled style={{ display: 'inline-block', width: '200px' }} />
                    <Button type="primary" style={{ marginLeft: "20px" }} onClick={() => { add(outerIndex) }}>添加</Button>
                    <Popconfirm
                        title="确定要删除这个标签分组吗？"
                        onConfirm={() => { deleteRow(outerIndex) }}
                        onCancel={() => { }}
                        okText="确定"
                        cancelText="取消"
                    >
                        <Button type="primary" style={{ marginLeft: "20px" }} >删除</Button>
                    </Popconfirm>
                    <Button type="primary" style={{ marginLeft: "20px" }} onClick={() => save(row, outerIndex)}>保存</Button>
                </Form.Item >
                {
                    kycs && kycs.map((item, innerIndex) => {
                        return <>
                            <Row>


                                <Form.Item label="kyc标签" name="label" style={{ display: "inline-block", width: "300px" }} >
                                    <Select defaultValue={item.label} showSearch 
                                        onChange={(e) => onValuesChange({ label: e }, outerIndex, innerIndex)}
                                        optionFilterProp={"children"}
                                       // onSearch={(value) => handleSearch(value)}
                                    >
                                        {
                                            curTags.map((item) => {
                                                return <Option value={item.label_code}>{item.label_name}</Option>
                                            })
                                        }
                                    </Select>

                                </Form.Item>
                                <Form.Item label="逻辑" name="logic" style={{ display: "inline-block", width: "300px" }}  >
                                    <Select defaultValue={item.logic} onSelect={(e) => onValuesChange({ logic: e }, outerIndex, innerIndex)} >
                                        {
                                            logicOptions.map(option => {
                                                return <Option value={option.key}>{option.label}</Option>
                                            })
                                        }
                                    </Select>
                                </Form.Item>
                                <Form.Item label="值" name="value" style={{ display: "inline-block", width: "200px" }} >
                                    {
                                        getValueTags(item, outerIndex, innerIndex)
                                    }


                                </Form.Item>

                                <Form.Item style={{ display: "inline-block", width: "200px" }} >
                                    {/* <Popconfirm
                                    title="确定要删除吗？"
                                    onConfirm={() => { deleteTag(outerIndex, innerIndex) }}
                                    onCancel={() => { }}
                                    okText="确定"
                                    cancelText="取消"
                                > */}
                                    <Button onClick={() => { deleteTag(outerIndex, innerIndex) }}>删除</Button>
                                    {/* </Popconfirm> */}
                                </Form.Item>

                            </Row>
                        </>

                    })
                }
            </Form>

        })}
    </>
}