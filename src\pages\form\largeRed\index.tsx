import React, { useEffect, useState } from 'react';
import { Switch, Input, Row, Col, message, Button } from 'antd';
import api from 'api';
const { getLargeRedBank, fetchLargeRed, postLargeRed } = api
export default function () {

    const [status, setStatus] = useState(false)  //功能开关
    const [initPageText, setInitPage] = useState('')  //页面初始化文案
    const [bankList, setBank] = useState<Array<any>>([])    //银行列表
    const [bankListText, setBankText] = useState<Array<any>>([])    //银行列表文案
    const [guideText, setGuide] = useState('')  //引导文案
    const [benefitText, setBenefit] = useState('')      //福利文案
    const [distributeText, setDistribute] = useState('')    //福利分发文案

    let style1 = {
        fontSize: '20px',
        marginBottom: '20px',
        fontWeight: 'bold'
    }
    let rowStyle = {
        width: '80%'
    }

    useEffect(() => {
        //获取银行信息列表
        getLargeRedBank().then((data: any) => {
            if (data.retcode === '0000') {
                setBank([].concat(data.data))
                setBankText([].concat(data.data))
            } else {
                message.error('接口异常')
            }
            fetchLargeRed().then((res: any) => {
                try {
                    res = JSON.parse(res.data);
                    console.log(res);
                    if (res) {
                        setStatus(res.status)
                        setInitPage(res.initPageText)
                        setBenefit(res.benefitText)
                        setGuide(res.guideText)
                        setDistribute(res.distributeText)
                        const _bankData = data.data
                        let _bank: any = []
                        let _bank2: any = []
                        console.log(_bank)
                        _bankData.map((val: any, index: number) => {
                            let target = res.bankList?.find((item: any) => item.vc_bankcode === val.vc_bankcode)
                            let target2 = res.bankListText?.find((item: any) => item.vc_bankcode === val.vc_bankcode)

                            let _bankItem = {
                                text: target?.text || '',
                                vc_bankcode: val.vc_bankcode,
                                vc_bankname: val.vc_bankname
                            }
                            let _bankTextItem = {
                                textStart: target2?.textStart || '',
                                textEnd: target2?.textEnd || '',
                                jump: target2?.jump || '',
                                vc_bankcode: val.vc_bankcode,
                                vc_bankname: val.vc_bankname
                            }
                            _bank.push(_bankItem)
                            _bank2.push(_bankTextItem)
                        })
                        console.log(_bank, _bank2)
                        setBank(_bank)
                        setBankText(_bank2)
                    }

                } catch (e) {
                  console.warn(e)
                }
              })
        })
    }, [])
    function handleBank(value: any, index: number) {
        let _bankList = [].concat(bankList)
        _bankList[index].text = value
        setBank(_bankList)
    }
    function handleBank2(value: any, index: number, type: string) {
        let _bankListText = [].concat(bankListText)
        _bankListText[index][type] = value
        setBankText(_bankListText)
    }
    function submit() {
        let sendData = {
            status,
            initPageText,
            bankList,
            bankListText,
            guideText,
            benefitText,
            distributeText
        }
        console.log(sendData)
        postLargeRed({
            value: JSON.stringify(sendData)
        }).then((res: any) => {
            try {
                if (res.code !== '0000') {
                    message.error(res.message);
                } else {
                    message.success('提交成功！');
                }
            } catch (e) {
                message.error(e.message);
            }
        })
    }
    return (<div style={rowStyle}>
        <Row >
            <Col span={6}>
                <p>功能开关</p>
            </Col>
            <Col offset={1} span={17}>
                <Switch checkedChildren="开" unCheckedChildren="关" checked={status} onChange={setStatus} />
            </Col>
        </Row>
        <p style={style1}>购买充值变更银行卡大额转账入口标签</p>
        <Row>
            <Col span={6}>
                <p>初始化页面文案</p>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => setInitPage(e.target.value)}
                    value={initPageText}
                />
            </Col>
        </Row>
        <p style={style1}>大额转账流程标签</p>
        {
            bankList.map((val: any, index: number) => {
                return <Row key={index} style={{ marginBottom: '5px' }}>
                    <Col span={6}>
                        <p>{val.vc_bankname}</p>
                    </Col>
                    <Col offset={1} span={17}>
                        <Input
                            onChange={(e) => handleBank(e.target.value, index)}
                            value={val.text || ''}
                        />
                    </Col>
                </Row>
            })
        }

        <p style={style1}>大额转账攻略文案</p>
        <Row>
            <Col span={6}>
                <p>引导文案</p>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => setGuide(e.target.value)}
                    value={guideText}
                />
            </Col>
        </Row>
        <Row>
            <Col span={6}>
                <p>福利文案</p>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => setBenefit(e.target.value)}
                    value={benefitText}
                />
            </Col>
        </Row>
        <Row>
            <Col span={6}>
                <p>福利发放文案</p>
            </Col>
            <Col offset={1} span={17}>
                <Input
                    onChange={(e) => setDistribute(e.target.value)}
                    value={distributeText}
                />
            </Col>
        </Row>
        <p style={style1}>大额转账提示文案（不受功能开关影响）</p>
        <Row style={{ marginBottom: '5px' }}>
            <Col span={6}>
                <p>银行名称</p>
            </Col>
            <Col offset={1} span={5}>
            <p>纯文本配置</p>
            </Col>
            <Col offset={1} span={5}>
            <p>跳转配置</p>
            </Col>
            <Col offset={1} span={5}>
            <p>纯文本配置</p>
            </Col>
        </Row>
        {
            bankListText.map((val: any, index: number) => {
                return <Row key={index} style={{ marginBottom: '5px' }}>
                    <Col span={6}>
                        <p>{val.vc_bankname}</p>
                    </Col>
                    <Col offset={1} span={5}>
                        <Input
                            onChange={(e) => handleBank2(e.target.value, index, 'textStart')}
                            value={val.textStart || ''}
                        />
                    </Col>
                    <Col offset={1} span={5}>
                        <Input
                            onChange={(e) => handleBank2(e.target.value, index, 'jump')}
                            value={val.jump || ''}
                        />
                    </Col>
                    <Col offset={1} span={5}>
                        <Input
                            onChange={(e) => handleBank2(e.target.value, index, 'textEnd')}
                            value={val.textEnd || ''}
                        />
                    </Col>
                </Row>
            })
        }
        <Button onClick={submit}>保存</Button>
    </div>)

}