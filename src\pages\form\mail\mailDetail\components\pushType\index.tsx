import React, { useState, useEffect } from 'react';
import { Cascader } from 'antd';
type FormRender = {
  name: string;
  onChange: Function;
  value: string;
};
export default function({ name, onChange, value }: FormRender) {
  // 通过query简单判断是否为编辑页
  // let query = location.href.match(/\?id=.+/g);
  const options = [
    {
      value: '1',
      label: '资讯与营销类消息',
      children: [
        {
          value: 'news',
          label: '最新资讯',
        },
        {
          value: 'activity',
          label: '限时活动',
        },
      ],
    },
    {
      value: '2',
      label: '服务与提醒类消息',
      children: [
        {
          value: 'community',
          label: '社区互动',
        },
        {
          value: 'tradeAlert',
          label: '交易提醒',
        },
        {
          value: 'service',
          label: '我的服务',
        },
      ],
    },
  ];
  const [typeDefault, setDefault] = useState<string[]>([]);
  useEffect(() => {
    if (value) {
      if (value === 'news') {
        setDefault(['1', value]);
      } else if (value === 'activity') {
        setDefault(['1', value]);
      } else {
        setDefault(['2', value]);
      }
    }
  }, [value]);
  const onSelectChange = (value: string[]) => {
    onChange(name, value[1]);
    setDefault(value);
  };
  return (
    <>
      <Cascader
        value={typeDefault}
        options={options}
        onChange={onSelectChange}
        placeholder="请选择"
        // disabled={query ? true : false}
      />
    </>
  );
}
