import React, { useState, useEffect, FC } from 'react';
import moment from 'moment';
import { Button, Input, message, Popconfirm, DatePicker } from 'antd';

import { ConfigData } from '../type';
import HotSpot from './HotSpot';
import ScreemStream from './ScreemStream';
import SectorAnalyze from './SectorAnalyze';
import ManagerAnalyze from './ManagerAnalyze';

import Banner from './Banner';
import Infomation from './Infomation';
import InputTitle from './InputTitle';
import FundRecommend from './FundRecommend';
import StreamCard from './StreamCard';
import SplitArea from './SplitArea';
import { configItemhelper, mapPropToName } from './configItemhelper';
import ChanceLift from './ChanceLift';
import api from 'api';
import { HOTSPOT_KEY, mergePoolToBase } from '../utils';
const { postHashDel } = api;
export interface IProps {
  baseData: ConfigData;
  basePool?: {
    hotspots: any[];
    relateInfos: any[];
  };
  onSave: () => void;
  onDel: () => void;
}

const ConfigItem: FC<IProps> = ({ onSave, baseData, basePool, onDel }) => {
  const [isEdit, setIsEdit] = useState(false);
  const [data, setData] = useState<ConfigData>(mergePoolToBase(baseData, basePool));

  const handleChange = (name, value) => {
    setData({
      ...data,
      [name]: value,
    });
  };
  return (
    <div>
      <div
        style={{
          backgroundColor: '#95badd',
          height: '44px',
          display: 'flex',
          justifyContent: 'flex-end',
          alignItems: 'center',
          marginBottom: '12px',
        }}
      >
        {!isEdit && (
          <Button
            onClick={() => {
              setIsEdit(true);
            }}
          >
            编辑
          </Button>
        )}
        {isEdit && (
          <Button
            type={'primary'}
            onClick={() => {
              try {
                data.hotspots=data.hotspots?.filter((item,index)=>index<3)||[]
                data.infomation=data.infomation?.filter((item,index)=>index<5)||[]
                configItemhelper.checkData(data);

                onSave(data);

                setIsEdit(false);
              } catch (e) {
                message.error(`请确认字段-${e.message}`);
              }
            }}
          >
            保存
          </Button>
        )}
        <Popconfirm
          title={'请确认是否删除'}
          okText={'确认'}
          cancelText={'取消'}
          onConfirm={() => onDel(data.index)}
        >
          <Button style={{ marginLeft: '12px' }} type={'primary'} danger>
            删除
          </Button>
        </Popconfirm>
      </div>
      <Input
        disabled={!isEdit}
        style={{ marginBottom: 10, width: 800 }}
        addonBefore={<InputTitle title={mapPropToName['detailLink']} />}
        value={data.detailLink}
        onChange={e => setData({ ...data, detailLink: e.target.value })}
      ></Input>
     <div>
     <Input
        disabled={!isEdit}
        style={{ marginBottom: 10, width: 300 }}
        addonBefore={<InputTitle title={mapPropToName['sectorTitle']} />}
        value={data.sectorTitle}
        onChange={e => setData({ ...data, sectorTitle: e.target.value })}
      ></Input>
      <Input
        disabled={!isEdit}
        style={{ marginBottom: 10, width: 300 }}
        addonBefore={<InputTitle title={mapPropToName['topTag']} isRequired={false} />}
        value={data.topTag}
        onChange={e => setData({ ...data, topTag: e.target.value })}
      ></Input>
      <Input
        disabled={!isEdit}
        style={{ marginBottom: 10, width: 300 }}
        addonBefore={<InputTitle title={mapPropToName['hotLogic']} isRequired={false} />}
        value={data.hotLogic}
        onChange={e => setData({ ...data, hotLogic: e.target.value })}
      ></Input>
      <Input
        disabled={!isEdit}
        style={{ marginBottom: 10, width: 600 }}
        addonBefore={<InputTitle title={mapPropToName['chanceTitle']} />}
        value={data.chanceTitle}
        onChange={e => setData({ ...data, chanceTitle: e.target.value })}
      ></Input>
      <Input
        disabled={!isEdit}
        style={{ marginBottom: 10, width: 600 }}
        addonBefore={<InputTitle title={mapPropToName['tabTag']} isRequired={false}/>}
        value={data.tabTag}
        onChange={e => setData({ ...data, tabTag: e.target.value })}
      ></Input>
     </div>
     <div>
        <Input
          disabled={!isEdit}
          style={{ marginBottom: 10, width: 600 }}
          addonBefore={<InputTitle title={mapPropToName['marker']} isRequired={false}/>}
          value={data.marker}
          onChange={e => setData({ ...data, marker: e.target.value })}
          maxLength={4}
        ></Input>
        <div style={{ marginBottom: 10, width: 300 }}>
          <InputTitle title={mapPropToName['markerStartTime']} isRequired={false} />
          <DatePicker
            value={data.markerStartTime ? moment(data.markerStartTime) : ''}
            onChange={(date, dateString) => handleChange('markerStartTime', dateString)}
            showTime
            disabled={!isEdit}
            placeholder={'请选择日期'}
          />
        </div>
        <div style={{ marginBottom: 10, width: 300 }}>
          <InputTitle title={mapPropToName['markerEndTime']} isRequired={false} />
          <DatePicker
            value={data.markerEndTime ? moment(data.markerEndTime) : ''}
            onChange={(date, dateString) => handleChange('markerEndTime', dateString)}
            showTime
            disabled={!isEdit}
            placeholder={'请选择日期'}
          />
        </div>
     </div>

      <SplitArea />
      <HotSpot
        disabled={!isEdit}
        data={data.hotspots}
        onChange={v => handleChange('hotspots', v)}
      />
      <SplitArea />
      <ScreemStream
        disabled={!isEdit}
        data={data.screenStream}
        onChange={v => handleChange('screenStream', v)}
      />
      <SplitArea />
      <SectorAnalyze
        disabled={!isEdit}
        data={data.sectorAnalyze}
        onChange={v => handleChange('sectorAnalyze', v)}
      />
      <SplitArea />
      <ManagerAnalyze
        disabled={!isEdit}
        data={data.managerAnalyze}
        onChange={v => handleChange('managerAnalyze', v)}
      />
      <SplitArea />
      <div>
        <h3>机会顺风车</h3>
        <ChanceLift
          disabled={!isEdit}
          data={data.chanceLift}
          onChange={v => handleChange('chanceLift', v)}
        />
        {/* <FundRecommend
          disabled={!isEdit}
          data={data.fundRecommend}
          onChange={v => handleChange('fundRecommend', v)}
        />
        <StreamCard
          disabled={!isEdit}
          data={data.streamCard}
          onChange={v => handleChange('streamCard', v)}
        /> */}
      </div>
      <SplitArea />
      <Banner disabled={!isEdit} data={data.banner} onChange={v => handleChange('banner', v)} />
      <SplitArea />
      <Infomation
        disabled={!isEdit}
        data={data.infomation}
        onChange={v => handleChange('infomation', v)}
      />
    </div>
  );
};
export default ConfigItem;
