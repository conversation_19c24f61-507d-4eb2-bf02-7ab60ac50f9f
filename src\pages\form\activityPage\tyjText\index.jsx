import React, { useState, useEffect } from 'react';
import { Button, message } from 'antd';
import api from 'api';
import PacketFrom from './tyjText';

const { fetchTyjTextList, postTyjText } = api;

function queryTyjTextList() {
  const dataToSend = { type: 'query' };
  return new Promise((resolve, reject) => {
    fetchTyjTextList(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data.data && JSON.parse(data.data));
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

// function deleteKcyMap(value) {
//   if (!value) message.info('缺少必要信息');
//   const dataToSend = { type: 'delete', value };
//   return new Promise((resolve, reject) => {
//     PostTyjText(dataToSend).then(data => {
//       if (data.code === '0000') {
//         resolve(data.data);
//       } else {
//         message.info(data.message);
//         reject();
//       }
//     }).catch(e => {
//       message.info(e.message);
//     });
//   });
// }

function addOrEditTyjText(value) {
  const dataToSend = { value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    postTyjText(dataToSend).then(data => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch(e => {
      message.info(e.message);
    });
  });
}

export default function KycMap() {
  const [tyjTexts, setTyjTexts] = useState([]); // kyc列表

  useEffect(() => {
    queryTyjTextList().then(data => {

      // const _kycList = Object.keys(data).map(key => data[key]);
      setTyjTexts(data||[]);
    });
  }, []);

  function addActivityListItem() {
    const _activityList = JSON.parse(JSON.stringify(tyjTexts));
    _activityList.push({});
    setTyjTexts(_activityList);
  }

  function submitPacketForm(data,index) {
    const _activityList = JSON.parse(JSON.stringify(tyjTexts));
    _activityList[index] = data;
    addOrEditTyjText(_activityList).then(data => {
      message.info('操作成功');
    });
  }

  return (
    <div style={{ padding: 30 }}>
      {
        tyjTexts.length ? tyjTexts.map((formData, index) =>
            <PacketFrom submit={submitPacketForm} index={index}  data={formData} key={index}/>,
          ) :
          <p className={'f-tc'}>体验金文案配置</p>
      }
      <Button type={'primary'} onClick={addActivityListItem}>新增</Button>
    </div>
  );
}
