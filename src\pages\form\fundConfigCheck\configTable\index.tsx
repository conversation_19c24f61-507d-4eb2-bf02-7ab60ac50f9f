import React, { useState, useEffect } from 'react';
import { Table, ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import classnames from 'classnames';
import styles from './index.less';
import { FundTableData, GdlcTableData, GsdataTableDate } from '../types';
import { renderText, fundRender, gdlcRender, gsdataRender } from './render';
import classNames from 'classnames';

interface tableProp {
  tableData: FundTableData[] | GdlcTableData[],
  type: string
}

export default function ConfigTable({ tableData, type }: tableProp) {
  const columns: any = {
    'fund': [
      {
        key: 'fundName',
        dataIndex: 'fundName',
        title: '基金名称',
        render: fundRender.renderFundName,
        className: styles['m-table-fundName']
      },
      {
        key: 'companyAndManager',
        dataIndex: 'companyAndManager',
        title: '基金公司/经理',
        className: styles['m-table-company'],
        render: fundRender.renderCompanyAndManager,
      },
      {
        key: 'tradeStatus',
        dataIndex: 'tradeStatus',
        title: '当前状态',
        className: styles['m-table-center'],
        render: fundRender.renderTradeStatus,
      },
      {
        key: 'fundType',
        dataIndex: 'fundType',
        title: '基金类型',
        className: styles['m-table-center'],
        render: renderText,
      },
      {
        key: 'soldTime',
        dataIndex: 'soldTime',
        title: '发售周期',
        className: styles['m-table-center'],
        render: fundRender.renderSoldTime,
      },
      {
        key: 'fundAmount',
        dataIndex: 'fundAmount',
        title: () => <p className={styles['m-title-center']}>规模上限<br />（万）</p>,
        render: fundRender.renderFundAmount,
      },
      {
        key: 'riskLevel',
        dataIndex: 'riskLevel',
        title: '风险等级',
        render: renderText,
      },
      {
        key: 'verifyStatus',
        dataIndex: 'verifyStatus',
        title: '准入审核状态',
        className: classnames(styles['m-table-center'], styles['m-table-verify']),
        render: fundRender.renderVerifyStatus,
      },
      {
        key: 'rgRates',
        dataIndex: 'rgRates',
        title: '认购费率',
        render: (value: any) => fundRender.renderRates('rg', value),
      },
      {
        key: 'sgRates',
        dataIndex: 'sgRates',
        title: '申购费率',
        className: classnames(styles['m-table-sgRates']),
        render: (value: any) => fundRender.renderRates('sg', value),
      },
      {
        key: 'shRates',
        dataIndex: 'shRates',
        title: '赎回费率',
        className: classnames(styles['m-table-shRates']),
        render: (value: any) => fundRender.renderRates('sh', value),
      },
      {
        key: 'discount',
        dataIndex: 'discount',
        className: classnames(styles['m-table-center'], styles['m-table-discount']),
        title: () => <p className="m-title-center">折扣率<br />（钱包/银行卡，%）</p>,
        render: fundRender.renderDiscount,
      },
    ],
    'gdlc': [
      {
        key: 'fundName',
        dataIndex: 'fundName',
        title: '基金名称',
        className: styles['m-table-fundName'],
        render: renderText,
      },
      {
        key: 'fundCode',
        dataIndex: 'fundCode',
        title: '基金ID',
        render: renderText,
      },
      {
        key: 'syvalue',
        dataIndex: 'syvalue',
        title: () => <p className={styles['m-title-center']}>业绩比较基准<br />（%）</p>,
        render: gdlcRender.renderRatio
      },
      {
        key: 'sydesc',
        dataIndex: 'sydesc',
        title: '收益描述',
        className: classnames(styles['m-table-desc'], styles['m-table-center']),
        render: renderText,
      },
      {
        key: 'manageRate',
        dataIndex: 'manageRate',
        title: () => <p className={styles['m-title-center']}>管理费<br />（%）</p>,
        render: gdlcRender.renderRatio
      },
      {
        key: 'tradeFeeRatio',
        dataIndex: 'tradeFeeRatio',
        title: () => <p className={styles['m-title-center']}>申购费率<br />（%）</p>,
        render: gdlcRender.renderRatio
      },
      {
        key: 'redeemRate',
        dataIndex: 'redeemRate',
        title: () => <p className={styles['m-title-center']}>赎回费率<br />（%）</p>,
        render: gdlcRender.renderRatio
      },
      {
        key: 'openPeriod',
        dataIndex: 'openPeriod',
        title: '开放期',
        className: styles['m-table-center'],
        render: gdlcRender.renderOpenPeriod,
      },
      {
        key: 'deadline',
        dataIndex: 'deadline',
        title: '最低持有天数',
        render: renderText,
      },
      {
        key: 'risklevel',
        dataIndex: 'risklevel',
        title: '风险等级',
        className: styles['m-table-center'],
        render: gdlcRender.renderRiskLevel
      },
    ],
    'gsdata': [
      {
        key: 'fundName',
        dataIndex: 'fundName',
        title: '基金名称',
        className: styles['m-table-fundName'],
        render: renderText,
      },
      {
        key: 'fundCode',
        dataIndex: 'fundCode',
        title: '基金ID',
        render: renderText,
      },
      {
        key: 'deadline',
        dataIndex: 'deadline',
        title: () => <p className={styles['m-title-center']}>计息期限<br />（天）</p>,
        render: renderText,
      },
      {
        key: 'yearsy',
        dataIndex: 'yearsy',
        title: () => <p className={styles['m-title-center']}>本期收益率<br />（%）</p>,
        render: renderText,
      },
      {
        key: 'openPeriod',
        dataIndex: 'openPeriod',
        title: '产品开放周期',
        render: gsdataRender.renderOpenPeriod,
      },
      {
        key: 'transactioncfmdate',
        dataIndex: 'transactioncfmdate',
        title: '产品确认日',
        classNames: classnames(styles['m-table-center']),
        render: gsdataRender.renderTime,
      },
      {
        key: 'redeemPeriod',
        dataIndex: 'redeemPeriod',
        title: '产品赎回周期',
        render: gsdataRender.renderRedeemPeriod,
      },
      {
        key: 'arrivaldate',
        dataIndex: 'arrivaldate',
        title: '资金到账时间',
        classNames: classnames(styles['m-table-center']),
        render: gsdataRender.renderTime,
      },
      {
        key: 'limitation',
        dataIndex: 'limitation',
        title: () => <p className={styles['m-title-center']}>本期额度<br />（万）</p>,
        render: gsdataRender.renderLimitation,
      },
      {
        key: 'isqs',
        dataIndex: 'isqs',
        title: '到期赎回方式',
        classNames: classnames(styles['m-table-center']),
        render: gsdataRender.renderIsqs,
      },
      {
        key: 'risklevel',
        dataIndex: 'risklevel',
        title: '风险等级',
        classNames: classnames(styles['m-table-center']),
        render: gsdataRender.renderrisklevel
      },
    ]
  }

  return (
    <section className={styles['m-configTable']}>
      <ConfigProvider locale={zhCN}>
        <Table
          dataSource={tableData}
          columns={columns[type]}
          rowKey={(record, index) => index.toString()}
          pagination={false}
        />
      </ConfigProvider>
    </section>
  );
}
