:global {
    .home-header {
        width: 100%;
        display: flex;
        flex-flow: column nowrap;
        margin-bottom: 10px;
        .home-type-title {
            font-weight: bold;
            font-size: 25px;
            color: #000000;
        }
        button {
            width: 100px;
            align-self: flex-end;
        }
    }
    .home-table {
        margin-bottom: 20px;
    }
    .home-footer {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
        button {
            width: 100px;
        }
    }
    .home-add-strategy {
        margin-bottom: 20px;
        .home-addType { 
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            .warning {
                color: red;
                margin-left: 10px;
            }
        }
    }
    .other-product-config {
        width: 900px;
        position: relative;
        border: 1px solid #dddddd;
        padding: 20px;
        margin-bottom: 20px;
        .product-config-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 700px;
            margin-bottom: 20px;
            .product-config {
                width: 500px;
                padding: 20px;
                border: 1px solid #dddddd;
                .home-addType { 
                    display: flex;
                    flex-flow: row nowrap;
                    align-items: center;
                    .warning {
                        color: red;
                        margin-left: 10px;
                    }
                }
            }
            .product-config-button {

            }
        }

        .product-config-move-button {
            position: absolute;
            top: 10px;
            right: 500px;
        }

        .product-delete {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    }
    .operation-config {
        width: 900px;
        position: relative;
        margin-bottom: 20px;
        padding: 20px;
        border: 1px solid #dddddd;
        .home-addType { 
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            .warning {
                color: red;
                margin-left: 10px;
            }
        }
        .operation-delete {
            position: absolute;
            top: 10px;
            right: 10px;
        }
    }
    .input-placeholder {
        input::-webkit-input-placeholder {
            color: rgba(0, 0, 0, 0.65);
        }
          input::-moz-placeholder {
            color: rgba(0, 0, 0, 0.65);
        }
          input:-moz-placeholder {
            color: rgba(0, 0, 0, 0.65);
        }
          input:-ms-input-placeholder {
            color: rgba(0, 0, 0, 0.65);
        }
    }
}