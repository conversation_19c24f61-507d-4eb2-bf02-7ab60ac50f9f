{"type": "page", "title": "基金信息表", "body": [{"type": "crud", "syncLocation": false, "api": {"url": "/aigc/audit/fund/type/page", "method": "post", "data": {"fundType": "${fundType|default:undefined}", "imgWord": "${imgWord|default:undefined}", "contentWord": "${contentWord|default:undefined}", "minModifyTime": "${dateRange|split|nth: 0|default:undefined}", "maxModifyTime": "${dateRange|split|nth: 1|default:undefined}", "offSet": "${current}", "pageSize": "${size}"}, "adaptor": "return {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.aigcFundTypeList || []\r\n  }\r\n}"}, "bulkActions": [], "footerToolbar": ["switch-per-page", "pagination", "statistics"], "columnsTogglable": false, "keepItemSelectionOnPageChange": false, "checkOnItemClick": true, "alwaysShowPagination": true, "name": "crud_materialMatchRule", "headerToolbar": [], "className": "text-lg", "filterTogglable": true, "pageField": "current", "perPageField": "size", "showPerPage": true, "showPageInput": true, "perPageAvailable": [40, 100, 200, 500], "perPage": 40, "filter": {"title": "", "body": [{"type": "input-text", "name": "fundType", "label": "基金分类", "clearable": true, "clearValueOnEmpty": true, "size": "sm", "required": false}, {"type": "select", "name": "imgWord", "label": "图片关键词", "clearable": true, "clearValueOnEmpty": true, "size": "md", "required": false, "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "return {\r\n  status: 0,\r\n  msg: '', data: payload.data.imgFundList\r\n}", "cache": 2000}}, {"type": "select", "name": "contentWord", "label": "文本关键词", "clearable": true, "clearValueOnEmpty": true, "size": "md", "required": false, "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "return {\r\n  status: 0,\r\n  msg: '', data: payload.data.contentFundList\r\n}", "cache": 2000}}, {"type": "input-datetime-range", "name": "date<PERSON><PERSON><PERSON>", "label": "更新时间", "format": "YYYY-MM-DD HH:mm:ss", "placeholder": "-", "className": "text-lg", "clearable": true, "size": "md"}, {"type": "submit", "label": "查询", "level": "primary"}, {"type": "reset", "label": "重置", "level": "primary"}, {"type": "action", "label": "导出模版", "actionType": "download", "api": {"url": "/aigc/audit/download/excel", "method": "get", "data": {"type": "fundType"}}}, {"type": "button", "label": "批量上传", "onEvent": {"click": {"actions": [{"dialog": {"type": "dialog", "title": "批量上传", "body": [{"type": "form", "title": "表单", "reload": "crud_materialMatchRule", "body": [{"type": "input-file", "name": "if_fundRuleFile", "label": "上传Excel", "accept": "*", "drag": true, "receiver": "/aigc/audit/upload/fund/excel", "fileField": "multipartFile", "hideUploadButton": true, "autoUpload": false}], "wrapWithPanel": false}], "showCloseButton": true, "showErrorMsg": false, "showLoading": true, "closeOnEsc": true, "id": "u:788364bf06fc", "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"componentId": "u:788364bf06fc", "groupType": "component", "actionType": "confirm"}]}}, "level": "primary"}]}, "actionType": "dialog"}]}}, "level": "enhance"}], "actions": [], "wrapWithPanel": true}, "columns": [{"name": "id", "label": "ID", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "fundType", "label": "基金分类", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "typePriority", "label": "基金找分类优先级", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "imgWord", "label": "图片使用词（基金侧）", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "contentWord", "label": "文本使用词（基金侧）", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "contentWordDescribe", "label": "文本使用词说明信息（基金侧）", "type": "text", "placeholder": "-", "className": "text-lg"}, {"name": "modifyTime", "label": "更新时间", "type": "text", "placeholder": "-", "className": "text-lg"}]}]}