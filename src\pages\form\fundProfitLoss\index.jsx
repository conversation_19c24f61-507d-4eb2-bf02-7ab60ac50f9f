/*
* 基金盈亏配置
* <AUTHOR>
* @time 2019.12
*/


import React, { Fragment } from 'react';
import {Button, Input, Tabs, Card, message, Col, Row, Switch, Icon, Popconfirm, InputNumber } from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';

var moment = require('moment');
moment().format();

const { TabPane } = Tabs;

const {fetchProfitLoss, postProfitLoss} = api;

@autobind
class fundProfitLoss extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            // tab
            activeKey: '其他',
            panes: [{
                title: '其他',
                content: {
                    type: '其他',
                    upValue: ['', '', ''],
                    downValue: ['', '', ''],
                    show: true
                },
                key: '其他',
                closable: false
            }],
            // input
            addType: '',
            // ref
            _ref: []
        };
    }

    submit = () => {
        const {_ref} = this.state;
        let list = [];
        for (let i = 0; i < _ref.length; i++) {
            list.push(_ref[i].submit());
        }
        console.log(list);
        for (let i = 0; i < list.length; i++) {
            if (list[i].show) {
                if (list[i].upValue.indexOf('') > -1) {
                    message.error(`开关打开的情况下，不允许有空值，请查看${list[i].type}-升至收益率`)
                    return;
                }
                if (list[i].downValue.indexOf('') > -1) {
                    message.error(`开关打开的情况下，不允许有空值，请查看${list[i].type}-降至收益率`)
                    return;
                }
            }
        }
        let _data = {list};
        postProfitLoss({
            value: JSON.stringify(_data)
        }).then((res) => {
            if (res.code === '0000') {
                message.success('发布成功');
                window.location.reload();
            } else {
                message.error(res.message);
            }
        }).catch((e = Error) => {
            message.error(e.message);
        })
    }

    loadData = () => {
        fetchProfitLoss().then(data => {
            let _data = JSON.parse(data.data);
            if (_data) {
                let _list = _data.list;
                let panes = [];
                for (let i = 0; i < _list.length; i++) {
                    let _closable = true;
                    if (_list[i].type === '其他') {_closable = false}
                    panes.push({ title: _list[i].type, content: _list[i], key: _list[i].type, closable: _closable });
                }
                this.setState({panes}, () => {
                    this.state._ref.map((ref, i) => {
                        ref.loadData(_list[i])
                    })
                })
            }
        })     
    }

    saveRef = ref => {
        let {_ref} = this.state;
        _ref.push(ref)
        this.setState({
            _ref
        })
    }

    handleAddTypeChange = e => {
        this.setState({
            addType: e.target.value
        })
    }

    handleTabChange = activeKey => {
        this.setState({ activeKey });
    }

    addTypeTab = () => {
        const { panes, addType } = this.state;
        if (addType === '') {
            message.error('请输入类型！');
            return;
        }
        panes.push({ title: addType, content: addType, key: addType, closable: true });
        this.setState({ panes, activeKey: addType, addType: '' });
    }

    onEdit = (targetKey, action) => {
        this[action](targetKey);
    };

    remove = targetKey => {
        let { activeKey } = this.state;
        let lastIndex;
        this.state.panes.forEach((pane, i) => {
            if (pane.key === targetKey) {
              lastIndex = i - 1;
            }
        });
        const panes = this.state.panes.filter(pane => pane.key !== targetKey);
        const _ref = this.state._ref.filter(ref => ref.props.type !== targetKey);
        if (panes.length && activeKey === targetKey) {
            if (lastIndex >= 0) {
                activeKey = panes[lastIndex].key;
            } else {
                activeKey = panes[0].key;
            }
        }
        this.setState({ panes, activeKey, _ref })
    }

    componentDidMount() {
        this.loadData();
    }

    render() {
        const { activeKey, panes, addType } = this.state;
        return (
            <article>
                <Input 
                    addonBefore="基金类型名"
                    placeholder="请输入基金类型名，中文"
                    style={{width: 300, marginRight: 10}}
                    value={addType}
                    onChange={this.handleAddTypeChange}
                />
                <Button type="primary" onClick={this.addTypeTab} style={{marginRight: '10px'}}>新增类型</Button>
                <Popconfirm
                    title="将发布数据到后台，请确认无误。"
                    okText="发布"
                    cancelText="取消"
                    onConfirm={this.submit}
                    onCancel={() => {}}
                    placement="bottom"
                >
                    <Button type="danger">发布</Button>
                </Popconfirm>
                <Tabs
                    style={{marginTop: '20px'}}
                    hideAdd
                    onChange={this.handleTabChange}
                    onEdit={this.onEdit}
                    activeKey={activeKey}
                    type="editable-card"
                >
                    {panes.map(pane => (
                        <TabPane
                            key={pane.key}
                            tab={pane.title}
                            closable={pane.closable}
                            forceRender={true}
                        >
                            <FundForm
                                type={pane.title}
                                onRef={this.saveRef}
                            />
                        </TabPane>
                    ))}
                </Tabs>
            </article>
        );
    }
}

// Component
// 配置面板
class FundForm extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            upRate: [{
                type: '默认收益率',
                value: ''
            }, {
                type: '推荐收益率',
                value: ''
            }, {
                type: '推荐收益率',
                value: ''
            }],
            downRate: [{
                type: '默认收益率',
                value: ''
            }, {
                type: '推荐收益率',
                value: ''
            }, {
                type: '推荐收益率',
                value: ''
            }],
            show: true
        }
    }

    submit = () => {
        const {upRate, downRate, show} = this.state;
        const {type} = this.props;
        let upValue = [],
            downValue = [];
        upRate.map(item => {
            if(item.value !== '') {
                upValue.push(item.value + '%');
            } else {
                upValue.push('');
            }
        })
        downRate.map(item => {
            if(item.value !== '') {
                downValue.push(item.value + '%');
            } else {
                downValue.push('');
            }
        })
        return {
            type,
            upValue,
            downValue,
            show
        }
    }

    loadData = (content) => {
        let upRate = [],
            downRate = [];
        content.upValue.map((value, i) => {
            upRate.push({
                type: i === 0 ? '默认收益率' : '推荐收益率',
                value: value.split('%')[0]
            })
        })
        content.downValue.map((value, i) => {
            downRate.push({
                type: i === 0 ? '默认收益率' : '推荐收益率',
                value: value.split('%')[0]
            })
        })
        this.setState({
            upRate,
            downRate,
            show: content.show
        })
    }

    handleInputChange = (value, type, index) => {
        if (type === 'up') {
            let _upRate = this.state.upRate;
            _upRate[index].value = value;
            this.setState({
                upRate: _upRate
            })
        } else if (type === 'down') {
            let _downRate = this.state.downRate;
            _downRate[index].value = value;
            this.setState({
                downRate: _downRate
            })
        }
    }

    addInput = (type) => {
        if (type === 'up') {
            let _upRate = this.state.upRate;
            _upRate.push({
                type: '推荐收益率',
                value: ''
            });
            this.setState({
                upRate: _upRate
            })
        } else if (type === 'down') {
            let _downRate = this.state.downRate;
            _downRate.push({
                type: '推荐收益率',
                value: ''
            });
            this.setState({
                downRate: _downRate
            })
        }
    }

    deleteInput = (type, index) => {
        if (type === 'up') {
            let _upRate = this.state.upRate;
            _upRate.splice(index, 1);
            this.setState({
                upRate: _upRate
            })
        } else if (type === 'down') {
            let _downRate = this.state.downRate;
            _downRate.splice(index, 1);
            this.setState({
                downRate: _downRate
            })
        }
    }

    onShowSwitchChange = checked => {
        this.setState({
            show: checked
        })
    }

    componentDidMount() {
        this.props.onRef(this)
    }

    render () {
        const {upRate, downRate, show} = this.state
        return (
            <section>
                <span s-word="1" style={{display: 'inline-block', paddingTop: '2px'}}>显示开关</span>
                <Switch 
                    style={{marginLeft: '10px'}}
                    checked={show ? true : false}
                    onChange={this.onShowSwitchChange} 
                />
                <Row style={{marginTop: 10}}>
                    <Col span={8}>
                        <Card
                            title="升至"
                            style={{width: 300, marginRight: 20}}
                        >
                            {upRate.map((item, i) => (
                                <section>
                                    <span>{item.type}</span>
                                    <InputNumber 
                                        key={'up' + i}
                                        min={-100}
                                        max={999}
                                        precision={0}
                                        value={item.value}
                                        onChange={(value) => this.handleInputChange(value, 'up', i)}
                                        style={{width: 70, marginBottom: 10, marginLeft: 5}}
                                    />
                                    <span>%</span>
                                    {
                                        i > 2 ?
                                        <Icon
                                            key={'upIcon' + i}
                                            style={{marginLeft: 10, marginTop: 10}} 
                                            type="delete"
                                            onClick={() => this.deleteInput('up', i)}
                                        /> : null
                                    }
                                    {/* <Input 
                                        type="number"
                                        key={'up' + i}
                                        addonBefore={item.type}
                                        addonAfter="%"
                                        value={item.value}
                                        onChange={(e) => this.handleInputChange(e.target.value, 'up', i)}
                                        style={{width: 200, marginBottom: 10, textAlign: 'right'}}
                                        onBlur={(e) => {cantBeEmpty(e, '请填写收益率！', show)}}
                                    />
                                    {
                                        i > 2 ?
                                        <Icon
                                            key={'upIcon' + i}
                                            style={{marginLeft: 10, marginTop: 10}} 
                                            type="delete"
                                            onClick={() => this.deleteInput('up', i)}
                                        /> : null
                                    } */}
                                </section>
                            ))}
                            <Button 
                                onClick={() => {this.addInput('up')}}
                                disabled={upRate.length >= 5}
                            >添加</Button>
                        </Card>
                    </Col>
                    <Col span={8}>
                        <Card
                            title="降至"
                            style={{width: 300}}
                        >
                            {downRate.map((item, i) => (
                                <section>
                                    <span>{item.type}</span>
                                    <InputNumber 
                                        key={'down' + i}
                                        min={-100}
                                        max={999}
                                        precision={0}
                                        value={item.value}
                                        onChange={(value) => this.handleInputChange(value, 'down', i)}
                                        style={{width: 70, marginBottom: 10, marginLeft: 5}}
                                    />
                                    <span>%</span>
                                    {
                                        i > 2 ?
                                        <Icon
                                            key={'downIcon' + i}
                                            style={{marginLeft: 10, marginTop: 10}} 
                                            type="delete"
                                            onClick={() => this.deleteInput('down', i)}
                                        /> : null
                                    }
                                    {/* <Input 
                                        key={'down' + i}
                                        addonBefore={item.type}
                                        addonAfter="%"
                                        value={item.value}
                                        onChange={(e) => this.handleInputChange(e.target.value, 'down', i)}
                                        style={{width: 200, marginBottom: 10, textAlign: 'right'}}
                                        onBlur={(e) => {cantBeEmpty(e, '请填写收益率！', show)}}
                                    /> */}
                                </section>
                            ))}
                            <Button 
                                onClick={() => {this.addInput('down')}}
                                disabled={downRate.length >= 5}
                            >添加</Button>
                        </Card>
                    </Col>
                </Row>
            </section>
        )
    }
}

function cantBeEmpty (e, notice, flag = true) {
    if (flag && e.target.value === '') {
        message.error(notice);
    }
}

export default fundProfitLoss;