import React, { useState, useEffect } from 'react';
import { Button, message, Radio, Collapse, Modal, Checkbox, Input, Popconfirm } from 'antd';
import FormRender from 'form-render/lib/antd';
import FORM_CONFIG from './form.json';
import uploadImg from './uploadImg';
import styles from '@/pages/frontend/frontend.less';
import UserIdList from '@/pages/form/components/userIdList';
import './type';
import api from 'api';
const { updateUserType } = api;

interface Props {
  dataList: uploadItem[];
  setDataList: Function;
  index: number;
  upActivity: (index: number) => void;
  downActivity: (index: number) => void;
  deleteActivity: (index: number) => void;
  setIsCanUpload: (key: boolean) => void;
}

const handle0Time = (number: number) => {
  return number < 10 ? '0' + number : number;
};

const handleTime = (d: string) => {
  if (!d) return '';
  let date = new Date(d);
  let _year = date.getFullYear(),
    _month = handle0Time(date.getMonth() + 1),
    _date = handle0Time(date.getDate()),
    _hour = handle0Time(date.getHours()),
    _minute = handle0Time(date.getMinutes()),
    _second = handle0Time(date.getSeconds());
  return `${_year}-${_month}-${_date} ${_hour}:${_minute}:${_second}`;
};

export default function({
  dataList,
  setDataList,
  index,
  upActivity,
  downActivity,
  deleteActivity,
  setIsCanUpload,
}: Props) {
  const [formData, setFormData] = useState<formData | {}>({}); //formData
  const [isEdit, setIsEdit] = useState(false); //是否可编辑
  const [isCanEditUser, setIsCanEditUser] = useState<boolean>(false); //是否可修改
  const [userTypeData, setUserTypeData] = useState({});
  const mutualTotalList: mutualItem[] = [];
  dataList.forEach(item => {
    if (item.id && item.id !== dataList[index].id) {
      mutualTotalList.push({
        label: item.name,
        value: item.id as string,
      });
    }
  });

  useEffect(() => {
    const activity = dataList[index];
    setFormData({
      endTime: activity.endTime,
      imgUrl: activity.imgUrl,
      jumpUrl: activity.jumpUrl,
      mutex: activity.mutex,
      name: activity.name,
      startTime: activity.startTime,
    });
  }, [dataList[index]]);

  const changeEditState = (e: any) => {
    console.log(userTypeData);
    e.stopPropagation();
    if (isEdit) {
      const _formData: formData = { ...(formData as formData) };
      if (!_formData.name) {
        return message.error('请先写活动名称');
      } else if (!_formData.imgUrl) {
        return message.error('请完成活动图片');
      } else if (!_formData.jumpUrl) {
        return message.error('请填写跳转链接');
      } else if (!/(https:\/\/|client.html).+/.test(_formData.jumpUrl)) {
        return message.error('请填写正确的跳转链接');
      } else if (_formData.mutex === '1' && dataList[index]['mutexList'].length === 0) {
        return message.error('请选择互斥内容');
      } else if (!dataList[index].filterId && isCanEditUser) {
        return message.error('请先上传用户类型');
      }
      let _dataList: uploadItem[] = [...dataList];
      _dataList[index] = {
        ..._dataList[index],
        ..._formData,
        startTime: handleTime(_formData.startTime),
        endTime: handleTime(_formData.endTime),
      };
      if (!_dataList[index].id) _dataList[index].id = new Date().getTime().toString();

      // 如果置灰并且没有filterid 表示为第一次进来
      if (!isCanEditUser && !dataList?.[index]?.['filterId']) {
        updateUserType(userTypeData)
          .then((res: any) => {
            if (res.code !== '0000') return message.error(res.message);
            _dataList[index]['filterId'] = res.data;
            message.success('提交用户类型成功');
            setDataList(_dataList);
            setIsCanUpload(true);
          })
          .catch((e: Error) => {
            message.error(e.message || '系统错误');
          });
      } else {
        setDataList(_dataList);
        setIsCanUpload(true);
      }
    } else {
      setIsCanUpload(false);
    }
    setIsEdit(!isEdit);
  };

  const changeMutualList = (data: string[]) => {
    let _data = [...dataList];
    _data[index]['mutexList'] = data;
    setDataList(_data);
  };

  const setFilterId = (key: string) => {
    let _data = [...dataList];
    _data[index]['filterId'] = key;
    setDataList(_data);
  };

  const _filterId = dataList?.[index]?.['filterId'];

  return (
    <>
      <Collapse className="g-mt20">
        <Collapse.Panel
          header={
            <div className="u-j-middle">
              <div className="u-l-middle">
                #{index + 1} {(formData as formData)?.name}
              </div>
              <div className="u-r-middle">
                <Button
                  type="primary"
                  onClick={e => {
                    e.stopPropagation();
                    upActivity(index);
                  }}
                >
                  上移
                </Button>
                <Button
                  type="primary"
                  className="g-ml20"
                  onClick={e => {
                    e.stopPropagation();
                    downActivity(index);
                  }}
                >
                  下移
                </Button>
              </div>
            </div>
          }
          key={index}
        >
          <div
            style={{
              position: 'absolute',
              width: 500,
              height: 500,
              zIndex: 1,
              display: isEdit ? 'none' : '',
            }}
          ></div>
          <div className="u-r-middle" style={{ marginBottom: 20 }}>
            <Button type="primary" className="g-ml20" onClick={changeEditState}>
              {isEdit ? '保存' : '编辑'}
            </Button>
            <Popconfirm
              title="确定删除?"
              className="g-ml20"
              onConfirm={e => {
                e.stopPropagation();
                deleteActivity(index);
              }}
              okText="是"
              cancelText="否"
            >
              <Button type="danger" onClick={e => e.stopPropagation()}>
                删除
              </Button>
            </Popconfirm>
          </div>
          <FormRender
            propsSchema={FORM_CONFIG}
            displayType="row"
            formData={formData}
            onChange={setFormData}
            onValidate={() => {}}
            readOnly={!isEdit}
            widgets={{ uploadImg: uploadImg }}
          />
          {mutualTotalList.length && (formData as formData).mutex === '1' ? (
            <div
              className="u-l-middle"
              style={{ marginTop: -10, marginBottom: 20, color: 'black' }}
            >
              <p className={styles['m-card-label']} style={{ marginBottom: 0 }}>
                <span className={styles['m-required']}>*</span>互斥活动:
              </p>
              <Checkbox.Group
                disabled={!isEdit}
                value={dataList[index].mutexList}
                options={mutualTotalList}
                onChange={changeMutualList}
              />
            </div>
          ) : null}
          <UserIdList
            filterId={_filterId}
            setFilterId={setFilterId}
            isAdd={true}
            isEdit={isEdit}
            setUserTypeData={setUserTypeData}
            isCanEdit={isCanEditUser}
            setIsCanEdit={setIsCanEditUser}
          />
        </Collapse.Panel>
      </Collapse>
    </>
  );
}
