{"propsSchema": {"type": "object", "properties": {"steadyFinance": {"title": "稳健理财", "type": "array", "ui:options": {"foldable": "true"}, "items": {"type": "object", "properties": {"key": {"title": "唯一key值", "type": "string", "ui:width": "33%"}, "title": {"title": "二级品类名称", "type": "string", "ui:width": "33%"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "33%"}}, "required": ["key", "title", "url"]}}, "combination": {"title": "组合", "type": "array", "ui:options": {"foldable": "true"}, "items": {"type": "object", "properties": {"key": {"title": "唯一key值", "type": "string", "ui:width": "33%"}, "title": {"title": "二级品类名称", "type": "string", "ui:width": "33%"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "33%"}}, "required": ["key", "title", "url"]}}}}}