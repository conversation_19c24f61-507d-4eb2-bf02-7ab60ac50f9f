import React, { useState, useEffect } from 'react';
import api from 'api';
import {
  Button,
  message,
  Popover,
  Table,
  Row,
  Col,
  Input,
  Select,
  Modal,
  Switch,
  PageHeader,
  List,
  Divider,
  InputNumber,
} from 'antd';
import classNames from 'classnames';
import data from '../indexValue/data';
import Search from 'antd/lib/input/Search';
import content from '../safelyWin/content';
const { Option } = Select;
const { TextArea } = Input;
const { postConsultFundRelation, getConsultFundRelation, cosultList } = api;
const numFormat=(num:number | string)=>{
  const res=num.toString().replace(/\d+/, function(n){ // 先提取整数部分
       return n.replace(/(\d)(?=(\d{3})+$)/g,function($1){
          return $1+",";
        });
  })
  return res;
}
const columns= [
  {
    title: '基金名称',
    dataIndex: 'fundName',
    width: '30%',
  },
  {
    title: '基金交易代码',
    dataIndex: 'tradeCode',
  },
  {
    title: '基金占比',
    dataIndex: 'fundRate',
  },
  {
    title: '基金规模',
    dataIndex: 'scale',
    render: text=>`${numFormat(text)}元`
  },
];
const pageSize = 10;
//
interface iFundRelation {
  insideRate: number;
  outsideRate: number; // 场外基金占比，小数
  fundRate: number; // 投资占比阈值，小数
  scale: number; // 基金规模阈值，单位：千万
  subjectFundMap: any;
}
interface subjectFundMapProps {
  subjectId: string;
  fundCode: string;
}
export default function() {
  const [kvData, setKvDate] = useState<iFundRelation>();

  const [plateId, setPlateId] = useState('');
  const [fundCode, setFundCode] = useState('');
  const [showEdit, setShowEdit] = useState(false);
  const [scaleDisabled, setScaleDisabled] = useState(false);
  const [scale, setScale] = useState(0);
  const [fundDisabled, setFundDisabled] = useState(false);
  const [fundRate, setFundRate] = useState(0);
  const [rateDisabled, setRateDisabled] = useState(false);
  const [insideRate, setInsideRate] = useState(0);
  const [outsideRate, setOutsideRate] = useState(0);
  const [itcProfitList,setItcProfitList]  = useState<any[]>([]);
  const [otcProfitList,setOtcProfitList]  = useState<any[]>([]);
  const [subjectMap, setSubjectMap] = useState<subjectFundMapProps[]>([]);
  useEffect(() => {
    getFundRelation();
  }, []);
  const getFundRelation = () => {
    getConsultFundRelation().then((res: { code: string; data: string }) => {
      if (res.code === '0000') {
        const data: iFundRelation = (JSON.parse(res.data)) || {};
        const subMap: subjectFundMapProps[] = [];
        data?.subjectFundMap && Object.keys(data?.subjectFundMap).forEach(e => {
          subMap.push({
            subjectId: e,
            fundCode: data.subjectFundMap[e],
          });
        });
        setSubjectMap(subMap);
        data?.scale?setScale(data?.scale):setScale(0);
        setKvDate(data);
        data?.fundRate? setFundRate(data?.fundRate * 100):setFundRate(0);
        data?.insideRate?setInsideRate(data?.insideRate * 100):setInsideRate(0);
        data?.outsideRate?setOutsideRate(data?.outsideRate * 100):setOutsideRate(0);
      }
    });
  };
  const setShowAdd = () => {
    setShowEdit(true);
  };
  /**保存规模 */
  const saveScale = () => {
    const data = { ...kvData, scale: scale };
    // setKvDate()
    setKvDate(data)
    postConsultFundRelation({ value: JSON.stringify(data) }).then((res: any) => {
      console.log('saveScale', res);
    });
  };
  /**保存基金筛选阈值 */
  const saveFundRate = () => {
    const data = { ...kvData, fundRate: fundRate / 100 };
    setKvDate(data)
    postConsultFundRelation({ value: JSON.stringify(data) }).then((res: any) => {
      console.log('saveFundRate', res);
    });
  };
  /**保存场内场外占比 */
  const saveConfigRate = () => {
    if(insideRate+outsideRate >100){
      Modal.error({
        title:'错误',
        content:'最大值为100%'
      })
    }else{
      const data = { ...kvData, insideRate: insideRate / 100, outsideRate: outsideRate / 100 };
      setKvDate(data)
      postConsultFundRelation({ value: JSON.stringify(data) }).then((res: any) => {
        console.log('saveFundRate', res);
      });
      }

  };
  /**处理搜索回调 */
  const onSearch = (val: string) => {
    val = `${val}.TI`
    cosultList({'indexId':val}).then((res: any) => {
      console.log('indexId', res);
      const itc = []
      const otc = []
      const itcTag = ['20','36','UJSJ','UJZJ']
      res?.data?.length > 0 && res?.data?.forEach(item=>{
        if(itcTag.includes(item.fundSubMarket)){
          itc.push(item)
        }else{
          otc.push(item)
        }
      })
      const temp=itc.sort((a,b)=>{
        if(Number(a.fundRate) > Number(b.fundRate)){
          return 1
        }else if(Number(a.fundRate) === Number(b.fundRate)){
          return  Number(b.scale)-Number(a.scale)
        } else return 0})
      otc.sort((a,b)=>{
        if(Number(a.fundRate) > Number(b.fundRate)){
          return 1
        }else if(Number(a.fundRate) === Number(b.fundRate)){
          return  Number(b.scale)-Number(a.scale)
        } else return 0})
      setItcProfitList(itc);
      setOtcProfitList(otc)
    });
  };
  /**处理搜索关联关系回调 */
  const onSearchBond=(val:string)=>{
    val = `${val}.TI`
    const findItem = subjectMap.find(e=>{return e.subjectId === val})
    if(findItem){
      Modal.confirm({
        title: '查询到此项',
        content: `${findItem.subjectId}已关联${findItem.fundCode}`,
        okText: '确认',
        cancelButtonProps:{style:{display:'none'}}
      });
    }else {
      Modal.confirm({
        title: '未查询到此项',
        content: ``,
        okText: '确认',
        cancelButtonProps:{style:{display:'none'}}
      });
    } 
  }
  /**删除kvmap中的项 */
  const deleteSubjectItem = (val:subjectFundMapProps)=>{
    const kvMap = kvData?.subjectFundMap;
    const deleteProp = val.subjectId;
    delete kvMap[deleteProp];
    const data = {...kvData,subjectFundMap:kvMap}
    const tempSubMap = [...subjectMap]
    tempSubMap.forEach((item, index, arr)=> {
      if(item.subjectId === deleteProp ) {
          arr.splice(index, 1);
      }
    })
    setSubjectMap(tempSubMap)
    setKvDate(data)
    postConsultFundRelation({ value: JSON.stringify(data) }).then((res: any) => {
      console.log('subjectFundMap', res);
    });
  }
  /**添加kvmap中的项 */
  const editKvDataMap = () => {
    const kvMap = kvData?.subjectFundMap || {};

    kvMap[`${plateId}.TI`] = fundCode;
    const tempSubMap = [...subjectMap]
    tempSubMap.push({
      subjectId: `${plateId}.TI`,
      fundCode: fundCode,
    })
    setSubjectMap(tempSubMap)
    const data = {...kvData, subjectFundMap:kvMap}
    console.log("data", data);
    setKvDate(data)
    postConsultFundRelation({ value:JSON.stringify(data)}).then((res: any) => {
      console.log('editKvDataMap', res);
    });
    
    // const addItem = {...kvMap,`${plateId}.TI`:fundCode}
  };

  return (
    <div>
      <section>
        <PageHeader
          style={{ width: '80%' }}
          title={`1. 人工指定行业和概念关联基金`}
          extra={
            <div
              style={{display:'flex',flexDirection:'row'}}>
              <Button type="primary" onClick={setShowAdd}>
                添加
              </Button>
              <Search
                placeholder="查询关联关系"
                allowClear
                enterButton="查询"
                style={{ marginLeft: '24px' }}
                onSearch={onSearchBond}
            />
            </div>
          }
        ></PageHeader>
        <Row className={'g-mb10'} style={{ marginLeft: '24px' }}>
          <Col span={20} style={{ display: `${showEdit ? 'block' : 'none'}` }}>
            <Input
              defaultValue={''}
              value={plateId}
              onChange={e => setPlateId((e.target.value))}
              style={{ width: '200px' }}
              placeholder={'输入行业，概念代码'}
            />
            <span>关联</span>
            <Input
              value={fundCode}
              defaultValue={''}
              onChange={e => setFundCode((e.target.value))}
              style={{ width: '200px' }}
              placeholder={'输入基金代码'}
            />
            <Button onClick={editKvDataMap} type="primary" className={'g-ml20'}>
              {' '}
              保存
            </Button>
          </Col>
        </Row>
        <div
          style={{maxHeight:'300',overflow:'scroll'}}
        >
          {subjectMap.map((subjectItem,index) => {
            return (
              <Row
                className={classNames('g-mb10')}
                style={{ width: '80%', marginLeft: '24px' }}
                type="flex"
                justify={'space-between'}
                key={subjectItem.subjectId}
              >
                <Col span={6}>
                  <span style={{color:'red'}}>
                  {index}.
                  </span>
                  <span style={{fontWeight:'bold'}}>
                    {subjectItem.subjectId} 关联 {subjectItem.fundCode}
                  </span>
                </Col>
                <Col span={4}>
                  <Button
                    type="danger"
                    onClick={() => {
                      deleteSubjectItem(subjectItem)
                    }}
                  >
                    删除
                  </Button>
                </Col>
              </Row>
            );
          })}
        </div>
      </section>
      <Divider></Divider>
      <section>
        <PageHeader style={{ width: '80%' }} title={`2.基金筛选阈值`}></PageHeader>
        <Row
          className={classNames('g-mb30')}
          style={{ width: '80%', marginLeft: '24px' }}
          type="flex"
          justify={'space-between'}
        >
          <Col style={{}} span={6}>
            <span>规模≥</span>
            <InputNumber
              value={scale}
              onChange={e => setScale(Number(e))}
              style={{ width: '100px', marginLeft: '20px' }}
              disabled={!scaleDisabled}
            />
            <span>千万</span>
          </Col>
          <Col
            span={4}
            style={{ display: 'flex', justifyContent: 'flex-end', marginRight: '48px' }}
          >
            <Button
              type="primary"
              onClick={() => {
                setScaleDisabled(true);
              }}
            >
              {' '}
              修改
            </Button>
            <Button
              type="primary"
              onClick={() => {
                saveScale();
              }}
              className={'g-ml20'}
            >
              {' '}
              保存
            </Button>
          </Col>
        </Row>

        <Row
          className={classNames('g-mb30')}
          style={{ width: '80%', marginLeft: '24px' }}
          type="flex"
          justify={'space-between'}
        >
          <Col span={6}>
            <span>投资占比≥</span>
            <InputNumber
              value={fundRate}
              onChange={e => setFundRate(Number(e))}
              style={{ width: '100px', marginLeft: '20px' }}
              disabled={!fundDisabled}
              defaultValue={fundRate}
            />
            <span>%</span>
          </Col>
          <Col
            span={4}
            style={{ display: 'flex', justifyContent: 'flex-end', marginRight: '48px' }}
          >
            <Button
              type="primary"
              onClick={() => {
                setFundDisabled(true);
              }}
              className={'g-ml200'}
            >
              {' '}
              修改
            </Button>
            <Button
              type="primary"
              onClick={() => {
                saveFundRate();
              }}
              className={'g-ml20'}
            >
              {' '}
              保存
            </Button>
          </Col>
        </Row>
      </section>
      <Divider></Divider>
      <section>
        <PageHeader
          style={{ width: '80%' }}
          title={`3.推荐结果场内场外占比`}
          extra={
            <div>
              <InputNumber
                value={insideRate}
                onChange={e => setInsideRate(Number(e))}
                style={{ width: '100px', marginLeft: '20px' }}
                disabled={!rateDisabled}
                defaultValue={insideRate*100}
              />
              <span>%场内</span>
              <InputNumber
                value={outsideRate}
                onChange={e => setOutsideRate(Number(e))}
                style={{ width: '100px', marginLeft: '30px' }}
                disabled={!rateDisabled}
                defaultValue={outsideRate}
              />
              <span>%场外</span>
              <Button
                type="primary"
                onClick={() => {
                  setRateDisabled(true);
                }}
                className={'g-ml20'}
              >
                {' '}
                修改
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  saveConfigRate();
                }}
                className={'g-ml20'}
              >
                {' '}
                保存
              </Button>
            </div>
          }
        ></PageHeader>
      </section>
      <Divider></Divider>
      <section>
        <PageHeader
          style={{ width: '80%' }}
          title={`4.行业和概念基金池排序结果查询`}
          extra={
            <Search
              placeholder="输入行业概念代码"
              allowClear
              enterButton="查询"
              onSearch={onSearch}
            />
          }
        ></PageHeader>
        <Row
          type="flex"
          justify={'space-between'}>
          <Col span={10} >
            <div>场内</div>
            <Table
              style={{
                height: 600,
                overflow: 'auto'
              }}
              columns={columns}
              dataSource={itcProfitList}
              
            >
            </Table>
          </Col>
          <Col span={10}
              style={{  marginLeft: '48px' }}>
          <div>场外</div>
            <Table
              style={{
                height: 600,
                overflow: 'auto'
              }}
              columns={columns}
              dataSource={otcProfitList}
            >
            </Table>
          </Col>
        </Row>
      </section>
    </div>
  );
}
