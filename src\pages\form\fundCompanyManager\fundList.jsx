import React from 'react';
import {Input, Icon, Button} from 'antd';
import api from 'api';

const {fetchFundNameByCode} = api;

class FundList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            fundList: []
        }
    }

    submit = () => {
        let _fundList = this.state.fundList;
        let _fundListData = [];
        for (let i = 0; i < _fundList.length; i++) {
            _fundListData.push(_fundList[i].ref.submit());
        }
        return _fundListData;
    }

    addFundPlane = () => {
        let _fundList = this.state.fundList;
        _fundList.push({
            randomKey: ('' + Math.random()).slice(-8),
            fundCode: '',
            fundName: '',
            ratio: '0',
            newRatio: ''
        })
        this.setState({
            fundList: _fundList
        })
    }

    deleteFundPlane = (count) => {
        let _fundList = this.state.fundList;
        _fundList.splice(count - 1, 1);
        this.setState({
            fundList: _fundList
        })
    }

    saveFundPlane = (ref, index) => {
        let _fundList = this.state.fundList;
        _fundList[index].ref = ref;
        this.setState({
            fundList: _fundList
        })
    }

    componentDidMount() {
        this.props.onRef(this);
        const _propData = this.props.data;
        console.log(_propData);
        let _fundList = [];
        _propData.map((item) => {
            _fundList.push({
                randomKey: ('' + Math.random()).slice(-8),
                fundCode: item.fundCode,
                fundName: item.fundName,
                ratio: item.ratio,
                newRatio: ''
            })
        })
        this.setState({
            fundList: _fundList
        })
    }

    render() {
        const {fundList} = this.state;
        return (
            <section>
                {
                    fundList.map((item, index) => (
                        <FundPlane 
                            key={item.randomKey}
                            count={index + 1}
                            data={item}
                            deleteFundPlane={this.deleteFundPlane}
                            onRef={(ref) => {this.saveFundPlane(ref, index)}}
                        />
                    ))
                }
                <Button style={{marginTop: 10}} onClick={this.addFundPlane} type="primary" size="small">添加</Button>
            </section>
        )
    }
}


class FundPlane extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            _fundcode: '', // 基金代码
            _fundname: '', // 基金名称
            _ratio: '', // 原占比
            _newRatio: '', // 现占比
        }
    }

    submit = () => {
        let {_fundcode, _ratio, _newRatio} = this.state;
        return {
            fundCode: _fundcode,
            ratio: _ratio,
            newRatio: _newRatio
        }
    }

    // 查询基金名称
    searchFundName = () => {
        const {_fundcode} = this.state;
        if (_fundcode === '' || _fundcode.length !== 6) {
            this.setState({
                _fundname: ''
            })
            return;
        }
        fetchFundNameByCode({
            fundCode: _fundcode
        }).then((data) => {
            if (data.code === '0000') {
                data = data.data;
                this.setState({
                    _fundname: data.name
                })
            }
        })
    }

    // 双向绑定输入框
    handleFundCodeChange = e => {
        this.setState({
            _fundcode: e.target.value
        })
    }
    handleNewRatioChange = e => {
        this.setState({
            _newRatio: e.target.value
        })
    }

    componentDidMount() {
        this.props.onRef(this);
        let _propData = this.props.data;
        this.setState({
            _fundcode: _propData.fundCode, // 基金代码
            _fundname: _propData.fundName, // 基金名称
            _ratio: _propData.ratio, // 原占比
            _newRatio: _propData.newRatio // 现占比
        })
    }

    render () {
        const {_fundcode, _fundname, _ratio, _newRatio} = this.state;
        const {deleteFundPlane, count} = this.props;
        return (
            <section style={{marginTop: '10px'}}>
                <Input
                    style={{width: '160px', marginRight: '10px'}}
                    value={_fundcode}
                    onChange={this.handleFundCodeChange}
                    onBlur={this.searchFundName}
                    addonBefore="基金代码"
                />
                <Input
                    style={{width: '240px', marginRight: '10px'}}
                    value={_fundname}
                    disabled
                    addonBefore="基金名称"
                />
                <Input
                    style={{width: '200px', marginRight: '10px'}}
                    value={_ratio}
                    addonBefore="当前占比"
                    addonAfter="%"
                    disabled 
                />
                <Input 
                    style={{width: '200px', marginRight: '10px'}}
                    value={_newRatio}
                    onChange={this.handleNewRatioChange}
                    addonBefore="目标占比"
                    addonAfter="%"
                />
                <Icon onClick={() => {deleteFundPlane(count)}} type="delete" theme="filled" />
            </section>
        )
    }
}

export default FundList;