import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import api from 'api';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import data from '../indexValue/data';
const { searchEquityList, saveEquty, deleteEquty, authRolePageList } = api;
export default function() {
  let amisScoped: any;
  const init = async () => {
    let roles = localStorage.getItem('roles');
    const highAuthorityList = ['admin', 'researcher', '运营'];
    let amis = amisRequire('amis/embed');
    let nestedSelectEquityOption = [];
    let roleOption = [];
    let equityMap = {};
    const nestedSelectEquityRes = await searchEquityList({
      offSet: 1,
      pageSize: 1000,
    });
    const nameOptionsRes = await authRolePageList({
      offSet: 1,
      pageSize: 1000,
    });

    if (nameOptionsRes.success) {
      const { data } = nameOptionsRes;
      roleOption = data.map(role => {
        return {
          label: role.roleName,
          value: role.id,
        };
      });
    }
    if (nestedSelectEquityRes.success) {
      const { configMap } = nestedSelectEquityRes.data;

      nestedSelectEquityOption = Object.keys(configMap).map(equity => {
        // console.log('e',equity)
        return {
          label: configMap[equity][0]?.rightsType,
          value: configMap[equity][0]?.rightsTypeId,
          children: configMap[equity]?.map(item => {
            equityMap[item.rightsId] = configMap[equity][0]?.rightsTypeId;
            return {
              label: item.rightsName,
              value: item.rightsId,
            };
          }),
        };
      });
    }
    console.log('nestedSelectEquityOption', equityMap);
    amisScoped = amis.embed(
      '#bankInfo',
      {
        ...amisJSON,
        data: {
          // 高级权限
          highAuthority: highAuthorityList.some(it => roles?.includes(it)),
          nestedSelectEquityOption,
          roleOption,
          equityMap,
        },
      },
      {},
      amisEnv(),
    );
  };
  useEffect(() => {
    init();
    return () => {
      amisScoped.unmount();
    };
  }, []);

  return <div id="bankInfo"></div>;
}
