import React, { useState, useEffect } from "react";
import classnames from 'classnames'
import FormRender from 'form-render/lib/antd';
import api from 'api';
import { Button, message, Popconfirm, Drawer, Row, Col } from 'antd'
import { CARD_JSON, LIST_JSON} from './config'
import uploadImg from './uploadImg'
import { DraggableArea } from './../../../components/DragTags'
import styles from './index.less'
import moment from 'moment'
const {
    fetchGroupCards,
    postGroupCards,
    fetchGroupList, //根据类型查询基金组合产品
    postGroupList, //保存基金组合产品列表
    fetchGroupConfig, //查询基金组合产品配置
    postGroupConfig, //保存基金组合产品配置
} = api

interface GROUP_LIST_ITEM {
    allAmount: number
    endDate: string
    flag: string
    fundPortfolioConfigDto: string | null
    groupId: string
    groupName: string
    groupType: string
    hot: boolean
    offTime: string
    putTime: string
    startDate: string
    status: string
    target: string
    updateTime: string
}
moment.locale('zh-cn');
export default function () {
    const [cardData, setCardData] = useState<any>({})
    const [cardValid, setCardValid] = useState([])

    const [listData, setListData] = useState<GROUP_LIST_ITEM[]>([])

    const [editGroupId, setEditGroupId] = useState('') //选中的组合id
    const [editGroup, setEditGroup] = useState<any>({})
    const [groupValid, setGroupValid] = useState([])
    const [upList, setUpList] = useState<string[]>([]) //上架列表  用于后续添加修改时间
    const [downList, setDownList] = useState<string[]>([]) //下架类表 用于后续添加修改时间

    const [isShowDrag, setIsShowDrag] = useState(false)
    const [isLoading, setIsLoading] = useState(true) //是否在加载

    useEffect(() => {
        fetchCardData()
        fetchGroupListData()
    }, [])

    /**
     * 获取组合运营卡片信息
     */
    function fetchCardData() {
        setIsLoading(true)
        fetchGroupCards().then((res: any) => {
            setIsLoading(false)
            if (res.code === '0000' && res.data) {
                let _cardData = JSON.parse(res.data).cardData
                setCardData(_cardData)
            }
            else return message.error(res.message || '系统错误')
        }).catch(() => {
            setIsLoading(false)
        })
    }

    /**
     * 提交组合运营卡片信息
     */
    function postCardData() {
        if (isLoading) return
        setIsLoading(true)
        if (cardValid.length > 0) {
            setIsLoading(false)
            message.error(JSON.stringify(cardValid))
            return
        }
        let tag: boolean = true
        cardData.groupCards.forEach((element: any) => {
            if (element.liveInfo.isOpen) {
                if (!element.liveInfo.liveId) {
                    tag = false
                    return message.error('请检查直播Id')
                }
                if (!element.liveInfo.liveUrl) {
                    tag = false
                    return message.error('请检查直播链接')
                }
            }
        });
        if (!tag) return setIsLoading(false)
        postGroupCards({
            value: JSON.stringify({cardData})
        }).then((res: any) => {
            setIsLoading(false)
            if (res.code !== '0000') return message.error(res.message)
            return message.success('提交成功！')
        }).catch(() => {
            setIsLoading(false)
        })
    }

    /**
     * 获取组合列表
     */
    function fetchGroupListData() {
        setIsLoading(true)
        fetchGroupList(null, 'all').then((res: any) => {
            setIsLoading(false)
            if (res.status_code !== 0) return message.error(res.status_msg)
            let data: GROUP_LIST_ITEM[] = res.data
            data.map((item: GROUP_LIST_ITEM, index: number) => {item.id = index})
            setListData(res.data)
        }).catch(() => {
            setIsLoading(false)
        })
    }

    /**
     * 保存组合列表
     */
    function saveGroupListData() {
        if (isLoading) return
        setIsLoading(true)
        let _listData: GROUP_LIST_ITEM[] = [...listData]
        _listData.map((item: GROUP_LIST_ITEM, index: number) => {
            let _groupId = item.groupId
            if (~upList.indexOf(_groupId)) {
                item.putTime = moment(new Date().getTime()).format('YYYYMMDD HH:mm')
                item.updateTime =  moment(new Date().getTime()).format('YYYYMMDD HH:mm')
            } else if (~downList.indexOf(_groupId)) {
                item.offTime = moment(new Date().getTime()).format('YYYYMMDD HH:mm')
                item.updateTime =  moment(new Date().getTime()).format('YYYYMMDD HH:mm')
            }    
        })
        postGroupList({
            sortList: _listData
        }, 'all').then((res: any) => {
            setIsLoading(false)
            try {
                if (res.status_code !== 0) return message.error(res.status_msg)
                message.success('提交成功')
                window.location.reload()
            } catch(e) {
                message.error('系统错误')
            }
        }).catch(() => {
            setIsLoading(false)
        })
    }

    /**
     * 获取组合信息
     * @param groupId 
     */
    function fetchGroup(groupId: string) {
        setEditGroupId(groupId)
        setIsLoading(true)
        fetchGroupConfig(null, groupId).then((res: any) => {
            setIsLoading(false)
            if (res.status_code !== 0) return message.error(res.status_msg || '系统错误')
            if (res.data) {
                let {
                    platform,
                    holderHave,
                    groupLabels,
                } = res.data
                if (!platform) res.data.platform = []
                if (!holderHave) res.data.holderHave = []
                if (!groupLabels) res.data.groupLabels = []
                if (!res?.data?.ownerImage)  {res.data = {
                    ownerImage: ''
                }}
                if (document.getElementsByClassName('upload-image')[0] && document.getElementsByClassName('upload-image')[0].getElementsByTagName('img')[0]) {
                    document.getElementsByClassName('upload-image')[0].getElementsByTagName('img')[0].src = ''
                }
                setEditGroup(res.data)
                setIsShowDrag(true)
            } else {
                setEditGroup({})
                setIsShowDrag(true)
            }
        }).catch(() => {
            setIsLoading(false)
        })
    }

    /**
     * 保存组合信息
     * @param groupId 
     */
    function saveGroup() {
        if (!checkGroup()) return;
        if (isLoading) return;
        setIsLoading(true)
        if (groupValid.length > 0) {
            setIsLoading(false)
            return message.error(JSON.stringify(groupValid))
        } 
        postGroupConfig(editGroup, editGroupId).then((res: any) => {
            setIsLoading(false)
            try {
                if (res.status_code !== 0) return message.error(res.message || '系统错误')
                message.success('提交成功')
                setIsShowDrag(false)
            } catch(e) {
                message.error('系统错误')
            }
        }).catch(() => {
            setIsLoading(false)
        })
    }

    /**
     * 校验组合信息
     */
    function checkGroup() {
        if (editGroup.operateEntrance === true) {
            if (!editGroup.operateIntro) {
                message.warning('运营入口说明不能为空！');
                return false;
            }
            if (!editGroup.operateUrl) {
                message.warning('运营入口链接不能为空！');
                return false;
            }
        }
        if (editGroup.hideArticle === false) {
            if (!editGroup.ownerThsid) {
                message.warning('主理人同顺号ID不能为空！');
                return false;
            }
        }
        return true;
    }

    /**
     * 置顶
     * @param index 
     */
    function stickTop(index: number) {
        let _groupList = [...listData],
        item = _groupList.splice(index, 1)
        console.log(item)
        _groupList.unshift(item[0])
        setListData(_groupList)
    }

    /**
     * 上下架
     * @param index 
     */
    //0上架中 1下架中
    function up_down(index: number) {
        let _groupList = [...listData],
        _preStatus: string = _groupList[index]['status'],
        _groupId: string = _groupList[index]['groupId'],
        _upList: string[] = [...upList],
        _downList: string[] = [...downList]
        console.log(_preStatus)
        if (_preStatus === '0') {   //现状态为上架中 操作为下架
            if (~_upList.indexOf(_groupId)) { //若为本次上架内容
                _upList.splice(_upList.indexOf(_groupId), 1)
                setUpList(_upList)
            } else {
                _downList.push(_groupId)
                setDownList(_downList)
            }
            // _groupList[index]['offTime'] = moment(new Date().getTime()).format('YYYYMMDD HH:MM')
            // _groupList[index]['updateTime'] = new Date()
            _groupList[index]['status'] = '1'
            setListData(_groupList)
        } else { //现状态为下架中 操作为上架
            // _groupList[index]['putTime'] = moment(new Date().getTime()).format('YYYYMMDD HH:MM')
            // _groupList[index]['updateTime'] = new Date()
            fetchGroupConfig(null, _groupId).then((res: any) => {
                if (res.status_code !== 0) return message.error(res.status_msg || '系统错误')
                console.log(res.data)
                if (res.data) {
                    if (~_downList.indexOf(_groupId)) { //若为本次下架内容
                        _downList.splice(_downList.indexOf(_groupId), 1)
                        setDownList(_downList)
                    } else {
                        _upList.push(_groupId)
                        setUpList(_upList)
                    }
                    _groupList[index]['status'] = '0'
                    setListData(_groupList)
                } else {
                    message.info('该产品未配置')
                }
            })
        }
    }

    function getGroupType(targetString: string) {
        let _rate: number = parseFloat(targetString)
        switch(true) {
            case _rate <= 4:
                return '活钱增值';
            case _rate <= 8:
                return '稳健理财';
            default:
                return '追求回报';
        }
    }

    /**
     * HTML反转义
     */
    function HTMLDecode(text: string) {
        var temp = document.createElement("div");
        temp.innerHTML = text;
        var output = temp.innerText || temp.textContent;
        temp = null;
        return output;
    }

    return (
        <div>
            <FormRender
                propsSchema={CARD_JSON}
                formData={cardData}
                onChange={setCardData}
                onValidate={setCardValid}
                displayType="row"
                showDescIcon={true}
                widgets={{
                    uploadImg
                }}
            />

            <div className="u-l-middle" style={{marginBottom: 20}}>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交组合运营卡片么'}
                    onConfirm={postCardData}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button 
                    type="danger" 
                    > 保存组合运营卡片 </Button>
                </Popconfirm>
            </div>

            <div className={styles['m-head']} style={{width: 1600}}>
                <div className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')} style={{width: 1600}}>
                    <p style={{width: 60}}>列表序号</p>
                    <p style={{width: 120}}>组合产品ID</p>
                    <p style={{width: 120}}>组合名称</p>
                    <p style={{width: 60}}>组合类型</p>
                    <p style={{width: 120}}>组合列表分类</p>
                    <p style={{width: 60}}>爆款组合</p>
                    <p style={{width: 120}}>总限额</p>
                    <p style={{width: 120}}>开放开始时间</p>
                    <p style={{width: 120}}>开放结束时间</p>
                    <p style={{width: 120}}>上架时间</p>
                    <p style={{width: 120}}>下架时间</p>
                    <p style={{width: 120}}>修改时间</p>
                    <p style={{width: 60}}>状态</p>
                    <p style={{width: 280}}>操作</p>
                </div> 
            </div> 

            <DraggableArea
                isList
                tags={listData}
                render={({tag, index}) => {
                    return (
                        <div className={styles['tag']} style={{width: 1600}}>
                            <div className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')} style={{width: 1600}}>
                                <p style={{width: 60}}>{index + 1}</p>
                                <p style={{width: 120}}>{tag.groupId}</p>
                                <p style={{width: 120}}>{HTMLDecode(tag.groupName)}</p>
                                <p style={{width: 60}}>{tag.groupType}</p>
                                <p style={{width: 120}}>{getGroupType(tag.target)}</p>
                                <p style={{width: 60}}>{tag.hot ? '是' : '否'}</p>
                                <p style={{width: 120}}>{tag.allAmount}</p>
                                <p style={{width: 120}}>{tag.startDate}</p>
                                <p style={{width: 120}}>{tag.endDate}</p>
                                <p style={{width: 120}}>{tag.putTime || '--'}</p>
                                <p style={{width: 120}}>{tag.offTime || '--'}</p>
                                <p style={{width: 120}}>{tag.updateTime || '--'}</p>
                                <p style={{width: 60}}>{tag.status === '0' ? '上架' : '下架'}</p>
                                <p style={{width: 280}}>
                                    <Button type="primary" onClick={() => {fetchGroup(tag.groupId)}}>编辑</Button>
                                    <Button type="primary" style={{marginLeft: 5, marginRight: 5}} onClick={() => {up_down(index)}}>{tag.status === '0' ? '下架' : '上架'}</Button>
                                    <Button type="primary" onClick={() => {stickTop(index)}}>置顶</Button>
                                </p>
                            </div> 
                        </div> 
                    )
                }}
                onChange={(data: any) => {
                    console.log(data)
                    setListData(data)
                }}
            />

            <div className="u-l-middle" style={{marginBottom: 20, marginTop: 20}}>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交产品信息配置么'}
                    onConfirm={saveGroupListData}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button 
                    type="danger" 
                    > 保存产品信息配置 </Button>
                </Popconfirm>
            </div>

            <Drawer 
            title="产品信息配置"
            width={800}
            onClose={() => {
                setEditGroup({
                    ownerImage: ''
                })
                if (document.getElementsByClassName('upload-image')[0] && document.getElementsByClassName('upload-image')[0].getElementsByTagName('img')[0]) {
                    document.getElementsByClassName('upload-image')[0].getElementsByTagName('img')[0].src = ''
                }
                setIsShowDrag(false)
            }}
            visible={isShowDrag}
            bodyStyle={{paddingBottom: 80}}
            >
                {
                    isShowDrag ?
                    <div>
                    <FormRender
                        propsSchema={LIST_JSON}
                        uiSchema={{}}
                        formData={editGroup}
                        onChange={setEditGroup}
                        onValidate={setGroupValid}
                        displayType="row"
                        showDescIcon={true}
                        widgets={{
                            uploadImg
                        }}
                    />
                </div>
                :
                null
                }
                <div>
                    <Button 
                        type="primary"
                        onClick={() => {
                            setIsShowDrag(false)
                            setEditGroup({})
                        }} 
                    > 取消 </Button>

                    <Popconfirm
                        placement="rightBottom"
                        title={'你确定要提交组合运营卡片么'}
                        onConfirm={() => {saveGroup()}}
                        okText="确认"
                        cancelText="取消"
                    >
                        <Button 
                        type="danger" 
                        > 提交 </Button>
                    </Popconfirm>
                </div>
            </Drawer>
        </div>
    )
}