import React, { useState, forwardRef } from 'react';
import UserIdList from '@/pages/form/components/userIdList';
import { Card } from 'antd';

const KycComponent = props => {
  /**
   * isAdd: 是否是新增的kyc
   * isEdit: 按钮是否可用
   * isCanEdit ? 提交用户类型 : 编辑用户类型
   */
  const { value, onChange } = props;
  const [isCanEdit, setIsCanEdit] = useState(false);
  const [filterId, setFilterId] = useState(value && '');
  return (
    <Card>
      <UserIdList
        filterId={value}
        setFilterId={id => {
          setFilterId(id);
          onChange(id);
        }}
        isAdd={false}
        isEdit={true}
        setUserTypeData={() => {}}
        isCanEdit={isCanEdit}
        setIsCanEdit={setIsCanEdit}
      />
    </Card>
  );
};

export default forwardRef(KycComponent);
