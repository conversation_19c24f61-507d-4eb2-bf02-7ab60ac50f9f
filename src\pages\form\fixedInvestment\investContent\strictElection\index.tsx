import React, { useEffect, useState } from 'react';
import { Button, Collapse, message, Tag } from 'antd';
import WrappedStrictCard from './myCard';
import classnames from 'classnames';
import styles from '../index.less';

const { Panel } = Collapse;

export interface iStrictElectionData {
    fundCode: string;
    fundName: string;
    reason: string;
    label: string;
    key: string;
}
interface iStrictElectionProps {
    isModify: boolean;
    handleModify: (flag: boolean) => void;
    handleData: (data: iStrictElectionData[]) => void;
    strictElectionData: iStrictElectionData[];
}
function StrictElection({handleData, strictElectionData, isModify, handleModify}: iStrictElectionProps) {

    const newAdd = () => {
        let arr = [...strictElectionData];
        let obj = {
            fundCode: '',
            fundName: '',
            reason: '',
            label: '',
            key: +new Date() + '',
        }
        arr.push(obj);
        if (arr.length > 5) {
            message.info('同顺严选模块配置项不超过5条');
            return;
        }
        handleData(arr);
    }
    const handleDelete = (key: number) => {
        if (!isModify) handleModify(true);
        let arr = [...strictElectionData];
        arr.splice(Number(key), 1);
        handleData(arr);
    }
    const modifyStrictElectionData = (data: iStrictElectionData, num: number) => {
        if (!isModify) handleModify(true);
        let arr = [...strictElectionData];
        arr[num] = data;
        handleData(arr);
    }
    const panelHeader = (obj: iStrictElectionData, num: number) => {
        return (
            <div className={classnames(styles['m-panel-header'], 'u-j-middle')}>
                <span>{Number(num) + 1}</span>
                { obj.fundCode && <span>基金ID: {obj.fundCode}</span> }
                { obj.reason && <span>精选理由: {obj.reason}</span> }
                { obj.label && <span>基金标签: {obj.label}</span> }
            </div>
        )
    }
    const goUp = (e: any, item: iStrictElectionData, index: number) => {
        e.stopPropagation();

        if (index === 0) {
            message.info('已在最上方')
            return
        }
        let arr = [...strictElectionData];
        arr.splice(index, 1);
        arr.splice(index - 1, 0, item);
        handleData(arr);
        if (!isModify) handleModify(true);
    }
    const goDown = (e: any, item: iStrictElectionData, index: number) => {
        e.stopPropagation();
        if (index === strictElectionData?.length - 1) {
            message.info('已在最下方')
            return
        }
        let arr = [...strictElectionData];
        arr.splice(index, 1);
        arr.splice(index + 1, 0, item);
        handleData(arr);
        if (!isModify) handleModify(true);
    }
    return <div style={{marginTop: 40}}>
        <h1 className="g-fs28 f-bold">同顺严选：</h1>
        <Collapse>
            {
                strictElectionData?.map((item: iStrictElectionData, index: number) => {
                    return (
                        <Panel 
                            header={panelHeader(item, index)} 
                            key={item.key}
                            extra={<div style={{marginTop: -5}}><Button onClick={(e) => { goUp(e, item, index) }}>上移</Button><Button onClick={(e) => { goDown(e, item, index) }}>下移</Button></div>}
                        >
                            <WrappedStrictCard currentData={item} handleData={(data) => modifyStrictElectionData(data, index)} handleDelete={() => handleDelete(index)}/>
                        </Panel>
                    )
                })
            }
            
        </Collapse>
        <div style={{marginTop: 20}}>
            <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
        </div>
    </div>
}
export default React.memo(StrictElection);
