import React, { useState, useEffect } from 'react';
import api from 'api';
import {
  Button,
  message,
  Popover,
  Table,
  Row,
  Col,
  Input,
  Select,
  Modal,
  Switch,
  PageHeader,
  List,
  Divider,
  InputNumber,
  Card,
} from 'antd';
import classNames from 'classnames';
import data from '../indexValue/data';
import Search from 'antd/lib/input/Search';
import content from '../safelyWin/content';
import api from '@/services/api';
const { Option } = Select;
const { TextArea } = Input;
const { postConsultFundRelation, getConsultFundRelation, cosultList } = api;
const HASHKEY = 'city_card_wallet'
const { fetchHashAll, postHash, fetchHash } = api;
export default function () {
  const [walletOpen, setWalletOpen] = useState(false);
  useEffect(() => {
    fetchHash({ key: HASHKEY,propName:'default' }).then(res => {
      if (res?.code === '0000') {
        // console.log('res', JSON.parse(res.data))
        let result = JSON.parse(res.data);
        // console.log('result', result.walletOpen)
        setWalletOpen(result?.walletOpen?true:false);
        return;
      } else {
        Modal.error({ content: '获取数据失败' });
      }
    })
  }, []);
  const commit = () => {
    let postBody = JSON.stringify({ walletOpen: walletOpen })
    postHash({ key: HASHKEY, value: postBody,propName:'default' }).then(res => {
      if (!res || res.code !== '0000') {
        Modal.error({ content: '更新失败' });
        return;
      } else {
        // setWalletOpen(JSON.parse(res.data)?.walletOpen);
        message.success
      }
    })
  };

  return (
    <section>
      <Card title="市民卡钱包兜底方案">
        <div>
          <Button className="g-mb20" onClick={commit}>
            保存
          </Button>
        </div>
        <span>切换兜底</span>
        <Switch
          checked={walletOpen}
          onChange={v => {
            setWalletOpen(v);
          }}
        />
        <span>兜底开启市民卡转入置灰</span>
      </Card>

    </section>
  );
}
