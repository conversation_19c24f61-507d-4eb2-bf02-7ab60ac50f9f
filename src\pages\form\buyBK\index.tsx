import React, { useState, useEffect } from 'react';
import { Input, Button, Popconfirm, message, Table, Modal, Row, Col, Drawer, Radio } from 'antd';
import api from 'api'
import FormRender from 'form-render/lib/antd';
import FROM_JSON from './form.json';
const { fetchBuyBK, postBuyBK } = api;



export default function () {
    const [formData, setFormData] = useState<any>({})
    useEffect(() => {
        fetchBuyBK().then((res: any) => {
            if (res.code === '0000' && res.data) {
                let _data = JSON.parse(res.data)
                setFormData(_data)
            }
        })
    }, [])
    function commit() {
        if(!formData.receiveText) {
            message.error('请填写红包领取前展示文案')
            return
        } if(!formData.receiveBtn) {
            message.error('请填写红包领取按钮文案')
            return
        } if(!formData.unReceiveAlert) {
            message.error('请填写未领取用户挽留文案')
            return
        } if(!formData.unUseAlert) {
            message.error('请填写未使用用户挽留文案')
            return
        } 
        let sendData = JSON.stringify(formData)
        postBuyBK({value: sendData}).then((res: any) => {
            if (res.code === '0000') {
                message.info('保存成功')
            } else {
                message.error('保存失败')
            }
        })
    }
    return (
        <section>
            <Button className='g-mb20' onClick={commit}>保存</Button>
            <FormRender
                propsSchema={FROM_JSON}
                formData={formData}
                onChange={setFormData}
                onValidate={() => { }}
            />
        </section>
    )
}