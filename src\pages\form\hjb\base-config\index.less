/* styles */

.base-config {
  height: calc(100vh - 150px - 48px);
  padding-right: 16px;
  margin-right: -16px;
  overflow: auto;

  > main {
    display: flex;
    flex-direction: row;

    > * {
      flex: 1;
      width: 0;

      &:first-child {
        padding-right: 32px;
        border-right: 2px solid #eee;
      }

      &:last-child {
        padding-left: 32px;
      }
    }
  }
}

.form-button-group {
  margin-top: 32px;
  display: flex;
  flex-direction: row;
  justify-content: center;

  > button {
    margin: 0 16px;
  }
}

.gold-fund-config {
  margin-bottom: 32px;

  main {
    display: flex;
    flex-direction: row;

    > * {
      flex: 1;
    }

    > form {
      padding: 0 32px 0 0;

      > footer {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;

        > * {
          margin-left: 16px;
        }
      }
    }

    .list-item {
      cursor: pointer;

      &:hover {
        background-color: #fafafa;
      }
    }
  }
}

.ad-banner-form,
.recommended-fund-form {
  overflow: hidden;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 24px 24px 0px;
  margin-bottom: 16px;

  > .ant-form-item:last-child {
    margin-bottom: 24px;
  }
}
