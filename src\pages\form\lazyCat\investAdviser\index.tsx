import React, { useEffect, useState } from "react";
import api from 'api'
import { <PERSON>, Divider, But<PERSON>, <PERSON>confirm, Drawer, Modal, Input, message, Cascader, TimePicker } from 'antd';
import './index.less'
import WrappedInvestForm from './investDrawer';
import WrappedInvestFormClass from './investDrawerClass';
import WrappedInvestFormPage from './investDrawerPage';
import DragSort from '../../components/DragSort/DragSort.jsx';

const riskText = {
    '1': '低风险',
    '2': '中低风险',
    '3': '中风险',
    '4': '中高风险',
    '5': '高风险',
}
const { fetchTGZCConfig, postTGZCConfig, postLazyfundcompanyTactics } = api;
export default function () {
    const [dataSource, setDataSource] = useState([]);
    const [entranceEdit, setEntranceEdit] = useState(false); //投顾专场入口
    const [pageEdit, setPageEdit] = useState(false); //投顾专场页面
    const [classEdit, setClassEdit] = useState(false); //投顾课堂
    const [currentEntranceData, setCurrentEntranceData] = useState<any>({});
    const [currentPageData, setCurrentPageData] = useState<any>({});
    const [currentClassData, setCurrentClassData] = useState<any>({});
    useEffect(() => {
        postLazyfundcompanyTactics().then((result: any) => {
            if(result?.code == '0000') {
                fetchTGZCConfig().then((res: any) => {
                    let { code, data } = res;
                    if (code === '0000') {
                        let tacticsData = result.data ?? [], arr: any = [], changeArr: any[] = [];
                        data = (data && JSON.parse(data)) ?? [];
                        console.log(tacticsData, data);
                        changeArr = [...tacticsData];

                        tacticsData?.forEach((item: any, index: number) => {
                            // item.serialNumber = index + 1;
                            for (let i = 0, len = data?.length; i < len; i++) {
                                let obj = data[i] ?? {};
                                if (item.taName === obj.taName && item.taCode === obj.taCode) {
                                    item.isShow = obj.isShow ?? true;
                                    item.topText = obj.topText ?? '';
                                    item.bottomText = obj.bottomText ?? '';
                                    item.pageData = obj.pageData ?? [];
                                    item.classData = obj.classData ?? [];
                                    item?.pageData?.forEach((item1: any, index1: number) => {
                                        item1.key = index1.toString();
                                    })
                                    item?.classData?.forEach((item1: any, index1: number) => {
                                        item1.key = index1.toString();
                                    })
                                    changeArr[index] = item;
                                    break;
                                }
                            }
                        })
                        data?.forEach((item: any, index: number) => {
                            for (let i = 0, len = changeArr?.length; i < len; i++) {
                                let obj = changeArr[i] ?? {};
                                if (item.taName === obj.taName && item.taCode === obj.taCode) {
                                    changeArr.splice(i, 1);
                                    arr.push(obj);
                                    break;
                                }
                            }
                        })
                        arr = [...arr, ...changeArr];
                        arr?.forEach((item: any, index: number) => {
                            item.serialNumber = index + 1;
                        })
                        setDataSource(arr);
                    } else {
                        message.error(res.message || '系统繁忙');
                    }
                })
            } else {
                message.error(result.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, []);

    // 主表格列配置项
    const columns = [
        {
            title: '序号',
            dataIndex: 'serialNumber',
        },
        {
            title: 'TA机构名称',
            dataIndex: 'taName',
        },
        {
            title: '操作',
            render: (text, record, index) => {
                return (
                    <span>
                        <a onClick={() => { handleEdit(record) }}>说明文案</a>
                        <Divider type="vertical" />
                        <a onClick={() => handleAddCard(record)}>新增卡片</a>
                        <Divider type="vertical" />
                        <a onClick={() => handleAddClass(record)}>投顾课堂</a>
                        <Divider type="vertical" />
                        <a onClick={() => handleShow(record)}>{record.isShow ? '隐藏' : '显示'}</a>
                    </span>
                )
            },
        },
    ];
    // 子表格配置项
    const expandedRowRender = (mainRecord: any) => {
        const pageData = mainRecord.pageData;
        const classData = mainRecord.classData;
        const pageColumns = [
            {
                title: '卡片名称',
                dataIndex: 'cardName',
            },
            {
                title: '策略名称',
                dataIndex: 'strategyName',
                render: (text: string) => (<span>{text || '--'}</span>)
            },
            {
                title: '风险等级',
                dataIndex: 'ictRisk',
                render: (text: string) => {
                    let value = riskText[text] ?? '--';
                    return (<span>{value}</span>)
                }
            },
            {
                title: '建议持有期',
                dataIndex: 'inHoldDay',
                render: (text: string) => (<span>{text || '--'}</span>)
            },
            {
                title: '操作',
                dataIndex: 'operation',
                render: (text, minorRecord, index) => (
                    <span>
                        <a onClick={() => { handlePageEdit(mainRecord, minorRecord) }}>编辑</a>
                        <Divider type="vertical" />
                        <Popconfirm placement="left" title="确定要删除吗?" onConfirm={() => handleDeletePageType(mainRecord, index)}>
                            <a>删除</a>
                        </Popconfirm>
                    </span>
                ),
            },
        ];
        const classColumns = [
            {
                title: '卡片标题',
                dataIndex: 'cardTitle',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                render: (text, minorRecord, index) => (
                    <span>
                        <a onClick={() => { handleClassEdit(mainRecord, minorRecord) }}>编辑</a>
                        <Divider type="vertical" />
                        <Popconfirm placement="left" title="确定要删除吗?" onConfirm={() => handleDeleteClassType(mainRecord, index)}>
                            <a>删除</a>
                        </Popconfirm>
                    </span>
                ),
            },
        ];
        return (
            <>
                <DragSort 
                    rowKey={(record: any) => record.key} 
                    columns={pageColumns} 
                    dataSource={pageData} 
                    pagination={false} 
                    handleDataSource={(dragIndex: number, hoverIndex: number) => handleDataSourcePage(dragIndex, hoverIndex, mainRecord)}
                ></DragSort>
                <DragSort 
                    rowKey={(record: any) => record.key} 
                    columns={classColumns} 
                    dataSource={classData} 
                    pagination={false} 
                    handleDataSource={(dragIndex: number, hoverIndex: number) => handleDataSourceClass(dragIndex, hoverIndex, mainRecord)}
                ></DragSort>
            </>
        )
    };
    const handleEdit = (record: any) => {
        setCurrentEntranceData({...record});
        setEntranceEdit(true);
    }
    const handleShow = (record: any) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        record.isShow = !record.isShow;
        arr[record.serialNumber - 1] = record;
        arr?.forEach((item: any, index: number) => {
            item.serialNumber = index + 1;
        })
        setDataSource(arr)
    }
    const handlePageEdit = (mainrecord: any, record: any) => {
        setCurrentEntranceData({...mainrecord});
        setCurrentPageData({...record});
        setPageEdit(true)
    }
    const handleDeletePageType = (mainrecord: any, index: number) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        let arrPage = mainrecord.pageData && JSON.parse(JSON.stringify(mainrecord.pageData));
        arrPage.splice(index, 1);
        arrPage?.forEach((item: any, index: number) => {
            item.key = index.toString();
        })
        mainrecord.pageData = arrPage;
        arr[mainrecord.serialNumber - 1] = mainrecord;
        setDataSource(arr);
    }
    const handleClassEdit = (mainrecord: any, record: any) => {
        setCurrentEntranceData({...mainrecord});
        setCurrentClassData({...record});
        setClassEdit(true)
    }
    const handleDeleteClassType = (mainrecord: any, index: number) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        let arrClass = mainrecord.classData && JSON.parse(JSON.stringify(mainrecord.classData));
        arrClass.splice(index, 1);
        arrClass?.forEach((item: any, index: number) => {
            item.key = index.toString();
        })
        mainrecord.classData = arrClass;
        arr[mainrecord.serialNumber - 1] = mainrecord;
        setDataSource(arr);
    }
    const handleAddCard = (record: any) => {
        setCurrentEntranceData({...record});
        setCurrentPageData({
            taName: record?.taName
        });
        setPageEdit(true)
    }
    const handleAddClass = (record: any) => {
        setCurrentEntranceData({...record});
        setCurrentClassData({
            taName: record?.taName
        });
        setClassEdit(true)
    }
    const handleSubmit = () => {
        postTGZCConfig({
            value: JSON.stringify(dataSource)
        }).then((data: any) => {
            if(data?.code == '0000') {
                message.success('提交成功', () => {
                  window.location.reload(true);
                });
            } else {
                message.error(data?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }
    const onEditClose = (num: number) => {
        if (num === 1) {
            setEntranceEdit(false);
        } else if (num === 2) {
            setPageEdit(false);
        } else {
            setClassEdit(false);
        }
        
    }
    const handleEntranceData = (data: any) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource)) || [];
        arr[data.serialNumber - 1] = data;
        arr?.forEach((item: any, index: number) => {
            item.serialNumber = index + 1;
        })
        setDataSource(arr)
    }
    const handlePageData = (data: any) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        let objEntrance = currentEntranceData && JSON.parse(JSON.stringify(currentEntranceData));
        let arrPage = objEntrance.pageData && JSON.parse(JSON.stringify(objEntrance.pageData)) || [];
        if (data?.key) {
            arrPage[data.key] = data;
        } else {
            arrPage.push(data);
        }
        arrPage?.forEach((item: any, index: number) => {
            item.key = index.toString();
        })
        objEntrance.pageData = arrPage;
        arr[objEntrance.serialNumber - 1] = objEntrance;
        setDataSource(arr);
    }
    const handleClassData = (data: any) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        let objEntrance = currentEntranceData && JSON.parse(JSON.stringify(currentEntranceData));
        let arrClass = objEntrance.classData && JSON.parse(JSON.stringify(objEntrance.classData)) || [];
        if (data?.key) {
            arrClass[data.key] = data;
        } else {
            arrClass.push(data);
        }
        arrClass?.forEach((item: any, index: number) => {
            item.key = index.toString();
        })
        objEntrance.classData = arrClass;
        arr[objEntrance.serialNumber - 1] = objEntrance;
        setDataSource(arr);
    }
    const handleDataSource = (dragIndex: number, hoverIndex: number) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        const dragRow = arr[dragIndex];
        arr.splice(dragIndex, 1);
        arr.splice(hoverIndex, 0, dragRow);
        arr?.forEach((item: any, index: number) => {
            item.serialNumber = index + 1;
        })
        setDataSource(arr);
    }
    const handleDataSourcePage = (dragIndex: number, hoverIndex: number, record: any) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        let arrPage = record.pageData && JSON.parse(JSON.stringify(record.pageData));
        const dragRow = arrPage[dragIndex];
        arrPage.splice(dragIndex, 1);
        arrPage.splice(hoverIndex, 0, dragRow);
        arrPage?.forEach((item: any, index: number) => {
            item.key = index.toString();
        })
        record.pageData = arrPage;
        arr[record.serialNumber - 1] = record;
        setDataSource(arr);
    }
    const handleDataSourceClass = (dragIndex: number, hoverIndex: number, record: any) => {
        let arr = dataSource && JSON.parse(JSON.stringify(dataSource));
        let arrClass = record.classData && JSON.parse(JSON.stringify(record.classData));
        const dragRow = arrClass[dragIndex];
        arrClass.splice(dragIndex, 1);
        arrClass.splice(hoverIndex, 0, dragRow);
        arrClass?.forEach((item: any, index: number) => {
            item.key = index.toString();
        })
        record.classData = arrClass;
        arr[record.serialNumber - 1] = record;
        setDataSource(arr);
    }
    return (
        <section>
            <section className="strategy-header">
                <span className="strategy-type-title">投顾专场</span>
            </section>
            <DragSort 
                className="components-table-demo-nested"
                rowKey={(record: any) => record.serialNumber}  
                expandedRowRender={expandedRowRender} 
                columns={columns} 
                dataSource={dataSource} 
                pagination={false} 
                handleDataSource={handleDataSource}
            ></DragSort>
            <section className="strategy-footer">
                <Popconfirm 
                    title="确定要提交吗？"
                    onConfirm={handleSubmit}
                    okText="确定"
                    cancelText="取消"
                >
                    <Button
                        type="primary"
                    >提交</Button>
                </Popconfirm>
            </section>
            { entranceEdit && (
                <Drawer
                    width={1000}
                    title="投顾专场说明文案"
                    placement="right"
                    closable
                    onClose={() => onEditClose(1)}
                    visible={entranceEdit}
                    >
                    { <WrappedInvestForm handleData={handleEntranceData} currentData={currentEntranceData} onEditClose={() => onEditClose(1)}></WrappedInvestForm> }
                </Drawer>
            ) }
            { pageEdit && (
                <Drawer
                    width={1000}
                    title="投顾专场入口"
                    placement="right"
                    closable
                    onClose={() => onEditClose(2)}
                    visible={pageEdit}
                    >
                    { <WrappedInvestFormPage consultTacticsList={currentEntranceData.consultTacticsList} handleData={handlePageData} currentData={currentPageData} onEditClose={() => onEditClose(2)}></WrappedInvestFormPage> }
                </Drawer>
            ) }
            { classEdit && (
                <Drawer
                    width={1000}
                    title="投顾课堂"
                    placement="right"
                    closable
                    onClose={() => onEditClose(3)}
                    visible={classEdit}
                    >
                    { <WrappedInvestFormClass handleData={handleClassData} currentData={currentClassData} onEditClose={() => onEditClose(3)}></WrappedInvestFormClass> }
                </Drawer>
            ) }
        </section>
    );
}
