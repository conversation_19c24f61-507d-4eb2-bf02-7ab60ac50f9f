import {
    Form,
    Input,
    <PERSON><PERSON>,
    <PERSON>confirm,
    DatePicker,
    Select
  } from 'antd';
import moment from 'moment';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import { iBaseData } from './index';
const { Option } = Select;
interface baseDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: iBaseData;
    handleData: (data: iBaseData) => void;
}

class baseDrawer extends React.Component<baseDrawerProps, any> {
    constructor(props: baseDrawerProps) {
        super(props);
    }

    formItemLayout = {
        labelCol: {
            span: 4
        },
        wrapperCol: {
            span: 19
        },
    };
    add0 = (m:number) => {
        return m < 10 ? '0' + m : m;
    }
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { currentData } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                let time = new Date(values.date) ?? new Date();
                let year = time.getFullYear();
                let month = time.getMonth()+1;
                let day = time.getDate();
                let date = year + '-' + this.add0(month) + '-' + this.add0(day)
                values = { 
                    ...currentData,
                    ...values,
                    date: date
                };
                console.log(values);
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;
        const { onEditClose } = this.props;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="日期" wrapperCol={{span: 4}}>
                        {getFieldDecorator('date', {
                            initialValue: this.props.currentData?.date ? moment(this.props.currentData?.date) : null,
                            rules: [{ required: true, message: '请输入日期' }],
                        })(
                            <DatePicker />
                        )}
                    </Form.Item>
                    <Form.Item label="活动ID" wrapperCol={{span: 6}}>
                        {getFieldDecorator('activityId', {
                            initialValue: this.props.currentData?.activityId,
                            rules: [{ required: true, message: '请输入活动ID' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="红包ID" wrapperCol={{span: 6}}>
                        {getFieldDecorator('redPaperId', {
                            initialValue: this.props.currentData?.redPaperId,
                            rules: [{ required: true, message: '请输入红包ID' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="门槛" wrapperCol={{span: 6}}>
                        {getFieldDecorator('threshold', {
                            initialValue: this.props.currentData?.threshold,
                            rules: [{ required: true, message: '请输入活动门槛' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="满减金额" wrapperCol={{span: 6}}>
                        {getFieldDecorator('minusMoney', {
                            initialValue: this.props.currentData?.minusMoney,
                            rules: [{ required: true, message: '请输入活动满减金额' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="红包状态" wrapperCol={{span: 6}}>
                        {getFieldDecorator('status', {
                            initialValue: this.props.currentData?.status,
                            rules: [{ required: true, message: '请选择红包状态' }],
                        })(
                            <Select style={{ width: 120 }}>
                                <Option key={'0'} value={'0'}>可领取</Option>
                                <Option key={'2'} value={'2'}>已抢完</Option>
                            </Select>
                        )}
                    </Form.Item>
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                            <Button type="primary">
                                确定
                            </Button>
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedBaseDrawer = Form.create<baseDrawerProps>({ name: 'baseDrawer' })(baseDrawer);
export default WrappedBaseDrawer