import React from 'react';
import { Breadcrumb } from 'antd';

interface iprops {
  location: string;
}
export default function({ location }: iprops) {
  return (
    <section style={{ marginBottom: 20 }}>
      <Breadcrumb separator=">">
        <Breadcrumb.Item href="#/frontend/home">当前位置：新版首页配置</Breadcrumb.Item>

        {/* <Breadcrumb.Item href="#/form/scFinancialTabV2/pageConfig">页面配置</Breadcrumb.Item> */}
        <Breadcrumb.Item>{location}</Breadcrumb.Item>
      </Breadcrumb>
    </section>
  );
}
