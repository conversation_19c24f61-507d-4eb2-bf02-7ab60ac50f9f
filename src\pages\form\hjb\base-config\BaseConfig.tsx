/**
 * 黄金宝 - 基础配置
 */
import React, { useState, useRef, useEffect } from 'react';
import styles from './index.less';
import { Spin, Button, notification, Modal } from 'antd';
import GoldAndBannerConfig from './GoldAndBannerConfig';
import EverydayLeadingGold from './EverydayLeadingGold';
import { FormComponentProps } from 'antd/es/form';
import api from 'api';

/** 生成简易 uid */
const uid = () =>
  Date.now().toString(36) +
  Math.random()
    .toString(36)
    .substr(2);

//
const BaseConfig: React.FunctionComponent = () => {
  //
  const [config, setConfig] = useState(null);

  const formRef = useRef({ gold_fund: [], activity: null, banner: {}, fund: {} });
  const [goldFund, setGoldFund] = useState([]);
  const [activity, setActivity] = useState({});
  const [banner, setBanner] = useState([]);
  const [fund, setFund] = useState([]);

  const handleAppendBanner = (n: number) => {
    const updated = banner.slice();
    updated.splice(n, 0, { _id: uid() });

    setBanner(updated);
  };

  const handleRemoveBanner = (n: number) => {
    const updated = banner.slice();
    updated.splice(n - 1, 1);

    setBanner(updated);
  };

  const handleAppendFund = (n: number) => {
    const updated = fund.slice();
    updated.splice(n, 0, { _id: uid() });

    setFund(updated);
  };

  const handleRemoveFund = (n: number) => {
    const updated = fund.slice();
    updated.splice(n - 1, 1);

    setFund(updated);
  };

  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = () => {
    const formNum = 1 + fund.length + banner.length;
    let noErrorFormNum = 0;
    let data = {
      gold_fund: goldFund,
      activity,
      fund,
      banner,
    };

    async function checkComplete() {
      if (noErrorFormNum !== formNum) return;

      if (data.gold_fund.length === 0) {
        notification.warning({ message: '需要至少配置一只黄金基金！' });
        return
      }

      if (data.fund.length === 0) {
        notification.warning({ message: '需要至少配置一只首购推荐基金！' });
        return
      }

      setGoldFund(data.gold_fund);
      setActivity(data.activity);
      setBanner(data.banner);
      setFund(data.fund);

      notification.info({ key: 'postLoading', message: '正在保存配置...', duration: null });
      setSubmitting(true);
      const response = await api.postHjbBaseConfig({ value: JSON.stringify(data) });
      setSubmitting(false);

      if (response?.code == '0000') {
        notification.success({ key: 'postLoading', message: '配置保存成功！', });

        setConfig(data);
        return;
      }

      notification.error({ key: 'postLoading', message: `配置保存失败！${response?.message}`, });
    }

    formRef.current.activity.validateFieldsAndScroll((err, values) => {
      if (err) return;

      data.activity = values;

      noErrorFormNum++;
      checkComplete();
    });

    Object.keys(formRef.current.banner).forEach(key => {
      const form: FormComponentProps['form'] = formRef.current.banner[key];

      form.validateFieldsAndScroll((err, values) => {
        if (err) return;

        const updated = data.banner.slice();
        const index = updated.findIndex(item => item._id === key);
        updated.splice(index, 1, { ...values, _id: key });
        data.banner = updated;

        noErrorFormNum++;
        checkComplete();
      });
    });

    Object.keys(formRef.current.fund).forEach(key => {
      const form: FormComponentProps['form'] = formRef.current.fund[key];

      form.validateFieldsAndScroll((err, values) => {
        if (err) return;

        const updated = data.fund.slice();
        const index = updated.findIndex(item => item._id === key);
        updated.splice(index, 1, { ...values, _id: key });
        data.fund = updated;

        noErrorFormNum++;
        checkComplete();
      });
    });
  };

  const [resetModal, setResetModal] = useState(false);

  const handleReset = () => {
    setResetModal(false);

    setGoldFund(config?.gold_fund || []);
    setActivity(config?.activity || {});
    setBanner(config?.banner || []);
    setFund(config?.fund || []);

    setTimeout(() => {
      formRef.current.activity.setFieldsValue(config?.activity || {});
      (config?.banner || []).forEach((item) => {
        formRef.current.banner[item._id].setFieldsValue(item);
      });
      (config?.fund || []).forEach((item) => {
        formRef.current.fund[item._id].setFieldsValue(item);
      });

      notification.info({ message: '配置已经重置！' });
    }, 1e3);
  };

  const [syncing, setSyncing] = useState();

  const handleSync = async () => {
    //
    notification.info({ key: 'syncing', message: '正在将配置同步到后端服务器...', duration: null });
    setSyncing(true);
    const response = await api.fetchHjbConfigSync();
    setSyncing(false);

    if (response?.status_code == 0) {
      notification.success({ key: 'syncing', message: '配置同步到后端服务器成功！' });
      return;
    }

    notification.error({ key: 'syncing', message: `配置同步到后端服务器失败！${ response?.status_msg }`, });
  };

  useEffect(() => {
    async function fetch () {
      notification.info({ key: 'fetchLoading', message: '正在拉取配置数据...', duration: null });
      const response = await api.fetchHjbBaseConfig();

      if (response?.code == '0000') {
        try {
          const data = JSON.parse(response?.data || '{}');

          setGoldFund(data?.gold_fund || []);
          setActivity(data?.activity || {});
          setBanner(data?.banner || []);
          setFund(data?.fund || []);

          notification.success({ key: 'fetchLoading', message: '拉取配置数据成功！' });
          setConfig(data);
        } catch {
          notification.warning({ key: 'fetchLoading', message: '拉取配置数据成功！但数据似乎有误...' });
          setConfig({});
        }

        return;
      }

      notification.warning({ key: 'fetchLoading', message: '拉取配置数据失败！' });
      setConfig({});
    }

    fetch();
  }, []);

  //
  if (!config) return <Spin />;

  // prettier-ignore
  return (<>
    <div className={ styles['base-config'] }>
      <main key={ config._reset }>
        <GoldAndBannerConfig
          formRef={ formRef }
          goldFundSaved={ config?.gold_fund || [] }
          goldFund={ goldFund }
          onUpdateGoldFund={ (goldFund) => setGoldFund(goldFund) }
          banners={ banner }
          onAppendBanner={ handleAppendBanner }
          onRemoveBanner={ handleRemoveBanner }
        />
        <EverydayLeadingGold
          formRef={ formRef }
          data={ activity }
          fund={ fund }
          onAppendFund={ handleAppendFund }
          onRemoveFund={ handleRemoveFund }
        />
      </main>
      <footer className={ styles['form-button-group'] }>
        <Button type="primary" onClick={ handleSubmit } loading={ submitting } disabled={ syncing }>
          保存
        </Button>
        <Button type="secondary" onClick={ () => setResetModal(true) } disabled={ syncing || submitting }>
          重置
        </Button>
        <Button type="danger" onClick={ handleSync } loading={ syncing }>立即同步</Button>
      </footer>
    </div>
    <Modal
      visible={ resetModal }
      title="重置确认"
      onOk={ handleReset }
      okText="确认"
      onCancel={ () => setResetModal(false) }
      cancelText="取消"
    >
      确认要撤销已完成的所有更改，将配置恢复到上一次保存后的状态吗？
    </Modal>
  </>)
};

export default BaseConfig;
