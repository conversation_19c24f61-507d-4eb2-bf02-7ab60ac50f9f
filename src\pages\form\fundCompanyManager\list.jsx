import React, { useEffect, useState } from "react";
import api from 'api';
import { Input, Button, Table, Spin, Modal, Popconfirm, message } from 'antd';

const { fetchFundCompany } = api;

export default function (props) {

    const [spinToggle, setSpinToggle] = useState(true); // 表格加载中控制
    const [dataSource, setDataSource] = useState([]); // 表格数据

    const [UI_groupAccountId, setUI_groupAccountId] = useState(''); // 用户输入_交易账号
    const [UI_spAccoName, setUI_spAccoName] = useState(''); // 用户输入_账号名称
    const [UI_spAccoTaName, setUI_spAccoTaName] = useState(''); // 用户输入_归属方
    
    const [modalVisible, setModalVisible] = useState(false); // 弹窗显示控制
    const [modalType, setModalType] = useState('create'); // 弹窗类型 - 创建 修改
    const [modalConfirmLoading, setModalConfirmLoading] = useState(false); // 弹窗确认按钮加载中
    const [MODAL_groupAccountId, setMODAL_groupAccountId] = useState(''); // 弹窗交易账号
    const [MODAL_spAccoName, setMODAL_spAccoName] = useState(''); // 弹窗账号名称
    const [MODAL_spAccoTaName, setMODAL_spAccoTaName] = useState(''); // 弹窗归属方

    const { goDetail } = props;

    const columns = [{
        title: '账号',
        dataIndex: 'groupAccountId',
        key: 'groupAccountId'
    }, {
        title: '名称',
        dataIndex: 'spAccoName',
        key: 'spAccoName'
    }, {
        title: '归属方',
        dataIndex: 'spAccoTaName',
        key: 'spAccoTaName'
    }, {
        title: '持仓总额',
        dataIndex: 'holdShare',
        key: 'holdShare'
    }, {
        title: '运行天数',
        dataIndex: 'holdDate',
        key: 'holdDate'
    }, {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: text => (getStatusText(text))
    }, {
        title: '操作',
        key: 'operator',
        render: (text, record) => (
            <p style={{textAlign: 'left', marginBottom: 0}}>
                <a
                    onClick={() => {goDetail(record)}}
                >查看详情</a>
                <a
                    style={{marginLeft: 5}}
                    onClick={() => {openModal('modify', record)}}
                >修改</a>
                {
                    (record.status === "0" || record.status === "2") ? 
                    <Popconfirm
                        title="是否确认删除"
                        onConfirm={() => {postOperation('2', record)}}
                        okText="确定"
                        cancelText="取消"
                    >
                        <a
                            style={{marginLeft: 5}}
                        >删除</a> 
                    </Popconfirm>
                    : null
                }
            </p>
        )
    }]

    useEffect(() => {
        fetchList();
    }, [])

    /**
     * 获取基金公司实盘列表
     */
    const fetchList = () => {
        let sendToData = {
            type: "list",
            value: JSON.stringify({
                groupAccountId: UI_groupAccountId,
                spAccoName: UI_spAccoName,
                spAccoTaName: UI_spAccoTaName
            })
        }
        setSpinToggle(true);
        fetchFundCompany(sendToData).then((data) => {
            console.log(data);
            setSpinToggle(false);
            if (data.code === "0000") {
                data = data.data;
                setDataSource(data);
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            setSpinToggle(false);
            message.error('网络错误，请重试')
        })
    }

    /**
     * 新增 修改账号
     */
    const handleAccount = (type) => {
        let record = {
            spAccoName: MODAL_spAccoName,
            spAccoTaName: MODAL_spAccoTaName
        }
        let flag = "0";
        if (type === 'modify') {record.groupAccountId = MODAL_groupAccountId;flag = "1"}
        setModalConfirmLoading(true);
        postOperation(flag, record);
    }

    /**
     * 增删改接口
     * @param {String} flag 0-增 1-改 2-删 
     * @param {Object} record 
     */
    const postOperation = (flag, record) => {
        let sendToData = {
            type: "operation",
            value: JSON.stringify({
                groupAccountId: record.groupAccountId,
                spAccoName: record.spAccoName,
                spAccoTaName: record.spAccoTaName,
                operator: "999",
                flag: flag
            })
        }
        fetchFundCompany(sendToData).then((data) => {
            console.log(data);
            setModalConfirmLoading(false);
            if (data.code === '0000') {
                if (flag === '0') {
                    message.success('新增成功');
                } else if (flag === '1') {
                    message.success('修改成功');
                } else if (flag === '2') {
                    message.success('删除成功');
                }
                setModalVisible(false);
                fetchList();
                // setTimeout(() => {
                    // window.location.reload();
                // }, 500);
            } else {
                message.error(data.message)
            }
        }).catch(() => {
            setModalConfirmLoading(false);
            message.error('网络错误，请重试')
        })
    }

    /**
     * 映射基金公司状态
     * @param {string} status 
     */
    const getStatusText = (status) => {
        if (status === "0") {
            return '未建仓';
        } else if (status === "1") {
            return '运行中';
        } else if (status === "2") {
            return '已清仓';
        } else {
            return '--'
        }
    }

    /**
     * 清除输入框
     */
    const clearUserInput = () => {
        setUI_groupAccountId('');
        setUI_spAccoName('');
        setUI_spAccoTaName('');
    }

    /**
     * 交易账号创建/修改 弹窗
     * @param {String} type create modify 
     * @param {Object} record 
     */
    const openModal = (type, record = {}) => {
        if (type === 'modify') {
            setMODAL_groupAccountId(record.groupAccountId);
            setMODAL_spAccoName(record.spAccoName);
            setMODAL_spAccoTaName(record.spAccoTaName);
        } else {
            setMODAL_groupAccountId('');
            setMODAL_spAccoName('');
            setMODAL_spAccoTaName('');
        }
        setModalType(type);
        setModalVisible(true);
    }

    return (
        <div>
            <div>
                <Input
                    addonBefore="交易账号"
                    style={{width: 260, marginLeft: 10}}
                    onChange={(e) => {setUI_groupAccountId(e.target.value)}}
                    value={UI_groupAccountId}
                />
                <Input
                    addonBefore="账号名称"
                    style={{width: 260, marginLeft: 10}}
                    onChange={(e) => {setUI_spAccoName(e.target.value)}}
                    value={UI_spAccoName}
                />
                <Input
                    addonBefore="归属方"
                    style={{width: 260, marginLeft: 10}}
                    onChange={(e) => {setUI_spAccoTaName(e.target.value)}}
                    value={UI_spAccoTaName}
                />
                <Button 
                    type="primary"
                    style={{marginLeft: 10}}
                    onClick={fetchList}
                >查询</Button>
                <Button
                    style={{marginLeft: 10}}
                    onClick={clearUserInput}
                >清除</Button>
                <Button
                    style={{float: 'right'}}
                    onClick={() => {openModal('create')}}
                >新建账号</Button>
                <div style={{clear: 'both'}}></div>
            </div>
            <Spin spinning={spinToggle} tip="加载中...">
                <Table 
                    style={{ marginTop: 20 }}
                    columns={columns}
                    dataSource={dataSource}
                />
            </Spin>
            <Modal
                title={modalType === 'create' ? '交易账号创建' : '交易账号修改'}
                visible={modalVisible}
                onOk={() => {handleAccount(modalType)}}
                confirmLoading={modalConfirmLoading}
                onCancel={() => {setModalVisible(false)}}
                cancelText="取消"
                okText={modalType === 'create' ? '创建' : '修改'}
                closable={false}
                centered={true}
            >
                <div>
                    <Input
                        addonBefore="账号名称"
                        style={{width: 260}}
                        onChange={(e) => {setMODAL_spAccoName(e.target.value)}}
                        value={MODAL_spAccoName}
                    />
                </div>
                <div>
                    <Input
                        addonBefore="归属方"
                        style={{width: 260, marginTop: 10}}
                        onChange={(e) => {setMODAL_spAccoTaName(e.target.value)}}
                        value={MODAL_spAccoTaName}
                    />
                </div>
            </Modal>
        </div>
    );
}