/**
 * @see 工具权限管理-角色管理
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Input,
  Button,
  Table,
  DatePicker,
  message,
  Select,
  Switch,
  Radio,
  Spin,
  PageHeader,
  Popconfirm,
  Modal,
  Form,
  InputNumber,
} from 'antd';
import api from 'api';
import { TOOLTITLE, USER_RSI_OPTIONS } from '../const';
import { safeValueShow } from '../../smsPlatform/fn';
const { authRolePageList, saveUpdateAuthRole, deleteAuthRole } = api;
const FormItem = Form.Item;
const { Option } = Select;

class Interval {
  constructor(min, max) {
    this.min = min;
    this.max = max;
  }
}

class IntervalManager {
  constructor() {
    this.intervals = [];
  }

  addInterval(min, max) {
    const newInterval = new Interval(min, max);

    // Check for overlap with existing intervals
    for (const interval of this.intervals) {
      if (
        Number(newInterval.min) < Number(interval.max) &&
        Number(newInterval.max) > Number(interval.min)
      ) {
        // Toast()
        console.log(interval);
        console.log(newInterval);
        message.error('区间重复');
        return false;
      }
    }

    // No overlap found, add the new interval
    this.intervals.push(newInterval);
    return true;
    console.log('New interval added successfully.');
  }

  getIntervals() {
    return this.intervals;
  }
}
export default function() {
  const [init, setInit] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [spinToggle, setSpinToggle] = useState(false);
  // 当前页码
  const [offSet, setOffset] = useState(1);
  const [total, setTotal] = useState(0);
  // 新增modal
  const [visible, setVisible] = useState(false);
  const [formValues, setFormValues] = useState({});
  // const [intervalManager, setIntervalManager] = useState(new IntervalManager());
  const columns = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      key: 'roleName',
    },
    {
      title: '持仓条件',
      key: 'userType',
      render: (text, record) => {
        return `${safeValueShow(record.minAmount)}万元~${safeValueShow(record.maxAmount)}万元`;
      },
    },
    {
      title: '操作',
      key: 'operator',
      render: (row, record, index) => (
        <div>
          <Button
            type="primary"
            onClick={() => {
              handleEditTenBillion(row);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="是否删除该角色？"
            okText="删除"
            cancelText="取消"
            onConfirm={() => {
              deleteRole(row);
            }}
          >
            <Button style={{ marginLeft: '20px' }} type="danger">
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  /**
   * 获取数据
   * @param {String} suffix
   */
  const fetchData = offSet => {
    setSpinToggle(true);
    authRolePageList({
      offSet,
      pageSize: 1000,
    }).then(data => {
      if (data.statusCode === 200) {
        setDataSource(data?.data.map(e => ({ ...JSON.parse(e.roleCondition || '{}'), ...e })));
        // intervalManager()
        setTotal(data?.data?.size);
        setSpinToggle(false);
      }
    });
  };

  // 页码改变
  const onPaginationChange = useCallback(page => {
    setOffset(page);
    fetchData(page);
  }, []);

  const add = () => {
    setVisible(true);
    setFormValues({
      userType: "1"
    });
  };

  const onCancel = () => {
    setVisible(false);
  };

  const checkNameRepeat = (name, id) => {
    const names = dataSource.map(item => {
      if (id !== item.id) return item.roleName;
    });
    return names.includes(name);
  };
  const checkQuJianRepeat = (min, max, paramId) => {
    const intervalManager = new IntervalManager();
    dataSource.forEach(item => {
      if (item.id !== paramId) {
        if (!intervalManager.addInterval(item.minAmount, item.maxAmount)) return false;
      }
    });
    // console.log(intervalManager.getIntervals())
    if (!intervalManager.addInterval(min, max)) return false;
    return true;
  };
  const onFinish = values => {
    console.log(formValues);
    const params = JSON.parse(JSON.stringify(formValues));
    //开始校验名字是否重复
    if (checkNameRepeat(params.roleName, params.id)) {
      message.error('您的角色名称设置重复，请修改');
      return;
    }
    if (!params.roleName) {
      message.error('请填写角色名称');
      return;
    }
    if (isNaN(formValues.minAmount) || isNaN(formValues.maxAmount)) {
      message.error('请检查输入金额是否符合格式');
      return;
    }
    if (Number(formValues.minAmount) > Number(formValues.maxAmount)) {
      message.error('下限须小于上限');
      return;
    }
    if (!checkQuJianRepeat(formValues.minAmount, formValues.maxAmount, params.id)) {
      return;
    }
    const { id, roleName, maxAmount, minAmount, userType } = formValues;
    saveUpdateAuthRole({
      roleName,
      id,
      userType,
      roleCondition: JSON.stringify({
        maxAmount,
        minAmount,
      }),
    }).then(data => {
      if (data.statusCode === 200 && data.status_code === 0) {
        message.success(params.id ? '修改' : '添加' + '成功');
        fetchData(1);
      } else {
        message.error('服务器异常');
      }
    });
    setVisible(false);
  };

  /**
   * @see 删除角色
   * @param {*} row
   */
  const deleteRole = row => {
    deleteAuthRole({ idList: row.id }).then(res => {
      if (res.status_code === 0) {
        message.success('删除成功');
        fetchData(1);
      } else {
        message.error('服务器异常');
      }
    });
  };

  const onchangeImp = (key, value) => {
    const obj = JSON.parse(JSON.stringify(formValues));
    if (key === 'userType' && value === '0') {
      delete obj['monety'];
    }
    obj[key] = value;
    setFormValues(obj);
  };

  /**
   * @see 编辑按钮
   */
  const handleEditTenBillion = row => {
    const { id, roleName, userType, minAmount, maxAmount } = row;
    setFormValues({
      id,
      roleName,
      userType,
      minAmount,
      maxAmount,
    });
    setVisible(true);
  };
  useEffect(() => {
    fetchData(1);
    setInit(true);
  }, []);

  if (!init) return '加载中';
  return (
    <article className="salaryManagerBackgroundColor" style={{ width: '1500px' }}>
      <section>
        <PageHeader
          title={TOOLTITLE.ROLE}
          extra={
            <Button type="primary" onClick={add}>
              添加
            </Button>
          }
        ></PageHeader>
      </section>
      <center>
        <div style={{ padding: 10 }}>
          <Spin spinning={spinToggle} tip="加载中...">
            <Table
              style={{ marginTop: 30 }}
              columns={columns}
              dataSource={dataSource}
              pagination={{
                pageSize: 20,
                onChange: onPaginationChange,
                total: total,
              }}
            />
          </Spin>
        </div>
      </center>
      <Modal
        title={formValues.id ? '编辑角色' : '新增角色'}
        okText="确认修改"
        cancelText="取消"
        visible={visible}
        onOk={onFinish}
        onCancel={onCancel}
        width={420}
      >
        <Form
          labelCol={{
            span: 10,
          }}
          wrapperCol={{
            span: 10,
          }}
        >
          <FormItem label="角色名称" name="roleName" required>
            <Input
              placeholder="请输入角色名称"
              value={formValues.roleName}
              onChange={e => {
                onchangeImp('roleName', e.target.value);
              }}
            />
          </FormItem>
          <FormItem label="用户类型" required>
            <Radio.Group
              value={String(formValues.userType)}
              onChange={e => {
                onchangeImp('userType', e.target.value);
              }}
            >
              <Radio value={'1'}>净入金</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label="请输入持仓条件下限" name="minAmount" required>
            <Input
              placeholder="请输入下限"
              value={formValues.minAmount}
              onChange={e => {
                onchangeImp('minAmount', e.target.value);
              }}
              addonAfter={<span>万元</span>}
            />
          </FormItem>
          <FormItem label="请输入持仓条件上限" name="maxAmount" required>
            <Input
              placeholder="请输入上限"
              value={formValues.maxAmount}
              onChange={e => {
                onchangeImp('maxAmount', e.target.value);
              }}
              addonAfter={<span>万元</span>}
            />
          </FormItem>
        </Form>
      </Modal>
    </article>
  );
}
