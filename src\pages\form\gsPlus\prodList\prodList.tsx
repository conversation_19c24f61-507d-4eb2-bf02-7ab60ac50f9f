import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Tag, Drawer, Toolt<PERSON>, Popconfirm } from 'antd';
import classnames from 'classnames';
import styles from './index.less';

import ProdCard from './prodCard';

interface PRODITEM {
  showType: string;
  fundCode: string;
  fundName: string;
  id?: string;
  recommendText: string;
  tags?: string[];
  tab?: string[];
  jumpUrl?: string;
}

interface dataProps {
  prodList: PRODITEM[];
  saveData: any;
  changeStatus: any;
  deleteProd: any;
  prodTag: string[];
  addProdData: any;
}

export default function(props: dataProps) {
  const { prodList, saveData, changeStatus, deleteProd, prodTag, addProdData } = props;

  const [showDrawer, set_showDrawer] = useState(false);
  const [showDrawer2, set_showDrawer2] = useState(false);
  const [editData, set_editData] = useState(null);
  const [editIndex, set_editIndex] = useState(-1);

  /**
   * 编辑产品
   * @param item
   * @param index
   */
  const editProd = (item: any, index: number) => {
    set_editIndex(index);
    set_editData(item);
    set_showDrawer(true);
  };

  /**
   * 完成编辑
   * @param data
   * @param index
   */
  const handleProdData = (data: any, index: number) => {
    saveData(data, index);
    set_showDrawer(false);
    set_editIndex(-1);
    set_editData(null);
  };

  /**
   * 添加产品
   */
  const addProd = (data: any, index: number) => {
    addProdData(data);
    set_showDrawer2(false);
  };

  return (
    <article>
      <div className={styles['m-head']} style={{ width: 1450 }}>
        <div className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')} style={{ width: 1450 }}>
          <p style={{ width: 80 }}>列表序号</p>
          <p style={{ width: 120 }}>基金代码</p>
          <p style={{ width: 200 }}>基金名称</p>
          <p style={{ width: 300 }}>推荐文案</p>
          <p style={{ width: 150 }}>系列</p>
          <p style={{ width: 300 }}>标签</p>
          <p style={{ width: 80 }}>状态</p>
          <p style={{ width: 220 }}>操作</p>
        </div>
      </div>
      <div style={{ height: 600, overflow: 'scroll' }}>
        {prodList.map((tag: any, index: number) => (
          <div key={tag.fundCode} className={styles['tag']} style={{ width: 1450 }}>
            <div
              className={classnames(styles['m-row'], 'f-tc', 'u-l-middle')}
              style={{ width: 1450 }}
            >
              <p style={{ width: 80 }}>{index + 1}</p>
              <p style={{ width: 120 }}>{tag.fundCode}</p>
              <p style={{ width: 200 }} className={classnames('f-ellipsis')}>
                {tag.fundName}
              </p>
              <Tooltip title={tag.recommendText}>
                <p style={{ width: 300 }} className={classnames('f-ellipsis')}>
                  {tag.recommendText}
                </p>
              </Tooltip>
              <p style={{ width: 150, overflow: 'hidden' }}>
                {tag.tab.map((item: string, index: number) => (
                  <Tag key={index} color="#4691ee">
                    {item === '001' ? '系列1' : item === '002' ? '系列2' : '--'}
                  </Tag>
                ))}
              </p>
              <p style={{ width: 300, overflow: 'hidden' }}>
                {tag.tags.map((item: string, index: number) => (
                  <Tag key={index} color="#ff801a">
                    {item}
                  </Tag>
                ))}
              </p>
              <p style={{ width: 80 }}>
                <Tag color={tag.showType === '0' ? '#fe5d4e' : '#ccc'}>
                  {tag.showType === '0' ? '开启' : '关闭'}
                </Tag>
              </p>
              <div style={{ width: 220 }}>
                <Button
                  type="primary"
                  onClick={() => {
                    editProd(tag, index);
                  }}
                >
                  编辑
                </Button>
                <Button
                  type="primary"
                  style={{ marginLeft: 5 }}
                  onClick={() => {
                    changeStatus(index);
                  }}
                >
                  {tag.showType === '0' ? '关闭' : '开启'}
                </Button>
                <Popconfirm
                  title="确定删除吗？"
                  onConfirm={() => {
                    deleteProd(index);
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="danger" style={{ marginLeft: 5 }}>
                    删除
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </div>
        ))}
      </div>
      <Button
        type="primary"
        onClick={() => {
          set_showDrawer2(true);
        }}
        style={{ marginTop: 10 }}
      >
        新增
      </Button>
      <Drawer
        title="编辑产品"
        placement="right"
        visible={showDrawer}
        onClose={() => {
          set_showDrawer(false);
        }}
        width={800}
        destroyOnClose={true}
      >
        <ProdCard
          baseData={editData}
          baseIndex={editIndex}
          prodTag={prodTag}
          handleProdData={handleProdData}
          type="list"
					ways="edit"
        />
      </Drawer>
      <Drawer
        title="新增产品"
        placement="right"
        visible={showDrawer2}
        onClose={() => {
          set_showDrawer2(false);
        }}
        width={800}
        destroyOnClose={true}
      >
        <ProdCard
          baseData={{}}
          baseIndex={-1}
          prodTag={prodTag}
          handleProdData={addProd}
          type="list"
        />
      </Drawer>
    </article>
  );
}
