import React, { useEffect, useState } from "react";
import { Tabs } from 'antd';
import CP_LIST from './list';
import CP_DETAIL from './detail';

const { TabPane } = Tabs;

export default function () {

    const [tabPanes, setTabPanes] = useState([]);
    const [addTabPane, setAddTabPane] = useState({});
    const [activeTabKey, setActiveTabKey] = useState('list');

    const goDetail = (record) => {
        setAddTabPane({
            title: '详情-' + record.spAccoName,
            key: 'detail_' + record.groupAccountId,
            content: (
                <CP_DETAIL 
                    record={record}
                />
            )
        });
    }

    let panes = [{
        title: '交易账号管理',
        key: 'list',
        closable: false,
        content: (
            <CP_LIST 
                goDetail={goDetail}
            />
        )
    }]

    const seeSame = (arr, obj) => {
        for (let i = 0; i < arr.length; i++) {
            if (arr[i].key === obj.key) {return true;}
        }
        return false;
    }

    const onEdit = (targetKey, action) => {
        if (action === 'remove') {
            let lastIndex;
            tabPanes.map((pane, i) => {
                if (pane.key === targetKey) {
                    lastIndex = i - 1;
                }
            })
            const _panes = tabPanes.filter(pane => pane.key !== targetKey);
            if (_panes.length && activeTabKey === targetKey) {
                if (lastIndex >= 0) {
                    setActiveTabKey(_panes[lastIndex].key);
                } else {
                    setActiveTabKey(_panes[0].key);
                }
            }
            setTabPanes([..._panes]);
        }
    }

    useEffect(() => {
        if (JSON.stringify(addTabPane) !== '{}') {
            let _tabPanes = [...tabPanes];
            if (!seeSame(_tabPanes, addTabPane)) {
                _tabPanes.push(addTabPane);
                setTabPanes([..._tabPanes]);
            }
            setActiveTabKey(addTabPane.key);
        }
    }, [addTabPane]);

    useEffect(() => {
        setTabPanes([...panes])
    }, []);

    return (
        <div style={{ padding: 10 }}>
            <Tabs
                hideAdd
                activeKey={activeTabKey}
                onChange={setActiveTabKey}
                onEdit={onEdit}
                type="editable-card"
            >
                {
                    tabPanes.map(pane => (
                        <TabPane
                            tab={pane.title}
                            key={pane.key}
                            closable={pane.closable}
                        >
                            {pane.content}
                        </TabPane>
                    ))
                }
            </Tabs>
        </div>
    );
}
