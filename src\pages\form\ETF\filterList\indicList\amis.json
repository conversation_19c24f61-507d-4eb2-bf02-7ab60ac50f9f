{"type": "page", "title": "ETF综合列表页配置", "body": [{"type": "combo", "id": "u:3e870c6efb32", "label": "", "name": "indicList", "multiple": true, "mode": "normal", "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:062ee3e7c613"}, "items": [{"type": "form", "title": "数据项:${id}-${indicName}", "body": [{"type": "fieldSet", "title": "折叠/展开数据项配置", "collapsable": true, "collapsed": true, "body": [{"type": "uuid", "name": "id", "id": "u:959f08b30a67", "unique": true, "length": 8}, {"type": "input-text", "name": "indicName", "id": "u:8b8adb8f0f18", "label": "数据项名称", "required": true, "mode": "inline", "size": "sm"}, {"type": "input-text", "label": "数据项描述", "name": "indicDesc", "id": "u:7c8dfeb65c42", "mode": "inline", "size": "lg"}, {"type": "select", "label": "数据项类型", "name": "indicType", "options": [{"label": "文本", "value": "STRING"}, {"label": "数字", "value": "BIGDECIMAL"}, {"label": "链接", "value": "URL"}, {"label": "JSON", "value": "JSON"}, {"label": "列表", "value": "LIST"}], "id": "u:fd97472b3335", "multiple": false, "required": true, "mode": "inline", "size": "sm"}, {"type": "select", "label": "数据来源", "name": "dataCenter", "options": [{"label": "问财", "value": "wencaiNlp"}, {"label": "ETF行情列表", "value": "etfTypeList"}], "id": "u:f17934db0ada", "multiple": false, "mode": "inline", "required": true, "size": "sm"}, {"type": "input-text", "label": "来源详情", "name": "dataIndic", "id": "u:ff2ee67a7930", "mode": "inline", "required": true, "labelRemark": {"trigger": ["hover"], "className": "Remark--warning", "placement": "top", "content": "数据来源指标的id、问财的问句、etf的榜单key", "title": "数据来源详情"}, "size": "lg"}, {"type": "input-number", "label": "刷新时间", "name": "refreshTime", "keyboard": true, "id": "u:3f32c631f73d", "step": 1, "mode": "inline", "labelRemark": {"trigger": ["hover"], "className": "Remark--warning", "placement": "top", "title": "主动刷新时间", "content": "单位分钟，默认-1不更新"}, "value": -1, "required": true, "size": "xs"}, {"type": "select", "label": "数据范围类型", "name": "range.rangeType", "options": [{"label": "单选", "value": "SINGLE"}, {"label": "单点按钮", "value": "RADIO"}], "id": "u:e0bec0c668d6", "multiple": false, "mode": "inline", "required": true, "size": "sm"}, {"type": "select", "label": "数据范围来源", "name": "range.from", "options": [{"label": "手动指定", "value": ""}, {"label": "预缓存列表", "value": "REDIS_LIST"}], "id": "u:e0bec0c668d6", "multiple": false, "mode": "inline", "size": "sm"}, {"type": "input-text", "label": "问句占位符", "name": "range.show", "id": "u:617e420fab78", "mode": "inline", "labelRemark": {"trigger": ["hover"], "className": "Remark--warning", "placement": "top", "content": "SHOW_PERCENT_ONE,SHOW_PERCENT_TWO,TRIM,支持只有一个%s的占位符，例如：ETF;按持仓占比从大到小排序;重仓%s"}, "visibleOn": "this.hasOwnProperty('range') && this.range.from && this.range.from === 'REDIS_LIST'", "clearValueOnHidden": true, "size": "lg"}, {"type": "combo", "label": "规则列表", "name": "range.list", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:f83b000bfade"}, "items": [{"type": "uuid", "name": "rangeId", "id": "u:959f08b30a67", "unique": true, "length": 8}, {"type": "input-text", "label": "可选范围名称", "name": "rangeName", "id": "u:2f4faf25e997", "mode": "inline", "required": true, "size": "sm"}, {"type": "combo", "label": "规则列表", "name": "filterRule", "multiple": true, "addable": true, "removable": true, "removableMode": "icon", "addBtn": {"label": "新增", "icon": "fa fa-plus", "level": "primary", "size": "sm", "id": "u:c7578e50e0ed"}, "items": [{"type": "select", "name": "operate", "placeholder": "比较类型", "options": [{"label": "大于", "value": "GREATER"}, {"label": "大于等于", "value": "GREATER_EQUAL"}, {"label": "小于", "value": "LESS"}, {"label": "小于等于", "value": "LESS_EQUAL"}, {"label": "等于", "value": "EQUAL"}, {"label": "不等于", "value": "NOT_EQUAL"}, {"label": "为空", "value": "NULL"}, {"label": "不为空", "value": "NOT_NULL"}], "id": "u:962c6d478613", "multiple": false, "required": true, "mode": "inline", "size": "sm"}, {"type": "input-text", "name": "target", "placeholder": "目标值", "id": "u:ee4799126c60", "required": true, "mode": "inline", "size": "lg"}], "id": "u:90141ba2bc95", "syncFields": [], "mode": "inline", "strictMode": true}], "id": "u:41d9e52b9e41", "syncFields": [], "multiLine": true, "mode": "inline", "strictMode": true, "disabledOn": "id === 't0' || id === 'etf_hq_rank'"}], "id": "u:5930fe039318"}], "mode": "normal", "id": "u:e3dab154c5eb", "submitText": "保存", "api": {"url": "/common_config/hash_data_save", "method": "post", "messages": {}, "data": {"key": "normal_config_etf_hqtab_filter", "propName": "${id}", "value": {"&": "$$"}}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}", "requestAdaptor": "if (api.body.value) {\r\n  api.body.value = JSON.stringify(api.body.value);\r\n}"}}], "syncFields": [], "strictMode": true, "multiLine": false, "deleteApi": {"url": "/common_config/hash_data_del", "method": "post", "messages": {}, "data": {"data": {"&": "$$"}, "key": "normal_config_etf_hqtab_filter", "propName": "${id}"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: payload.message,\r\n}"}, "size": "full"}, {"type": "form", "title": "", "body": [{"label": "", "type": "textarea", "name": "config", "id": "u:892fa08400ce", "minRows": 3, "maxRows": 20}], "id": "u:557953fb3741", "actions": [{"type": "button", "label": "获取配置", "onEvent": {"click": {"actions": [{"componentId": "u:892fa08400ce", "args": {"value": "${indicList}"}, "actionType": "setValue"}]}}, "id": "u:3f6d93564d96", "level": "primary"}, {"type": "button", "label": "导入配置", "onEvent": {"click": {"actions": [{"args": {"fromPage": true, "value": "${DECODEJSON(config)}"}, "actionType": "setValue", "componentId": "u:3e870c6efb32"}]}}, "id": "u:0601c0ba6d8f", "level": "primary"}]}], "regions": ["body", "header"], "id": "u:9179dc7a5d0b", "initApi": {"url": "/common_config/hash_data_get_all", "method": "get", "messages": {}, "dataType": "form", "data": {"key": "normal_config_etf_hqtab_filter"}, "adaptor": "const indicList = [];\r\n\r\nif (payload.data) {\r\n  for (const [key, value] of Object.entries(payload.data)) {\r\n    indicList.push(JSON.parse(value));\r\n  }\r\n}\r\nreturn {\r\n  data: {\r\n    indicList: indicList,\r\n  },\r\n};"}}