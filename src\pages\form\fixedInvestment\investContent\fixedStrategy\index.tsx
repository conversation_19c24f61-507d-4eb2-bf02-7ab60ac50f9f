import React, { useEffect, useState } from 'react';
import { Button, Collapse, message, Tag } from 'antd';
import WrappedStrictCard from './myCard';
import classnames from 'classnames';
import styles from '../index.less';

const { Panel } = Collapse;

export interface iFixedStrategyData {
    title: string;
    link: string;
    key: string;
}
interface iFixedStrategyProps {
    handleData: (data: iFixedStrategyData[]) => void;
    fixedStrategyData: iFixedStrategyData[];
    isModify: boolean;
    handleModify: (flag: boolean) => void;
}
function StrictElection({handleData, fixedStrategyData, isModify, handleModify}: iFixedStrategyProps) {
    const newAdd = () => {
        
        let arr = [...fixedStrategyData];
        let obj = {
            title: '',
            link: '',
            key: +new Date() + '',
        }
        arr.push(obj);
        if (arr.length > 5) {
            message.info('定投攻略模块配置项不超过5条');
            return;
        }
        handleData(arr);
    }
    const handleDelete = (key: number) => {
        if (!isModify) handleModify(true);
        let arr = [...fixedStrategyData];
        arr.splice(Number(key), 1);
        handleData(arr);
    }
    const modifyStrictElectionData = (data: iFixedStrategyData, num: number) => {
        if (!isModify) handleModify(true);
        let arr = [...fixedStrategyData];
        arr[num] = data;
        handleData(arr);
    }
    const panelHeader = (obj: iFixedStrategyData, num: number) => {
        return (
            <div className={classnames(styles['m-panel-header'], 'u-flex')}>
                <span style={{marginRight: 40}}>{Number(num) + 1}</span>
                { obj.title && <span>Q{Number(num) + 1}: {obj.title}</span> }
            </div>
        )
    }
    const goUp = (e: any, item: iFixedStrategyData, index: number) => {
        e.stopPropagation();
        if (index === 0) {
            message.info('已在最上方')
            return
        }
        let arr = [...fixedStrategyData];
        arr.splice(index, 1);
        arr.splice(index - 1, 0, item);
        handleData(arr);
        if (!isModify) handleModify(true);
    }
    const goDown = (e: any, item: iFixedStrategyData, index: number) => {
        e.stopPropagation();
        if (index === fixedStrategyData?.length - 1) {
            message.info('已在最下方')
            return
        }
        let arr = [...fixedStrategyData];
        arr.splice(index, 1);
        arr.splice(index + 1, 0, item);
        handleData(arr);
        if (!isModify) handleModify(true);
        
    }
    return <div style={{marginTop: 40}}>
        <h1 className="g-fs28 f-bold">定投攻略：</h1>
        <Collapse>
            {
                fixedStrategyData?.map((item: iFixedStrategyData, index: number) => {
                    return (
                        <Panel 
                            header={panelHeader(item, index)} 
                            key={item.key}
                            extra={<div style={{marginTop: -5}}><Button onClick={(e) => { goUp(e, item, index) }}>上移</Button><Button onClick={(e) => { goDown(e, item, index) }}>下移</Button></div>}
                        >
                            <WrappedStrictCard currentData={item} handleData={(data) => modifyStrictElectionData(data, index)} handleDelete={() => handleDelete(index)}/>
                        </Panel>
                    )
                })
            }
            
        </Collapse>
        <div style={{marginTop: 20}}>
          <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
        </div>
    </div>
}
export default React.memo(StrictElection);
