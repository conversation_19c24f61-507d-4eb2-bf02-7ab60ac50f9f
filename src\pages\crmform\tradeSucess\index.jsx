/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React, { Fragment } from 'react';
import {<PERSON><PERSON>, Card, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import FormRender from 'form-render/lib/antd';
import schema from './form.json';

const { fetchTradeSucess, postTradeSucess } = api;

@autobind
class tradeSucess extends React.Component {
    constructor (props) {
        super(props);
        this.state = {formData: schema.formData || {}};
    }


    onChange = formData => {
        this.setState({ formData }, () => {console.log(this.state)});
    }
    
    dataInit () {
        fetchTradeSucess({
          type: 'query'
      }).then(data => {
          
        if (data.data) {
          data = data.data;
          console.log(data);
          let _obj = {};
          _obj.personaInfo = data.personaInfo;
          _obj.nonPersonaInfo = data.nonPersonaInfo;
          this.setState({formData: _obj});
        }
      })
    }
    
    updateForm () {
        const {formData} = this.state;
        let data = formData.personaInfo.concat(formData.nonPersonaInfo);
        for (let i = 0; i < data.length; i ++) {
            if (data[i].photoUrl !== '' || data[i].redirectUrl !== '' || data[i].subTitle !== '' || data[i].mainTitle !== '') {
                if (data[i].photoUrl === '' || data[i].redirectUrl === '' || data[i].subTitle === '' || data[i].mainTitle === '') {
                    message.error(`请填全${data[i].name}`);
                    return;
                }
            }
        }
        console.log(this.state.formData)
        postTradeSucess ({
            type: 'update',
            value: JSON.stringify(this.state.formData)
        }).then(data => {
            if (data) {
                message.success('保存成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }
    componentDidMount () {
        this.dataInit();
        // this.newBoyDataInit();
    }

    render () {
        return (
            <section style={{ width: '800px' }}>

                <FormRender
                    propsSchema={schema.propsSchema}
                    formData={this.state.formData}
                    onChange={this.onChange}
                    displayType="row"
                />

                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={this.updateForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                        // style={{ marginLeft: '300px' }}
                    >
                        提交修改
                    </Button>
                </Popconfirm>

            </section>
        )
    }
}

export default tradeSucess;