:global {
    .elite-hold-edit {
        .config-area {
            padding-bottom: 60px;
            .module-title {
                font-size: 20px;
                font-weight: bold;
                line-height: 40px;
                border-bottom: 1px solid rgb(233, 233, 233);
                .add-btn {
                    margin-left: 10px;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 40px;
                    color: #1677FF;
                    cursor: pointer;
                }
            }
            .label {
                margin-right: 10px;
                min-width: 84px;
                &.require {
                    &::before {
                         content: '*';
                         display: 'inline';
                         color: #f00;
                    }
                }
            }
            .upload-img-box {
                .flex(row, center, center, nowrap);
                border: 1px dashed rgb(233, 233, 233);
                border-radius: 8px;
                cursor: pointer;
                .plus-sign {
                    font-size: 32px;
                    color: rgba(0,0,0,0.45);
                    font-weight: bold;
                }
            }
            .row {
                .flex(row, flex-start, center, wrap);
                padding: 10px 0;
            }
            .hold-detail, .hold-analysis, .investment-trends {
                .label {
                    margin: 0 10px;
                    min-width: 56px;
                }
            }
            .investment-trends {
                .investment-trend {
                    position: relative;
                    margin: 10px 0;
                    border: 1px solid rgb(233, 233, 233);
                    border-radius: 10px;
                    .delete-btn {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                    }
                }
            }
        }
        .footer {
            position: absolute;
            right: 0px;
            bottom: 0px;
            width: 100%;
            border-top: 1px solid rgb(233, 233, 233);
            padding: 10px 16px;
            background: rgb(255, 255, 255);
            text-align: left;
        }
    }
}

.flex(@dir, @j, @a, @wrap) {
    display: flex;
    flex-direction: @dir;
    justify-content: @j;
    align-items: @a;
    flex-wrap: @wrap;
}
