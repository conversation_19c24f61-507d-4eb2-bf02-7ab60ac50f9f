import React, { useState, useEffect } from 'react';
import api from 'api';
import { Input, Select, message } from 'antd';

interface IBaseProps {
	fundName?: string;
	fundCode?: string;
	fundDesc?: string;
	jumpUrl?: string;
	imgUrl?: string;
	syRange?: string;
	thsJumpUrl?: string;
}

interface IProps {
	baseData?: IBaseProps;
	saveData: any;
}

export default function({baseData = {}, saveData}: IProps) {

	const { fetchFundNameByCode } = api;

	/**
   * 获取基金名称
   */
	 const checkFundCode = () => {
    if (baseData.fundCode === '') return;
    if (baseData.fundCode && baseData.fundCode.length === 6 && !baseData.fundCode.includes(' ')) {
      fetchFundNameByCode({
        fundCode: baseData.fundCode,
      }).then((data: any) => {
        if (data.code === '0000') {
          data = data.data;
          if (data) {
						watchChange(data.name, 'fundName');
          } else {
            message.error('该基金无数据，请核对基金代码');
          }
        } else {
          message.error('请求基金详情接口失败，请手动修改基金名称');
        }
      });
    } else {
      message.error('基金代码格式错误！');
    }
  };

	const watchChange = (value: string, type: ('fundName'|'fundCode'|'fundDesc'|'jumpUrl'|'imgUrl'|'syRange'|'thsJumpUrl')) => {
		let _data = {
			fundName: baseData.fundName,
			fundCode: baseData.fundCode,
			fundDesc: baseData.fundDesc,
			jumpUrl: baseData.jumpUrl,
			imgUrl: baseData.imgUrl,
			syRange: baseData.syRange,
			thsJumpUrl: baseData.thsJumpUrl
		}
		if (type === 'fundCode') {
			_data.imgUrl = `https://fund.10jqka.com.cn/fundminicon/${value}_${baseData.syRange}.jpg`
		} else if (type === 'syRange') {
			_data.imgUrl = `https://fund.10jqka.com.cn/fundminicon/${baseData.fundCode}_${value}.jpg`
		}
		_data[type] = value;
		saveData(_data);
	}

  return (
    <div>
      <div>
        <span>基金代码<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 300 }}
          placeholder="请输入基金代码"
          value={baseData.fundCode || ''}
					onBlur={checkFundCode}
          onChange={e => {
            watchChange(e.target.value, 'fundCode');
          }}
        />
      </div>
      <div style={{marginTop: 10}}>
        <span>基金名称<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 300 }}
          placeholder="输入基金代码后自动显示，可手动编辑"
          value={baseData.fundName || ''}
          onChange={e => {
            watchChange(e.target.value, 'fundName');
          }}
        />
      </div>
			<div style={{marginTop: 10}}>
        <span>基金描述<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 300 }}
          placeholder="请输入基金描述"
          value={baseData.fundDesc || ''}
          onChange={e => {
            watchChange(e.target.value, 'fundDesc');
          }}
        />
      </div>
			<div style={{marginTop: 10}}>
        <span>基金跳转链接：</span>
        <Input
          style={{ width: 500 }}
          placeholder="请输入跳转链接，不填默认跳基金详情页"
          value={baseData.jumpUrl || ''}
          onChange={e => {
            watchChange(e.target.value, 'jumpUrl');
          }}
        />
      </div>
			<div style={{marginTop: 10}}>
        <span>手炒跳转链接：</span>
        <Input
          style={{ width: 500 }}
          placeholder="请输入跳转链接，不填默认跳基金详情页"
          value={baseData.thsJumpUrl || ''}
          onChange={e => {
            watchChange(e.target.value, 'thsJumpUrl');
          }}
        />
      </div>
			<div style={{marginTop: 10}}>
        <span>收益区间<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Select 
					style={{width: 120}} 
					value={baseData.syRange || ''}
					onChange={(value: string) => {
						watchChange(value, 'syRange');
					}}
				>
					<Select.Option value="month">近一月</Select.Option>
					<Select.Option value="tmonth">近三月</Select.Option>
					<Select.Option value="hyear">近半年</Select.Option>
					<Select.Option value="year">近一年</Select.Option>
					<Select.Option value="tyear">近三年</Select.Option>
				</Select>
      </div>
			<div style={{marginTop: 10}}>
        <span>走势图链接<i style={{color: '#fe5d4e'}}> *</i>：</span>
        <Input
          style={{ width: 500 }}
          placeholder="输入基金代码后自动显示，可手动编辑"
          value={baseData.imgUrl || ''}
          onChange={e => {
            watchChange(e.target.value, 'imgUrl');
          }}
        />
      </div>
    </div>
  );
}
