import React, { useState, useEffect } from 'react';
import styles from './index.less';
import api from 'api';
import moment from 'moment';
import {Row, Col, Input, Button, Divider, message, Drawer, Table, Modal, Spin, Select } from 'antd';

const { Option  } = Select;
const { confirm } = Modal;
message.config({
    duration: 3,// 持续时间
    maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
    top: 70,// 到页面顶部距离
});
const {getGuessRiseFallTaskList, saveGuessRiseFallTaskForm, deleteGuessRiseFallTask, getGuessRiseFallTaskDetail, publishGuessRiseFallTaskList } = api;
const FORMDATAMAP = {
    'fundAccount': '基金开户(成就任务)',
    'fundFirBuy': '基金首购(成就任务)',
    'fundBuy': '购买基金(无限次任务)',
    'share': '分享(每日任务)',
    'browse': '浏览页面(每日任务)',
    'guess': '竞猜(每日任务)',
    'point': '积分',
    'message': '填写用户反馈(成就任务)',
}; // 表单数据映射关系
export default function() {
    useEffect(()=>{
        handleGetTaskList(1);
        initFormData();
    },[]);
    const [pageLoading,setPageLoading] = useState(false); // 页面loading
    const [saveBtnLoading,setSaveBtnLoading] = useState(false); // 保存按钮loading
    const [listData, setListData] = useState([]);// 任务列表数据
    const [listTotal,setListTotal] = useState(); // 列表总条数
    const [currentPageNum, setCurrentPageNum] = useState(); // 当前页码
    const [addOrRevise,setAddOrRevise] = useState(0); // 新增状态：0；编辑状态：1。
    const [isShowDrawer,setIsShowDrawer] = useState(false); // 是否显示新增/编辑面板
    const [formData,setFormData] = useState({}); // 表单数据
    const [isShowJump,setIsShowJump] = useState(false); // 是否显示跳转页面模块
    const pageSize = 10; // 列表页每页数量
    const eventTypeList = [
        {
            id: 'fundAccount',
            name: '基金开户(成就任务)'
        },
        {
            id: 'fundFirBuy',
            name: '基金首购(成就任务)'
        },
        {
            id: 'fundBuy',
            name: '购买基金(无限次任务)'
        },
        {
            id: 'share',
            name: '分享(每日任务)'
        },
        {
            id: 'browse',
            name: '浏览页面(每日任务)'
        },
        {
            id: 'guess',
            name: '竞猜(每日任务)'
        },
        {
            id: 'message',
            name: '填写用户反馈(成就任务)'
        },
    ]; // 事件类型列表
    const rewardTypeList = [
        {
            id: 'point',
            name: '积分'
        },
    ]; // 奖励类型列表
    const tableColumns = [
        {
            title: '任务id',
            dataIndex: 'taskId',
            key: 'taskId',
          },
        {
          title: '任务名称',
          dataIndex: 'taskName',
          key: 'taskName',
        },
        {
          title: '任务条件',
          dataIndex: 'condition',
          key: 'condition',
        },
        {
          title: '任务奖励',
          dataIndex: 'reward',
          key: 'reward',
        },
        {
          title: '排序',
          dataIndex: 'taskSort',
          key: 'taskSort',
        },
        {
          title: '操作时间',
          dataIndex: 'operateTime',
          key: 'operateTime',
        },
        {
          title: '操作',
          key: 'action',
          render: (row, record, index) => (
              <div>
                    <span style={{color: '#1890FF', cursor: 'pointer', marginRight: '10px'}} onClick={()=>{openEditDrawer(record)}}>编辑</span>
                    <span style={{color: '#1890FF', cursor: 'pointer'}} onClick={() => deleteRow(record)}>删除</span>
              </div>
          ),
        },
    ]; // 列表项

    // 获取任务列表
    const handleGetTaskList = (pageNum) => {
        setPageLoading(true);
        setCurrentPageNum(pageNum);
        getGuessRiseFallTaskList({},pageNum,pageSize).then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                const data = res.data;
                const _listData = data.pointGuessTaskDtoList && data.pointGuessTaskDtoList.map((item,index)=>{
                    const _item = {};
                    _item.taskId = item.taskId;
                    _item.taskName = item.taskName;
                    _item.condition = `${FORMDATAMAP[item.taskCondition?.eventType]}/${item.taskCondition?.eventNumber}次`;
                    _item.reward = `${item.taskReward?.rewardNumber}${FORMDATAMAP[item.taskReward?.rewardType]}`;
                    _item.taskSort = item.taskSort;
                    _item.operateTime = item.updateTime;
                    return _item;
                });
                setListTotal(data.size);
                setListData(_listData);
            } else {
                message.error('获取列表失败');
            }
        });
    };
    // 点击删除操作
    const deleteRow = (record) => {
        confirm({
            title: '提示',
            content: `确定删除该任务？`,
            okText: '确定',
            okType: 'danger',
            cancelText: '取消',
            centered: 'true',
            onOk() {
                setPageLoading(true);
                deleteGuessRiseFallTask({taskId: record.taskId}).then((res)=>{
                    setPageLoading(false);
                    if (res.code === '0000') {
                        handleGetTaskList(currentPageNum);
                        message.success('删除成功');
                    } else {
                        message.error('删除失败');
                    }
                });
            }
          });
    };
    // 发布
    const onPublish = () => {
        setPageLoading(true);
        publishGuessRiseFallTaskList().then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                message.success('发布成功');
            } else {
                message.error(res.message);
            }
        });
    };
    // 初始化表单
    const initFormData = () => {
        let _formData = {};
        _formData = {
            taskName: '',
            taskId: '',
            taskCondition: {
                eventType: 'fundAccount',
                eventNumber: ''
            },
            taskReward: {
                rewardType: 'point',
                rewardNumber: '',
            },
            taskSort: '',
            jumpPage: '',
            updateTime: ''
        }
        setIsShowJump(false);
        setFormData(_formData);
    };
    // 列表项发生变化
    const changeFormItem = (type, value) => {
        const _formData = deepClone(formData);
        switch (type) {
            case 'eventType':
                _formData.taskCondition.eventType = value;
                if ( value === 'browse' || value === 'fundFirBuy' || value === 'fundBuy') {
                    _formData.jumpPage = '';
                    setIsShowJump(true);
                } else {
                    _formData.jumpPage = '';
                    setIsShowJump(false);
                }
                break;
            case 'eventNumber':
                _formData.taskCondition.eventNumber = value;
                break;
            case 'rewardType':
                _formData.taskReward.rewardType = value;
                break;
            case 'rewardNumber':
                _formData.taskReward.rewardNumber = value;
                break;
            case 'taskSort':
                _formData.taskSort = value;
                break;
            case 'jumpPage':
                _formData.jumpPage = value;
                break;
            default:
                _formData[type] = value;
                break;
        }
        setFormData(_formData);
    };
    // 表单校验
    const checkFormData = () => {
        let isPass = false;
        switch (true) {
            case !formData.taskName:
                message.error('请填写任务名称');
                break;
            case !formData.taskCondition?.eventNumber:
                message.error('请填写要求发生次数');
                break;
            case isNaN(formData.taskCondition?.eventNumber) || parseInt(formData.taskCondition?.eventNumber) < 1:
                message.error('要求发生次数不符合规则');
                break;
            case !formData.taskReward?.rewardNumber:
                message.error('请填写奖励数量');
                break;
            case isNaN(formData.taskReward?.rewardNumber) || parseInt(formData.taskReward?.rewardNumber) < 0:
                message.error('奖励数量不符合规则');
                break;
            case !formData.taskSort:
                message.error('请填写排序');
                break;
            case isNaN(formData.taskSort) || parseInt(formData.taskSort) < 0:
                message.error('排序不符合规则');
                break;
            case isShowJump && !formData.jumpPage:
                message.error('请填写跳转地址');
                break;
            default:
                isPass = true;
                break;
        }
        return isPass;
    };
    // 保存
    const saveForm = () => {
        if (!checkFormData()) return; 
        const params = deepClone(formData);
        params.updateTime = moment().format('YYYY-MM-DD HH:mm:ss');
        setSaveBtnLoading(true);
        saveGuessRiseFallTaskForm(params).then((res)=>{
            setSaveBtnLoading(false);
            if (res.code === '0000') {
                closeDrawer();
                if (addOrRevise === 0) {
                    handleGetTaskList(1);
                } else {
                    handleGetTaskList(currentPageNum);
                }
                message.success('保存成功');
            } else {
                message.error(res.message);
            }
        });
    };
    // 打开新增抽屉
    const openAddDrawer = () => {
        setAddOrRevise(0);
        initFormData();
        setIsShowDrawer(true);
    };
    // 打开编辑抽屉
    const openEditDrawer = (record) => {
        setPageLoading(true);
        // 调用接口获取返显数据
        getGuessRiseFallTaskDetail({taskId: record.taskId}).then((res)=>{
            setPageLoading(false);
            if (res.code === '0000') {
                let data = res.data;
                let _formData = deepClone(formData);
                _formData.taskName = deepClone(data.taskName);
                _formData.taskId = deepClone(data.taskId);
                _formData.taskCondition = deepClone(data.taskCondition);
                _formData.taskReward = deepClone(data.taskReward);
                _formData.taskSort = data.taskSort;
                _formData.jumpPage = data.jumpPage;
                setFormData(_formData);
                setAddOrRevise(1);
                setIsShowDrawer(true);
                if (_formData.taskCondition.eventType === 'browse' || _formData.taskCondition.eventType === 'fundFirBuy' || _formData.taskCondition.eventType === 'fundBuy') {
                    setIsShowJump(true);
                } else {
                    setIsShowJump(false);
                }
            } else {
                message.error('获取任务信息失败');
            }
        });
    };
    // 关闭新增/编辑抽屉
    const closeDrawer = () => {
        initFormData();
        setIsShowDrawer(false);
    };
    // 深拷贝对象/数组
    const deepClone = (obj) => {
        return JSON.parse(JSON.stringify(obj));
    };
    return (
        <div className={styles['task-page']}>
            <Spin spinning={pageLoading}>
                <Button type="danger " style={{marginBottom: '20px',marginRight: '20px'}}  onClick={()=>{onPublish()}}>发布</Button>
                <Button type="primary" style={{marginBottom: '20px'}} onClick={()=>{openAddDrawer()}}>新增</Button>
                <Table
                    columns={tableColumns}
                    dataSource={listData}
                    pagination={{ pageSize: pageSize, total: listTotal, current: currentPageNum}}
                    onChange={(pagination)=>{handleGetTaskList(pagination.current);}}
                    rowKey={record=>record.taskId}
                />
            </Spin>
            <Drawer
                className="task-page-drawer"
                title={addOrRevise === 0 ? '添加' : '编辑'}
                placement="right"
                width="1300"
                maskClosable={false}
                destroyOnClose={true}
                onClose={closeDrawer}
                visible={isShowDrawer}
            >
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>任务名称：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={8}>
                        <Input placeholder='请填入任务名称' value={formData.taskName} onChange={(e)=>{changeFormItem('taskName',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>任务条件：</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={6}>
                        <div>事件类型：</div>
                        <Select value={formData.taskCondition?.eventType} onChange={(e) => {changeFormItem('eventType',e)}} style={{width:'100%'}} disabled={addOrRevise === 1}>
                            {
                                eventTypeList && eventTypeList.map((item, index) => {
                                    return (
                                        <Option value={item.id} key={index}>{item.name}</Option>
                                    );
                                })
                            }
                        </Select>
                    </Col>
                </Row>
                <Row gutter={[40,14]}>
                    <Col span={6}>
                        <div>要求发生次数（输入值>=1）：</div>
                        <Input value={formData.taskCondition?.eventNumber} onChange={(e)=>{changeFormItem('eventNumber',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>任务奖励：（特殊情况：购买基金任务，填写的数额是指购买10元时获得的积分）</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={6}>
                        <div>奖励类型：</div>
                        <Select value={formData.taskReward?.rewardType} onChange={(e) => {changeFormItem('rewardType',e)}} style={{width:'100%'}}>
                            {
                                rewardTypeList && rewardTypeList.map((item, index) => {
                                    return (
                                        <Option value={item.id} key={index}>{item.name}</Option>
                                    );
                                })
                            }
                        </Select>
                    </Col>
                </Row>
                <Row gutter={[40,14]}>
                    <Col span={6}>
                        <div>奖励数量（输入值>=0）：</div>
                        <Input value={formData.taskReward?.rewardNumber} onChange={(e)=>{changeFormItem('rewardNumber',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600'}}><Col><span style={{color: '#f00'}}>*</span>排序：（数字从小到大，专区内从上至下，输入值>=0）</Col></Row>
                <Row gutter={[40,14]}>
                    <Col span={4}>
                        <Input value={formData.taskSort} onChange={(e)=>{changeFormItem('taskSort',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Divider />
                <Row gutter={[40,14]} style={{fontWeight: '600', display: isShowJump ? '' : 'none'}}><Col><span style={{color: '#f00'}}>*</span>跳转页面：</Col></Row>
                <Row gutter={[40,14]} style={{display: isShowJump ? '' : 'none'}}>
                    <Col span={16}>
                       <div>跳转到：</div>
                       <Input value={formData.jumpPage} onChange={(e)=>{changeFormItem('jumpPage',e.target.value)}}></Input>
                    </Col>
                </Row>
                <Row gutter={[40,14]}>
                    <Col span={8}>
                        <Button type="primary" style={{ marginRight: 24 }} loading={saveBtnLoading} onClick={()=>{saveForm()}}>保存</Button>
                        <Button type="danger" onClick={()=>{closeDrawer()}}>取消</Button>
                    </Col>
                </Row>
            </Drawer>
        </div>
    )
}

