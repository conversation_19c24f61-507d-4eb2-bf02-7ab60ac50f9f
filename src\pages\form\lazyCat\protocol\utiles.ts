export const TGProtoConfigKey='TGProtoConfig'
export const getTAConfigKey=(taCode)=>{
    return `${TGProtoConfigKey}_${taCode}`
}

export enum ProtoType{
    RISK='risk',//风险揭示书
    SERVE='serve',//服务协议
    RULE='rule',//业务规则
    STRATEGY='strategy',//策略说明书
    ALTER='alter'//基金备选库
}
export type ProtoConfig={name:string,link:string,type:ProtoType}
export type STRATEGYConfig={
    consultTacticsId:string;
    investConsultTacticsName:string;
    protos:ProtoConfig[];
    showPerformance?: 'Y' | 'N';
    fundAlterPool?:ProtoConfig
}
export type TAConfig={
    taCode?:string;
    taName?:string;
    share?:{
        protos?:ProtoConfig[];
        stProto?:ProtoConfig[];
    }
    strategy?:{
        [propName]:STRATEGYConfig;
    }
}
export type STRATEGYTable={
    taCode:string;
    taName:string;
    consultTacticsId:string;
    investConsultTacticsName:string;
    strategy?:STRATEGYConfig;
}

export type LogoCopyInfo = {
    // logo图片
    logoImg: string;
    // 策略详情页底部文案
    strategyDetailBottomCopy: string;
    // 转入规则-转入费率文案
    intoRuleRateCopy: string;
    // 转入规则-投顾服务费率文案
    intoRuleServiceRateCopy: string;
    // 转出规则-转出费率文案
    rollOutRateCopy: string;
}
export type TATable={
    taCode:string;
    taName:string;
    share?:TAConfig['share'];
    // 适当性匹配规则
    appropriatenessRule?: string;
    logoCopyInfo?: LogoCopyInfo;
    strategyList:STRATEGYTable[];
}
export type TAModel={
    taCode:string;
    taName:string;
    consultTacticsList:{
        consultTacticsId:string;
        investConsultTacticsName:string;
        taName:string;
        taCode:string;
    }[]
}
//json数据转表格数据
export function TableDataConfigAdapter(models:TAModel[],configMap:{[propName]:TAConfig}):TATable[]{
    return models.map(model=>{
        let config=null;
        try{
            if(configMap&&configMap[model.taCode]){
                config=JSON.parse(configMap[model.taCode])
            }
            
        }catch(e){
            console.error(e)
        }
       console.log(config)
        return {
            taCode:model.taCode,
            taName:model.taName,
            appropriatenessRule: config?.appropriatenessRule,
            share:config?.share,
            logoCopyInfo:config?.logoCopyInfo,
            strategyList:model.consultTacticsList?.map(item=>{
                return {
                    taCode:model.taCode,
                    taName:model.taName,
                    consultTacticsId:item.consultTacticsId,
                    investConsultTacticsName:item.investConsultTacticsName,
                    strategy:config?.strategy?config.strategy[item.consultTacticsId]:null
                }
            })||[]
        }
    })
}

export function TableToConfig(table:TATable[]):{[propName:string]:TAConfig}{
    const taConfig:{[propName:string]:TAConfig}={}
    table.forEach(item=>{
        const strategy={

        }
        item.strategyList?.forEach(tg=>{
            strategy[tg.consultTacticsId]={
                consultTacticsId:tg.consultTacticsId,
                investConsultTacticsName:tg.investConsultTacticsName,
                protos:tg.strategy?.protos,
                fundAlterPool:tg.strategy?.fundAlterPool
            }
           
        })    
        taConfig[item.taCode]={
            taCode:item.taCode,
            taName:item.taName,
            share:item.share,
            strategy
        }  
    });

    return taConfig;
}
export function TableToConfigSingle(table:TATable):TAConfig{
  
    const strategy={

    }
    table.strategyList?.forEach(tg=>{
            strategy[tg.consultTacticsId]={
                consultTacticsId:tg.consultTacticsId,
                investConsultTacticsName:tg.investConsultTacticsName,
                protos:tg.strategy?.protos,
                fundAlterPool:tg.strategy?.fundAlterPool,
                showPerformance:tg.strategy?.showPerformance
            }
           
    })    
    return {
        appropriatenessRule: table.appropriatenessRule,
        taCode:table.taCode,
        taName:table.taName,
        share:table.share,
        logoCopyInfo:table.logoCopyInfo,
        strategy
    };
}
export type FormValue={
    riskProtoName?:string;
    riskProtoLink?:string;
    serveProtoName?:string;
    serveProtoLink?:string;
    ruleProtoName?:string;
    ruleProtoLink?:string;
    strategyProtoName?:string;
    strategyProtoLink?:string;
    fundAlterPoolName?:string;
    fundAlterPoolLink?:string;
}
export function ProtoListToForm(protos:ProtoConfig[]):FormValue{
    const obj={};
    obj['fundAlterPoolName']='基金备选库'
    protos?.forEach(proto=>{
        switch(proto.type){
            case ProtoType.RISK:
                obj['riskProtoName']=proto.name;
                obj['riskProtoLink']=proto.link;
                break;
                case ProtoType.SERVE:
                obj['serveProtoName']=proto.name;
                obj['serveProtoLink']=proto.link;
                break;
                case ProtoType.RULE:
                obj['ruleProtoName']=proto.name;
                obj['ruleProtoLink']=proto.link;
                break;
                
                case ProtoType.STRATEGY:
                obj['strategyProtoName']=proto.name;
                obj['strategyProtoLink']=proto.link;
                break;
                case ProtoType.ALTER:
                    obj['fundAlterPoolName']=proto.name||'基金备选库';
                    obj['fundAlterPoolLink']=proto.link;
                    break;
        }
    })
    return obj;
}
export function FormToProtoList(form:FormValue):ProtoConfig[]{
    const arr=[];
    if(form['riskProtoName']&&form['riskProtoLink']){
        arr.push(
            {name:form['riskProtoName'],link:form['riskProtoLink'],type:ProtoType.RISK}
        )
    }
    if(form['serveProtoName']&&form['serveProtoLink']){
        arr.push(
            {name:form['serveProtoName'],link:form['serveProtoLink'],type:ProtoType.SERVE}
        )
    }
    if(form['ruleProtoName']&&form['ruleProtoLink']){
        arr.push(
            {name:form['ruleProtoName'],link:form['ruleProtoLink'],type:ProtoType.RULE}
        )
    }

    if(form['strategyProtoName']&&form['strategyProtoLink']){
        arr.push(
            {name:form['strategyProtoName'],link:form['strategyProtoLink'],type:ProtoType.STRATEGY}
        )
    }
    if(form['fundAlterPoolName']&&form['fundAlterPoolLink']){
        arr.push(
            {name:form['fundAlterPoolName'],link:form['fundAlterPoolLink'],type:ProtoType.ALTER}
        )
    }
    return arr;
}