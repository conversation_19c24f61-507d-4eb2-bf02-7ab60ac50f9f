import React from 'react';
import { Button, Popconfirm, Input } from 'antd';
import FundSelect from './fundSelect'

export default function ({
    obj,
    index,
    deleteFunc = (index: number) => {},
    item,
    items,
    setItems = () => {},
    disabled
}: {
    obj: any
    index: number
    deleteFunc: Function
    item: any
    items: any
    setItems: Function
    disabled: boolean
}) {

    function handleItem(value: string): void {
        let _items = [...items]
        _items[index] = obj.filter((item: any) => item.tradecode === value)[0]
        console.log(_items)
        setItems(_items)
    }

    return (
        <div className="u-l-middle"
        style={{
            width: '1200px',
            marginBottom: 20
        }}>
            <span className="u-block_il u-w100">{Number(index) + 1}</span>
            <FundSelect 
            placeholder='ETF基金 ETF名称'
            obj = {obj}
            item={item}
            width = {400}
            marginRight = {100}
            handleSelect={handleItem}
            disabled={disabled}
            />
            <Popconfirm
                title="请问是否删除"
                okText="确定"
                cancelText="取消"
                onConfirm={() => {deleteFunc(item.tradecode)}}
                placement="bottom"
            >
                <Button type="primary" disabled={disabled}>删除</Button>
            </Popconfirm>
        </div>
    )
}