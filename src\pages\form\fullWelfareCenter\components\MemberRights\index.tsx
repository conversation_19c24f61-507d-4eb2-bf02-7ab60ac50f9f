import React from 'react';
import { Form, Select, Input, Radio, DatePicker, Card } from 'antd';
import KycComponent from './KycComponent';

const { Option } = Select;
const { RangePicker } = DatePicker;

const RightsForm = props => {
  const {
    getFieldDecorator,
    showRightsId,
    setShowRightsId,
    setIsGiveRedPacket,
    isGiveRedPacket,
    isOpenConditionMatching,
    setIsOpenConditionMatching,
  } = props;

  const handleTypeChange = (value: string, i: number) => {
    const updatedShowRightsId = [...showRightsId];
    if (value === '3') {
      updatedShowRightsId[i] = false;
    } else {
      updatedShowRightsId[i] = true;
    }
    setShowRightsId(updatedShowRightsId);
  };

  const openAccountEncourage = [];
  for (let i = 0; i < 3; i++) {
    const openAccountEncourageItem = (
      <Card key={i}>
        <h3>权益{i + 1}</h3>
        <Form.Item label="权益特征">
          {getFieldDecorator(`rights.openAccountEncourage.encourageList[${i}].feature`)(<Input />)}
        </Form.Item>
        <Form.Item label="权益价值">
          {getFieldDecorator(`rights.openAccountEncourage.encourageList[${i}].value`)(<Input />)}
        </Form.Item>
        <Form.Item label="权益名称">
          {getFieldDecorator(`rights.openAccountEncourage.encourageList[${i}].name`)(<Input />)}
        </Form.Item>
        <Form.Item label="权益类型">
          {getFieldDecorator(`rights.openAccountEncourage.encourageList[${i}].type`)(
            <Select onChange={(value: string) => handleTypeChange(value, i)}>
              <Option value="0">优惠券</Option>
              <Option value="1">体验金</Option>
              <Option value="2">0折卡</Option>
              <Option value="3">其他</Option>
            </Select>,
          )}
        </Form.Item>
        {showRightsId[i] ? (
          <Form.Item label="权益ID">
            {getFieldDecorator(`rights.openAccountEncourage.encourageList[${i}].id`)(<Input />)}
          </Form.Item>
        ) : null}
      </Card>
    );
    openAccountEncourage.push(openAccountEncourageItem);
  }
  return (
    <>
      <h2>未开户用户开户激励</h2>
      {openAccountEncourage}
      <Form.Item label="按钮文描">
        {getFieldDecorator('rights.openAccountEncourage.buttonText')(<Input />)}
      </Form.Item>
      <h2>优惠券兑换其他权益</h2>
      <Form.Item label="跳转链接">
        {getFieldDecorator('rights.exchangeRightsUrl')(<Input />)}
      </Form.Item>
      <h2>0折卡跳转</h2>
      <Form.Item label="0折卡跳转链接">
        {getFieldDecorator('rights.zeroOffJump', {
          rules: [{ required: true, message: '请输入0折卡跳转链接' }],
        })(<Input />)}
      </Form.Item>
      <h2>体验金跳转</h2>
      <Form.Item label="体验金跳转链接">
        {getFieldDecorator('rights.experienceGoldJump', {
          rules: [{ required: true, message: '请输入体验金跳转链接' }],
        })(<Input />)}
      </Form.Item>
      <h2>发放红包</h2>
      <Form.Item label="是否发放红包">
        {getFieldDecorator('rights.redPacket.isGiveRedPacket', {
          rules: [{ required: true, message: '请选择是否发放红包' }],
        })(
          <Radio.Group onChange={e => setIsGiveRedPacket(e.target.value === '1')}>
            <Radio value="1">是</Radio>
            <Radio value="0">否</Radio>
          </Radio.Group>,
        )}
      </Form.Item>
      {isGiveRedPacket ? (
        <>
          <Form.Item label="红包ID">
            {getFieldDecorator('rights.redPacket.kycConfig.couponId', {
              rules: [{ required: true, message: '请填写红包id' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="理财知识小文描">
            {getFieldDecorator('rights.redPacket.kycConfig.financialKnowledge', {
              rules: [{ required: true, message: '请填写理财知识文描' }],
            })(<Input onChange={e => e.target.value = e.target.value.slice(0, 13)} />)}
          </Form.Item>
          <Form.Item label="红包发放时间">
            {getFieldDecorator('rights.redPacket.kycConfig.deliveryTime', {
              rules: [{ type: 'array', required: true, message: '请填写红包发放时间' }],
            })(
              <RangePicker
                showTime={{ format: 'HH:mm:ss' }}
                placeholder={['开始时间', '结束时间']}
              />,
            )}
          </Form.Item>
          <Form.Item label="开启条件匹配">
            {getFieldDecorator('rights.redPacket.kycConfig.isOpenConditionMatching', {
              rules: [{ required: true, message: '请选择是否开启条件匹配' }],
            })(
              <Radio.Group onChange={e => setIsOpenConditionMatching(e.target.value === '1')}>
                <Radio value="1">开启</Radio>
                <Radio value="0">关闭</Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          {isOpenConditionMatching ? (
            <>
              <Form.Item wrapperCol={{ sm: 20 }}>
                {getFieldDecorator('rights.redPacket.kycConfig.optional.kycFilterId')(
                  <KycComponent />,
                )}
              </Form.Item>
              <Form.Item label="条件匹配红包ID">
                {getFieldDecorator('rights.redPacket.kycConfig.optional.couponId', {
                  rules: [{ required: true, message: '请填写红包id' }],
                })(<Input />)}
              </Form.Item>
              <Form.Item label="条件匹配理财小知识文描">
                {getFieldDecorator('rights.redPacket.kycConfig.optional.financialKnowledge', {
                  rules: [{ required: true, message: '请填写红包理财小知识文描' }],
                })(<Input />)}
              </Form.Item>
              <Form.Item label="红包发放时间">
                {getFieldDecorator('rights.redPacket.kycConfig.optional.deliveryTime', {
                  rules: [{ required: true, message: '请填写红包发放时间' }],
                })(
                  <RangePicker
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm"
                    placeholder={['开始时间', '结束时间']}
                  />,
                )}
              </Form.Item>
            </>
          ) : null}
        </>
      ) : null}
    </>
  );
};

export default RightsForm;
