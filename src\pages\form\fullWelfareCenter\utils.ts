import { ITableItem } from './types';

export const getBase64 = (img: File | Blob, callback) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result));
  reader.readAsDataURL(img);
};

// 将map根据创建时间排序为数组
export const sortByCreateTime = (tableMap: { [propName: string]: ITableItem }) => {
  const temp = Object.values(tableMap);
  const tableData = temp.map(item => JSON.parse(item));
  tableData.sort((a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime());
  return tableData;
};

// 区分正式环境和测试环境
export const isTest = () => {
  return (
    window.location.hostname.includes('localhost') || window.location.hostname.includes('febs.')
  );
};
