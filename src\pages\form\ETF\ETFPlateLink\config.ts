export function getSchema (isEdit) {
	console.log(isEdit, 'isEdit');
	return {
		"type": "object",
		"required": [
			"title",
			"desc",
		],
		"properties": {
			"title": {
				"title": "标题",
				"type": "string",
				"description": "",
				"ui:options": {},
			},
			"desc": {
				"title": "正文",
				"description": "",
				"type": "string",
				"ui:widget": "textarea",
			}
		}
	}
}

export const KEY = 'plateLink';
export const PROPNAME = 'thscodeHq';
