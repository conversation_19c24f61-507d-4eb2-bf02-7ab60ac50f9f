import { Collapse } from 'antd';
import React, { useState, useEffect, FC, useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';

export interface IProps {}
const DragAndSortWrapper: FC<IProps> = ({ children, ...props }) => {
  const ref = useRef(null);
  const style=props.style||{}
  const [, drop] = useDrop({
    accept: 'DragDropBox',
    drop: () => ({ id: props.id, type: 'DragDropBox', index: props.index }),
    // hover: (item,monitor) => {
    //   console.log("hover",ref,item)
    //     if(!ref.current){
    //         return;
    //     }

    //     const dragIndex=item.index;
    //     const hoverIndex=props.index;
    //     if(dragIndex===hoverIndex){
    //         return;
    //     }
    //     props.changePosition?.(dragIndex,hoverIndex)
    // },
  });
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'DragDropBox',
    item: { id: props.id, type: 'DragDropBox', index: props.index },
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if(!dropResult||!item){
        return
      }
      console.log(item, dropResult);
      props.changePosition?.(item.index,dropResult.index)
    },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  }),[props.changePosition]);
  const changeRef = drag(drop(ref));
  return (
    <div ref={changeRef} style={{ opacity: isDragging ? 0.5 : 1,...style }}>
      {children}
    </div>
  );
};
export default DragAndSortWrapper;
