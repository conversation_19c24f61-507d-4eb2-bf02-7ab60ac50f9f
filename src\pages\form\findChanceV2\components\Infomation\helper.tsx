import { Infomation } from '../../type';
import { checkRequiredAndThrow, checkUrlAndThrow } from '../../utils';
export const mapPropToName = {
  id: 'id',
  type: '类型',
  name: '文章标题',
  from: '文章来源',
  readAmount: '阅读量',
  createTime: '发文时间',
  cover: '封面图',
  link: '文章链接',
};
export const informationHelper = {
  addArrayData: function(): Infomation {
    return {
      id: '',
      type: null,
      name: '',
      from: '',
      readAmount: '',
      createTime: '',
      cover: '',
      link: '',
    };
  },
  addInit: function(): Infomation[] {
    return [];
  },

  checkData: function(data: Infomation[]) {
    data.forEach((item, index) => {
      try {
        checkRequiredAndThrow(item.id, mapPropToName['id']);
        checkRequiredAndThrow(item.type, mapPropToName['type']);
        checkRequiredAndThrow(item.link, mapPropToName['link']);
        checkUrlAndThrow(item.link, mapPropToName['link']);
      } catch (e) {
        throw new Error(`相关资讯-${e.message}(第${index + 1}项)`);
      }
    });
    return true;
  },
};
