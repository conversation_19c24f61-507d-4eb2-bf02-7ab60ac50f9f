{"propsSchema": {"type": "object", "properties": {"tabService": {"title": "数组", "type": "array", "items": {"type": "object", "properties": {"ID": {"title": "同顺号ID", "type": "string", "ui:width": "50%", "ui:options": {}}, "author": {"title": "作者", "type": "string", "ui:width": "50%", "ui:options": {}}, "userId": {"title": "同顺号UserID", "type": "string", "ui:width": "50%", "ui:options": {}}, "name": {"title": "实盘名称", "type": "string", "ui:width": "50%", "ui:options": {}}, "describe": {"title": "策略描述", "type": "string", "ui:width": "50%", "ui:options": {}}, "group": {"title": "实盘组合", "description": "下拉多选", "type": "array", "enum": [], "ui:widget": "select", "ui:options": {"mode": "tags"}, "ui:width": "44%"}, "authList": {"title": "创作平台功能权限", "description": "下拉多选", "type": "array", "items": {"type": "string"}, "enum": ["gjfx", "spgk", "spfx"], "enumNames": ["个基分析", "实盘概况", "实盘分析"], "ui:widget": "multiSelect", "ui:width": "44%"}}}}}}}