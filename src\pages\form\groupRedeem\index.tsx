import React, { useState, useEffect } from "react";
import Form<PERSON><PERSON> from 'form-render/lib/antd';
import api from 'api';
import { Button, message } from 'antd'
import FORM_CONFIG from './form.json';

const { fetchGroupRedeem, postGroupRedeem } = api;

export default function () {
  const [init, setInit] = useState(false);
  const [formConfig, setFormConfig] = useState({});
  const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);

  useEffect(() => {
    fetchGroupRedeem().then((res: any) => {
      try {
        res = JSON.parse(res.data);
        // console.log(res);
        if (res) {
          FORM_CONFIG.formData = {
            ...res
          };
        }
      } catch (e) {
        console.warn(e)
      }
      setInit(true);
      setFormConfig(FORM_CONFIG);
      setData(FORM_CONFIG.formData);
    }).catch((e: Error) => {
      message.error(e.message);
    })
  }, [init]);

  const onSubmit = () => {
    if (valid.length > 0) {
      message.error(`校验未通过字段：${valid.toString()}`);
    } else {
      let _postData = {
        ...formData
      }

      for (let i = 0; i < _postData.groupList.length; i++) {
        if ( !_postData.groupList[i].profit && !_postData.groupList[i].days ) {
          message.error(`组合持有天数与持有收益率必填其一`);
          return;
        }
      }
      postGroupRedeem({
        value: JSON.stringify(_postData)
      }).then((res: any) => {
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          } else {
            message.success('发布成功！');
            setTimeout(() => {
              location.reload();
            }, 1000)
          }
        } catch (e) {
          message.error(e.message);
        }
      })
    }
  };

  if (!init) return '加载中'
  return (
    <div style={{ padding: 60 }}>
      <FormRender
        propsSchema={FORM_CONFIG.propsSchema}
        formData={formData}
        onChange={setData}
        onValidate={setValid}
        showDescIcon={true}
        displayType="row"
      />
      <Button type="primary" onClick={onSubmit}>提交</Button>
    </div>
  );
}
