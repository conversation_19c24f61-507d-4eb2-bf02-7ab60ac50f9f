import React, {useEffect, useState} from 'react'
import api from 'api';
import FormRender from 'form-render/lib/antd';
import {Button,message, Card, Collapse, AutoComplete, DatePicker, Input, Cascader} from 'antd';

interface option {
    key: string
    value: string
}

interface savedOption {
    name: string
    url: string
    options: Object
}

const {
    generalExport,
    generalExportSave,
    fetchGeneralExport
} = api

const JSON = {
    "type": "object",
    "properties": {
      "otherOptions": {
        "title": "其他参数",
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "key": {
              "title": "参数名",
              "type": "string",
              "ui:width": "50%",
              "ui:options": {}
            },
            "value": {
              "title": "参数值",
              "type": "string",
              "ui:width": "50%",
              "ui:options": {}
            }
          }
        },
        "ui:options": {}
      }
    }
}
  
export default function () {
    const [startTime, setStartTime] = useState('') //开始时间
    const [endTime, setEndTime] = useState('') //结束时间
    const [data, setData] = useState([]) //以前保存的数据
    const [names, setNames] = useState([]) //所有存储的名字
    const [urls, setUrls] = useState([]) //已经有了的链接
    const [options, setOptions] = useState([]) //以前选择过得选项
    const [name, setName] = useState('') //名称
    const [url, setUrl] = useState('') //选择的通用导出接口
    const [otherOptions, setOtherOptions] = useState({otherOptions: []}) //其他参数

    useEffect(() => {
        fetchGeneralExport().then((data: any) => {
            if (data.code !== '0000' || !data.data) return
            data = window.JSON.parse(data.data).formData
            console.log(data)
            setData(data)
            setNames(data.map((item: savedOption) => item.name))
            setUrls(data.map((item: savedOption) => item.url))
            setOptions(data.map((item: savedOption) => item.options))
        }).catch((e: Error) => {
            console.log(e.message)
        })
    }, [])

    /**
     * 修改时间参数
     * @param value 
     */
    function onchangeDownLoadTime (value: any) {
        // largeTransferStartTime
        function getTime(Date: Date) {
            let _year = Date.getFullYear();
            let _month: number | string = Date.getMonth() + 1;
            _month = _month < 10 ? '0' + _month : _month;
            let _day: number | string = Date.getDate();
            _day = _day < 10 ? '0' + _day : _day;
            let _hour: number | string = Date.getHours();
            _hour = _hour < 10 ? '0' + _hour : _hour;
            let _minute: number | string = Date.getMinutes();
            _minute = _minute < 10 ? '0' + _minute : _minute;
            let _second: number | string = Date.getSeconds();
            _second = _second < 10 ? '0' + _second : _second;
            // return `${_year}-${_month}-${_day} ${_hour}:${_minute}:${_second}`;
            return `${_year}${_month}${_day}`;
        }
        let _startTime = getTime(value[0]._d);
        let _endTime = getTime(value[1]._d);
        setStartTime(_startTime)
        setEndTime(_endTime)
    }

    /**
     * 下载
     */
    function downLoadFundActivity () {
        if (!url) return message.error('请输入链接')
        if (!name) return message.error('请输入名称')
        let _data = window.JSON.parse(window.JSON.stringify(data))
        let queries: string = '?'
        if (startTime && endTime) {
            queries += `startDate=${startTime}&endDate=${endTime}`
        }
        otherOptions.otherOptions.forEach((item: option) => {
            queries === '?' ? null : queries += '&'
            queries += (item.key.trim() + '=' + item.value.trim())
        })
        if (names.some((item: string) => item === name) 
        && !urls.some((item: string) => item === url)){
            return message.error('该名称已被赋值请重新赋值')
        }
        generalExport({},
            url + queries,
            null,
            {responseType: 'blob'}
        ).then((data: any) => {
            console.log(data)
            if (data.data && data.data.size === 0 && data.message !== 'OK' && data.message) return message.error('下载失败，请查看参数是否正确或稍后再试')
            if (data.code !== '0000' && data.message !== 'OK' && data.message) return message.error(data.message)
            if (urls.some((item: string) => item === url)){
                generalExportSave({
                    value: window.JSON.stringify({formData: _data.map((item: savedOption) => {
                        if (item.url === url && item.name === name) item.options = otherOptions
                        return item
                    })})
                })
            } else {
                _data.unshift({
                    name,
                    url,
                    options: otherOptions
                })
                generalExportSave({
                    value: window.JSON.stringify({formData: _data})
                })
            }
        }).catch((e: Error) => {
            message.error('下载失败，请查看参数是否正确或稍后再试')
        })
    }

    function changeName(value: string) {
        if (~names.indexOf(value)) {
            setUrl(urls[names.indexOf(value)])
            setOtherOptions(options[names.indexOf(value)])
        } else {
            setOtherOptions({otherOptions: []})
        }
        setName(value.trim())
    }

    function changeUrl(value: string) {
        setUrl(value.trim())
    }

    return (
        <div style={{width: 800}}>
            <div>
                说明：将导出地址中yytjapi后和?之前截取复制如请求接口中<br></br>
                例：http://febs.5ifund.com/yytjapi/yytjapi/report/fundregisterinfo/download?startDate=20201104&endDate=20201107<br></br>
                需要在请求接口中输入：report/fundregisterinfo/download<br></br>
                之后将?后相应参数输入进对应参数中<br></br>
                本例子中参数则对应为<br></br>
                参数名：startDate      参数值：20201104<br></br>
                参数名：endDate        参数值：20201107     <br></br>
                如有问题请联系********************
            </div>
            <div>
                <span className="u-block_il" style={{width: 150, marginBottom: 20, marginTop: 20}}>
                    接口名称：
                </span>
                <AutoComplete
                style={{ width: 400 }}
                dataSource={names}
                onChange={changeName}
                />
            </div>
            <div>
                <span className="u-block_il" style={{width: 150}}>
                    请求接口：
                </span>
                <Input value={url} onChange={(e) => {changeUrl(e.target.value)}} style={{width: 400}} />
            </div>

            {/* <div className="g-mt20">
                <span className="u-block_il" style={{width: 150}}>
                    时间参数(年月日)：
                </span>
                <DatePicker.RangePicker onOk={onchangeDownLoadTime} onChange={onchangeDownLoadTime}/>
            </div> */}

            <FormRender
                propsSchema={JSON}
                uiSchema={{}}
                formData={otherOptions}
                onChange={setOtherOptions}
                displayType="row"
                showDescIcon={true}
                column={2}
            />
 
            <Button
                type="primary" 
                style={{ float: 'right' }}
                onClick={downLoadFundActivity}
            >
                下载
            </Button>
        </div>
    )
}