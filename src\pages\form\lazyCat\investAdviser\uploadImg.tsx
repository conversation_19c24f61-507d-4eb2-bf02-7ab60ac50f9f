import React from 'react';
import ImgUpload from '../../../frontend/compoment/uploadImg';

interface dataProps {
    onChange: Function,
    imgUrl: string
}

export default function (props:dataProps) {

    return <div>
        <ImgUpload 
            handleChange={(value: any) => props.onChange(value)}
            imageUrl={props.imgUrl}
            isEdit={true}
            title=''
        />
    </div>

}