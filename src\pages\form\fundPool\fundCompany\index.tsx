import React from 'react';
import api from 'api';
import { autobind } from 'core-decorators';
import { Button, Input, Table, Modal, Form, message, Upload, InputNumber, Popconfirm, DatePicker } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import styles from './index.less';

const { Search, TextArea } = Input;
const { MonthPicker } = DatePicker;
const { fetchCompanyInfo, updateCompanyInfo, updateFundManager, updateFundInfo, uploadFundExcel } = api;
const fundType: any = {
    qy: '权益类基金',
    cz: '纯债类基金',
    gs: '固收+',
    hy: '行业主题',
    fg: '风格',
    zs: '指数'
}
const recommendLevel = ['一星', '二星', '三星', '四星', '五星'];
const modalTitle: any = {
    company: '编辑基金公司内容',
    manager: '编辑基金经理内容',
    fund: '编辑基金内容'
}
interface FundCompanyProps extends FormComponentProps {
    [propsName: string]: any
}
@autobind
class FundCompany extends React.Component<FundCompanyProps, any> {
    constructor(props: FundCompanyProps) {
        super(props);
        this.state = {
            visible: false,
            dataFund: {
                fundCompany: [],
                fundInfos: [],
                fundManagerInfos: []
            },
            editFund: {},
            modalStyle: 'company',
            searchText: '万家基金',
            selectDate: ''
        }
    }
    
    componentDidMount() {
        this.fetchCompanyInfo();
    }

    // 基金公司查询
    async fetchCompanyInfo() {
        let { searchText, selectDate } = this.state;
        selectDate = selectDate ? selectDate + '-01' : selectDate;
        try {
            const { status_code, data, status_msg } = await fetchCompanyInfo(null, searchText, selectDate);
            if (status_code === 0) {
                if (data.fundCompany) {
                    data.fundCompany.key = 1;
                    data.fundCompany.num = 1;
                    data.fundCompany.managementScale = (data.fundCompany.managementScale / *********).toFixed(2) + '亿';
                    data.fundCompany.managementScaleChange = data.fundCompany.managementScaleChange + '%';
                }
                data.fundInfos &&　data.fundInfos.forEach((item: any, index: number) => {
                    item.key = index;
                    item.num = index + 1;
                    item.annualProfitChange = (item.annualProfit !== undefined && item.annualProfit !== null) ?
                      `${(item.annualProfit * 100).toFixed(2)}%` : '--';
                    item.maxDrawdownChange = (item.maxDrawdown !== undefined && item.maxDrawdown !== null) ?
                        `${(item.maxDrawdown * 100).toFixed(2)}%` : '--';
                })
                data.fundManagerInfos && data.fundManagerInfos.forEach((item: any, index: number) => {
                    item.key = index;
                    item.num = index + 1;
                })
                let dataFund = {
                    fundCompany: data.fundCompany && [data.fundCompany],
                    fundInfos: [...data.fundInfos],
                    fundManagerInfos: [...data.fundManagerInfos]
                }
                this.setState({
                    dataFund,
                })
            } else {
                message.error(status_msg || '查询数据失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    onSearch(value: any) {
        this.setState({
            searchText: value
        }, () => {
            this.fetchCompanyInfo();
        });
    }
    searchChange(e: React.ChangeEvent<HTMLInputElement>) {
        this.setState({
            searchText: e && e.target && e.target.value
        })
    }
    // 编辑
    handleEdit(record: any, type: string) {
        this.setState({
            visible: true,
            modalStyle: type,
            editFund: {...record}
        });
    }
    // 保存
    async saveContent(type: string, obj: any) {
        try {
            let params: any = {}, temp: any = {};
            let result = null;
            switch(type) {
                case 'company':
                    temp.id = obj.id;
                    temp.fundCompanyFeature = obj.fundCompanyFeature;
                    params.value = JSON.stringify(temp);
                    result = await updateCompanyInfo(params);
                    break;
                case 'manager':
                    temp.id = obj.id;
                    temp.stockSelectIdea  = obj.stockSelectIdea;
                    temp.investIdea  = obj.investIdea;
                    params.value = JSON.stringify(temp);
                    result = await updateFundManager(params);
                    break;
                case 'fund':
                    temp.id = obj.id;
                    temp.orgName = obj.orgName;
                    temp.fundCode  = obj.fundCode;
                    temp.fundName = obj.fundName;
                    temp.fundManager  = obj.fundManager;
                    temp.threeAssess = obj.threeAssess;
                    temp.remark  = obj.remark;
                    temp.annualProfit = obj.annualProfit;
                    temp.maxDrawdown  = obj.maxDrawdown;
                    params.value = JSON.stringify(temp);
                    result = await updateFundInfo(params);
                    break;
                default:
                    break;
            }
            const { status_code } = result;
            if (status_code === 0) {
                message.success('保存成功');
                this.setState({
                    visible: false
                });
                this.fetchCompanyInfo();
            } else {
                message.error('保存失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    // 上传文件
    async customRequest(options: any, type: string) {
		try {
            const file = options.file;
            const formData = new FormData();
            formData.append('type', type);
            formData.append('file', file);
            const { status_code } = await uploadFundExcel(formData);
            if (status_code === 0) {
                message.success('上传文件成功');
            } else {
                message.error('上传文件失败');
            }
        }catch(e) {
            message.error(e.message);
        }
    }
    handleSubmit() {
        let { modalStyle, editFund } = this.state;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                editFund = {...editFund, ...values};
                this.saveContent(modalStyle, editFund);
            }
        });
    }
    handleCancel() {
        this.setState({
            visible: false
        });
    }
    dateChange(date: any, dateString: string) {
        this.setState({
            selectDate: dateString
        }, () => {
            this.fetchCompanyInfo();
        });
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
        };
        const columnsCompany = [
            {
                title: '序号',
                dataIndex: 'num',
                key: 'num',
            },
            {
                title: '基金公司',
                dataIndex: 'orgName',
                key: 'orgName',
            },
            {
                title: '成立时间',
                dataIndex: 'createDate',
                key: 'createDate',
            },
            {
                title: '全部管理规模',
                dataIndex: 'managementScale',
                key: 'managementScale',
            },
            {
                title: '管理规模排名',
                dataIndex: 'managementScaleRank',
                key: 'managementScaleRank',
            },
            {
                title: '管理规模变更',
                dataIndex: 'managementScaleChange',
                key: 'managementScaleChange',
            },
            {
                title: '基金公司特色',
                dataIndex: 'fundCompanyFeature',
                key: 'fundCompanyFeature',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (text: any, record: any) => <>
                    <Button type="primary" onClick={() => this.handleEdit(record, 'company')}>编辑</Button>
                </>,
            },
        ];
        const columnsManager = [
            {
                title: '基金经理',
                dataIndex: 'fundManager',
                key: 'fundManager',
            },
            {
                title: '管理年限',
                dataIndex: 'manageTime',
                key: 'manageTime',
            },
            {
                title: '赚钱能力',
                dataIndex: 'earnAbility',
                key: 'earnAbility',
            },
            {
                title: '稳定能力',
                dataIndex: 'stableAbility',
                key: 'stableAbility',
            },
            {
                title: '抗跌能力',
                dataIndex: 'antiFallAbility',
                key: 'antiFallAbility',
            },
            {
                title: '管理经验',
                dataIndex: 'manageExperience',
                key: 'manageExperience',
            },
            {
                title: '选股能力',
                dataIndex: 'stockSelectAbility',
                key: 'stockSelectAbility',
            },
            {
                title: '择时能力',
                dataIndex: 'marketTimeAbility',
                key: 'marketTimeAbility',
            },
            {
                title: '综合分数',
                dataIndex: 'totalAbility',
                key: 'totalAbility',
            },
            {
                title: '备注',
                dataIndex: 'remark',
                key: 'remark',
            },
            {
                title: '选股思路',
                dataIndex: 'stockSelectIdea',
                key: 'stockSelectIdea',
            },
            {
                title: '投资思路和框架',
                dataIndex: 'investIdea',
                key: 'investIdea',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (text: any, record: any) => <>
                    <Button type="primary" onClick={() => this.handleEdit(record, 'manager')}>编辑</Button>
                </>,
            },
        ];    
        const columnsFund = [
            {
                title: '序号',
                dataIndex: 'num',
                key: 'num',
            },
            {
                title: '基金代码',
                dataIndex: 'fundCode',
                key: 'fundCode',
            },
            {
                title: '基金名称',
                dataIndex: 'fundName',
                key: 'fundName',
            },
            {
                title: '基金经理',
                dataIndex: 'fundManager',
                key: 'fundManager',
            },
            {
                title: '晨星3年评级',
                dataIndex: 'threeAssess',
                key: 'threeAssess',
            },
            {
                title: '年化收益率',
                dataIndex: 'annualProfitChange',
                key: 'annualProfitChange',
            },
            {
                title: '最大回撤',
                dataIndex: 'maxDrawdownChange',
                key: 'maxDrawdownChange',
            },
            {
                title: '备注',
                dataIndex: 'remark',
                key: 'remark',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (text: any, record: any) => <>
                    <Button type="primary" onClick={() => this.handleEdit(record, 'fund')}>编辑</Button>
                </>,
            },
        ];
        const { visible, dataFund, modalStyle, editFund, searchText } = this.state;

        return (
            <div className={styles['fund-company']}>
                <section>
                    <header className={styles['f-center']}>
                        <Search 
                            placeholder="基金公司" 
                            onSearch={this.onSearch} 
                            enterButton="搜索"
                            style={{width: 300}}
                            value={searchText}
                            onChange={this.searchChange}
                            allowClear
                        />
                        <h1>基金公司</h1>
                        <div>
                            <MonthPicker onChange={this.dateChange}/>
                        </div>
                    </header>
                    <Table dataSource={dataFund.fundCompany} columns={columnsCompany} pagination={false}/>
                </section>
                <section style={{margin: '10px 0'}}>
                    <header className={styles['company-header']}>
                        <h1>优秀基金经理</h1>
                        <div className={styles['upload']}>
                            <Upload
                                accept=".xls,.xlsx"
                                customRequest={(options) => this.customRequest(options, 'manager')}
                                showUploadList={false}
                            >
                                <Button type="primary">Excel上传</Button>
                            </Upload>
                        </div>
                    </header>
                    <Table dataSource={dataFund.fundManagerInfos} columns={columnsManager} />
                </section>
                <section>
                    <header className={styles['company-header']}>
                        <h1>优秀基金</h1>
                        <div className={styles['upload']}>
                            <Upload
                                accept=".xls,.xlsx"
                                customRequest={(options) => this.customRequest(options, 'fund')}
                                showUploadList={false}
                            >
                                <Button type="primary">Excel上传</Button>
                            </Upload>
                        </div>
                    </header>
                    <Table dataSource={dataFund.fundInfos} columns={columnsFund} />
                </section>
                <Modal
                    title={modalTitle[modalStyle]}
                    visible={visible}
                    footer={null}
                    onCancel={this.handleCancel}
                    wrapClassName="recommendation-wrapper"
                    destroyOnClose
                    >
                    <Form layout="horizontal" {...formItemLayout}>
                        { modalStyle === 'company' && (
                            <>
                                <Form.Item label="基金公司特色">
                                    {getFieldDecorator("fundCompanyFeature", {
                                        initialValue: editFund.fundCompanyFeature,
                                    })(<TextArea style={{height: 80}}></TextArea>)}
                                </Form.Item>
                            </>
                        )}
                        { modalStyle === 'manager' && (
                            <>
                                <Form.Item label="选股思路">
                                    {getFieldDecorator("stockSelectIdea", {
                                        initialValue: editFund.stockSelectIdea,
                                    })(<TextArea style={{height: 80}}></TextArea>)}
                                </Form.Item>
                                <Form.Item label="投资思路和框架">
                                    {getFieldDecorator("investIdea", {
                                        initialValue: editFund.investIdea,
                                    })(<TextArea style={{height: 80}}></TextArea>)}
                                </Form.Item>
                            </>
                        )}
                        { modalStyle === 'fund' && (
                            <>
                                <Form.Item label="基金代码" wrapperCol={{span: 5}}>
                                    {getFieldDecorator("fundCode", {
                                        initialValue: editFund.fundCode,
                                        rules: [{ required: true, message: "请输入基金代码" }],
                                    })(<Input />)}
                                </Form.Item>
                                <Form.Item label="基金名称" >
                                    {getFieldDecorator("fundName", {
                                        initialValue: editFund.fundName,
                                        rules: [{ required: true, message: "请输入基金名称" }],
                                    })(<Input />)}
                                </Form.Item>
                                <Form.Item label="基金经理" wrapperCol={{span: 6}}>
                                    {getFieldDecorator("fundManager", {
                                        initialValue: editFund.fundManager,
                                    })(<Input />)}
                                </Form.Item>
                                <Form.Item label="晨星3年评级" wrapperCol={{span: 4}}>
                                    {getFieldDecorator("threeAssess", {
                                        initialValue: editFund.threeAssess,
                                    })(<Input />)}
                                </Form.Item>
                                <Form.Item label="年化收益率" wrapperCol={{span: 4}}>
                                    {getFieldDecorator("annualProfit", {
                                        initialValue: editFund.annualProfit,
                                    })(<InputNumber precision={4} ></InputNumber>)}
                                </Form.Item>
                                <Form.Item label="最大回撤" wrapperCol={{span: 4}}>
                                    {getFieldDecorator("maxDrawdown", { 
                                        initialValue: editFund.maxDrawdown,
                                    })(<InputNumber precision={4} ></InputNumber>)}
                                </Form.Item>
                                <Form.Item label="备注">
                                    {getFieldDecorator("remark", {
                                        initialValue: editFund.remark,
                                    })(<TextArea style={{height: 80}}></TextArea>)}
                                </Form.Item>
                            </>
                        )}
                        <Form.Item wrapperCol={{span: 24}} style={{textAlign: 'center'}}>
                            <Button onClick={this.handleCancel} style={{marginRight: 20}}>取消</Button>
                            <Popconfirm title={'是否保存该基金？'} onConfirm={() => this.handleSubmit()}>
                                <Button type="primary">保存</Button>
                            </Popconfirm>
                        </Form.Item>
                    </Form>
                </Modal>
            </div>
        )
    }
}

export default Form.create({ name: 'fundCompany' })(FundCompany)