import React, { useEffect, useState } from 'react';
import { Checkbox, Button, message, Popconfirm } from 'antd';
import ImgUpload from '@/pages/frontend/compoment/uploadImg/index';
interface dataProps {
  name: string;
  onChange: Function;
  value: string;
}

export default function(props: dataProps) {
  return (
    <div>
      <ImgUpload
        handleChange={(value: any) => props.onChange('imgUrl', value)}
        imageUrl={props.value}
        size={['686px* 不限高']}
        limit={0.2}
        isEdit={true}
        title=""
        isOnlyPng={false}
        isNoCheckHeight={true}
      />
    </div>
  );
}
