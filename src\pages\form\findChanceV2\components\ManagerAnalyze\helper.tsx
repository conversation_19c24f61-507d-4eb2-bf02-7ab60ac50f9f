import { HotSpot, ManagerAnalyze } from '../../type';
import { checkLengthAndThrow, checkRequiredAndThrow, checkTagAndThrow } from '../../utils';
export const mapPropToName = {
  id: '基金经理id',
  name: '基金经理',
  tags: '标签',
  intro: '简介',
  content: '解读内容',
  avatar: '基金经理头像',
  'content.title': '小标题',
  'content.value': '解读内容',
};
export const managerAnalyzeHelper = {
  addInit: function(): ManagerAnalyze {
    return {
      id: '',
      name: '',
      tags: '',
      intro: '',
      content: [],
      avatar: '',
    };
  },

  checkData: function(data: ManagerAnalyze) {
    try {
      checkRequiredAndThrow(data.id, mapPropToName['id']);
      checkRequiredAndThrow(data.name, mapPropToName['name']);
      checkRequiredAndThrow(data.intro, mapPropToName['intro']);

      checkRequiredAndThrow(data.avatar, mapPropToName['avatar']);
      checkTagAndThrow(data.tags, mapPropToName['tags'], 2, 7);
      if (data.content) {
        data.content.forEach((ct, index) => {
          try {
            checkRequiredAndThrow(ct.title, mapPropToName['content.title']);
            checkRequiredAndThrow(ct.value, mapPropToName['content.value']);
            checkLengthAndThrow(ct.title, mapPropToName['content.title'], 20);
          } catch (e) {
            throw new Error(`${e.message}（第${index + 1}项）`);
          }
        });
      }
    } catch (e) {
      throw new Error(`基金经理解读-${e.message}`);
    }
    return true;
  },
};
