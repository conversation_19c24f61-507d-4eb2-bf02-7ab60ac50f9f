import React, { useState, useEffect } from 'react';
import './index.less';
import api from 'api';
import {
  Row,
  Col,
  Input,
  Upload,
  Button,
  Divider,
  message,
} from 'antd';
const {
    postAgencyIntelligenceConfig,
    getAgencyIntelligenceConfig,
    newCommonUpload,
} = api;
message.config({
  duration: 3, // 持续时间
  maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
  bottom: 100, // 到页面顶部距离
});
// 初始化表单数据
const initFormData = () => {
    return {
        // 描述文案
        desc: '',
        // 未开户用户配置
        noAccountOpenedConfig: {
            // 按钮文案
            btnText: '',
            // 按钮跳转url
            btnUrl : '',
        },
        // 开户未首购用户配置
        accountOpenedNoSgConfig: {
             // 按钮文案
             btnText: '',
             // 按钮跳转url
             btnUrl : '',
        },
        // 运营图片url
        imgUrl: '',
    }
};
export default function() {
    // 表单数据
    const [formData, setFormData] = useState(initFormData());
    // 是否正在上传中
    const [isUploading, setIsUploading] = useState(false);
    // 修改表单项
    const changeFormItem = (type, value) => {
        const _formData = deepClone(formData);
        switch (type) {
            case 'noKhBtnText':
                _formData.noAccountOpenedConfig.btnText = value;
                break;
            case 'noKhBtnUrl':
                _formData.noAccountOpenedConfig.btnUrl = value;
                break;
            case 'khNoSgBtnText':
                _formData.accountOpenedNoSgConfig.btnText = value;
                break;
            case 'khNoSgBtnUrl':
                _formData.accountOpenedNoSgConfig.btnUrl = value;
                break;
            default:
                _formData[type] = value;
                break;
        }
        setFormData(_formData);
    };
    // 深拷贝对象/数组
    const deepClone = (obj) => {
        return JSON.parse(JSON.stringify(obj));
    };
    // 图片上传
    const uploadImg = (options)=> {
        if (options.file.size > 2048000) {
            message.error('文件大小不得超过2M');
            return;
        };
        setIsUploading(true);
        message.info('图片上传中');
        let params = new FormData();
        params.append('file', options.file);
        newCommonUpload(params).then((res)=>{
            if (res.status_code === 0) {
            let _url = '';
            if (res.data.includes('http')) {
                //兼容以前
                _url = res.data.replace('http://', 'https://');
            } else {
                //区分正式环境、测试环境
                if (
                window.location.hostname.includes('localhost') ||
                window.location.hostname.includes('febs.')
                ) {
                _url = 'https://testo.thsi.cn/' + res.data;
                } else {
                _url = 'https://o.thsi.cn/' + res.data;
                }
            }
            let _formData = deepClone(formData);
            _formData.imgUrl = _url;
            setFormData(_formData);
            message.success('上传成功');
            } else {
                message.error(res.status_msg);
            }
            setIsUploading(false);
        }).catch(()=>{
            message.error('上传失败');
            setIsUploading(false);
        });
    };
    // 表单校验
    const checkFormData = () => {
        let isPass = false;
        switch (true) {
        case !formData.desc:
            message.error('请填写描述文案');
            break;
        case !formData.noAccountOpenedConfig.btnText: 
            message.error('请填写未开户用户按钮文案');
            break;
        case !formData.noAccountOpenedConfig.btnUrl: 
            message.error('请填写未开户用户按钮跳转链接');
            break;
        case !formData.accountOpenedNoSgConfig.btnText: 
            message.error('请填写已开户且非货持仓小于1000元用户按钮文案');
            break;
        case !formData.accountOpenedNoSgConfig.btnUrl: 
            message.error('请填写已开户且非货持仓小于1000元用户按钮跳转链接');
            break;
        case !formData.imgUrl: 
            message.error('请上传图片');
            break;
        default:
            isPass = true;
            break;
        }
        return isPass;
    };
    // 保存
    const handleSave = () => {
        if (!checkFormData()) return;
        postAgencyIntelligenceConfig({
            value: JSON.stringify(formData),
          })
          .then(res => {
            if (res?.code !== '0000') {
              message.error('保存失败');
            } else {
              message.success('保存成功！');
            }
          });
    }
    // 获取配置信息
    const getConfig = () => {
        getAgencyIntelligenceConfig().then(res => {
          try {
            const _data = res?.data ? JSON.parse(res?.data) : initFormData();
            setFormData(_data);
          } catch (error) {
            console.log(error);
          }
        });
    };
    useEffect(() => {
        getConfig();
    }, []);
    return (
        <div className={'agency-intelligence'}>
            <Row style={{fontSize: '30px', fontWeight: 'bold', marginBottom: '10px',textAlign: 'center'}}>机构情报配置</Row>
            <Row gutter={[40, 14]}>
                <Col span={8}>
                    <div><span style={{color: '#f00'}}>*</span>描述文案：</div>
                    <Input placeholder='请输入描述文案' value={formData.desc} onChange={(e)=>{changeFormItem('desc',e.target.value)}}/>
                </Col>
            </Row>
            <Divider/>
            <Row style={{fontSize: '20px', fontWeight: 'bold', marginBottom: '10px'}}>未开户用户</Row>
            <Row gutter={[40, 14]}>
                <Col span={6}>
                    <div><span style={{color: '#f00'}}>*</span>按钮文案：</div>
                    <Input placeholder='请输入按钮文案' value={formData.noAccountOpenedConfig?.btnText} onChange={(e)=>{changeFormItem('noKhBtnText',e.target.value)}}/>
                </Col>
                <Col span={12}>
                    <div><span style={{color: '#f00'}}>*</span>按钮跳转链接：</div>
                    <Input placeholder='请输入按钮跳转链接' value={formData.noAccountOpenedConfig?.btnUrl} onChange={(e)=>{changeFormItem('noKhBtnUrl',e.target.value)}}/>
                </Col>
            </Row>
            <Divider/>
            <Row style={{fontSize: '20px', fontWeight: 'bold', marginBottom: '10px'}}>已开户且非货持仓小于1000元用户</Row>
            <Row gutter={[40, 14]}>
                <Col span={6}>
                    <div><span style={{color: '#f00'}}>*</span>按钮文案：</div>
                    <Input placeholder='请输入按钮文案' value={formData.accountOpenedNoSgConfig?.btnText} onChange={(e)=>{changeFormItem('khNoSgBtnText',e.target.value)}}/>
                </Col>
                <Col span={12}>
                    <div><span style={{color: '#f00'}}>*</span>按钮跳转链接：</div>
                    <Input placeholder='请输入按钮跳转链接' value={formData.accountOpenedNoSgConfig?.btnUrl} onChange={(e)=>{changeFormItem('khNoSgBtnUrl',e.target.value)}}/>
                </Col>
            </Row>
            <Divider/>
            <div className='img-area'>
                <img style={{width: '100%',height: '100%',display: formData.imgUrl ? '' : 'none'}} src={formData.imgUrl} alt=""/>
            </div>
            <Upload
                customRequest={options => {
                    uploadImg(options);
                }}
                showUploadList={false}
                >
                <Button style={{ marginLeft: '4px' }} disabled={isUploading}>
                    上传图片
                </Button>
            </Upload>
            <Divider/>
            <Button
                type="primary"
                style={{ marginBottom: '20px', marginRight: '20px' }}
                onClick={()=>{handleSave()}}
            >
                保存
            </Button>
        </div>
    );
}
