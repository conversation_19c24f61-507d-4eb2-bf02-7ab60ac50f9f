import React, { useState, useEffect } from 'react';
import api from 'api';
import { Button, message, Tag, InputNumber } from 'antd';
import PopularityInvestment, { iPopularityData } from './popularityInvestment';
import PlanInvestment, { iPlanData } from './planInvestment';
import StrictElection, { iStrictElectionData } from './strictElection';
import FixedStrategy, { iFixedStrategyData } from './fixedStrategy';

const { fetchInvestContent, postInvestContent, synchroData, fetchkycContent, postkycContent, fetchPopularData } = api;

function InvestContent() {
  const [isModify, setModify] = useState(false);
  const [kycData, setKycData] = useState({});
  const [allData, setAllData] = useState({});
  const [oldData, setOldData] = useState({});
  const [popularityData, setPopularityData] = useState<iPopularityData[]>([]);
  const [strictElectionData, setStrictElectionData] = useState<iStrictElectionData[]>([]);
  const [fixedStrategynData, setFixedStrategynData] = useState<iFixedStrategyData[]>([]);
  const [planData, setPlanData] = useState<iPlanData[]>([]);
  const [sync, setSync] = useState('-1');
  const [configBase, setConfigBase] = useState(undefined);
  const [configRatio, setConfigRatio] = useState(undefined);
  const [oldConfigBase, setOldConfigBase] = useState(undefined);
  const [oldConfigRatio, setOldConfigRatio] = useState(undefined);
  useEffect(() => {
    init();
  }, []);

  useEffect(() => {
    fetchkycContent().then((res: any) => {
        let { code, data } = res;
        if (code === '0000') {
            data = (data && JSON.parse(data)) ?? {};
            setKycData(data);
        }
    }).catch((e: Error) => {
        console.log(e?.message);
    })
  }, [])
  const handlePopularityData = (data: iPopularityData[]) => {
    setPopularityData(data);
    let obj = {
      ...allData,
      popularity: data
    }
    setAllData({...obj})
  }
  const handleStrictElectionData = (data: iStrictElectionData[]) => {
    setStrictElectionData(data);
    let obj = {
      ...allData,
      strictSelect: data
    }
    setAllData({...obj})
  }
  const handleFixedStrategyData = (data: iFixedStrategyData[]) => {
    setFixedStrategynData(data);
    let obj = {
      ...allData,
      strategy: data
    }
    setAllData({...obj})
  }
  const handlePlanData = (data: iPlanData[]) => {
    
    setPlanData(data);
    let obj = {
      ...allData,
      plan: data
    }
    setAllData({...obj})
  }
  const overallSave = () => {
    for (let i = 0,len = strictElectionData?.length; i < len; i++) {
      let item = strictElectionData[i];
      if (!item.fundCode) {
        message.error(`请填写同顺严选第${i+1}项基金ID`)
        return;
      }
      if (!item.reason) {
        message.error(`请填写同顺严选第${i+1}项精选理由`)
        return;
      }
    }
    for (let i = 0,len = fixedStrategynData?.length; i < len; i++) {
      let item = fixedStrategynData[i];
      if (!item.title) {
        message.error(`请填写定投攻略第${i+1}项标题`)
        return;
      }
      if (!item.link) {
        message.error(`请填写定投攻略第${i+1}项跳转链接`)
        return;
      }
    }
    let obj: any = {...allData};
    obj.sync = '0';
    
    postInvestContent({
      value: JSON.stringify(obj),
    }).then((res: any) => {
      if (res.code !== '0000') {
          message.error(res.message || '系统繁忙');
      } else {
          message.success('保存成功！');
          setTimeout(()=>location.reload(),1000)
      }
    }).catch((e: Error) => {
        message.error(e?.message || '系统繁忙');
    }) 
  }
  const getSynchroData = () => {
    synchroData().then((res: any) => {
      if (res.status_code !== 0) {
          message.error(res.status_msg || '系统繁忙');
      } else {
          message.success('同步成功！');
          let obj: any = {...oldData};
          obj.sync = '1';
          let obj1: any = {...kycData};
          if (JSON.stringify(obj1) === "{}") {
            return;
          }
          obj1.sync = '1';
          let promise1 = postInvestContent({
            value: JSON.stringify(obj)
          });
          let promise2 = postkycContent({
            value: JSON.stringify(obj1)
          })
          Promise.all([promise1, promise2]).then((res) => {
            if (res[0]?.code !== '0000' || res[1]?.code !== '0000' ) {
              message.error(res[0]?.message || '系统繁忙');
              return;
            }
            location.reload();
          }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
          })
      }
    }).catch((e: Error) => {
        message.error(e?.message || '系统繁忙');
    })
  }
  const init = () => {
    fetchInvestContent().then((res: any) => {
      let { code, data } = res;
      if (code === '0000') {
          data = (data && JSON.parse(data)) ?? {};
          setAllData(data);
          setOldData(data);
          setStrictElectionData(data?.strictSelect ?? []);
          setFixedStrategynData(data?.strategy ?? []);
          data?.plan?.forEach((item: iPlanData, index: number) => {
            item.num = index.toString();
          })
          setPlanData(data?.plan ?? []);
          setConfigBase(data?.configBase ?? '0');
          setConfigRatio(data?.configRatio ?? '1');
          setOldConfigBase(data?.configBase ?? '0');
          setOldConfigRatio(data?.configRatio ?? '1');
          setSync(data?.sync ?? '-1');
          console.log(data);
          fetchPopularData().then((result: any) => {
            if (result?.status_code === 0 && result?.data) {
              let arr: any= [];
              result.data?.forEach((item: iPopularityData, index: number) => {
                item.key = index.toString();
                item.modifyDate = '';
                for (let i = 0, len = data?.popularity?.length; i < len; i++) {
                  if (data?.popularity[i]?.fundCode === item.fundCode) {
                    item.modifyDate = data?.popularity[i]?.modifyDate;
                    break;
                  }
                }
                arr.push(item);
              })
              setPopularityData(arr ?? []);
            }
          }).catch((e: Error) => {
              console.log(e?.message);
          })
      } else {
          message.error(res.message || '系统繁忙');
      }
    }).catch((e: Error) => {
        message.error(e?.message || '系统繁忙');
    })
  }
  const handleBase = (val: any, str: string) => {
    let obj: any = {
      ...allData,
    }
    val = val?.toString();
    if (str === 'configBase') {
      if (val < 1) {
        val = '1';
      }
      setConfigBase(val ?? '0');
      if (oldConfigBase !== val) {
        setModify(true)
      }
      obj.configBase = val ?? '0';
    } else {
      if (val < 0) {
        val = '1';
      }
      setConfigRatio(val ?? '1');
      if (oldConfigRatio !== val) {
        setModify(true)
      }
      obj.configRatio = val ?? '1';
    }
    
    setAllData({...obj})
  }
  const handleModify = (flag: boolean) => {
    setModify(flag)
  }
  return (
    <div style={{ padding: 40 }}>
      <div style={{marginTop: 20, marginBottom: 20}}>
        { sync === '0' ? <Tag color="red" style={{marginRight: 20}}>已修改，未同步</Tag> : sync === '1' ? <Tag color="green" style={{marginRight: 20}}>已同步</Tag> : null }   
          <Button type="primary" style={{marginRight: 20}} onClick={overallSave} disabled={!isModify}>保存</Button>
          <Button type="primary" onClick={getSynchroData} disabled={sync !== '0'}>同步</Button>
      </div>
        <PopularityInvestment isModify={isModify} handleModify={handleModify} popularityData={popularityData} handleData={handlePopularityData}/>
        <StrictElection isModify={isModify} handleModify={handleModify} strictElectionData={strictElectionData} handleData={handleStrictElectionData}/>
        <FixedStrategy  isModify={isModify} handleModify={handleModify} fixedStrategyData={fixedStrategynData} handleData={handleFixedStrategyData}/>
        <PlanInvestment isModify={isModify} handleModify={handleModify} planData={planData} handleData={handlePlanData}/>

        <div style={{ marginTop: 20 }}>
          <div>基数：<InputNumber value={configBase} onChange={(val) => handleBase(val, 'configBase')}/></div>
          <div style={{ marginTop: 20 }}>系数：<InputNumber value={configRatio} onChange={(val) => handleBase(val, 'configRatio')}/></div>
        </div>
    </div>
  )
}

export default InvestContent;