import React, { useState, useEffect, FC } from 'react';

import { Button, Drawer, message, Popconfirm, Image } from 'antd';
import Upload from '../../../../components/uploadFile';
import { TATable } from '../../utiles';
import FormRender from 'form-render/lib/antd';
import styles from './index.less';

export interface IProps {
  visible: boolean;
  data: TATable;
  onSubmit: (data: object) => void;
  onClose: () => void;
}

const LogoUpload = ({ value, onChange, name}) => {
  const uploadCallback = (fileName: string, size: number, url: string) => {
    onChange(name, url);
  }
  return (
    <>
      {
        value && <img src={value} />
      }
      <span className={styles['upload-logo']}>
        <Upload text="选择文件" callback={uploadCallback}/>
      </span>
      <span>
        图片要求：1. 格式为.png 2.正方形 3.底色透明
      </span>
    </>
  )
}
const TgAppropriatenessRuleConfig: FC<IProps> = ({ visible, data, onSubmit, onClose }) => {
  const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  const schema = {
    type: 'object',
    properties: {
      logoImg: {
        title: 'logo图片',
        type: 'string',
        'ui:widget': 'logoUpload',
      },
      strategyDetailBottomCopy: {
        title: '策略详情页底部文案',
        'ui:widget': 'textarea',
        type: 'string',
      },
      intoRuleRateCopy: {
        title: '转入规则-转入费率文案',
        type: 'string',
      },
      intoRuleServiceRateCopy: {
        title: '转入规则-投顾服务费率文案',
        type: 'string',
      },
      rollOutRateCopy: {
        title: '转出规则-转出费率文案',
        type: 'string',
      },
    },
   
  };
  useEffect(()=>{
    if(visible){
      setData(data.logoCopyInfo || {})
    }
  },[visible,data])
  const handleSave=()=>{
    if (valid.length > 0) {
      message.error(`输入校验未通过，请检查`)
      return ;
    }
    onSubmit(formData);
  }
  return (
    <Drawer title={`投顾机构：${data?.taName||'--'}`} width={1000} visible={visible} onClose={onClose} >
      <div style={{ paddingTop: '40px' }}>
        <FormRender formData={formData} 
         onChange={setData}
         onValidate={setValid}
         widgets={{
          logoUpload: LogoUpload
         }}
        labelWidth={200} displayType={'row'} propsSchema={schema} />
        <div style={{display:'flex',justifyContent:'flex-end'}}>
        <Button style={{marginRight:'12px'}} onClick={onClose}>取消</Button>
        <Popconfirm title={'确定要提交吗？'} cancelText={'取消'} okText={'确定'} onConfirm={handleSave}>
        <Button type='primary' >提交</Button>
        </Popconfirm>
        </div>
      </div>
    </Drawer>
  );
};
export default TgAppropriatenessRuleConfig;
