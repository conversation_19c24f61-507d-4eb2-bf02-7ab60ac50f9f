import React from 'react';
import { Modal } from 'antd';

function StockModal (props: any) {
    const { title, visible, handleModal, stockInfo } = props;
    console.log(11,visible)
    return (
        <div>
            <Modal
                title={title}
                visible={visible}
                onCancel={() => handleModal(false)}
                footer={null}
                >
                <ul>
                    {
                        stockInfo?.length > 0 ? stockInfo.map((item: any, index: number) => {
                            return (
                                <li key={index} className="g-mb20">
                                    <span className="u-block">修改时间：{item.confDate ?? '--'}</span>
                                    <span className="u-block">{`${title === '调仓记录' ? '当前建仓' : ''}成分基金及份数:${item.stockList ?? '--'}`}</span>
                                    {title === '调仓记录' && <span className="u-block">调仓说明：{item.detail ?? '--'}</span>}
                                </li>
                            )
                        }) : <li>
                            <span>暂无数据</span>
                        </li>
                    }
                    
                </ul>
            </Modal>
        </div>
    )
}

export default React.memo(StockModal);