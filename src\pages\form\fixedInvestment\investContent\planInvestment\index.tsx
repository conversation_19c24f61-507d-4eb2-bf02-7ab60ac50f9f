import React, { useState } from 'react';
import {But<PERSON>, Popconfirm, Drawer, Table, Tag } from 'antd';
import WrappedInvestDrawer from './investDrawer';
import { timeFormat2 } from '@/utils/utils';
import DragSort from '../components/DragSort/DragSort.jsx';

export interface iPlanData {
  fundCode: string;
  fundName?: string;
  key?: string;
  num?: string;
  peopleType: string;
  label1?: string;
  label2?: string;
  profitType: number;
  baseNum: string;
  modifyDate?: string;
  status?: string;
}

interface iPlanProps {
  handleData: (data: iPlanData[]) => void;
  planData: iPlanData[];
  isModify: boolean;
  handleModify: (flag: boolean) => void;
}
const applyPeople: any = {
  xsd: '学生党',
  zcxr: '职场新人',
  zclr: '职场老炮',
  zrz: '责任族',
  txz: '退休族'
}
function PlanInvestment({handleData, planData, isModify, handleModify}: iPlanProps) {
    const [edit, setEdit] = useState(0); // 1 新增 2 编辑
    const [currentData, setCurrentData] = useState<iPlanData>({
      fundCode: '',
      peopleType: '',
      profitType: 0,
      baseNum: ''
    });

    const columns = [
      {
        title: '基金名称',
        dataIndex: 'fundName',
        key: 'fundName',
      },
      {
        title: '基金ID',
        dataIndex: 'fundCode',
        key: 'fundCode',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => {
          return (
          <span>{status === '1' ? '下架' : status === '0' ? '上架' : '--'}</span>
          )
        }
      },
      {
        title: '适用人群',
        dataIndex: 'peopleType',
        key: 'peopleType',
        render: (peopleType: string) => {
          return (
          <span>{applyPeople[peopleType] ?? '--'}</span>
          )
        }
      },
      {
        title: '修改时间',
        dataIndex: 'modifyDate',
        key: 'modifyDate',
        render: (modifyDate: string) => {
          return (
          <span>{modifyDate ?? '--'}</span>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text: any, record: iPlanData) => {
          let num: any = record?.num;
          let status: any = record?.status;
          return (
            <>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => handleEdit(record)}>编辑</Button>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => handleGoUp(num, status)}>{status === '0' ? '下架' : '上架'}</Button>
              <Popconfirm title="确定要删除吗?" onConfirm={() => handleDelete(num)}>
                <Button type="danger">删除</Button>
              </Popconfirm>
            </>
          )
        }
      },
    ];

    const handleEdit = (val: iPlanData) => {
      setCurrentData(val);
      setEdit(2);
    }
    const handleDelete = (num: string) => {
      if (!isModify) handleModify(true);
      let arr = planData && JSON.parse(JSON.stringify(planData));
      arr.splice(Number(num), 1);
      arr?.forEach((item: iPlanData, index: number) => {
        item.num = index.toString();
      })
      handleData(arr);
    }
    const handleGoUp = (num: string, status: string) => {
      if (!isModify) handleModify(true);
      let arr = planData && JSON.parse(JSON.stringify(planData));
      arr?.forEach((item: iPlanData, index: number) => {
        if (index.toString() === num) {
          item.status = status === '0' ? '1' : '0'
        }
      })
      handleData(arr);
    }
    const modifyPlanData = (data: iPlanData) => {
      if (!isModify) handleModify(true);
      let arr = planData && JSON.parse(JSON.stringify(planData));
      let time = timeFormat2();
      if (data?.num) {
        let index: any = data.num;
        data = {
          ...data,
          modifyDate: time,
        }
        arr[index] = data;
      } else {
        data = {
          ...data,
          modifyDate: time,
        }
        arr.push(data);
      }
      arr?.forEach((item: iPlanData, index: number) => {
        item.num = index.toString();
      })
      handleData(arr);
    }
    const onEditClose = () => {
      setEdit(0);
    }
    const newAdd = () => {
      setCurrentData({
        fundName: '',
        fundCode: '',
        peopleType: 'xsd',
        label1: '',
        label2: '',
        profitType: 0,
        baseNum: '',
        key: +new Date() + '',
      });
      setEdit(1);
    }
    const handleDataSource = (dragIndex: number, hoverIndex: number) => {
      if (!isModify) handleModify(true);
      let arr = planData && JSON.parse(JSON.stringify(planData));
      const dragRow = arr[dragIndex];
      arr.splice(dragIndex, 1);
      arr.splice(hoverIndex, 0, dragRow);
      arr?.forEach((item: iPlanData, index: number) => {
        item.num = index.toString();
      })
      handleData(arr);
    }
    return (
      <div style={{marginTop: 40}}>
        <h1 className="g-fs28 f-bold">定投方案：</h1>
        <DragSort columns={columns} dataSource={planData} handleDataSource={handleDataSource}></DragSort>
        <div style={{marginTop: 20}}>
          <Button type="primary" style={{marginRight: 20}} onClick={newAdd}>新增</Button>
        </div>
        <Drawer
          width={1000}
          title="产品信息配置"
          placement="right"
          closable
          onClose={onEditClose}
          visible={Boolean(edit)}
          >
          { Boolean(edit) && <WrappedInvestDrawer currentData={currentData} onEditClose={onEditClose} handleData={modifyPlanData}></WrappedInvestDrawer> }
        </Drawer>
      </div>
    )
}

export default React.memo(PlanInvestment);