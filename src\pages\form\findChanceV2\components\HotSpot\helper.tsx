import { HotSpot } from '../../type';
import {
  checkLengthAndThrow,
  checkRequiredAndThrow,
  checkTagAndThrow,
  checkUrlAndThrow,
} from '../../utils';
export const mapPropToName = {
  title: '标题',
  time: '时间点',
  link: '链接',

  content: '摘要内容',
  tags: '机会标签',
  analyze: '热点剖析',
};
export const hotSpotHelper = {
  addArrayData: function(): HotSpot {
    return {
      title: '',
      time: '',
      link: '',

      content: '',
      tags: '',
      analyze: '',
    };
  },
  addInit: function(): HotSpot[] {
    return [];
  },

  checkData: function(data) {
    if (!data || data.length === 0) {
      throw new Error('重磅热点为空');
    }
    data.forEach((item, index) => {
      try {
        checkRequiredAndThrow(item.title, mapPropToName['title']);
        checkLengthAndThrow(item.title, mapPropToName['title'], 40);
        checkRequiredAndThrow(item.time, mapPropToName['time']);
        checkRequiredAndThrow(item.link, mapPropToName['link']);
        checkTagAndThrow(item.tags, mapPropToName['tags'], 3, 7);
        checkUrlAndThrow(item.link, mapPropToName['link']);
        // checkRequiredAndThrow(item.content, mapPropToName['content']);
      } catch (e) {
        throw new Error(`重磅热点-${e.message}(第${index + 1}项)`);
      }
    });
    return true;
  },
};
