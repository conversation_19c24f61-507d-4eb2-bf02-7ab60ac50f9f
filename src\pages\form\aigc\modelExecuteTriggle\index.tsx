/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-11-21 15:41:14
 * @LastEditTime: 2023-11-27 16:12:11
 * @FilePath: /thsjj-jj-fefund-boplatform/src/pages/form/aigc/modelExecuteTriggle/index.tsx
 * @Description: 
 */
import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import { env } from 'config';

export default function() {
  let amisScoped: any;
  const init = () => {
    let amis = amisRequire('amis/embed');
    amisScoped = amis.embed(
      '#modelExecuteTriggle',
      {
        ...amisJSON,
        data: {
          domain: `https://${env === 'dev' ? 'test' : ''}fund.10jqka.com.cn`,
        },
      },
      {},
      amisEnv(),
    );
  };
  useEffect(() => {
    init();
    return () => {
      amisScoped.unmount();
    };
  }, []);

  return <div id="modelExecuteTriggle"></div>;
}
