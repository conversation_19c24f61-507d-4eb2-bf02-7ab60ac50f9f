import React, { useState, useEffect } from 'react';
import Form<PERSON><PERSON> from 'form-render/lib/antd';
import api from 'api';
import { Button, Popconfirm, message } from 'antd';
import FORM_JSON from './form.json';

const { fetchFinancialDay, postFinancialDay } = api;

export default function () {
    const [init, setInit] = useState(false);
    const [formData, setFormData] = useState({});
    const [valid, setValid] = useState([]);

    useEffect(() => {
        let dataJSON = FORM_JSON;
        fetchFinancialDay().then((res: any) => {
            let { code, data } = res;
            if ( code === '0000' &&　data ) {
                data = JSON.parse(data);
                dataJSON.formData = {
                    productInfo: data.productInfo,
                    fundType: data.fundType
                }
            }
            setInit(true);
            setFormData(dataJSON.formData);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, []);

    const saveForm = () => {
        // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
        if (valid.length > 0) {
            alert(`校验未通过字段：${valid.toString()}`);
        } else {
            postFinancialDay({
                value: JSON.stringify(formData),
            }).then((res: any) => {
                if (res.code !== '0000') {
                    message.error(res.msg);
                } else {
                    message.success('保存成功！');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000)
                }
            }).catch((e: Error) => {
                message.error(e.message);
            });
        } 
    };
    if (!init) return '加载中';
    return (
        <div style={{padding: 60}}>
            <FormRender 
                propsSchema={FORM_JSON.propsSchema}
                formData={formData}
                onChange={setFormData}
                onValidate={setValid}
                displayType="row"
                showDescIcon={true}
            />
            <footer style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={saveForm}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="primary" 
                    >
                        保存
                    </Button>
                </Popconfirm>
                
            </footer>
        </div>
    )
}