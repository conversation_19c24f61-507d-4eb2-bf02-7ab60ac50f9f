
.light {
  background-color: #fff;
}
.dark {
  background-color: #001529;
  color: #eee;
}

.loader {
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100000;
  opacity: 1;


  .warpper {
    width: 100px;
    height: 100px;
    display: inline-flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-around;
  }

  .inner {
    width: 50px;
    height: 50px;
    margin: 0 auto;
    text-indent: -12345px;
    // border-top: 1px solid rgba(0, 0, 0, 0.1);
    // border-right: 1px solid rgba(0, 0, 0, 0.1);
    // border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    // border-left: 1px solid #fe4d5e;
    // border-radius: 50%;
    background: url(../../../public/logo.png) no-repeat 0 0;
    background-size: 100% 100%;
    z-index: 100001;

    :local {
      animation: spinner 600ms infinite linear;
    }
  }

  &.hidden {
    z-index: -1;
    opacity: 0;
    transition: opacity 0.6s ease 0.5s, z-index 0.1s ease 1.5s;
  }
}
@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
