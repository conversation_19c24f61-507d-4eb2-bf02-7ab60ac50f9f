
.higher-finance-boutique {
    .title {
        text-align: center;
        font-size: 30px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
    }
    .panel-btn {
        margin: -5px 0 0 10px;
    }
    .m-drag {
        height: 100%;
        width: 100%;
        overflow: hidden;
    }
    .tag {
        margin: 3px;
        font-size: 13px;
        border: 1px dashed #cccccc;
        border-radius: 4px;
        // padding: 0 8px;
        line-height: 30px;
        color: #666666;
        background: rgba(255, 255, 255, 0.7);   
    }
    .m-row{
        padding-left: 5px;
    }
}
:global {
    .higher-finance-boutique-drawer {
        .ant-col {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: nowrap;
            div {
                white-space: nowrap;
            }
        }
    }
}