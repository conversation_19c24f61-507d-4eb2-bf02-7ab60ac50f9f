import React, { useState } from 'react';
import { Upload, message, Icon } from 'antd';
import api from 'api';

const { uploadImg } = api;

const UploadImage = props => {
  const { value, onChange } = props;
  const [isLoading, setIsLoading] = useState(false);

  const getBase64 = (img, callback) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result));
    reader.readAsDataURL(img);
  };

  const beforeUpload = (file: File | Blob) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('你只能上传图片文件');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('文件不能大于2M');
    }
    return isJpgOrPng && isLt2M;
  };

  const customRequest = info => {
    const imgFile = info.file;
    getBase64(imgFile, imageUrl => {
      setIsLoading(true);
      uploadImg({
        base64str: imageUrl,
      }).then(data => {
        if (data && data.code === '0000') {
          let _img = data.data.url;
          if (_img.includes('http') && !_img.includes('https')) {
            _img = _img.replace('http', 'https');
          }
          info.file.status = 'done';
          info.file.url = _img;
          message.success('图片上传成功');
          if (onChange) {
            onChange(_img);
          }
          setIsLoading(false);
        } else {
          message.error('图片上传失败，请重新上传');
        }
      });
    });
  };

  const uploadButton = (
    <div>
      <Icon type={isLoading ? 'loading' : 'plus'} />
      <div className="ant-upload-text">上传</div>
    </div>
  );

  return (
    <Upload
      name="avatar"
      listType="picture-card"
      className="avatar-uploader"
      showUploadList={false}
      beforeUpload={beforeUpload}
      customRequest={customRequest}
    >
      {value ? <img src={value} alt="avatar" style={{ width: '100%' }} /> : uploadButton}
    </Upload>
  );
};

export default React.forwardRef(UploadImage);
