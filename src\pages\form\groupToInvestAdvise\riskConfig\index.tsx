import React, {useState, useEffect} from 'react';
import classnames from 'classnames';
import styles from './index.less';
import { Input, Button, Divider, Select, Table, Drawer, message, Spin, Popconfirm } from 'antd';
import api from 'api';

const { Option } = Select;
const { getGroupToInvestAdviseRiskConfig, saveGroupToInvestAdviseRiskConfig, getMappingList, getAllConsultList } = api;

// 风险适当性规则下拉选项
const riskRuleOptions = [
  {
    key: 'strong',
    label: '强匹配'
  },
  {
    key: 'weak',
    label: '弱匹配'
  },
]
const riskRuleMap = {
  strong: '强匹配',
  weak: '弱匹配'
}
// 是否允许组合转入下拉选项
const agreeIntoOptions = [
  {
    key: 'yes',
    label: '是'
  },
  {
    key: 'no',
    label: '否'
  },
]
const agreeIntoMap = {
  yes: '是',
  no: '否'
}

export default function () {
  const [formData, setFormData] = useState({
    vcTacode: '',
    vcTaname: '',
    riskRule: '',
    agreeInto: '',
  });
  const [listData,setListData] = useState<any>([
    // {
    //   vcTacode: '01',
    //   vcTaname: '华夏财富',
    //   agreeInto: 'no', // no：不允许。yes：允许。
    //   riskRule: 'strong', // strong：强匹配。weak：弱匹配。
    // },
    // {
    //   vcTacode: '01',
    //   vcTaname: '华夏财富',
    //   agreeInto: 'yes', // no：不允许。yes：允许。
    //   riskRule: 'strong', // strong：强匹配。weak：弱匹配。
    // }
  ]); //显示的列表内容
  const [mappingList, setMappingList] = useState<any>([]); // 映射列表
  const [mappingListMap, setMappingListMap] = useState<any>([]); // 映射列表map
  const [successInitMappingList, setSuccessInitMappingList] = useState<any>(false); // 映射列表是否初始化完毕
  const [configInfoList, setConfigInfoList] = useState<any>([]); // 投顾机构对应配置信息
  const [configInfoListMap, setConfigInfoListMap] = useState<any>([]); // 投顾机构对应配置信息map
  const [successInitConfigInfoList, setSuccessInitConfigInfoList] = useState<any>(false); // 配置信息列表是否初始化完毕
  const [showEdit, setShowEdit] = useState<boolean>(false); // 是否显示编辑面板
  const [isSaveLoading, setIsSaveLoading] = useState<boolean>(false); // 保存按钮loading
  const [isListLoading, setIsListLoading] = useState<boolean>(false); // 列表加载loading
  const tableColumns = [
    {
      title: '投顾机构名称',
      dataIndex: 'vcTaname',
      key: 'vcTaname',
    },
    {
      title: '是否允许组合转入',
      key: 'agreeInto',
      render: (row:any, record:any, index:number) => (agreeIntoMap[row.agreeInto])
    },
    {
      title: '风险适当性规则',
      key: 'riskRule',
      render: (row:any, record:any, index:number) => (riskRuleMap[row.riskRule])
    },
    {
      title: '操作',
      key: 'action',
      render: (row:any, record:any, index:number) => (
        <div>
          <span
            style={{ color: '#1890FF', cursor: 'pointer', marginRight: '10px' }}
            onClick={()=>{openEditDrawer(row)}}
          >
            编辑
          </span>
        </div>
      ),
    },
  ]; // 列表列

  useEffect(() => {
    handleGetMappingList();
    handleGetConfigInfo();
  }, [])

  useEffect(() => {
    if (successInitConfigInfoList && successInitMappingList) {
      handleGetAllConsult();
    }
  }, [successInitConfigInfoList, successInitMappingList, configInfoList])
  
  // 获取组合投顾映射列表
  const handleGetMappingList = () => {
    getMappingList().then((res: any)=>{
      if (res.code === '0000') {
        const _mappingListMap:any = new Map();
        const data = res.data || '[]';
        JSON.parse(data) && JSON.parse(data).forEach((item:any,index:number) => {
          if (!_mappingListMap.has(item.groupId)) {
            _mappingListMap.set(item.groupId, item);
          }
        });
        setMappingList(JSON.parse(data));
        setMappingListMap(_mappingListMap);
        setSuccessInitMappingList(true);
      } else {
        message.error('获取组合投顾映射列表失败');
      }
    }).catch(()=>{
      message.error('获取组合投顾映射列表失败');
    })
  };
  // 获取投顾机构对应配置信息
  const handleGetConfigInfo = () => {
    getGroupToInvestAdviseRiskConfig().then((res:any)=>{
      if (res.code === '0000') {
        const _configInfoListMap:any = new Map();
        const data = res.data || '[]';
        JSON.parse(data) && JSON.parse(data).forEach((item:any,index:number) => {
          if (!_configInfoListMap.has(item.vcTacode)) {
            _configInfoListMap.set(item.vcTacode, item);
          }
        });
        setConfigInfoList(JSON.parse(data));
        setConfigInfoListMap(_configInfoListMap);
        setSuccessInitConfigInfoList(true);
      } else {
        message.error('获取投顾机构配置信息失败');
      }
    }).catch(()=>{
      message.error('获取投顾机构配置信息失败');
    });
  }
  // 获取投顾机构列表
  const handleGetAllConsult = () => {
    setIsListLoading(true);
    getAllConsultList().then((res:any)=>{
      setIsListLoading(false);
      let consultListMap = new Map(); // 投顾机构列表map
      if (res.status_code === 0) {
        res.data.forEach((item:any,index:number)=> {
          if (!consultListMap.has(item.vcTacode)) {
            consultListMap.set(item.vcTacode, {vcTacode: item.vcTacode, vcTaname: item.vcTaname || item.vcTacode});
          };
        })
        let needInit = false; // 是否需要为空置赋默认值
        let _listData = [...consultListMap.values()].map((item,index)=> {
          let configInfo = {
            agreeInto: '',
            riskRule: ''
          };
          if (configInfoListMap.has(item.vcTacode)) {
            const mappingItem = configInfoListMap.get(item.vcTacode);
            configInfo.agreeInto = mappingItem.agreeInto;
            configInfo.riskRule = mappingItem.riskRule;
          };
          if (configInfo.agreeInto === '' || configInfo.riskRule === '') {
            needInit = true;
          }
          return {
            ...item,
            ...configInfo
          }
        });
        _listData = _listData.map((item,index)=>{
          item.agreeInto = item.agreeInto || 'no',
          item.riskRule = item.riskRule || 'weak'
          return item;
        })
        setListData(_listData);
        if (needInit) {
          saveGroupToInvestAdviseRiskConfig({
            value: JSON.stringify(_listData)
          }).then((res:any)=>{
            if (res.code === '0000') {
            } else {
              message.error('更新默认值失败');
            } 
          }).catch(()=>{
            message.error('更新默认值失败');
          })
        }
      } else {
        message.error('获取机构列表失败')
      }
    }).catch(()=>{
      setIsListLoading(false);
      message.error('获取机构列表失败')
    });
  };
  // 打开编辑面板
  const openEditDrawer = (row:any) => {
    let _formData = {...row};
    setShowEdit(true);
    setFormData(_formData);
  };
  // 关闭编辑面板
  const closeEditDrawer = () => {
    setShowEdit(false);
  };
  // 更改配置项
  const changeFormData = (type:string,value:any) => {
    const _formData = {...formData};
    _formData[type] = value;
    setFormData(_formData);
  };
  // 判断该投顾机构是否已有转入的组合
  const isExistGroup = (vcTacode:string) => {
    // let result = false;
    return mappingList.some((item:any,index:number) => {
      // if (item.vcTacode === vcTacode) {
      //   result = true;
      // }
      return item.vcTacode === vcTacode;
    })
    // return result;
  }
  // 保存
  const handleSave = () => {
    if (formData.agreeInto === 'no' && isExistGroup(formData.vcTacode)) {
      message.error('该机构已存在转入的投顾，无法更改');
      return;
    }
    setIsSaveLoading(true);
    configInfoListMap.set(formData.vcTacode,formData);
    saveGroupToInvestAdviseRiskConfig({
      value: JSON.stringify([...configInfoListMap.values()])
    }).then((res:any)=>{
      setIsSaveLoading(false);
      if (res.code === '0000') {
        message.success('保存成功');
        handleGetConfigInfo();
        closeEditDrawer();
      } else {
        message.error('保存失败');
      } 
    }).catch(()=>{
      message.error('保存失败');
      setIsSaveLoading(false);
    })
  }

  return (
    <div className={styles['risk-config']}>
      <Spin spinning={isListLoading}>
        <div className={styles['title']}>风险适当性配置</div>
        {/* 列表 */}
        <Table
          style={{marginTop: '20px'}}
          columns={tableColumns}
          dataSource={listData}
          pagination={false}
          rowKey={record => record.id}
        />
        {/* 编辑面板 */}
        <Drawer
          className="risk-config-drawer"
          title='编辑'
          placement="right"
          width="1000"
          maskClosable={false}
          destroyOnClose={true}
          onClose={closeEditDrawer}
          visible={showEdit}
        >
          {/* 风险适当性规则 */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>风险适当性规则：</div>
            <Select style={{ width: '250px',marginRight: '20px' }} value={formData.riskRule} onChange={(e:any) => {changeFormData('riskRule',e)}}>
              {
                  riskRuleOptions.map((item,index)=>{
                  return (
                    <Option key={item.key}>{item.label}</Option>
                  )
                })
              }
            </Select>
          </div>
          {/* 是否允许组合转入 */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>是否允许组合转入：</div>
            <Select style={{ width: '250px',marginRight: '20px' }} value={formData.agreeInto} onChange={(e:any) => {changeFormData('agreeInto',e)}}>
              {
                  agreeIntoOptions.map((item,index)=>{
                  return (
                    <Option key={item.key}>{item.label}</Option>
                  )
                })
              }
            </Select>
          </div>
          {/* 保存 */}
          <Popconfirm
            title="确定要保存吗"
            onConfirm={()=>{handleSave()}}
            okText="确定"
            cancelText="取消"
          >
            <Button style={{marginTop:'15px'}} type='primary' loading={isSaveLoading}>保存</Button>
          </Popconfirm>
        </Drawer>
      </Spin>
    </div>
  )
}
