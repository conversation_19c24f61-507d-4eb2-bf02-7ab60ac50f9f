import React, {useEffect, useState, useCallback, useRef} from 'react';
import { Button, message, Popconfirm, Select, Input, InputNumber } from 'antd';
import FormRender from "form-render/lib/antd";
import styles from '../../pageConfig/index.less';
import DesignatedUser from '../DesignatedUser';
import api from 'api';
import { timeFormat2 } from '@/utils/utils';
import { checkUrl } from '../../script/utils'; 


import classnames from 'classnames';
import ConditionParserWidget from '../formWidget/ConditionParser'
// import CouponUploadImg from '../../pageConfig/coupon/uploadImg';

// import StepUploadImg from '../../pageConfig/threesteps/uploadImg';

// import StepList from '../../pageConfig/threesteps/stepList';

// import ConfigSelect from '../selectModel/index';
// import TagModel from '../tagModel/index'
import STRICT_SELECTION_JSON from '../../pageConfig/strictSelection/form.json'
import VIP_SELECTION_JSON from '../../pageConfig/vipSelection/form.json'
import { STRICT_PROPNAME, VIP_PROPNAME } from '../../const';
import {checkForm as strictSelectionCheckForm} from '../../pageConfig/strictSelection/check'
import {checkForm as vipSelectionCheckForm} from '../../pageConfig/vipSelection/check'
import wrapperUploadImg from '../wrapperUploadImg'
import configHelper from '../../pageConfig/configHelper';

const { updateUserType, checkCoupon, postActivityDetails } = api;
const { Option } = Select;
interface dataProps {
    data: any;
    kycTag: any;
    olasTag: any;
    position: number;
    handleUpdate: Function;
    handleDelete: Function;

    floorType: string;
}
interface iSelectProps {
    onChange: Function,
    value: string,
    readonly: boolean
}
// import ConfigSelect from '../../../compoment/selectModel/index'
export default function ({floorType, data, kycTag, olasTag, position, handleUpdate, handleDelete}: dataProps) {

    const [isEdit, setEdit] = useState(false);
    const [valid, setValid] = useState([]);
    const [designatedData, setDesignatedData] = useState<any>();
    const [formConfig, setFormConfig] = useState<any>(null);
    const [formData, setFormState] = useState<any>({});
    const [commentList, setCommentList] = useState([]);
    const [codeEdit, setCodeEdit] = useState(true);
    useEffect(() => {
        let json: any = '';
        switch(floorType) {
           
            case STRICT_PROPNAME:
               
                json = STRICT_SELECTION_JSON;
                break;
          
            case VIP_PROPNAME:
                json = VIP_SELECTION_JSON;
                break;
            
        }
        console.log("formCONFIG",floorType,json)
        setFormConfig(json);
    }, [floorType])
    useEffect(() => {
        setFormState(data.formData);
        // if (floorType === 'usercomment') {
        //     setCommentList(data.commentList);
        // }   
        setDesignatedData({
            configData: data.configData,
            relationData: data.relationData
        })
    }, [data]);

    const onValidate = (valid: any) => {
        setValid(valid);
    }
    const handleChange = async () => {
        if(isEdit){
            let checkForm=null;
            // switch(floorType){
            //     case STRICT_PROPNAME:checkForm=strictSelectionCheckForm;break;
            //     case VIP_PROPNAME:checkForm=vipSelectionCheckForm;break;
            //     default:break;
            // }
            // let realData: any = {};
            // const {configData, relationData} = designatedData;
            // if (relationData?.targetType === 'kyc') {
            //     realData.olasId = '';
            //     realData.olasType = '';
            // } else if (relationData?.targetType === 'olas') {
            //     realData.kycLogic = '';
            //     realData.kycs = [];
            // }
            // if (configData.platform?.length === 0) {
            //     message.error('请选择使用平台')
            //     return;
            // }
            // if (configData.utype?.length === 0) {
            //     message.error('请选择用户类型')
            //     return;
            // }
            let _data: any = {
                formData,
                // configData,
                // relationData,
            }
            
            const helper=configHelper(floorType)
            if(!helper.checkForm||helper.checkForm(formData,position)){
                // setEdit(false);
                // if (relationData?.id) {
                    handleUpdate(_data, position)
                    setEdit(!isEdit)   
                // } else {
                //     let time = timeFormat2();
                //     let obj = {
                //         ...configData,
                //         ...realData,
                //         updateTime: time
                //     };
                //     updateUserType(obj).then((res: any) => {
                //         if (res.code !== '0000') return message.error(res.message);
                //         let id = res.data;
                //         _data.relationData.id = id;
                //         _data.relationData.updateTime = time;
                //         handleUpdate(_data, position);
                //         setEdit(!isEdit)
                //     })
                //     .catch((e: Error) => {
                //         message.error(e.message || '系统错误');
                //     });
                // }
            }
            
        } else {
            setEdit(!isEdit)
        }
    }
   
    const returnWidgets = () => {
        let widgets = {};
        switch(floorType) {
           
            case STRICT_PROPNAME: 
                widgets = {
                    conditionParserWidget:ConditionParserWidget
                    
                }
                break;
           
            case VIP_PROPNAME: 
                widgets = {
                    conditionParserWidget:ConditionParserWidget,
                    uploadImg: wrapperUploadImg
                }
                break;
           
        }
        return widgets
    }
    const onFormChange = (val: any) => {
       
        setFormState({...data.formData,...val})
    }
   
    function handleSelect(data){
        console.log('data',data)
        //setConfig(data)
        setDesignatedData({
            ...designatedData,
            configData: data,
            
        })
    }
    function handleTag(data:any){
        console.log('tag',data)
        //setRelation(data)
        setDesignatedData({
            ...designatedData,
            // configData: data,
            relationData:data
        })
    }
    return <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                    <Button ghost className={styles['m-button']} disabled={!codeEdit} onClick={handleChange}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={() => {handleDelete(position)}}
                        okText="是"
                        cancelText="否"
                    >
                        <Button ghost type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <h1 className="g-fs20 f-bold">配置详情</h1>
               
                 <FormRender
                        propsSchema={formConfig?.schema}
                        displayType='row'
                        formData={formData}
                        onValidate={onValidate}
                        onChange={onFormChange}
                        readOnly={!isEdit}
                        labelWidth={135}
                        widgets={returnWidgets()}
                    />
              {/* <DesignatedUser
                    data={data}
                    position={position}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                    setDesignatedData={setDesignatedData}
                /> */}
                {/* <ConfigSelect
                    handleChange={handleSelect}
                    isHead={false}
                    isEdit={isEdit}
                    data={designatedData?.configData}  
                />
                <TagModel
                    handleChange={handleTag}
                    data={designatedData?.relationData}
                    kycTag={kycTag}
                    olasTag={olasTag}
                    isEdit={isEdit}
                /> */}
               
            </div>

}