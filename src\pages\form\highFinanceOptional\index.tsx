import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import env from 'config';

export default function() {
  let amisScoped: any;
  const init = () => {
    let amis = amisRequire('amis/embed');
    amisScoped = amis.embed(
      '#whiteList',
      {
        ...amisJSON,
        data: {
          fuyaoApi:
            env.env === 'dev'
              ? 'https://testm.10jqka.com.cn/fuyao/fund_rank/indic/v1/optionalfund/indic'
              : 'https://dq.10jqka.com.cn/fuyao/fund_rank/indic/v1/optionalfund/indic',
        },
      },
      {},
      amisEnv(),
    );
  };
  useEffect(() => {
    init();

    return () => {
      amisScoped.unmount();
    };
  }, []);

  return <div id="whiteList"></div>;
}
