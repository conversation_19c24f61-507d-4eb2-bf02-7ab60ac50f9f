// 表单项
export type CommandItem<T> = {
  commandName: T;
  commandId?: T;
  applicationType: '1' | '2' | '3';
  systemSelection: '1' | '2';
  iosAppLeft: T;
  iosAppRight: T;
  iosSystemLeft: T;
  iosSystemRight: T;
  androidAppLeft: T;
  androidAppRight: T;
  androidSystemLeft: T;
  androidSystemRight: T;
  applicationMarket: T;
  userIdType: '1' | '2' | '3' | '4';
  distributionMode: '1' | '2';
  contentRules: T;
  mode: '0' | '1' | '2';
  userIdList: T;
  commandType: T;
  commandValue: T;
  commandExecutionTimes: number;
  commit: T;
};
// add | update 请求
export type ClientRiskData<T> = {
  id: T;
  commandListName: T;
  startDate: T;
  endDate: T;
  status: '0' | '1';
  commandList: CommandItem<string>[];
};
// change | query 请求
export interface ClientRisk {
  status_code: 0 | 1000 | 1001 | 1002 | 9100;
  status_msg: string;
  data?: ClientRiskData<string>;
}
// queryAll 请求
export interface QueryAllClientRisk {
  status_code: 0 | 1000 | 1001 | 1002 | 9100;
  status_msg: string;
  total: number;
  data?: ClientRiskData<string>[];
}
// delete 请求
export interface DeleteClientRisk {
  status_code: 0 | 1000 | 1001 | 1002 | 9100;
  status_msg: string;
  data?: string;
}
