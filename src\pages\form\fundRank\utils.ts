import { ITableItem, IRequestData, IFormData } from './type';

// 将表格数据转化为表单数据，打开表单的时候用
export const transformTableDataToFormData = (tableItem: ITableItem, index: number) => {
  if (index !== -1) {
    const temp = {};
    for (let i = 0; i < tableItem.filterList.length; i++) {
      temp[`filterField${i}`] = tableItem.filterList[i].filterField;
      temp[`filterSymbol${i}`] = tableItem.filterList[i].filterSymbol;
      temp[`filterValue${i}`] = tableItem.filterList[i].filterValue;
    }
    // 波段值需要改变数据格式
    for (let key in temp) {
      if (temp[key] === 'rsi') {
        temp[key.replace('Field', 'Value')] = (
          Number(temp[key.replace('Field', 'Value')]) * 100
        ).toString();
      }
    }

    const fundTypeFilterArr = tableItem.filterList.filter(item => item.filterField === 'l2code');
    const yearsFilterArr = tableItem.filterList.filter(item => item.filterField === 'nowDayAmount');
    if (fundTypeFilterArr.length === 0 && yearsFilterArr.length === 0) {
      return {
        online: tableItem.online === '1',
        name: tableItem.name,
        sortType: tableItem.sortType,
        sort: tableItem.sort,
        initialField: tableItem.initialField,
        ...temp,
        financialFloor: tableItem.financialFloor,
        recommend: tableItem.recommend === '1',
        operation: tableItem.operation === '1',
        operationText: tableItem.operationText,
        operationLink: tableItem.operationLink,
      } as IFormData;
    }
    if (fundTypeFilterArr.length) {
      temp['fundTypeValue'] = fundTypeFilterArr[0].filterValue.split(',');
    }
    if (yearsFilterArr.length) {
      temp['yearsSymbol'] = yearsFilterArr[0].filterSymbol;
      if (yearsFilterArr[0].filterSymbol === 'BETWEEN') {
        temp['yearsStartValue'] = Number(yearsFilterArr[0].filterValue.split(',')[0]) / 365;
        temp['yearsEndValue'] = Number(yearsFilterArr[0].filterValue.split(',')[1]) / 365;
      } else if (yearsFilterArr[0].filterSymbol === 'GREATER') {
        temp['yearsStartValue'] = Number(yearsFilterArr[0].filterValue) / 365;
      }
    }
    return {
      online: tableItem.online === '1',
      name: tableItem.name,
      sortType: tableItem.sortType,
      sort: tableItem.sort,
      initialField: tableItem.initialField,
      ...temp,
      financialFloor: tableItem.financialFloor,
      recommend: tableItem.recommend === '1',
      operation: tableItem.operation === '1',
      operationText: tableItem.operationText,
      operationLink: tableItem.operationLink,
    } as IFormData;
  } else {
    return {};
  }
};

// 将表单数据转化为请求数据，提交表单的时候用
const transformFormDataToReqItem = (
  formData: IFormData,
  key: string,
  editor: string,
  editTime: string,
  emptyData: '0' | '1' | null,
) => {
  const filterKeys = Object.keys(formData).filter(item => item.match('filter'));
  const filterLength = filterKeys.length / 3;
  const filterList = [];
  for (let i = 0; i < filterLength; i++) {
    filterList.push({
      filterField: formData[`filterField${i}`],
      filterSymbol: formData[`filterSymbol${i}`],
      filterValue: formData[`filterValue${i}`],
    });
  }
  // 波段值的数据为小数
  filterList.forEach(item => {
    if (item.filterField === 'rsi') {
      item.filterValue = (Number(item.filterValue) / 100).toString();
    }
  });
  // 新增基金类型筛选条件
  if (formData.fundTypeValue) {
    filterList.push({
      filterField: 'l2code',
      filterSymbol: 'IN',
      filterValue: formData.fundTypeValue.toString(),
    });
  }
  // 新增成立年限筛选条件
  if (formData.yearsSymbol === 'BETWEEN') {
    filterList.push({
      filterField: 'nowDayAmount',
      filterSymbol: 'BETWEEN',
      filterValue: `${Number(formData.yearsStartValue) * 365},${Number(formData.yearsEndValue) *
        365}`,
    });
  } else if (formData.yearsSymbol === 'GREATER') {
    filterList.push({
      filterField: 'nowDayAmount',
      filterSymbol: 'GREATER',
      filterValue: (Number(formData.yearsStartValue) * 365).toString(),
    });
  }

  const reqItem = {
    key,
    online: formData.online ? '1' : '0',
    name: formData.name,
    sortType: formData.sortType,
    sort: formData.sort,
    initialField: formData.initialField,
    filterList,
    financialFloor: formData.financialFloor,
    recommend: formData.recommend ? '1' : '0',
    operation: formData.operation ? '1' : '0',
    operationText: formData.operationText,
    operationLink: formData.operationLink,
    emptyData,
    lastEditor: editor,
    editorTime: editTime,
  } as ITableItem;
  return reqItem;
};
export const transformFormDataToReqData = (
  formData: IFormData,
  tableData: ITableItem[],
  zeroTag: boolean,
  key: string,
  editor: string,
  editTime: string,
) => {
  const targetIndex = tableData.findIndex(item => item.key === key);
  let rankList;
  // 是编辑还是新增
  if (targetIndex !== -1) {
    rankList = tableData.map((tableItem, index) => {
      if (index === targetIndex) {
        return transformFormDataToReqItem(formData, key, editor, editTime, tableItem.emptyData);
      }
      return tableItem;
    });
  } else {
    rankList = [...tableData, transformFormDataToReqItem(formData, key, editor, editTime, null)];
  }
  return {
    zeroTag: zeroTag ? '1' : '0',
    rankList,
  } as IRequestData;
};

// 生成唯一id
export const generateUniqueId = (dataSource: ITableItem[]) => {
  let id = '';
  let alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  for (let i = 0; i < 12; i++) {
    id += alphabet.charAt(Math.floor(Math.random() * alphabet.length));
  }
  while (dataSource.some(dataItem => dataItem.key === id)) {
    id = generateUniqueId(dataSource);
  }
  return id;
};
