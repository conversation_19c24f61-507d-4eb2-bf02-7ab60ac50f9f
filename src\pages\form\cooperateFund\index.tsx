import React, { useEffect, useState, useReducer } from 'react';
import { Table, Button, Modal, message, Spin, Input, Select } from 'antd';
import { EditContentType, ContentProps, columnsProps, CooperateStatus, ReducerState, SearchReducer } from './types';
import { initData } from './config';
import api from 'api';
import ModalContent from './components/ModalContent';
const { Option } = Select;

// 是否是合作基金
const renderCoop = (text: CooperateStatus) => text === '1' ? '是' : '否';
// 跳转情况
const renderJump = (text: any, record: ContentProps) => ['20', '36'].includes(record.subMarket) ? '场内' : '场外';
// 名称+别名处理+扩展名称
const renderName = (text: string, record: ContentProps) => {
  const nickNameList = record?.nickNameList || [];
  const displayName = nickNameList.length > 0 ? `${text}(${nickNameList.join(',')})` : text;
  return displayName;
};

/** 渲染百分比字段 */
const renderPercent = (text: any) => {
  if (text === null || text === undefined || text === '') {
    return '--';
  }
  const percentValue = parseFloat(text) * 100;
  return `${percentValue}%`;
};

const styleMargin = { marginRight: '12px' };
const initState: ReducerState = { search: '', subMarket: '', isCooperate: '' };
const reducer: SearchReducer = (state, action) => {
  switch (action.type) {
    case 'changeName':
      return { ...state, ...{ search: action.payload } };
    case 'changeMarket':
      return { ...state, ...{ subMarket: action.payload } };
    case 'changeCooperate':
      return { ...state, ...{ isCooperate: action.payload } };
    default:
      throw new Error();
  }
}

const Index = () => {
  const [tableData, setTableData] = useState<any[]>([]);
  const [currentData, setCurrentData] = useState<any>({}); // 单条数据
  const [edit, setEdit] = useState<number>(0); //0 新增 1编辑
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [total, setTotal] = useState(0);
  const [pageIndex, setPageIndex] = useState(1);

  const [state, dispatch] = useReducer(reducer, initState); // 所有查询参数

  /**
   * @description: 获取列表数据
   */
  const queryList = (page: number = pageIndex) => {
    // console.log(state, 'reducer')
    setTableLoading(true)
    api.cooperateList({
      ...state,
      ...{
        pageSize: 20,
        pageNum: page
      }
    }).then((res: any) => {
      if (res?.code !== '0000') {
        Modal.error({ content: res?.message || '获取数据失败' });
        return;
      }
      setTotal(res?.data?.total)
      setPageIndex(page)
      setTableData(res?.data?.list || [])
    }).finally(() => {
      setTableLoading(false)
    })
  }

  /**
   * @param {EditContentType} editContentType
   * @param {Partial} record
   * @description: 新增或编辑
   */
  const showDialogContent = (editContentType: EditContentType, record?: Partial<ContentProps>) => {
    if (editContentType === 0) {
      setCurrentData(JSON.parse(JSON.stringify(initData)))
      setShowDialog(true)
      setEdit(editContentType)
    } else if (editContentType === 1) {
      api.cooperateGet(null, record?.fundCode).then((res: any) => {
        if (res?.code !== '0000') {
          Modal.error({ content: res?.message || '获取数据失败' });
          return;
        }
        setCurrentData(res.data)
        setShowDialog(true)
        setEdit(editContentType)
      }).finally(() => {
      })
    }
  };

  /**
   * @description: 删除
   */
  const showDeleteConfirm = (item: Partial<ContentProps>) => {
    Modal.confirm({
      title: '确定删除?',
      okText: '确认',
      okType: 'danger',
      cancelText: '返回',
      onOk() {
        api.cooperateDel(null, item?.fundCode
        //   {
        //   data: { privateFundId: item.fundCode }
        // }
        ).then((res: any) => {
          if (res?.code !== '0000') {
            message.error('操作失败');
            return;
          }
          message.success('删除成功');
          queryList();
        })
      }
    });
  }

  const changeFundMessage = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.persist();
    dispatch({
      type: 'changeName',
      payload: e.target.value
    })
  }

  const changeMarket = (e: React.ChangeEvent<HTMLInputElement>) => {
    dispatch({
      type: 'changeMarket',
      payload: e.target.value
    })
  }

  const changeIsCooperate = (e: string) => {
    dispatch({
      type: 'changeCooperate',
      payload: e
    })
  }

  const publish = () => {
    setTableLoading(true)
    api.cooperatePublish().then((res: any) => {
      if (res?.code !== '0000') {
        Modal.error({ content: res?.message || '获取数据失败' });
        return;
      } else {
        message.success('发布成功')
      }
    }).finally(() => {
      setTableLoading(false)
    })
  }


  /** 操作 */
  const renderOperate = (text: string, record: any) => {
    return <div className="u-l-middle">
      <Button type="primary" onClick={() => { showDialogContent(EditContentType.Edit, record) }}>编辑</Button>
      <Button type="danger" className="g-ml10" onClick={() => { showDeleteConfirm(record) }}>删除</Button>
    </div>
  }

  /** 关联板块 */
  const renderPlate = (row: string, record: any) => {
    const themeList = record?.themeList || [];
    const displayTheme = themeList.map((item: any) => `${item.thscodeHq}${item.name}`);
    return <div className="u-l-middle">{displayTheme ? displayTheme.join(',') : ''}</div>
  }

  const columns: columnsProps[] = [
    { title: '基金代码', dataIndex: 'fundCode', key: 'fundCode' },
    { title: '市场代码', dataIndex: 'subMarket', key: 'subMarket' },
    { title: '基金名称', dataIndex: 'simpleFundName', key: 'simpleFundName', render: renderName },
    { title: '关联板块', key: 'relatedPlate', render: renderPlate, width: '240px' },
    { title: '跳转情况', dataIndex: 'jumpCode', key: 'jumpCode', render: renderJump },
    { title: '是否为合作基金', dataIndex: 'isCooperate', key: 'isCooperate', render: renderCoop },
    { title: 'ETF热度(数值)', dataIndex: 'popularity', key: 'popularity' },
    { title: 'ETF热度(百分比)', dataIndex: 'popularityPercent', key: 'popularityPercent', render: renderPercent },
    { title: 'ETF自选数(数值)', dataIndex: 'selfSelectCount', key: 'selfSelectCount' },
    {
      title: 'ETF自选数(百分比)',
      dataIndex: 'selfSelectCountPercent',
      key: 'selfSelectCountPercent',
      render: renderPercent,
    },
    { title: '操作', key: '_operate', width: '220px', render: renderOperate },
  ];
  useEffect(queryList, []);
  const onEditClose = () => {
    setShowDialog(false)
  }
  const onPageChange = (pageIndex: number) => {
    queryList(pageIndex);
  }
  const pagination = {
    showQuickJumper: true,
    total,
    current: pageIndex,
    pageSize: 20,
    onChange: onPageChange,
    showTotal: (total: number) => `共${total}条数据`
  }
  return <div>
    <header>
      <Button style={{marginBottom: '12px'}} type="danger" onClick={() => publish()}>发布</Button>
      <h2> 合作基金配置列表 </h2>
      <section className='u-j-middle g-mb20'>
        <div className='u-l-middle'>
          <Input style={{ width: 200, marginRight: 12 }} value={state.search} onChange={changeFundMessage} placeholder="基金代码/基金名称" />
          <div style={styleMargin} className="u-j-middle">
            <span>市场代码：</span>
            <Input style={{ width: 150 }} value={state.subMarket} onChange={changeMarket} placeholder="市场代码" />
          </div>
          <div style={styleMargin} className="u-l-middle">
            <span>合作基金：</span>
            <Select defaultValue="" style={{ width: 150 }} onChange={changeIsCooperate}>
              <Option value="">全部</Option>
              <Option value="1">是</Option>
              <Option value="0">否</Option>
            </Select>
          </div>
          <Button type="primary" style={styleMargin} onClick={() => {queryList(1)}}>搜索</Button>
        </div>
        <Button style={{marginRight: '68px'}} type="primary" onClick={() => showDialogContent(EditContentType.Add)}>添加</Button>
      </section>
    </header>
    <Spin spinning={tableLoading}>
      <Table
        rowKey='fundCode'
        columns={columns as any}
        dataSource={tableData}
        pagination={pagination}
      />
    </Spin>
    {
      showDialog &&
      <ModalContent
        currentData={currentData}
        showDialog={showDialog}
        onEditClose={onEditClose}
        queryList={queryList}
        edit={edit}
      ></ModalContent>
    }
  </div>
}

export default Index;