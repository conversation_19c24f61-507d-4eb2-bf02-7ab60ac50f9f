/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React, { Fragment } from 'react';
import {But<PERSON>, Card, Row, message, Popconfirm, Collapse} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import FormRender from 'form-render/lib/antd';

const { fetchSpringFestival, fetchNewBoy, fetchNewBoyInit, postNewBoy, postSeniorUser, fetchSeniorUser, fetchSeniorUserDev} = api;
const schema = {
    "propsSchema": {
        "type": "object",
        "properties": {
            "name": {
                "title": "活动名称",
                "type": "string"
            },
            "ticketName": {
                "title": "副标题",
                "type": "string",
                "description": "例：百信月薪宝 2天体验金"
            },
            "prodId": {
                "title": "产品代码",
                "type": "string"
            },
            "prodName": {
                "title": "产品名称",
                "type": "string"
            },
            "awardShare": {
                "title": "奖励金额",
                "type": "number"
            },
            "awardRate": {
                "title": "奖励利率",
                "type": "number",
                "description": "单位：小数 如：2%为0.02"
            },
            "awardDate": {
                "title": "奖励天数",
                "type": "number"
            },
            "maxActivityAward": {
                "title": "活动最大奖励金额",
                "type": "number"
            },
            "startDate": {
                "title": "活动开始时间",
                "type": "string",
                "format": "dateTime"
            },
            "endDate": {
                "title": "活动结束时间",
                "type": "string",
                "format": "dateTime"
            },
            "activityState": {
                "title": "活动状态",
                "type": "number",
                "description": "0 - 正常"
            }
        }
    },
    "formData": {
        "name": "",
        "ticketName": "",
        "prodId": "",
        "prodName": "",
        "awardShare": "",
        "awardRate": "",
        "awardDate": "",
        "maxActivityAward": "",
        "startDate": "",
        "endDate": "",
        "activityState": ""
    },
    "seniorUserSchema": {
        "type": "object",
        "properties": {
            "aibProdId": {
              "title": "百信产品Id",
              "type": "string"
            },
            "aibProdName": {
              "title": "百信产品名称",
              "type": "string"
            },
            "aibOperator": {
              "title": "百信渠道标识",
              "type": "string"
            },
            "paProdId": {
              "title": "平安产品Id",
              "type": "string"
            },
            "paProdName": {
              "title": "平安产品名称",
              "type": "string"
            },
            "paOperator": {
              "title": "平安渠道标识",
              "type": "string"
            },
            "startDate": {
                "title": "活动开始时间",
                "type": "string",
                "format": "dateTime"
            },
            "endDate": {
                "title": "活动结束时间",
                "type": "string",
                "format": "dateTime"
            },
            "awardDate": {
              "title": "奖励天数",
              "type": "string"
            },
            "maxPersonAward": {
              "title": "活动最大个人奖励金额",
              "type": "string"
            },
            "maxActivityAward": {
              "title": "活动最大奖励金额",
              "type": "string"
            },
            "awardThreshold": {
                "title": "预值",
                "type": "string"
              },
            "activityState": {
              "title": "活动状态 0-正常 3-提前结束（活动总资金达到上限）",
              "type": "string"
            }
          }
    },
    "seniorUserData": {
        "aibProdId": "",
        "aibProdName": "",
        "aibOperator": "",
        "paProdId": "",
        "paProdName": "",
        "paOperator": "",
        "startDate": "",
        "endDate": "",
        "awardDate": "",
        "maxPersonAward": "",
        "maxActivityAward": "",
        "activityState": "",
        "awardThreshold": "",
    }
};


@autobind
class codeInterface extends React.Component {
    constructor (props) {
        super(props);
        this.state = {newBoyData: schema.formData || {},
                    seniorUserData: schema.seniorUserData || {}};
    }

    downloadSpringFestival () {
        fetchSpringFestival({
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '春节加息活动' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    downloadNewBoy () {
        fetchNewBoy({
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '新手专区体验金' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    downloadSeniorUser () {
        fetchSeniorUserDev({
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '高净值用户拉新活动' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    newBoyDataInit () {
        fetchNewBoyInit({
            type: 'query'
        }).then(data => {
            this.setState({newBoyData: data.data});
        })
    }

    newBoyUpload () {
        let _newBoyData = this.state.newBoyData;
        console.log(_newBoyData)
        postNewBoy ({
            type: 'init',
            value: JSON.stringify(_newBoyData)
        }).then(data => {
            if (data.code === '0000') {
                message.success('修改成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    onChange = newBoyData => {
        this.setState({ newBoyData }, () => {console.log(this.state)});
    }

    seniorUserDataInit () {
        fetchSeniorUser({
            type: 'query'
        }).then(data => {
            console.log(data)
            this.setState({seniorUserData: data.data});
        })
    }

    seniorUserUpload () {
        let _seniorUserData = this.state.seniorUserData;
        console.log(_seniorUserData)
        postSeniorUser ({
            type: 'init',
            value: JSON.stringify(_seniorUserData)
        }).then(data => {
            if (data.code === '0000') {
                message.success('修改成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    onChangeSeniorUser = seniorUserData => {
        this.setState({ seniorUserData }, () => {console.log(this.state)});
    }

    componentDidMount () {
        // this.newBoyDataInit();
    }

    render () {
        return (
            <section className="codeInterface">
                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-15563'}
                    extra={'上线时间：2020/01/20'}
                >
                    <Row>
                        <span
                        style={{fontSize: '18px'}}>春节加息活动 下载奖励列表</span>
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadSpringFestival}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-15232'}
                    extra={'上线时间：2020/02'}
                >
                    <Row>
                        <span
                        style={{fontSize: '18px'}}>新手专区体验金 下载奖励列表</span>
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadNewBoy}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-15232'}
                    extra={'上线时间：2020/02'}
                >
                    <Row>
                        <span
                        style={{fontSize: '18px'}}>高净值用户拉新活动 下载奖励列表</span>
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadSeniorUser}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Collapse onChange={this.newBoyDataInit}>
                    <Collapse.Panel 
                        header="FS-15232 新手专区 上线时间：2020/02" 
                        key="1">
                        <FormRender
                            propsSchema={schema.propsSchema}
                            formData={this.state.newBoyData}
                            onChange={this.onChange}
                        />

                        <Popconfirm
                            placement="leftBottom"
                            title={'你确定要提交么'}
                            onConfirm={this.newBoyUpload}
                            okText="确认"
                            cancelText="取消"
                            >

                            <Button
                                type="primary" 
                                style={{ float: 'right' }}
                            >
                                提交修改
                            </Button>

                        </Popconfirm>
                    </Collapse.Panel>
                </Collapse>

                <Collapse onChange={this.seniorUserDataInit}>
                    <Collapse.Panel 
                        header="FS-15805 2月高净值用户拉新活动 上线时间：2020/02" 
                        key="1">
                        <FormRender
                        propsSchema={schema.seniorUserSchema}
                        formData={this.state.seniorUserData}
                        onChange={this.onChangeSeniorUser}
                        />

                        <Popconfirm
                            placement="leftBottom"
                            title={'你确定要提交么'}
                            onConfirm={this.seniorUserUpload}
                            okText="确认"
                            cancelText="取消"
                        >
                            <Button
                                type="primary" 
                                style={{ float: 'right' }}
                            >
                                提交修改
                            </Button>
                        </Popconfirm>
                    </Collapse.Panel>
                </Collapse>

            </section>
        )
    }
}

export default codeInterface;