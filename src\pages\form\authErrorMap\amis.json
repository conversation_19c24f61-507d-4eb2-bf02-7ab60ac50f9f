{"type": "page", "body": [{"type": "crud", "syncLocation": false, "api": {"url": "/common_config/hash_data_get_all?key=AUTH_ERROR_MAP", "adaptor": "const finalData = []\r\nObject.keys(payload.data).map(keyItem=>{finalData.push({InnerErrorCode: keyItem,...JSON.parse(payload.data[keyItem]||'{}'),THSErrorCodeDescString:(JSON.parse(payload.data[keyItem]||'{}').THSErrorCodeDesc)?.text})}) \r\n return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.message,\r\n data: finalData\r\n}"}, "loadDataOnce": true, "autoGenerateFilter": true, "filterSettingSource": ["InnerErrorCode", "InnerErrorDesc", "scene", "THSErrorCode", "value"], "columns": [{"name": "InnerErrorCode", "label": "内部错误码", "id": "u:ae3832892f01", "searchable": {"type": "input-text", "name": "InnerErrorCode", "label": "内部错误码", "clearable": true, "multiple": true, "searchable": true, "id": "u:6fd7affc8001"}}, {"name": "InnerErrorDesc", "label": "内部错误描述", "id": "u:ae3832892f02", "searchable": {"type": "input-text", "name": "InnerErrorDesc", "label": "内部错误描述", "clearable": true, "multiple": true, "searchable": true, "id": "u:6fd7affc8002"}}, {"name": "scene", "label": "场景", "tpl": "<%= this.sceneShowMap[this.scene] %>", "id": "u:16f1ac2f1d03", "searchable": {"type": "select", "source": "${sceneMap}", "label": "场景", "clearable": true, "multiple": true, "searchable": true, "id": "u:6fd7affc8003"}}, {"name": "THSErrorCode", "label": "展示错误码", "id": "u:ae3832892f91", "searchable": {"type": "input-text", "name": "THSErrorCode", "label": "展示错误码", "clearable": true, "multiple": true, "searchable": true, "id": "u:6fd7affc805b"}}, {"name": "THSErrorCodeDescString", "label": "展示描述及解决方案", "id": "u:16f1ac2f1dc8", "tpl": "<%= this.THSErrorCodeDesc.text %>", "searchable": {"type": "input-text", "name": "THSErrorCodeDescString", "label": "展示描述及解决方案", "clearable": true, "multiple": true, "searchable": true, "id": "u:6fd7affc805b"}}, {"type": "operation", "label": "操作", "id": "u:96d5b5297574", "buttons": [{"label": "编辑", "type": "button", "actionType": "dialog", "level": "link", "dialog": {"title": "编辑", "body": {"type": "form", "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"key": "AUTH_ERROR_MAP", "propName": "${InnerErrorCode}", "&": "$$"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.poolFundHistoryDtoList || []\r\n  }\r\n};", "requestAdaptor": "const { url, data } = api;\r\nconst postBody = JSON.stringify(data)\r\nconsole.log(postBody)\r\napi.data={value:postBody,key:'AUTH_ERROR_MAP',propName:`${data.propName}&${data.scene}`} \r\nreturn { ...api  };"}, "body": [{"type": "input-text", "name": "InnerErrorCode", "label": "内部错误码", "id": "u:4eee8d512aa1", "disabled": true}, {"type": "input-text", "name": "InnerErrorDesc", "label": "内部错误描述", "id": "u:4eee8d512aa2"}, {"type": "select", "source": "${sceneMap}", "name": "scene", "label": "场景", "id": "u:4eee8d532a16", "required": true}, {"type": "select", "name": "THSErrorCode", "label": "展示错误码", "id": "u:4eee8d512a12", "source": "${authDictOption}", "multiple": false, "required": true}, {"type": "textarea", "name": "THSErrorCodeDesc", "label": "展示描述及解决方案", "disabled": true, "id": "u:4e12ee8d512aa5", "value": "${authDictMap[THSErrorCode]}"}]}}, "id": "u:11d4d8ca99d5"}, {"type": "button", "label": "删除", "actionType": "ajax", "level": "link", "className": "text-danger", "confirmText": "确定要删除？", "api": {"method": "post", "url": "/common_config/hash_data_del", "dataType": "form", "messages": {}, "adaptor": "return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.message}", "data": {"key": "AUTH_ERROR_MAP", "propName": "${InnerErrorCode}&${scene}"}}, "id": "u:4eee8d50eaa3"}]}], "id": "u:f229bd411595", "features": ["create"], "bulkActions": [], "headerToolbar": [{"label": "新增", "type": "button", "actionType": "dialog", "level": "primary", "dialog": {"title": "新增", "body": {"type": "form", "api": {"method": "post", "url": "/common_config/hash_data_save", "data": {"key": "AUTH_ERROR_MAP", "propName": "${InnerErrorCode}", "&": "$$"}, "dataType": "form", "adaptor": "return {\r\n  status: payload.code === '0000' ? 0 : 1,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    total: payload.data?.size,\r\n    items: payload.data?.poolFundHistoryDtoList || []\r\n  }\r\n};", "requestAdaptor": "const { url, data } = api;\r\nconst postBody = JSON.stringify(data)\r\nconsole.log(postBody)\r\napi.data={value:postBody,key:'AUTH_ERROR_MAP',propName:`${data.propName}&${data.scene}`} \r\nreturn { ...api  };"}, "body": [{"type": "input-text", "name": "InnerErrorCode", "label": "内部错误码", "id": "u:4eee8d512aa1", "required": true}, {"type": "input-text", "name": "InnerErrorDesc", "label": "内部错误描述", "id": "u:4eee8d512aa2"}, {"type": "select", "source": "${sceneMap}", "name": "scene", "label": "场景", "id": "u:4eee8d532a16", "required": true}, {"type": "select", "name": "THSErrorCode", "label": "展示错误码", "id": "u:4eee8d512a12", "source": "${authDictOption}", "multiple": false, "required": true}, {"type": "textarea", "disabled": true, "name": "THSErrorCodeDesc", "label": "展示描述及解决方案", "id": "u:4e12ee8d512aa5", "value": "${authDictMap[THSErrorCode]}"}]}}, "id": "u:16b6381a0efd"}, "export-csv", "bulkActions"]}], "id": "u:1e770f769e23"}