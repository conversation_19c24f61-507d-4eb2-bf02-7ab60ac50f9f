import React, { useState, useEffect, FC, } from 'react';
import classnames from 'classnames';

import { Button, Collapse, message, Spin } from 'antd';
import ConfigItem from './components/ConfigItem';
import { ConfigData } from './type';
import { configItemhelper } from './components/configItemhelper';
const { Panel } = Collapse;
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import DragAndSortWrapper from './components/DragAndSortWrapper';
import api from "api";
import { changeZiXunToHotSpot, changeZiXunToInfo, HOTSPOT_KEY, KEY, setRandomId } from './utils';
import HotSpotPool from './hotSpotPool';
import {TopicFundContext} from './context'

const {postHash,fetchHashAll,postHashDel,getTopicFunds} = api


export interface IProps {}
const FindChanceV2: FC<IProps> = () => {
  // const [baseConfigDataList,setBaseConfigDataList]=useState<ConfigData[]>([]);
  const [configDataList, setConfigDataList] = useState<ConfigData[]>([]);

  const [isSavedKey, setIsSavedKey] = useState<string[]>([]);
  const [isDelKey, setIsDelKey] = useState<string[]>([]);
  const [poolDataAll,setPoolDataAll]=useState({});
  const [topicFundList,setTopicFundList]=useState([]);
  const [loading,setLoading]=useState(false);
  const [init,setInit]=useState(false);
  const getConfigDataList=async()=>{
    try{
      setInit(false);
      const res= await fetchHashAll({key:KEY})
      const res2=await fetchHashAll({key:HOTSPOT_KEY});
      if(res.code!=="0000"){
        throw new Error(res.message)
      }
      if(res2.code!=="0000"){
        throw new Error(res2.message)
      }
      const {data}=res;
      const {data:poolDataAll}=res2;
      if(!data){
        return;
      }
     
      const list=[];
      for(const key in poolDataAll){
        poolDataAll[key]=JSON.parse(poolDataAll[key]);
      }
      setPoolDataAll(poolDataAll)
      for(const key in data){
        const normalData=JSON.parse(data[key]);
      //   if(poolDataAll[key]){
      //     const poolData=JSON.parse(poolDataAll[key]);
      //   const poolDataHotSpot=poolData?.hotspots?.map(changeZiXunToHotSpot)||[];
      //   const poolDataInfo=poolData?.relateInfos?.map(changeZiXunToInfo)||[];
      //   normalData.hotspots=[...poolDataHotSpot,...normalData.hotspots] 
      //   normalData.infomation=[...poolDataInfo,...normalData.infomation]   
      // }
       
        setRandomId(normalData.id)
        list.push(normalData)
      }
      list.sort((a,b)=>a.index-b.index);
      setConfigDataList(list);
      setInit(true);
    }catch(e){
      
      message.error(e.message||"未知异常")
    }
  }

  const getTopicFund=async()=>{
    try{
      const res= await getTopicFunds();
      // const res={
      //   status_code:0,
      //   status_msg:"success",
      //   data:Array.from(new Array(300),(item,index)=>{
      //     return {themeCode:`00001.TI${index}`,themeName:`test0${index}`}
          
      //   })
      // }
      if(res.status_code!==0){
        throw new Error(res.status_msg)
      }
      setTopicFundList(res.data)
    }catch(e){
      
      message.error(`获取主题列表：${e.message||"未知异常"}`)
    }
  }
  const addSavedKey=(keys:string[])=>{
    const savedKeySet=new Set(isSavedKey);
   
    for(const i in keys){
      const keyStr=keys[i];
      console.log(keyStr)
      savedKeySet.add(keyStr)
    }
   
    setIsSavedKey([...savedKeySet])
  }
  const removeSavedKey=(keys:string[])=>{
    const savedKeySet=new Set(isSavedKey);
    for(const i in keys){
      const keyStr=keys[i];
      savedKeySet.delete(keyStr)
    }
    setIsSavedKey([...savedKeySet])
  }
  const addDelKey=(keys:string[])=>{
    const delKeySet=new Set(isDelKey);
    for(const i in keys){
      const keyStr=keys[i];
      delKeySet.add(keyStr)
    }
    setIsDelKey([...delKeySet])
    removeSavedKey(keys)
  }
  const changePosition=(dragIndex,hoverIndex)=>{
    
        let dragItem=configDataList[dragIndex];
        let hoverItem=configDataList[hoverIndex];
        if(!dragItem||!hoverItem){
          return;
        }
        //hoverIndex后面的都往后移动一位
      
        let newData=[...configDataList];
        //先去掉在数组中被拖动的数据
        newData.splice(dragIndex,1,undefined);

        //获取放置位置以及后面的数据
        const postfixArr=newData.slice(hoverIndex)||[];
        const prefixArr=newData.slice(0,hoverIndex+1)||[];
        prefixArr[hoverIndex]=dragItem;
        const changeIndexKeys=[];
        console.log(prefixArr,postfixArr)
        newData=[...prefixArr,...postfixArr].filter(item=>Boolean(item)).map((item,index)=>{
         
          if(item.index!==index){
            item.index=index;
            changeIndexKeys.push(item.id)
          }
          return item;
        })
        console.log("newData",newData)
        
      //   dragItem={...dragItem,index:hoverIndex};
      //   hoverItem={...hoverItem,index:dragIndex};
        
      //   const newData=[...configDataList];
      //   newData[hoverIndex]=dragItem
      //   newData[dragIndex]=hoverItem
     
       setConfigDataList(newData)    
       addSavedKey(changeIndexKeys)    
  }

  const handleChange=(index,v)=>{
      const newData=[...configDataList]
      newData[index]=v;
      newData[index].index=index;
      setConfigDataList(newData);
     
      
      addSavedKey([v.id])
  }
  const handleDel=(index)=>{
    const newData=[...configDataList];
    const targetItem=newData[index]
    newData.splice(index,1);
    newData.forEach((item,index)=>{
      item.index=index
    })
   addDelKey([targetItem.id])
   setConfigDataList(newData);
  }
  // const saveHashValue=(propName,value)=>{
  //     return 
  // }

  // const delHashValue=(propName)=>{

  // }
  const onPublish=async()=>{
    console.log(isSavedKey,isDelKey)
    //处理保存
    setLoading(true);
    try{
      for(const i in isSavedKey){
        const propName=isSavedKey[i]
        
        const value=configDataList.find((item)=>item.id===propName)
        configItemhelper.checkData(value)
        if(!value){
          continue;
        }
        if(poolDataAll[propName]){
          const res=await postHashDel({key:HOTSPOT_KEY,propName})
          
          if(res.code!=="0000"){
            throw new Error(`id:${propName},热点池数据销毁失败`)
          }
        }
        const res = await postHash({key:KEY,propName,value:JSON.stringify(value)})
        if(res.code==="0000"){
          message.success(`第${value.index+1}项发布成功，id=${value.id}`)
        }else{
          throw new Error(res.message)
        }
      }
    }catch(e){
      message.error(e.message||"未知错误")
    }finally{
      setLoading(false);
    }
    setLoading(true);
    //处理删除
    try{
      for(const i in isDelKey){
        const propName=isDelKey[i]
        // const value=configDataList.find((item)=>item.id===propName)
        if(!propName){
          return;
        }
        if(poolDataAll[propName]){
          const res=await postHashDel({key:HOTSPOT_KEY,propName})
          if(res.code!=="0000"){
            throw new Error(`id:${propName},热点池数据销毁失败`)
          }
        }
        const res = await postHashDel({key:KEY,propName})
        if(res.code==="0000"){
          message.success(`删除成功，id=${propName}`)
        }else{
          throw new Error(res.message)
        }
      }
    }catch(e){
      message.error(e.message||"未知错误")
    }finally{
      setLoading(false);
    }
  }

  useEffect(()=>{
    getConfigDataList();
    getTopicFund();
  },[])
  return (
    <div>
      <Spin spinning={loading}>
      <TopicFundContext.Provider value={topicFundList}>
      <Button disabled={!init} onClick={onPublish} type={'primary'} style={{ marginBottom: '12px' }}>
        发布
      </Button>
      <DndProvider key={"1"} backend={HTML5Backend}>
          <Collapse> 
          
            {configDataList?.map((item,index)=>{
              
              return  <Panel
              key={item.id}
              header={<div style={{display:'flex',justifyContent:"space-between"}}>
                <div className='u-l-middle'>
                <span>#{index+1}</span>
                <span>{item.sectorTitle}</span>
                <span style={{marginLeft:"120px"}}>(id值：{item.id})</span>
                </div>
                <DragAndSortWrapper changePosition={changePosition} id={item.id} key={item.id} index={index}>
                <Button>拖拽移动(位置:{index})</Button>
                </DragAndSortWrapper>
              </div>}
              >
                  <ConfigItem baseData={item} basePool={poolDataAll[item.id]} onSave={(v)=>handleChange(index,v)} onDel={()=>handleDel(index)}></ConfigItem>
              </Panel>
            })}
           
            </Collapse> 
     
      </DndProvider>
      <Button
      disabled={!init}
        type={'primary'}
        style={{ marginTop: '12px' }}
        onClick={() => {
          setConfigDataList([...configDataList, configItemhelper.addInit(configDataList.length)]);
        }}
      >
        新增
      </Button>
      </TopicFundContext.Provider>
        {/* <HotSpotPool/> */}
        </Spin>
    </div>
  );
};
export default FindChanceV2;
