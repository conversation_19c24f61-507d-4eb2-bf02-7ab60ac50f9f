{"schema": {"type": "object", "properties": {"title": {"title": "tab标题", "type": "string", "ui:width": "60%", "ui:options": {}}, "mainCondition": {"title": "核心条件设置", "type": "", "ui:widget": "conditionParserWidget"}, "condition": {"title": "条件设置", "type": "", "ui:widget": "conditionParserWidget"}, "vipInfo": {"title": "牛人信息区", "type": "object", "properties": {"avatar": {"title": "牛人头像", "type": "string", "ui:widget": "uploadImg"}, "name": {"title": "牛人名称", "type": "string", "format": "textarea", "maxLength": 10}, "background": {"title": "牛人背景", "type": "string", "format": "textarea", "maxLength": 25}, "mind": {"title": "选基理念", "type": "string", "format": "textarea", "maxLength": 50}, "jumpUrl": {"title": "跳转链接", "type": "string", "pattern": "^http(|s)?://[^\n ，]*$"}}, "required": ["avatar", "name", "background", "mind", "jumpUrl"]}}, "required": ["title", "mainCondition", "condition"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 140}