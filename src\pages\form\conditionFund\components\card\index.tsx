import React, { useEffect, useState } from 'react';
import { Button, message, Collapse, Input, Popconfirm } from 'antd';
// import api from 'api';
import MyCard from '../cardItem';
import styles from '../../pageConfig/index.less';
// import ConfigSelect from '../../../compoment/selectModel/index';
// import LocationModel from '../locationModel';
import { timeFormat2 } from '@/utils/utils';
import { fetchData, addData, handleApi } from '../../script/utils';
import { checkUrl } from '../../script/utils'; 
import configHelper from '../../pageConfig/configHelper';

// const { fetchCBAS, fetchOLAS, checkCoupon, postActivityDetails } = api;
const { Panel } = Collapse;

interface iProps {
    floorType: string;
    config:{ utype: string[], platform: string[] };
    isModify:boolean;
    setModify:any;
    kycTag:any[];
    olasTag:any[]
    originData:any;
    setOriginData:any;
}

export default function ({floorType,config,isModify,setModify,kycTag,olasTag,originData,setOriginData}: iProps) {
    const [init, setInit] = useState(true);
    
    const [activeKey, setActiveKey] = useState(0);
    


  
    function addItem() {
        
        let obj:any = {
            formData: configHelper(floorType).addData(),
            configData: {
                platform: config.platform,
                utype: config.utype,
            },
           
            relationData: {
              
                targetType: '',
                kycLogic: '',
                kycs: [],
                olasId: '',
                
            },
            other: {
                sv: null,
                needLogin: null,
                updateTime: null
            }
        }
        
        let data = [...originData, obj];
        setOriginData(data);
        setActiveKey(data.length - 1);
    }

    function handleUpdate(data: any, index: number) {
        if (!isModify) setModify(true);
        let _originData: any = [...originData];
        _originData[index] = data;
        setOriginData(_originData);  
    }
    function handleDelete(index: number) {
        if (!isModify) setModify(true);
        let _originData = [...originData];
        _originData.splice(index, 1);
        setOriginData(_originData);
    }
    // function handleSelect(item: any) {
    //     console.log("handleSelect",config,item)
    //     let tag = 0;
    //     if (item?.configData.platform?.length === 0 && item?.configData.utype?.length === 0) {
    //         tag = 1;
    //     } else if (config.utype?.length === 0) {
    //         for (let data of item?.configData?.platform) {
    //             if (config.platform?.indexOf(data) != -1) {
    //                 tag = 1;
    //                 break;
    //             }
    //         }
    //     } else if (config.platform?.length === 0) {
    //         for (let data of item?.configData.utype) {
    //             if (config.utype?.indexOf(data) != -1) {
    //                 console.log("cautch",data)
    //                 tag = 1;
    //                 break;
    //             }
    //         }
    //     } else {
    //         for (let data of item?.configData?.platform) {
    //             if (config.platform?.indexOf(data) != -1) {
    //                 for (let data of item?.configData?.utype) {
    //                     if (config.utype?.indexOf(data) != -1) {
    //                         tag = 1;
    //                         break;
    //                     }
    //                 }
    //                 if (tag === 1) {
    //                     break;
    //                 }
    //             }
    //         }
    //     }
    //     return !!tag
    // }
    function handleActiveKey (key: any) {
        setActiveKey(key);
    }
    function goUp(e: any, item: any, index: number) {
        e.stopPropagation();

        if (index === 0) {
            message.info('已在最上方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index - 1, 0, item);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
    function goDown(e: any, item: any, index: number) {
        e.stopPropagation();
        if (index === originData.length - 1) {
            message.info('已在最下方');
            return
        }
        let _originData = [...originData];
        _originData.splice(index, 1);
        _originData.splice(index + 1, 0, item);
        setOriginData(_originData);
        if (!isModify) setModify(true);
    }
   
    
    return <div>
        {/* <LocationModel location={modelName(floorType).location} /> */}
        {/* <ConfigSelect handleChange={handleChange} isHead={true} /> */}
        {/* <Button type="primary" style={{marginBottom: 20}} onClick={onSubmit} disabled={!isModify}>保存</Button> */}
       
        <Collapse activeKey={activeKey} onChange={handleActiveKey}>
            {
                originData?.map((item: any, index: number) => {
                   
                    return <Panel 
                    header={(<span style={{height: 22, display: 'inline-block', verticalAlign: 'middle'}}>#{index+1} {item.formData?.[configHelper(floorType)?.modelName]}
                    <span style={{marginLeft:"120px"}}>id: {item.formData?.id||"--"}</span>
                    </span>)} 
                    extra={<div className={styles['m-collpase-button']}><Button onClick={(e) => { goUp(e, item, index) }}>上移</Button><Button onClick={(e) => { goDown(e, item, index) }}>下移</Button></div>}
                    key={index}>
                    <MyCard
                        data={item}
                        // gonggeClassify={gonggeClassify}
                        position={index}
                        floorType={floorType}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        handleDelete={handleDelete}
                        handleUpdate={handleUpdate}></MyCard>
                </Panel>
                
                

                })
            }
        </Collapse>
        <Button onClick={addItem} type='primary' style={{ marginTop: '20px' }}>添加</Button>
    </div>

}