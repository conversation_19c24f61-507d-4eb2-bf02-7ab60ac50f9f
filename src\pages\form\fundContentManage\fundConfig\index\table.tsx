import React, { useState, useEffect } from 'react';
import { Input, Button, Table } from 'antd';

const columns = [
    {
      title: '基金代码',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '基金名称',
      dataIndex: 'age',
      key: 'age',
    },
    {
        title: '关联同顺号',
        dataIndex: 'address',
        key: 'address',
    },
    {
        title: '同顺号文章',
        dataIndex: 'address',
        key: 'address',
    },
    {
        title: '资讯文章',
        dataIndex: 'address',
        key: 'address',
    },
    {
      title: 'Action',
      key: 'action',
      render: (text, record) => (
        <span>配置</span>
      )
    }
  ];

export default function () {

    // const handleTableChange = (pagination, filters, sorter) => {
    //     this.fetch({
    //         sortField: sorter.field,
    //         sortOrder: sorter.order,
    //         pagination,
    //         ...filters,
    //     });
    // };

    // const fetch = (params = {}) => {
    //     this.setState({ loading: true });
    //     reqwest({
    //         url: 'https://randomuser.me/api',
    //         method: 'get',
    //         type: 'json',
    //         data: getRandomuserParams(params),
    //     }).then(data => {
    //         console.log(data);
    //         this.setState({
    //             loading: false,
    //             data: data.results,
    //             pagination: {
    //                 ...params.pagination,
    //                 total: 200,
    //             },
    //         });
    //     });
    // };
    return (
        <Table
            columns={columns}
            // rowKey={record => record.login.uuid}
            dataSource={data}
            pagination={pagination}
            loading={loading}
            onChange={handleTableChange}
        />
    )
}