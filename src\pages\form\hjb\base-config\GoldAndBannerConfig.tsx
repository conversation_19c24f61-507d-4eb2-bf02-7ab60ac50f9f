/**
 * 黄金宝 - 基础配置 - 基本配置
 */
import React from 'react';
import GoldFundForm from './GoldFundForm';
import AdBannerForm from './AdBannerForm';
import { Typography, Button } from 'antd';
import BlockWithToolBar from '../BlockWithToolBar';

type Props = {
  formRef: unknown;
  goldFundSaved: { [k: string]: unknown }[];
  goldFund: { [k: string]: unknown }[];
  onUpdateGoldFund: (goldFund: Props['goldFund']) => void;
  banners: { [k: string]: unknown }[];
  onAppendBanner: (n: number) => void;
  onRemoveBanner: (n: number) => void;
};

//
const GoldAndBannerConfig: React.FunctionComponent<Props> = ({
  formRef,
  goldFundSaved,
  goldFund,
  onUpdateGoldFund,
  banners,
  onAppendBanner,
  onRemoveBanner,
}) => {
  //

  // prettier-ignore
  return (
    <section>
      <GoldFundForm
        goldFundSaved={ goldFundSaved }
        goldFund={ goldFund }
        onUpdateGoldFund={ onUpdateGoldFund }
      />
      <Typography.Title level={ 4 }>持仓页 Banner 配置</Typography.Title>
      {
        banners.length === 0 && onAppendBanner &&
        <Button type="primary" size="large" onClick={ () => onAppendBanner(0) }>添加 Banner</Button>
      }
      {
        banners.map((data, index) =>
          <BlockWithToolBar key={ data._id } number={ index + 1 } onAppend={ onAppendBanner } onRemove={ onRemoveBanner }>
            <AdBannerForm formRef={ formRef } data={ data } />
          </BlockWithToolBar>
        )
      }
    </section>
  )
};

export default GoldAndBannerConfig;
