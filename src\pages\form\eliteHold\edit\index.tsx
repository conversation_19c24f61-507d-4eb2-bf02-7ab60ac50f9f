import React, { useState, useEffect } from 'react';
import zh_CN from 'antd/lib/locale-provider/zh_CN';
import 'moment/locale/zh-cn';
import './index.less';
import api from 'api';
import {
  Col,
  Input,
  Upload,
  Button,
  DatePicker,
  ConfigProvider,
  message,
  Drawer,
  Select,
} from 'antd';
import moment from 'moment';

const { Option  } = Select;
const {
    newCommonUpload,
    getEliteHoldCountId
} = api;
const { RangePicker } = DatePicker;
message.config({
  duration: 3, // 持续时间
  maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
  top: 70, // 到页面底部距离
});

export default function({drawerType, show, closeDrawer, accountList,setAccountList, accountInfo, initFundDetail, initIndustryDetail, initInvestmentTrend,handlePostEliteHoldConfig}) {
    // 表单数据
    const [formData, setFormData] = useState(accountInfo);
    // 是否正在上传中
    const [isUploading, setIsUploading] = useState(false);
    // 修改账户基本信息
    const changeBasicInfo = (key, value) => {
        const newFormData = { ...formData };
        newFormData[key] = value;
        setFormData(newFormData);
    };
    // 修改持仓详情信息
    const changeHoldDetail = (key, value, index) => {
        const newFormData ={...formData};
        if (key === 'rangeTime') {
            const [holdTime, clearTime] = value;
            newFormData.holdDetailList[index].holdTime = holdTime;
            newFormData.holdDetailList[index].clearTime = clearTime;
        } else {
            newFormData.holdDetailList[index][key] = value;
        }
        setFormData(newFormData);
    };
    // 修改投资策略信息
    const changeStrategyInfo = (key, value) => {
        const newFormData ={...formData};
        newFormData.strategyInfo[key] = value;
        setFormData(newFormData);
    };
    // 修改持仓详情信息
    const changeHoldAnalysis = (key, value, index) => {
        const newFormData ={...formData};
        newFormData.holdAnalysisList[index][key] = value;
        setFormData(newFormData);
    };
    // 修改投资动态信息
    const changeInvestmentTrends = (key, value, index) => {
        const newFormData ={...formData};
        newFormData.investmentTrends[index][key] = value;
        setFormData(newFormData);
    };
    // 模块中列表新增/删除操作
    const handleListAddDelete = (type: 'add' | 'delete', moduleId, index?) => {
        const _formData = {...formData};
        const MODULENAMEMAP = {
            holdDetail: {
                name: '持仓详情',
                property: 'holdDetailList',
                initInfoFunc: initFundDetail,
                minNum: 1,
            },
            holdAnalysis: {
                name: '持仓分析',
                property: 'holdAnalysisList',
                initInfoFunc: initIndustryDetail,
                minNum: 1,
            },
            investmentTrends: {
                name: '投资动态',
                property: 'investmentTrends',
                initInfoFunc: initInvestmentTrend,
                minNum: 0,
            },
        }
        const moduleInfo =  MODULENAMEMAP[moduleId];
        if (type === 'add') {
            _formData[moduleInfo.property].push(moduleInfo.initInfoFunc());
        } else {
            _formData[moduleInfo.property].length <= moduleInfo.minNum ? 
            message.warning(`${moduleInfo.name}数量最少为${moduleInfo.minNum}个`) : 
            _formData[moduleInfo.property].splice(index, 1);
        }
        setFormData(_formData);
    };
    // 图片上传
    const uploadImg = (options, type ,index?)=> {
        if (isUploading) {
            message.error('已有图片正在上传中，请稍后');
            return;
        }
        if (options.file.size > 2048000) {
            message.error('文件大小不得超过2M');
            return;
        };
        setIsUploading(true);
        message.info('图片上传中');
        let params = new FormData();
        params.append('file', options.file);
        newCommonUpload(params).then((res)=>{
            if (res.status_code === 0) {
            let _url = '';
            if (res.data.includes('http')) {
                //兼容以前
                _url = res.data.replace('http://', 'https://');
            } else {
                //区分正式环境、测试环境
                if (
                window.location.hostname.includes('localhost') ||
                window.location.hostname.includes('febs.')
                ) {
                _url = 'https://testo.thsi.cn/' + res.data;
                } else {
                _url = 'https://o.thsi.cn/' + res.data;
                }
            }
            let _formData = {...formData};
            switch (type) {
                case 'avatar':
                    _formData.avatar = _url;
                    break;
                case 'toolImg':
                    _formData.strategyInfo.toolImg = _url;
                    break;
                case 'picUrl':
                    _formData.investmentTrends[index].picUrl = _url;
                    break;
                default:
                    break;
            }
            setFormData(_formData);
            message.success('上传成功');
            } else {
                message.error(res.status_msg);
            }
            setIsUploading(false);
        }).catch(()=>{
            message.error('上传失败');
            setIsUploading(false);
        });
    };
    // 表单校验
    const checkFormData = () => {
        let isPass = false;
        switch (true) {
        case !formData.name:
            message.error('请填写账户名称');
            break;
        case !formData.avatar: 
            message.error('请上传账户头像');
            break;
        case !formData.tags: 
            message.error('请填写账户标签');
            break;
        case formData.tags.indexOf('，') !== -1: 
            message.error('账户标签中不能出现中文逗号');
            break;
        case !formData.holdDetailList.every(item => {return item.fundCode && item.amount && item.holdTime && !(item.status === 'clear' && !item.clearTime)}): 
            message.error('请填写持仓详情必填项');
            break;
        case !formData.strategyInfo.title: 
            message.error('请填写策略标题');
            break;
        case !formData.strategyInfo.desc: 
            message.error('请填写策略描述');
            break;
        case !formData.strategyInfo.toolImg: 
            message.error('请上传工具图片');
            break;
        case !formData.strategyInfo.toolUrl: 
            message.error('请填写工具地址');
            break;
        case !formData.strategyInfo.intro: 
            message.error('请填写策略简介');
            break;
        case !formData.strategyInfo.explain: 
            message.error('请填写策略说明');
            break;
        case !formData.holdAnalysisList.every((item) => {return item.code && item.name && item.percent}): 
            message.error('请填写持仓分析必填项');
            break;
        case !formData.investmentTrends.every((item) => {return item.id && item.title && item.picUrl && item.author && item.url}): 
            message.error('请填写投资动态必填项');
            break;
        default:
            isPass = true;
            break;
        }
        return isPass;
    }
    // 获取唯一id
    const handleGetEliteHoldCountId =  async () => {
        try {
            const res = await getEliteHoldCountId();
            return res.data || '';
        } catch (error) {
            message.error('获取唯一id失败，请重试');
            return '';
        }
    }
    // 提交
    const handleSubmit = async () => {
        if (checkFormData()) {
            const _formData = {...formData};
            let _accountList = [...accountList];
            console.log(formData)
            _formData.lastEditor = JSON.parse(localStorage.userInfos).username;
            _formData.lastEditTime = new Date().getTime();
            _formData.holdDetailList = formData.holdDetailList.map(item => {
                let _item = {...item};
                _item.holdTime = item.holdTime ? item.holdTime?.valueOf() : '';
                _item.clearTime = item.clearTime ? item.clearTime?.valueOf() : '';
                return _item;
            })
            if (drawerType === '0') {
                _formData.creator = JSON.parse(localStorage.userInfos).username;
                _formData.createTime = new Date().getTime();
                _formData.id = await handleGetEliteHoldCountId();
                _accountList.push(_formData);
            } else {
                _accountList = accountList.map((item, index) => {
                    return item.id === _formData.id ? _formData : item;
                })
            }
            // 编辑时DraggableArea组件监听不到更新，所以先将列表清空。
            setAccountList([]);
            if (_formData.id) {
                handlePostEliteHoldConfig(_accountList);
            }
        }
    };

    useEffect(() => {
        const _accountInfo = JSON.parse(JSON.stringify(accountInfo));
        _accountInfo.holdDetailList = accountInfo.holdDetailList.map((item) => {
            let _item = {...item};
            _item.holdTime = item.holdTime ? moment(item.holdTime) : null;
            _item.clearTime = item.clearTime ? moment(item.clearTime) : null;
            return _item;
        })
        setFormData(_accountInfo);
    }, [accountInfo])
    
    return (
        <Drawer
            className='elite-hold-edit'
            title={drawerType === '0' ? '新增账户' : '编辑账户'}
            placement="right"
            width="1500"
            maskClosable={false}
            destroyOnClose={true}
            onClose={closeDrawer}
            visible={show}
        >
            <ConfigProvider locale={zh_CN}>
                <div className='config-area'>
                    {/* 账户基本信息 */}
                    <div className='basic-info'>
                        <div className='module-title'>账户基本信息</div>
                        <div className='row'>
                            <div className='label require'>账户名称</div>
                            <Col span={8}><Input placeholder='请输入账户名称' value={formData.name} onChange={e => {changeBasicInfo('name', e.target.value)}} /></Col>
                        </div>
                        <div className='row'>
                            <div className='label require'>账户头像</div>
                            <Upload
                                customRequest={options => {
                                    uploadImg(options, 'avatar');
                                }}
                                showUploadList={false}
                                >
                                <div className="upload-img-box" style={{width: '80px', height: '80px'}}>
                                    {
                                    formData.avatar ? 
                                    <img src={formData.avatar} alt="加载失败" style={{width: '100%', height: '100%'}} />:
                                    <div className="plus-sign">+</div>
                                    }
                                </div>
                            </Upload>
                        </div>
                        <div className='row'>
                            <div className='label require'>账户标签</div>
                            <Col span={8}><Input placeholder='请输入账户标签，多个标签用英文逗号分隔' value={formData.tags} onChange={e => {changeBasicInfo('tags', e.target.value)}} /></Col>
                        </div>
                        <div className='row'>
                            <div className='label require'>收益统计区间</div>
                            <Col span={8}>
                                <Select style={{width: '100%'}} value={formData.profitRange} onChange={(e) => {changeBasicInfo('profitRange',e)}}>
                                    <Option value={'tMonth'}>近三月</Option>
                                    <Option value={'lastMonth'}>上月</Option>
                                    <Option value={'nowYear'}>今年</Option>
                                    <Option value={'lastYear'}>去年</Option>
                                </Select>
                            </Col>
                        </div>
                    </div>
                    {/* 持仓详情 */}
                    <div className='hold-detail'>
                        <div className='module-title'>
                            持仓详情
                            <span className="add-btn" onClick={() => {handleListAddDelete('add','holdDetail')}}>添加</span>
                        </div>
                        {
                            formData.holdDetailList.map((item, index) => {
                                return (
                                    <div className='row' key={index}>
                                        <div className='label require'>持仓基金</div>
                                        <Col span={2}><Input placeholder='基金代码' value={item.fundCode} onChange={e => {changeHoldDetail('fundCode', e.target.value, index)}} /></Col>
                                        <div className='label require'>持仓状态</div>
                                        <Col span={2}>
                                            <Select style={{width: '100%'}} value={item.status} onChange={(e) => {changeHoldDetail('status', e, index)}}>
                                                <Option value={'hold'}>持有</Option>
                                                <Option value={'clear'}>已清仓</Option>
                                            </Select>
                                        </Col>
                                        <div className='label require'>持有成本(元)</div>
                                        <Col span={2}><Input placeholder='持有成本' value={item.amount} onChange={e => {changeHoldDetail('amount', e.target.value, index)}}  /></Col>
                                        <div className='label require'>{item.status === 'hold' ? '建仓日期' : '建仓至清仓日期'}</div>
                                        {
                                            item.status === 'hold' ? 
                                            <Col span={4}><DatePicker style={{ width: '100%' }} value={item.holdTime} onChange={e => {changeHoldDetail('holdTime', e, index);}}/></Col> :
                                            <Col span={4}><RangePicker style={{ width: '100%' }} value={[item.holdTime, item.clearTime]} onChange={e => {changeHoldDetail('rangeTime', e, index);}}/></Col>
                                        }
                                        <Button type='danger' style={{marginLeft: '20px'}} onClick={() => {handleListAddDelete('delete','holdDetail',index)}}>删除</Button>
                                    </div>
                                )
                            })
                        }
                    </div>
                    {/* 投资策略 */}
                    <div className='strategy-info'>
                        <div className='module-title'>投资策略</div>
                        <div className='row'>
                            <div className='label require'>策略标题</div>
                            <Col span={8}><Input placeholder='请输入策略标题' value={formData.strategyInfo.title} onChange={e => {changeStrategyInfo('title', e.target.value)}} /></Col>
                        </div>
                        <div className='row'>
                            <div className='label require'>策略描述</div>
                            <Col span={8}><Input placeholder='请输入策略描述' value={formData.strategyInfo.desc} onChange={e => {changeStrategyInfo('desc', e.target.value)}} /></Col>
                        </div>
                        <div className='row'>
                            <div className='label require'>工具图片</div>
                            <Upload
                                customRequest={options => {
                                    uploadImg(options, 'toolImg');
                                }}
                                showUploadList={false}
                                >
                                <div className="upload-img-box" style={{width: '116px', height: '84px'}}>
                                    {
                                    formData.strategyInfo.toolImg ? 
                                    <img src={formData.strategyInfo.toolImg} alt="加载失败" style={{width: '100%', height: '100%'}} />:
                                    <div className="plus-sign">+</div>
                                    }
                                </div>
                            </Upload>
                        </div>
                        <div className='row'>
                            <div className='label require'>工具地址</div>
                            <Col span={8}><Input placeholder='请输入工具地址' value={formData.strategyInfo.toolUrl} onChange={e => {changeStrategyInfo('toolUrl', e.target.value)}} /></Col>
                        </div>
                        <div className='row'>
                            <div className='label require'>投资策略</div>
                            <Col span={8}>
                                <Select style={{width: '100%'}} value={formData.strategyInfo.strategy} onChange={(e) => {changeStrategyInfo('strategy',e)}}>
                                    <Option value={'qsls'}>趋势猎手</Option>
                                    <Option value={'cdzs'}>抄底指数</Option>
                                    <Option value={'zsgz'}>指数估值</Option>
                                </Select>
                            </Col>
                        </div>
                        <div className='row'>
                            <div className='label require'>策略简介</div>
                            <Col span={8}><Input placeholder='请输入策略简介' value={formData.strategyInfo.intro} onChange={e => {changeStrategyInfo('intro', e.target.value)}} /></Col>
                        </div>
                        <div className='row'>
                            <div className='label require'>策略说明</div>
                            <Col span={8}><Input placeholder='请输入策略说明' value={formData.strategyInfo.explain} onChange={e => {changeStrategyInfo('explain', e.target.value)}} /></Col>
                        </div>
                    </div>
                    {/* 持仓分析 */}
                    <div className='hold-analysis'>
                        <div className='module-title'>
                            持仓分析
                            <span className="add-btn" onClick={()=>{handleListAddDelete('add','holdAnalysis')}}>添加</span>
                        </div>
                        {
                            formData.holdAnalysisList.map((item, index) => {
                                return (
                                    <div className='row' key={index}>
                                        <div className='label require'>成分名称</div>
                                        <Col span={4}><Input placeholder='请输入成分名称' value={item.name} onChange={e => {changeHoldAnalysis('name', e.target.value, index)}} /></Col>
                                        <div className='label require'>指数代码(带后缀)</div>
                                        <Col span={4}><Input placeholder='请输入指数代码' value={item.code} onChange={e => {changeHoldAnalysis('code', e.target.value, index)}} /></Col>
                                        <div className='label require'>占比(%)</div>
                                        <Col span={4}><Input placeholder='请输入占比' value={item.percent} onChange={e => {changeHoldAnalysis('percent', e.target.value, index)}} /></Col>
                                        <Button type='danger' style={{marginLeft: '20px'}} onClick={() => {handleListAddDelete('delete', 'holdAnalysis', index)}}>删除</Button>
                                    </div>
                                )
                            })
                        }
                    </div>
                    {/* 投资动态 */}
                    <div className='investment-trends '>
                        <div className='module-title'>
                            投资动态
                            <span className="add-btn" onClick={()=>{handleListAddDelete('add','investmentTrends')}}>添加</span>
                        </div>
                        {
                            formData.investmentTrends.map((item, index) => {
                                return (
                                    <div className='investment-trend' key={index}>
                                        <div className='row'>
                                            <div className='label require'>资讯id</div>
                                            <Col span={4}><Input placeholder='请输入资讯id' value={item.id} onChange={e => {changeInvestmentTrends('id', e.target.value, index)}} /></Col>
                                            <div className='label require'>标题</div>
                                            <Col span={8}><Input placeholder='请输入标题' value={item.title} onChange={e => {changeInvestmentTrends('title', e.target.value, index)}} /></Col>
                                        </div>
                                        <div className='row'>
                                            <div className='label'>摘要</div>
                                            <Col span={13}><Input placeholder='请输入摘要' value={item.absOfContent} onChange={e => {changeInvestmentTrends('absOfContent', e.target.value, index)}} /></Col>
                                        </div>
                                        <div className='row'>
                                            <div className='label require'>图片</div>
                                            <Upload
                                                customRequest={options => {
                                                    uploadImg(options, 'picUrl', index);
                                                }}
                                                showUploadList={false}
                                                >
                                                <div className="upload-img-box" style={{width: '126px', height: '93px'}}>
                                                    {
                                                    item.picUrl ? 
                                                    <img src={item.picUrl} alt="加载失败" style={{width: '100%', height: '100%'}} />:
                                                    <div className="plus-sign">+</div>
                                                    }
                                                </div>
                                            </Upload>
                                        </div>
                                        <div className='row'>
                                            <div className='label'>标签</div>
                                            <Col span={4}><Input placeholder='请输入标签' value={item.label} onChange={e => {changeInvestmentTrends('label', e.target.value, index)}} /></Col>
                                            <div className='label require'>作者</div>
                                            <Col span={4}><Input placeholder='请输入作者' value={item.author} onChange={e => {changeInvestmentTrends('author', e.target.value, index)}} /></Col>
                                            <div className='label require'>跳转链接</div>
                                            <Col span={10}><Input placeholder='请输入跳转链接' value={item.url} onChange={e => {changeInvestmentTrends('url', e.target.value, index)}} /></Col>
                                        </div>
                                        <Button type='danger' className='delete-btn' onClick={() => {handleListAddDelete('delete', 'investmentTrends', index)}}>删除</Button>
                                    </div>
                                )
                            })
                        }
                    </div>
                </div>
                <div className='footer'>
                    <Button type="primary" style={{ marginRight: 12 }} onClick={handleSubmit}>提交</Button>
                    <Button onClick={closeDrawer}>取消</Button>
                </div>
            </ConfigProvider>
        </Drawer>
    );
}
