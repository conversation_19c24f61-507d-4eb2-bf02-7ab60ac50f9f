import {
    Form,
    Input,
    Button,
    message,
    Popconfirm
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import { iStrictElectionData } from './index';
import styles from '../index.less';

interface StrictCardProps extends FormComponentProps {
    currentData: iStrictElectionData;
    handleData: (data: iStrictElectionData) => void;
    handleDelete: () => void;
}

class StrictCard extends React.Component<StrictCardProps, any> {
    constructor(props: StrictCardProps) {
        super(props);
        this.state = {
            fundName: '',
            isEdit: false,
        }
    }

    formItemLayout = {
        labelCol: {
            span: 3
        },
        wrapperCol: {
            span: 19
        },
    };
    handleCode = (e: any) => {
        let val = e.target.value;
        val = val.replace(/\s/g, '');
        val = val.replace(/[^\d]/g, '');
        if (val?.length === 6) {
            this.getJsonp(val);
        } else {
            this.setState({
                fundName: ''
            })
        }
    }
    handleName= (e: any) => {
        let val = e.target.value;
        this.setState({
            fundName: val
        })
    }
    handleSubmit = () => { 
        const { fundName, isEdit } = this.state;
        if (isEdit) {
            const { currentData } = this.props;
            this.props.form.validateFields((err, values) => {
                if (!err) {
                    values = { ...currentData, ...values, fundName };
                    console.log(values);
                    this.props.handleData(values);
                    this.setState({
                        isEdit: !isEdit
                    })
                }
            });
        } else {
            this.setState({
                isEdit: !isEdit
            })
        }
        
    };
    getJsonp(code: string) {
        let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/fund/multiFundInfo/${code}_name?return=jsonp&jsonp=jsonp`
        console.log(_script)
        document.body.appendChild(_script);
        _script.onload = function() {
            document.body.removeChild(_script);
        };
    }

    componentDidMount() {
        const _this = this;
        const { fundName } = this.props.currentData;
        this.setState({
            fundName
        })
        window.jsonp = function (res: any) {
            console.log(res);
            const { error, data } = res;
            if (error?.id === 0 && data) {
                Object.keys(data)?.forEach((key: string) => {
                    console.log(data[key]);
                    _this.setState({
                        fundName: data[key]?.name
                     })
                })
            }
        }
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const { fundName, isEdit } = this.state;
        const { handleDelete } = this.props;
        return (
            <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                <Button className={styles['m-button']} onClick={this.handleSubmit}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={handleDelete}
                        okText="是"
                        cancelText="否"
                    >
                        <Button type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <Form {...this.formItemLayout}>
                    <Form.Item label="基金ID" wrapperCol={{span: 4}}>
                        {getFieldDecorator('fundCode', {
                            initialValue: this.props.currentData?.fundCode,
                            rules: [{ required: true, message: '请输入基金ID' }],
                        })(
                            <Input onChange={this.handleCode} disabled={!isEdit}/>
                        )}
                    </Form.Item>
                    <Form.Item label="基金名称" wrapperCol={{span: 6}}>
                        <Input onChange={this.handleName} value={fundName} disabled={!isEdit}/>
                    </Form.Item>
                    <Form.Item label="精选理由">
                        {getFieldDecorator('reason', {
                            initialValue: this.props.currentData?.reason,
                            rules: [{ required: true, message: '请输入精选理由' }],
                        })(
                            <Input disabled={!isEdit} maxLength={72}/>
                        )}
                    </Form.Item>
                    <Form.Item label="基金标签" wrapperCol={{span: 6}}>
                        {getFieldDecorator('label', {
                            initialValue: this.props.currentData?.label
                        })(
                            <Input disabled={!isEdit} maxLength={10}/>
                        )}
                        <span className={styles['m-card-required']}>多个基金标签以'|'分隔</span>
                    </Form.Item>
                </Form>
            </div>
        )
    }
}
const WrappedStrictCard = Form.create<StrictCardProps>({ name: 'strictCard' })(StrictCard);
export default WrappedStrictCard;