import {
    Form,
    Input,
    Button,
    message,
    Popconfirm
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import api from 'api';
import { iPopularityData } from './index';

const { modifyFundPosition, getTradeDay } = api;
const { TextArea } = Input;

interface investDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: iPopularityData;
    handleData: (data: iPopularityData) => void;
}

class InvestDrawer extends React.Component<investDrawerProps, any> {
    constructor(props: investDrawerProps) {
        super(props);
        this.state = {
            fundName: ''
        }
    }

    formItemLayout = {
        labelCol: {
            span: 5
        },
        wrapperCol: {
            span: 19
        },
    };
    handleCode = (e: any) => {
        let val = e.target.value;
        val = val.replace(/\s/g, '');
        val = val.replace(/[^\d]/g, '');
        if (val?.length === 6) {
            this.getJsonp(val);
        } else {
            this.setState({
                fundName: ''
            })
        }
    }
    handleName= (e: any) => {
        let val = e.target.value;
        this.setState({
            fundName: val
        })
    }
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { fundName } = this.state;
        const { currentData } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                values = { ...currentData, ...values, fundName };
                console.log(values);
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    getJsonp(code: string) {
        let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/fund/multiFundInfo/${code}_name?return=jsonp&jsonp=jsonp`
        console.log(_script)
        document.body.appendChild(_script);
        _script.onload = function() {
            document.body.removeChild(_script);
        };
    }

    componentDidMount() {
        const _this = this;
        const { fundName } = this.props.currentData;
        this.setState({
            fundName
        })
        window.jsonp = function (res: any) {
            console.log(res);
            const { error, data } = res;
            if (error?.id === 0 && data) {
                Object.keys(data)?.forEach((key: string) => {
                    console.log(data[key]);
                    _this.setState({
                        fundName: data[key]?.name
                     })
                })
            }
        }
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const { onEditClose } = this.props;
        const { fundName } = this.state;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="基金ID" wrapperCol={{span: 4}}>
                        {getFieldDecorator('fundCode', {
                            initialValue: this.props.currentData?.fundCode,
                            rules: [{ required: true, message: '请输入基金ID' }],
                        })(
                            <Input onChange={this.handleCode}/>
                        )}
                    </Form.Item>
                    <Form.Item label="基金名称" wrapperCol={{span: 6}}>
                        <Input onChange={this.handleName} value={fundName}/>
                    </Form.Item>
                    <Form.Item label="解读">
                        {getFieldDecorator('description', {
                            initialValue: this.props.currentData?.description,
                        })(
                            <TextArea maxLength={46}/> 
                        )}
                    </Form.Item>
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button type="primary" style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                            <Button type="danger">
                                提交
                            </Button>
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedInvestDrawer = Form.create<investDrawerProps>({ name: 'investDrawer' })(InvestDrawer);
export default WrappedInvestDrawer