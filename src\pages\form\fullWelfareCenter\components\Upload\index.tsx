import React, { forwardRef } from 'react';
import { Form } from 'antd';
import UploadImage from './UploadImg';

const UploadImageForm = props => {
  const { getFieldDecorator, fieldLocation, label, required } = props;

  return (
    <Form.Item label={label}>
      {getFieldDecorator(fieldLocation, {
        rules: required ? [{ required: true, message: '请上传图片' }] : [],
      })(<UploadImage />)}
    </Form.Item>
  );
};

export default UploadImageForm;
