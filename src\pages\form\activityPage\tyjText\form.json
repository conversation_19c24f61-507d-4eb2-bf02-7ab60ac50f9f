{"type": "object", "properties": {"activityId": {"title": "活动id", "type": "string", "description": "字母数字组合，与体验金配置的活动id要统一", "ui:width": "60%"}, "activityDescription": {"title": "活动描述", "type": "string", "ui:width": "40%"}, "intro": {"title": "产品介绍", "type": "string", "ui:width": "50%"}, "rule": {"title": "规则列表", "type": "array", "items": {"type": "object", "properties": {"value": {"type": "string"}}, "required": ["value"]}}}, "required": ["rule", "activityId"]}