import React from 'react';
import api from 'api';
import { Button, Input, Table, Radio, Form, Modal, message, Upload, Popconfirm, DatePicker } from 'antd';
import { autobind } from 'core-decorators';
import { FormComponentProps } from 'antd/es/form';
import styles from './index.less';
import { RadioChangeEvent } from 'antd/lib/radio/interface';

const { TextArea } = Input;
const { MonthPicker } = DatePicker;
const { fetchProductInfo, uploadProductExcel, updateProductInfos, operateFundRecommend, delProductInfo } = api;

const recommendLevel = ['一星', '二星', '三星', '四星', '五星'];

interface ProductEvaluationProps extends FormComponentProps {
    [propsName: string]: any
}
@autobind
class ProductEvaluation extends React.Component<ProductEvaluationProps, any> {
    constructor(props: ProductEvaluationProps) {
        super(props);
        this.state = {
            init: false,
            visible: false,
            dataFund: [],
            editFund: {},
            selectStyle: 'qy',
            selectDate: ''
        }
    }

    componentDidMount () {
        this.fetchProductInfo();
    }
    // 查询产品评价
    async fetchProductInfo() {
        try {
            const { selectDate, selectStyle } = this.state;
            let params = {
                type: selectStyle,
                monthDay: selectDate ? selectDate + '-01' : selectDate
            }
            const { status_code, data, status_msg } = await fetchProductInfo(params);
            if (status_code === 0) {
                let arr: any[] = [];
                data && data.forEach((item: any, index: number) => {
                    let obj = { ...item };
                    obj.num = index + 1;
                    obj.key = index;
                    obj.level = recommendLevel[obj.recommendLevel - 1];
                    obj.scale = obj.fundScale ? `${obj.fundScale}亿` : '--';
                    arr.push(obj);
                });
                this.setState({
                    init: true,
                    dataFund: arr,
                });
            } else {
                message.error(status_msg || '查询数据失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    // 保存产品评价
    async updateProductInfos(obj: any) {
        try {
            let data = {
                id: obj.id,
                recommendReason: obj.recommendReason
            };
            let params = {
                value: JSON.stringify(data)
            };
            const { status_code } = await updateProductInfos(params);
            if (status_code === 0) {
                message.success('保存成功');
                this.setState({
                    visible: false,
                });
                this.fetchProductInfo();
            } else {
                message.error('保存失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    // 推荐基金
    async operateFundRecommend(record: any) {
        try {
            const {id, type, fundCode, fundName, recommendReason, recommendLevel, remark } = record;
            let value = {
                id,
                fundCode,
                fundName,
                recommendReason,
                recommendLevel,
                remark,
                fundType: type
            }
            let params = {
                type: 'add',
                value: JSON.stringify(value)
            }
            const { status_code, status_msg } = await operateFundRecommend(params);
            if (status_code === 0) {
                message.success('推荐成功');
            } else {
                message.error(status_msg || '推荐失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    // 编辑
    handleEdit(record: any) {
        this.setState({
            visible: true,
            editFund: {...record}
        });
    }
    //保存
    handleSubmit() {
        let { editFund } = this.state;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                editFund = {...editFund, ...values};
                this.updateProductInfos(editFund);
            }
        });
    }
    handleCancel() {
        this.setState({
            visible: false
        });
    }
    // 上传excel
    async customRequest(options: any) {
        try {
            const formData = new FormData();
            formData.append('file', options.file);
            const { status_code } = await uploadProductExcel(formData);
            if (status_code === 0) {
                message.success('上传文件成功');
                this.fetchProductInfo();
            } else {
                message.error('上传文件失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    // 选择基金类型
    selectFund(e: RadioChangeEvent) {
        this.setState({
            selectStyle: e.target.value
        }, () => {
            this.fetchProductInfo()
        })
    }
    async handleDelete(record: any) {
        try {
            let data = {
                id: record.id,
            };
            let params = {
                value: JSON.stringify(data)
            }
            const { status_code } = await delProductInfo(params);
            if (status_code === 0) {
                message.success('删除成功');
                this.fetchProductInfo();
            } else {
                message.error('删除失败');
            }
        } catch(e) {
            console.log(e.message)
        }
    }
    dateChange(date: any, dateString: string) {
        this.setState({
            selectDate: dateString
        }, () => {
            this.fetchProductInfo();
        });
    }
    render() {
        const columns = [
            {
                title: '序号',
                dataIndex: 'num',
                key: 'num',
            },
            {
                title: '基金代码',
                dataIndex: 'fundCode',
                key: 'fundCode',
            },
            {
                title: '基金名称',
                dataIndex: 'fundName',
                key: 'fundName',
            },
            {
                title: '基金规模',
                dataIndex: 'scale',
                key: 'scale',
            },
            {
                title: '基金经理',
                dataIndex: 'fundManager',
                key: 'fundManager',
            },
            {
                title: '超额收益',
                dataIndex: 'aboveProfit',
                key: 'aboveProfit',
            },
            {
                title: '收益稳健',
                dataIndex: 'profitStable',
                key: 'profitStable',
            },
            {
                title: '风险管理',
                dataIndex: 'riskManager',
                key: 'riskManager',
            },
            {
                title: '投研团队',
                dataIndex: 'researchTeam',
                key: 'researchTeam',
            },
            {
                title: '推荐理由',
                dataIndex: 'recommendReason',
                key: 'recommendReason',
            },
            {
                title: '推荐评级',
                dataIndex: 'level',
                key: 'level',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (text: any, record: any) => <>
                    <Button type="primary" size="small" style={{marginRight: 10}} onClick={() => this.handleEdit(record)}>编辑</Button>
                    <Button type="primary" size="small" onClick={() => this.operateFundRecommend(record)}>推荐</Button>
                    <Popconfirm title="是否删除该基金？" onConfirm={() => this.handleDelete(record)}>
                      <Button type="primary" size="small">删除</Button>
                    </Popconfirm>
                </>
            },
        ];
        const { getFieldDecorator } = this.props.form;
        const formItemLayout = {
            labelCol: { span: 4 },
            wrapperCol: { span: 18 },
        };
        const { visible, dataFund, editFund, selectStyle } = this.state;

        if (!this.state.init) return '加载中';
        return (
            <div className={styles['product-evaluation']}>
                <header>
                    <div style={{position: 'absolute', top: 0, left: 0}}>
                        <Upload
                            accept=".xls,.xlsx"
                            customRequest={this.customRequest}
                            showUploadList={false}
                        >
                            <Button type="primary">Excel上传</Button>
                        </Upload>
                    </div>
                    <h1>产品评价</h1>
                </header>
                <section className={styles['f-center']}>
                    <Radio.Group value={selectStyle} buttonStyle="solid" onChange={this.selectFund}>
                        <Radio.Button value="qy">权益类基金</Radio.Button>
                        <Radio.Button value="cz">纯债类基金</Radio.Button>
                        <Radio.Button value="gs">固收+</Radio.Button>
                        <Radio.Button value="hy">行业主题</Radio.Button>
                        <Radio.Button value="fg">风格</Radio.Button>
                        <Radio.Button value="zs">指数</Radio.Button>
                    </Radio.Group>
                    <div>
                        <MonthPicker onChange={this.dateChange}/>
                    </div>
                </section>
                <Table dataSource={dataFund} columns={columns} />
                <div className={styles['recommend-rate']}>
                    <span>推荐评级：</span>
                    <span>五星=强烈推荐</span>
                    <span>四星=适当推荐</span>
                    <span>三星=谨慎推荐</span>
                    <span>二星，一星=不推荐</span>
                </div>
                <Modal
                    title="编辑推荐理由"
                    visible={visible}
                    footer={null}
                    onCancel={this.handleCancel}
                    wrapClassName="recommendation-wrapper"
                    destroyOnClose
                    >
                    <Form layout="horizontal" {...formItemLayout}>
                        <Form.Item label="推荐理由">
                            {getFieldDecorator("recommendReason", {
                                initialValue: editFund.recommendReason,
                            })(<TextArea style={{height: 150}}></TextArea>)}
                        </Form.Item>
                        <Form.Item wrapperCol={{span: 24}} style={{textAlign: 'center'}}>
                            <Button onClick={this.handleCancel} style={{marginRight: 20}}>取消</Button>
                            <Popconfirm title={'是否保存该基金？'} onConfirm={() => this.handleSubmit()}>
                                <Button type="primary">保存</Button>
                            </Popconfirm>
                        </Form.Item>
                    </Form>
                </Modal>
            </div>
        )
    }
}

export default Form.create({ name: 'productEvaluation' })(ProductEvaluation)