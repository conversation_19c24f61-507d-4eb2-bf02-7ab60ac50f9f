import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Table } from  'antd';
import './../types'

export default React.memo(function({
    isShow,
    tsh,
    setTsh,
    setIsShow,
    setIsShowAdd,
    modifiedData,
    setModifiedData,
    save
}: any) {

    function handleBlackList(record: TSH, index: number) {
        let delBlackThsList = [...modifiedData.delBlackThsList],
            addBlackThsList = [...modifiedData.addBlackThsList],
            _tsh: TSH[] = [...tsh],
            _modifiedData: temporaryData = {...modifiedData}
        const {
            id
        } = record
        if (record.isBlack) {
            _tsh[index].isBlack = false
            //先判断是否在添加黑名单内，若没有则在删除黑名单内增加
            if (~addBlackThsList.indexOf(id)) {
                addBlackThsList.splice(addBlackThsList.indexOf(id), 1)
            }
            delBlackThsList.push(id)
        } else {
            _tsh[index].isBlack = true
            if (~delBlackThsList.indexOf(id)) {
                delBlackThsList.splice(delBlackThsList.indexOf(id), 1)
            }
            addBlackThsList.push(id)
        }
        _modifiedData.addBlackThsList = addBlackThsList
        _modifiedData.delBlackThsList = delBlackThsList
        setTsh(_tsh)
        setModifiedData(_modifiedData)
        // save(_modifiedData)
    }

    return (
        <Modal 
        title="同顺号黑名单" 
        visible={isShow} 
        width={800}
        onCancel={() => {setIsShow(false)}}
        footer={
            <div className="u-r-middle">
                <Button type="danger" onClick={() => {
                    setIsShowAdd(true)}
                }>新增</Button>
                <Button type="primary" className="g-ml20" onClick={() => {
                    setIsShow(false)
                    save(modifiedData)
                }}>保存</Button>
            </div>
        }
        >
            <Table dataSource={tsh}>
                <Table.Column title="同顺号id" dataIndex={'id'} key="id" width={200}></Table.Column>
                <Table.Column title="同顺号名称" dataIndex={'name'} key="name" width={300}></Table.Column>
                <Table.Column title="操作" key="action" width={200} render={(text, record: TSH, index: number) => {
                    return (
                        <span key={index} style={{color: 'blue'}} onClick={() => {
                            handleBlackList(record, index)
                        }}>{record.isBlack ? '移除黑名单' : '重新添加回黑名单'}</span>
                    )
                }}></Table.Column>
            </Table>
        </Modal>
    )
})