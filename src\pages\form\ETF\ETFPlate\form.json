{"type": "object", "properties": {"notice": {"title": "小广播模块", "type": "object", "properties": {"show": {"title": "定时展示", "type": "string", "enum": ["true", "false"], "enumNames": ["定时展示", "不展示"], "widget": "radio"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}, "content": {"title": "广播内容", "type": "string", "props": {}, "maxLength": 20}, "contentMore": {"title": "备注", "type": "html", "default": "广播内容不超过20个字符，超出后轮播"}, "url": {"title": "配置链接", "type": "string", "props": {}}}}, "article": {"title": "推文模块", "type": "object", "properties": {"show": {"title": "定时展示", "type": "string", "enum": ["true", "false"], "enumNames": ["定时展示", "不展示"], "widget": "radio"}, "startTime": {"title": "开始时间", "type": "string", "format": "dateTime", "ui:width": "60%"}, "endTime": {"title": "结束时间", "type": "string", "format": "dateTime", "ui:width": "60%"}, "content": {"title": "内容标题", "type": "string", "props": {}}, "contentMore1": {"title": "备注", "type": "html", "default": "内容标题不限制字数，建议两行内，约34个字符"}, "contentIntroduce": {"title": "内容简介", "type": "string", "props": {}}, "contentMore2": {"title": "备注", "type": "html", "default": "内容简介最多显示两行，不超过43个字符，超出后显示省略号"}, "url": {"title": "配置链接", "type": "string", "props": {}}}}}, "labelWidth": 120, "displayType": "row"}