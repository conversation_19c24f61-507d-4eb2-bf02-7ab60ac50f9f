import React, { useState, useEffect } from 'react';
import api from 'api';
import { message, Button, Tag } from 'antd';
import FundConfig, { iFundConfigData } from './fundConfig';
import BannerConfig, { iBannerConfigData } from './bannerConfig';

const { fetchInvestContent, postInvestContent, fetchkycContent, postkycContent, synchroData } = api;

function InvestContent() {
  const [isModify, setModify] = useState(false);
  const [investData, setInvestData] = useState({});
  const [allData, setAllData] = useState({});
  const [oldData, setOldData] = useState({});
  const [fundConfigData, setFundConfigData] = useState<iFundConfigData[]>([]);
  const [bannerConfigData, setBannerConfigData] = useState<iBannerConfigData[]>([]);
  const [sync, setSync] = useState('-1');

  useEffect(() => {
    fetchkycContent().then((res: any) => {
        let { code, data } = res;
        if (code === '0000') {
            data = (data && JSON.parse(data)) ?? {};
            console.log('kycContent', data);
            setAllData(data);
            setOldData(data);
            setFundConfigData(data?.fundConfigData ?? []);
            setBannerConfigData(data?.bannerConfigData ?? []);
            setSync(data?.sync ?? '-1');
        } else {
            message.error(res.message || '系统繁忙');
        }
    }).catch((e: Error) => {
        message.error(e?.message || '系统繁忙');
    })
  }, [])

  useEffect(() => {
    fetchInvestContent().then((res: any) => {
      let { code, data } = res;
      if (code === '0000') {
          data = (data && JSON.parse(data)) ?? {};
          setInvestData(data);
      } else {
          message.error(res.message || '系统繁忙');
      }
    }).catch((e: Error) => {
        message.error(e?.message || '系统繁忙');
    })
  }, [])

  const handleFundConfigData = (data: iFundConfigData[]) => {
    setFundConfigData(data);
    let obj = {
      ...allData,
      fundConfigData: data
    }
    setAllData({...obj})
  }
  const handleBannerConfigData = (data: iBannerConfigData[]) => {
    setBannerConfigData(data);
    let obj = {
      ...allData,
      bannerConfigData: data
    }
    setAllData({...obj})
  }
  const overallSave = () => {
    for (let i = 0,len = bannerConfigData?.length; i < len; i++) {
      let item = bannerConfigData[i];
      if (item.functionType === 'kyc' && item.kycData) {
        const {
          xsdtargetText,
          xsdbtnTitle,
          zcxrtargetText,
          zcxrbtnTitle,
          zclrtargetText,
          zclrbtnTitle,
          zrztargetText,
          zrzbtnTitle,
          txztargetText,
          txzbtnTitle} = item.kycData;
          if (!xsdtargetText) {
            message.error(`请填写Banner位第${i+1}项学生党目标文案`);
            return;
          }
          if (!xsdbtnTitle) {
            message.error(`请填写Banner位第${i+1}项学生党按钮标题`);
            return;
          }
          if (!zcxrtargetText) {
            message.error(`请填写Banner位第${i+1}项职场新人目标文案`);
            return;
          }
          if (!zcxrbtnTitle) {
            message.error(`请填写Banner位第${i+1}项职场新人按钮标题`);
            return;
          }
          if (!zclrtargetText) {
            message.error(`请填写Banner位第${i+1}项职场老炮目标文案`);
            return;
          }
          if (!zclrbtnTitle) {
            message.error(`请填写Banner位第${i+1}项职场老炮按钮标题`);
            return;
          }
          if (!zrztargetText) {
            message.error(`请填写Banner位第${i+1}项责任族目标文案`);
            return;
          }
          if (!zrzbtnTitle) {
            message.error(`请填写Banner位第${i+1}项责任族按钮标题`);
            return;
          }
          if (!txztargetText) {
            message.error(`请填写Banner位第${i+1}项退休族目标文案`);
            return;
          }
          if (!txzbtnTitle) {
            message.error(`请填写Banner位第${i+1}项退休族按钮标题`);
            return;
          }
          
      } else if (item.functionType === 'jjtj' && item.fundData){
        const { cardTitle, contentTitle, btnText, jumpUrl, checkType,
          fundText, productId, productName, rate } = item.fundData;
        if (!cardTitle) {
          message.error(`请填写Banner位第${i+1}项卡片标题`);
          return;
        } 
        if(!contentTitle) {
          message.error(`请填写Banner位第${i+1}项内容主题`);
          return;
        } 
        if (checkType) {
          if (checkType === 'text') {
            if (!fundText) {
              message.error(`请填写Banner位第${i+1}项亮点文案`);
              return;
            }
            let arr = fundText.split('|');
            for (let i = 0, len = arr?.length; i < len; i++) {
              if (arr[i]?.length > 5) {
                message.error(`Banner位第${i+1}项文案每个限制5个字`);
                return;
              }
            }
          } else {
            if (!productId) {
              message.error(`请填写Banner位第${i+1}项产品ID`);
              return;
            } else if(!productName) {
              message.error(`请填写Banner位第${i+1}项产品名称`);
              return;
            } if (rate === null || rate === undefined) {
              message.error(`请填写Banner位第${i+1}项收益率`);
              return;
            }
          }
        } 
        if(!btnText) {
          message.error(`请填写Banner位第${i+1}项按钮文案`);
          return;
        } 
        if (!jumpUrl) {
          message.error(`请填写Banner位第${i+1}项跳转链接`);
          return;
        } 
      }
    }
    for (let i = 0,len = fundConfigData?.length; i < len; i++) {
      let item = fundConfigData[i];
      if (!item.rankType) {
        message.error(`请填写适合定投的基金第${i+1}项项榜单类型`)
        return;
      }
    }
    let obj: any = {...allData};
    obj.sync = '0';
    postkycContent({
      value: JSON.stringify(obj),
    }).then((res: any) => {
      if (res.code !== '0000') {
          message.error(res.message || '系统繁忙');
      } else {
          message.success('保存成功！');
          setTimeout(()=>location.reload(),1000)
      }
    }).catch((e: Error) => {
        message.error(e?.message || '系统繁忙');
    }) 
  }
  const getSynchroData = () => {
    synchroData().then((res: any) => {
      if (res.status_code !== 0) {
          message.error(res.status_msg || '系统繁忙');
      } else {
          message.success('同步成功！');
          let obj: any = {...oldData};
          obj.sync = '1';

          let obj1: any = {...investData};
          if (JSON.stringify(obj1) === "{}") {
            return;
          }
          obj1.sync = '1';
          let promise1 = postkycContent({
            value: JSON.stringify(obj)
          })
          let promise2 = postInvestContent({
            value: JSON.stringify(obj1)
          })
          Promise.all([promise1, promise2]).then((res) => {
            if (res[0]?.code !== '0000' || res[1]?.code !== '0000' ) {
              message.error(res[0]?.message || '系统繁忙');
              return;
            }
            location.reload();
          }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
          })
      }
    }).catch((e: Error) => {
        message.error(e?.message || '系统繁忙');
    })
  }
  const handleModify = (flag: boolean) => {
    setModify(flag)
  }
  return (
    <div style={{ padding: 40 }}>
      <div style={{marginTop: 20}}>
        { sync === '0' ? <Tag color="red" style={{marginRight: 20}}>已修改，未同步</Tag> : sync === '1' ? <Tag color="green" style={{marginRight: 20}}>已同步</Tag> : null }   
          <Button type="primary" style={{marginRight: 20}} onClick={overallSave} disabled={!isModify}>保存</Button>
          <Button type="primary" onClick={getSynchroData} disabled={sync !== '0'}>同步</Button>
      </div>
      <BannerConfig isModify={isModify} handleModify={handleModify} bannerConfigData={bannerConfigData} handleData={handleBannerConfigData}/>
      <FundConfig isModify={isModify} handleModify={handleModify} fundConfigData={fundConfigData} handleData={handleFundConfigData}/>  
    </div>
  )
}

export default InvestContent;