import React, { useState, useEffect } from "react";
import FormRender from 'form-render/lib/antd';
import { connect } from 'dva'
//import axios from 'axios'
import api from 'api';
import { Button, message } from 'antd'

const {fetchRegisterr, postRegisterr} = api;

let FORM_CONFIG: any = {
    propsSchema: {
        type: 'object',
        properties: {
            "ruleList": {
                "title": "绑定银行卡错误信息匹配 ",
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "key": {
                        title: '错误信息',
                        type: 'string',
                        maxLength: 999,
                        "ui:width": "45%",
                    },
                    "value": {
                        title: '显示信息',
                        type: 'string',
                        maxLength: 999,
                        "ui:width": "45%",
                    }
                  }
                }
            }
        },
        required: ['ruleList']
    }
};

function TegistErr (props: any) {
    const canOpc = props.limits && props.limits.includes('crmform:errtxtopc');
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState({});
    const [formData, setData]: any = useState({});
    const [valid, setValid] = useState([]);
    
    useEffect(() => {
        fetchRegisterr().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res) {
                    let ruleList = [];
                    for(let i in res) {
                        ruleList.push({
                            key: i,
                            value: res[i]
                        });
                    }
                    FORM_CONFIG.formData = {
                        ruleList
                    };
                }
            } catch (e) {
                console.warn(e)
            }
            
            setInit(true);
            setFormConfig(FORM_CONFIG);
            setData(FORM_CONFIG.formData);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [init]);

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            console.log(formData);
            let _postData: any = {};
            formData.ruleList.map((item: any) => {
                _postData[item.key] = item.value;
            });

            postRegisterr({
                value: JSON.stringify(_postData)
            }).then((res: any) => {
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('发布成功！')
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        }
    };

    //if (!init) return ('加载中')
    return (
        <div style={{ padding: 60 }}>
        {
            init && (<FormRender
                propsSchema={FORM_CONFIG.propsSchema}
                formData={formData}
                onChange={setData}
                onValidate={setValid}
                showDescIcon={true}
            />)
        }
        {canOpc && <Button type="primary" onClick={onSubmit}>提交</Button>}
        </div>
    );
}


export default connect(({app, permission}: any) => ({
    limits: permission.limits
}))(TegistErr);

