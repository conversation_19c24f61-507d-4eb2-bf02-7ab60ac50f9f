{"type": "page", "title": "高端自选配置", "body": [{"type": "form", "id": "u:f8898ddccbfc", "title": "表单", "mode": "horizontal", "dsType": "api", "body": [{"type": "tpl", "tpl": "（一）卡片配置（用户无高端自选）", "inline": true, "wrapperComponent": "h1", "id": "u:35c9b938c7c2"}, {"type": "radios", "label": "配置方式", "name": "introCard.selectedOption", "options": [{"label": "默认配置", "value": 0}, {"label": "手动配置", "value": 1}], "id": "u:9ea11c8ae95e", "value": 0, "required": true, "selectFirst": true, "size": "lg"}, {"type": "flex", "id": "u:9f0e63f8f987", "className": "p-1", "visibleOn": "${introCard.selectedOption === 0}", "items": [{"type": "input-text", "label": "卡片标题", "name": "introCard.defaultConfig.title", "id": "u:22f691bbf682", "required": true, "value": "高端理财", "clearable": true, "maxLength": 15, "size": "lg", "placeholder": "请输入卡片标题(限15个字符)"}, {"type": "container", "body": [{"type": "grid", "columns": [{"body": [{"type": "input-text", "label": "文案", "name": "introCard.defaultConfig.line1LeftText", "id": "u:c5df5f523439", "maxLength": 10, "clearable": true, "value": "起购门槛至少30w", "required": true, "size": "lg", "placeholder": "第一行标题1(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.defaultConfig.line1LeftAction", "clearable": true, "value": "", "id": "u:e35f7bac1caea", "size": "lg", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:4aee00dbf8c1", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"marginBottom": "var(--sizes-size-7)"}}}}, {"body": [{"type": "input-text", "label": "文案", "name": "introCard.defaultConfig.line1RightText", "maxLength": 10, "clearable": true, "value": "资管计划、专户、私募", "required": true, "size": "lg", "id": "u:27c1fefc8f3c", "placeholder": "第一行标题2(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.defaultConfig.line1RightAction", "clearable": true, "value": "", "size": "lg", "id": "u:cf5cf9a3f8e6", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:8de9415ad43b"}], "id": "u:2f4b6ae0ec90"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:ce7ac22551a2"}, {"type": "container", "body": [{"type": "grid", "columns": [{"body": [{"type": "input-text", "label": "文案", "name": "introCard.defaultConfig.line2LeftText", "id": "u:c5df5f523439", "maxLength": 10, "clearable": true, "value": "核心的投研能力", "required": true, "size": "lg", "placeholder": "第二行标题1(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.defaultConfig.line2LeftAction", "clearable": true, "value": "", "id": "u:e35f7bac1caea", "size": "lg", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:4aee00dbf8c1"}, {"body": [{"type": "input-text", "label": "文案", "name": "introCard.defaultConfig.line2RightText", "maxLength": 10, "clearable": true, "value": "有效的投资策略", "required": true, "size": "lg", "id": "u:5313ba357a27", "placeholder": "第二行标题2(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.defaultConfig.line2RightAction", "clearable": true, "value": "", "size": "lg", "id": "u:aa79ca603032", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:8de9415ad43b"}], "id": "u:2f4b6ae0ec90"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:031365356c48"}, {"type": "container", "body": [{"type": "grid", "columns": [{"body": [{"type": "input-text", "label": "文案", "name": "introCard.defaultConfig.line3LeftText", "id": "u:c5df5f523439", "maxLength": 10, "clearable": true, "value": "起购门槛至少30w", "required": true, "size": "lg", "placeholder": "第三行标题1(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.defaultConfig.line3LeftAction", "clearable": true, "value": "", "id": "u:e35f7bac1caea", "size": "lg", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:4aee00dbf8c1"}, {"body": [{"type": "input-text", "label": "文案", "name": "introCard.defaultConfig.line3RightText", "maxLength": 10, "clearable": true, "value": "资管计划、专户、私募", "required": true, "size": "lg", "id": "u:fefdd107b6a8", "placeholder": "第三行标题2(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.defaultConfig.line3RightAction", "clearable": true, "value": "", "size": "lg", "id": "u:ce2ed843554a", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:8de9415ad43b"}], "id": "u:2f4b6ae0ec90"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:5c49739b1e01"}], "style": {"position": "relative", "inset": "auto", "flexWrap": "nowrap", "flexDirection": "column", "justifyContent": "center", "alignItems": "stretch"}, "isFixedHeight": false, "isFixedWidth": false}, {"type": "flex", "className": "p-1", "items": [{"type": "input-text", "size": "lg", "id": "u:22f691bbf682", "label": "卡片标题", "name": "introCard.customConfig.title", "required": true, "clearable": true, "maxLength": 15, "placeholder": "请输入卡片标题(限15个字符)"}, {"type": "container", "body": [{"type": "input-date-range", "id": "u:b5cd9a765a6c", "label": "生效时间", "name": "introCard.customConfig.startTime", "value": "", "displayFormat": "YYYY-MM-DD", "format": "YYYY-MM-DD", "required": true}, {"type": "grid", "columns": [{"body": [{"type": "input-text", "label": "文案", "name": "introCard.customConfig.line1LeftText", "id": "u:c5df5f523439", "maxLength": 10, "clearable": true, "required": true, "size": "lg", "placeholder": "第一行标题1(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.customConfig.line1LeftAction", "clearable": true, "value": "", "id": "u:e35f7bac1caea", "size": "lg", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:4aee00dbf8c1", "themeCss": {"baseControlClassName": {"padding-and-margin:default": {"marginBottom": "var(--sizes-size-7)"}}}}, {"body": [{"type": "input-text", "label": "文案", "name": "introCard.customConfig.line1RightText", "maxLength": 10, "clearable": true, "required": true, "size": "lg", "id": "u:27c1fefc8f3c", "placeholder": "第一行标题2(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.customConfig.line1RightAction", "clearable": true, "value": "", "size": "lg", "id": "u:cf5cf9a3f8e6", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:8de9415ad43b"}], "id": "u:2f4b6ae0ec90"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:ce7ac22551a2"}, {"type": "container", "body": [{"type": "grid", "columns": [{"body": [{"type": "input-text", "label": "文案", "name": "introCard.customConfig.line2LeftText", "id": "u:c5df5f523439", "maxLength": 10, "clearable": true, "required": true, "size": "lg", "placeholder": "第二行标题1(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.customConfig.line2LeftAction", "clearable": true, "value": "", "id": "u:e35f7bac1caea", "size": "lg", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:4aee00dbf8c1"}, {"body": [{"type": "input-text", "label": "文案", "name": "introCard.customConfig.line2RightText", "maxLength": 10, "clearable": true, "required": true, "size": "lg", "id": "u:5313ba357a27", "placeholder": "第二行标题2(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.customConfig.line2RightAction", "clearable": true, "value": "", "size": "lg", "id": "u:aa79ca603032", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:8de9415ad43b"}], "id": "u:2f4b6ae0ec90"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:031365356c48"}, {"type": "container", "body": [{"type": "grid", "columns": [{"body": [{"type": "input-text", "label": "文案", "name": "introCard.customConfig.line3LeftText", "id": "u:c5df5f523439", "maxLength": 10, "clearable": true, "required": true, "size": "lg", "placeholder": "第三行标题1(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.customConfig.line3LeftAction", "clearable": true, "value": "", "id": "u:e35f7bac1caea", "size": "lg", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:4aee00dbf8c1"}, {"body": [{"type": "input-text", "label": "文案", "name": "introCard.customConfig.line3RightText", "maxLength": 10, "clearable": true, "required": true, "size": "lg", "id": "u:fefdd107b6a8", "placeholder": "第三行标题2(限10个字符)"}, {"type": "input-text", "label": "跳转链接", "name": "introCard.customConfig.line3RightAction", "clearable": true, "value": "", "size": "lg", "id": "u:ce2ed843554a", "placeholder": "请输入跳转链接", "trimContents": true}], "id": "u:8de9415ad43b"}], "id": "u:2f4b6ae0ec90"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:5c49739b1e01"}], "style": {"position": "relative", "inset": "auto", "flexWrap": "nowrap", "flexDirection": "column", "justifyContent": "center", "alignItems": "stretch"}, "id": "u:9f0e63f8f987", "isFixedHeight": false, "isFixedWidth": false, "visibleOn": "${introCard.selectedOption === 1}"}, {"type": "tpl", "tpl": "（二）高端自选推荐产品配置（用户无高端自选）", "inline": true, "wrapperComponent": "h1", "id": "u:3b61ec975c20"}, {"type": "radios", "label": "更新方式", "required": true, "name": "recommendProductList.selectedOption", "options": [{"label": "自动更新", "value": 0}, {"label": "手动更新", "value": 1}], "id": "u:e3d9eac408af", "value": 0, "selectFirst": false}, {"type": "container", "body": [{"type": "input-text", "label": "自选tab页面底部标签文案", "name": "recommendProductList.tabTip", "id": "u:ad723984ee32", "size": "lg", "required": true, "maxLength": 4, "placeholder": "请填写标题文案（限2-4个字符）", "minLength": 2, "visibleOn": ""}], "style": {"position": "static", "display": "block"}, "size": "none", "wrapperBody": false, "id": "u:c9f901869da1", "isFixedHeight": false, "isFixedWidth": false, "visibleOn": "${recommendProductList.selectedOption === 0}"}, {"type": "container", "body": [{"type": "input-date-range", "label": "生效时间", "name": "recommendProductList.startTime", "id": "u:87a5aae2ccc4", "required": true, "value": "", "displayFormat": "YYYY-MM-DD", "format": "YYYY-MM-DD"}, {"type": "input-text", "label": "自选tab页面底部标签文案", "name": "recommendProductList.tabTip", "id": "u:e11a2953cc52", "size": "lg", "required": true, "maxLength": 4, "placeholder": "请填写标题文案（限2-4个字符）", "minLength": 2, "visible": true}, {"type": "combo", "id": "u:f12e58304153", "label": "产品列表", "maxLength": 5, "addable": true, "removable": true, "multiple": true, "minLength": 3, "name": "recommendProductList.products", "validateOnChange": false, "validateApi": {"url": "${fuyao<PERSON><PERSON>}", "method": "post", "dataType": "form", "data": {"codeList": "${recommendProductList.products}", "temp": "${recommendProductList.products}"}, "adaptor": "let status = 0; const products = api.body.temp; let errorsCode = [] ; const errList = []; const data = payload.data; for (let key in data) {  if (!data[key].simpleName) {    errList.push({      marketId: key.split(':')[0],      fundCode: key.split(':')[1]    })  }}; errList.forEach(item => {  errorsCode.push(item.fundCode) }); if (errorsCode.length > 0) status = 422; return {  errors: `以下产品代码存在错误：[${errorsCode.join(',')}]`, status};", "requestAdaptor": "const list = api.body.codeList; const arr = []; list.forEach(item => { arr.push(`${item.marketId}:${item.fundCode}`) }); api.data.codeList = arr.join(','); return api;"}, "items": [{"type": "select", "name": "marketId", "label": "市场号", "options": [{"label": "专户", "value": "U112"}, {"label": "小集合", "value": "U113"}, {"label": "私募", "value": "U111"}], "id": "u:1d6d456e40a1", "value": "U112", "required": true}, {"type": "input-text", "label": "产品代码", "id": "u:04c1c7acd92f", "name": "fundCode", "required": true, "trimContents": true}], "strictMode": true, "syncFields": []}], "style": {"position": "static", "display": "block"}, "size": "none", "wrapperBody": false, "id": "u:c85b1ed64a87", "isFixedHeight": false, "isFixedWidth": false, "visibleOn": "${recommendProductList.selectedOption === 1}"}, {"type": "tpl", "tpl": "（三）高端自选单品推荐配置（用户有高端自选）", "inline": true, "wrapperComponent": "h1", "id": "u:fd827522364b"}, {"type": "radios", "label": "更新方式", "name": "recommendProductCard.selectedOption", "options": [{"label": "自动更新", "value": 0}, {"label": "手动更新", "value": 1}], "id": "u:661a224aab41", "value": 0, "selectFirst": false, "required": true}, {"type": "input-text", "label": "自选tab页面底部标签文案", "name": "recommendProductCard.tabTip", "id": "u:ad723984ee32", "size": "lg", "required": true, "maxLength": 4, "placeholder": "请填写标题文案（限2-4个字符）", "minLength": 2, "visibleOn": ""}, {"type": "container", "body": [{"type": "combo", "id": "u:71238daa5fc9", "label": "产品列表", "name": "recommendProductCard.products", "multiple": true, "validateOnChange": false, "validateApi": {"url": "${fuyao<PERSON><PERSON>}", "method": "post", "dataType": "form", "data": {"codeList": "${recommendProductCard.products}", "temp": "${recommendProductCard.products}"}, "adaptor": "let status = 0; const products = api.body.temp; let errorsCode = [] ; const errList = []; const data = payload.data; for (let key in data) {  if (!data[key].simpleName) {    errList.push({      marketId: key.split(':')[0],      fundCode: key.split(':')[1]    })  }}; errList.forEach(item => {  errorsCode.push(item.fundCode) }); if (errorsCode.length > 0) status = 422; return {  errors: `以下产品代码存在错误：[${errorsCode.join(',')}]`, status};", "requestAdaptor": "const list = api.body.codeList; const arr = []; list.forEach(item => { arr.push(`${item.marketId}:${item.fundCode}`) }); api.data.codeList = arr.join(','); return api;"}, "items": [{"type": "select", "name": "marketId", "options": [{"label": "专户", "value": "U112"}, {"label": "小集合", "value": "U113"}, {"label": "私募", "value": "U111"}], "id": "u:5205216969d4", "required": true}, {"type": "input-text", "label": "产品代码", "id": "u:1250d1a25315", "name": "fundCode", "required": true, "trimContents": true}], "strictMode": true, "maxLength": 1, "minLength": 1, "syncFields": []}, {"type": "input-date-range", "label": "生效时间", "name": "recommendProductCard.startTime", "id": "u:e1673bee42bc", "required": true, "value": "", "displayFormat": "YYYY-MM-DD", "format": "YYYY-MM-DD"}], "style": {"position": "static", "display": "block"}, "size": "none", "wrapperBody": false, "id": "u:4707b42ca1f1", "visibleOn": "${recommendProductCard.selectedOption === 1}"}], "actions": [{"type": "button", "label": "提交", "onEvent": {"click": {"actions": [{"actionType": "submit", "componentId": "u:f8898ddccbfc"}]}}, "level": "primary", "id": "u:55e2fa979d0f"}], "initApi": {"method": "get", "url": "/common_config/hash_data_get?key=highFinance&propName=optionalConfig", "adaptor": "const data = payload.data ? JSON.parse(payload.data) : {};\r\nreturn {...data};"}, "api": {"method": "post", "url": "/common_config/hash_data_save", "dataType": "form", "requestAdaptor": "api.data.value = JSON.stringify(api.data.value);\r\nreturn api;", "adaptor": "response.status = payload.code === '0000' ? 0 : -1; return response", "data": {"key": "highFinance", "propName": "optionalConfig", "value": {"introCard": "${introCard}", "recommendProductList": "${recommendProductList}", "recommendProductCard": "${recommendProductCard}"}}}, "debug": true, "feat": "View", "resetAfterSubmit": true}], "id": "u:5a67e570b8d3", "asideResizor": false, "pullRefresh": {"disabled": true}}