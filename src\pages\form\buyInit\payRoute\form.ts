export const abTestForm = {
  type: 'object',
  properties: {
    applicationType: {
      title: "应用类型",
      type: "array",
      "ui:labelWidth": 110,
      items: {
        type: "string"
      },
      enum: [1, 2, 3],
      enumNames: ["APP", "SDK-普通版", "SDK-至尊版"]
    },
    systemSelection: {
      title: "系统选择",
      type: "array",
      enum: ["ios", "android"],
      items: {
        type: "string"
      },
      "ui:labelWidth": 110,
      enumNames: ['iOS端', 'Android端'],
    },
    "mode": {
      "title": "开关",
      "type": "string",
      "enum": ["1", "2", "0"],
      "enumNames": ["全量开", "匹配规则", "全量关"],
      "ui:labelWidth": 110,
      "ui:widget": "select",
      "ui:width": "20%",
    },
    "contentRules": {
      "title": "custId匹配规则内容",
      "type": "string",
      "ui:hidden": "{{formData.mode !== '2'}}",
      // "ui:labelWidth": 110,
      "ui:width": "50%",
      "ui:options": {
        "placeholder": "请使用英文字符输入，多个以“,”分隔"
      },
      "pattern": "^[A-Za-z0-9]+([,][A-Za-z0-9]+)*$",
      "message": {
        "pattern": "请使用英文字符输入，多个以“,”分隔"
      }
    },
  },
  "required": [
    "applicationType",
    "systemSelection",
    "mode"
  ]
}
