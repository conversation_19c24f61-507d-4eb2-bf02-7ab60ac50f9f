/**
 * 黄金宝 - 任务配置
 */
import React, { useState, useRef, useEffect } from 'react';
import React from 'react';
import styles from './index.less';
import { Spin, Button, notification, Modal } from 'antd';
import api from 'api';
import TaskList from './TaskList';
import TaskForm from './TaskForm';
import { TASK_TYPE, REWARD_TYPE, TASK_CYCLE } from '../const';

/** 生成简易 uid */
const uid = () =>
  Date.now().toString(36) +
  Math.random()
    .toString(36)
    .substr(2);

const createNewTask = (sort_key: number) => {
  return {
    _id: uid(),
    _create_at: Date.now(),
    _update_at: Date.now(),
    _is_deleted: false,
    sort_key: String(sort_key),
    name: null,
    type: String(TASK_TYPE.SIGN_IN),
    condition_time: null,
    condition_amount: null,
    is_cycle_task: true,
    cycle: String(TASK_CYCLE.DAY),
    times_in_cycle: String(1),
    is_super_task: false,
    reward_type: String(REWARD_TYPE.FIXED_REWARD),
    reward_beans: null,
    reward_every_full: null,
    reward_grams: null,
    link: null,
    share_text: null,
    share_title: null,
    share_image: null,
    share_link: null,
  };
};

const tempDataSet = Array(50).fill(1).map((item, index) => {
  return createNewTask(index);
});

//
const TaskConfig: React.FunctionComponent = () => {
  //
  const [tasks, setTasks] = useState<[]>(null);
  const hasFirstPurchaseTask = !!(tasks || []).find(task => task.type == TASK_TYPE.FIRST_PURCHASE);
  const hasHighTask = !!(tasks || []).find(task => task.type == TASK_TYPE.HIGH_END_FINANCIAL_MANAGEMENT);
  const hasSuperTask = !!(tasks || []).find(task => task.is_super_task);

  const handleReorderTasks = (fromIndex: number, toIndex: number) => {
    const updated = tasks.slice();
    const removed = updated.splice(fromIndex, 1);

    removed.forEach(item => {
      item._update_at = Date.now();
    });

    updated.splice(toIndex, 0, ...removed);
    updated.forEach((item, index) => {
      item.sort_key = String(index);
    });

    setTasks(updated);

    handleSubmit(updated);
  }

  const [editing, setEditing] = useState(null);

  const handleEdit = (id: string) => {
    let item;
    if (!id || !(item = tasks.find(task => task._id === id))) {
      setEditing(createNewTask(tasks.length));
    } else {
      setEditing({ ...item, _update_at: Date.now() });
    }
  }

  const handleEditComplete = (data: { [k: string]: unknown }) => {
    const updated = tasks.slice();
    const index = updated.findIndex(task => task._id === data._id);

    // * 处理一下字段的类型
    for (let [key, value] of Object.entries(data)) {
      if (['_create_at', '_update_at'].includes(key)) {
        data[key] = Number(value);
      }
      if (!['_create_at', '_update_at'].includes(key) && value !== null && typeof value !== 'boolean') {
        data[key] = String(value);
      }
    }

    if (~index) {
      updated.splice(index, 1, data);
    } else {
      updated.push(data);
    }

    setTasks(updated);
    setEditing(null);

    handleSubmit(updated);
  }

  const [toggleModal, setToggleModal] = useState([]);

  const handleToggle = (ids: string[]) => {
    if (toggleModal.length === 0) {
      setToggleModal(ids);
      return;
    }
    ids = toggleModal;

    const updated = tasks.map((task, index) => {
      if (ids.includes(task._id)) {
        return { ...task, _is_deleted: !task._is_deleted, _update_at: Date.now() };
      }
      return task;
    });

    setTasks(updated);
    setToggleModal([]);

    handleSubmit(updated);
  }

  const handleDelete = (id: string) => {
    const updated = tasks.slice();
    const index = updated.findIndex(task => task._id === id);

    if (~index) {
      updated.splice(index, 1);
    } else {
      return;
    }

    setTasks(updated);

    handleSubmit(updated);
  }

  const [submitting, setSubmitting] = useState(false);

  /**
   * 保存配置
   */
  const handleSubmit = async (data: typeof tasks) => {
    notification.info({ key: 'postLoading', message: '正在保存配置...', duration: null });
    setSubmitting(true);
    const response = await api.postHjbTasksConfig({ value: JSON.stringify(data || tasks) });
    setSubmitting(false);

    if (response?.code == '0000') {
      notification.success({ key: 'postLoading', message: '配置保存成功！', });

      return;
    }

    notification.error({ key: 'postLoading', message: `配置保存失败！${response?.message}`, });
  };

  const [resetModal, setResetModal] = useState(false);

  const [syncing, setSyncing] = useState();

  const handleSync = async () => {
    //
    notification.info({ key: 'syncing', message: '正在将配置同步到后端服务器...', duration: null });
    setSyncing(true);
    const response = await api.fetchHjbConfigSync();
    setSyncing(false);

    if (response?.status_code == 0) {
      notification.success({ key: 'syncing', message: '配置同步到后端服务器成功！' });
      return;
    }

    notification.error({ key: 'syncing', message: `配置同步到后端服务器失败！${ response?.status_msg }`, });
  };

  useEffect(() => {
    async function fetch () {
      notification.info({ key: 'fetchLoading', message: '正在拉取配置数据...', duration: null });
      const response = await api.fetchHjbTasksConfig();

      if (response?.code == '0000') {
        try {
          const data = JSON.parse(response?.data || '[]');

          notification.success({ key: 'fetchLoading', message: '拉取配置数据成功！' });

          setTasks(data);
        } catch {
          notification.warning({ key: 'fetchLoading', message: '拉取配置数据成功！但数据似乎有误...' });

          setTasks([]);
        }

        return;
      }

      notification.warning({ key: 'fetchLoading', message: '拉取配置数据失败！' });

      setTasks([]);
    }

    fetch();
  }, []);

  //
  if (!tasks) return <Spin />;

  // prettier-ignore
  return (<>
    <div className={ styles['tasks-config'] }>
      {
        !editing &&
        <TaskList
          tasks={ tasks }
          onReorderTasks={ handleReorderTasks }
          submitting={ submitting }
          onToggle={ handleToggle }
          onDelete={ handleDelete }
          onEdit={ handleEdit }
          onSync={ handleSync }
          syncing={ syncing }
        />
      }
      {
        editing &&
        <TaskForm
          data={ editing }
          onBack={ () => setEditing(null) }
          onSubmit={ handleEditComplete }
          hasFirstPurchaseTask={ hasFirstPurchaseTask }
          hasHighTask={ hasHighTask }
          hasSuperTask={ hasSuperTask }
        />
      }
    </div>
    <Modal
      visible={ toggleModal.length !== 0 }
      title="任务上下线确认"
      onOk={ handleToggle }
      okText="确认"
      onCancel={ () => setToggleModal([]) }
      cancelText="取消"
    >
      确定要上线/下线该配置吗？
    </Modal>
  </>)
};

export default TaskConfig;
