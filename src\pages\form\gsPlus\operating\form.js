const REDPAPER_CONFIG = {
	"type": "object",
	"required": ["title", "subTitle", "btnText", "jumpUrl", "startDate", "endDate", "platform", "user"],
	"properties": {
		"title": {
			"title": "标题文案",
			"type": "string",
		},
		"subTitle": {
			"title": "副标题文案",
			"type": "string",
		},
		"btnText": {
			"title": "按钮文案",
			"type": "string",
			"maxLength": 4
		},
		"jumpUrl": {
			"title": "红包链接",
			"type": "string",
			"pattern": "^https://[^\n ，]*$"
		},
		"startDate": {
            "title": "生效时间",
            "type": "string",
            "format": "dateTime"
        },
		"endDate": {
            "title": "失效时间",
            "type": "string",
            "format": "dateTime"
        },
		"platform": {
			"title": "适用平台",
			"type": "array",
			"items": {
				"type": "string"
			},
			"ui:widget": "checkboxes",
			"enum": [
				"and",
				"ios",
				"andsdk",
				"iossdk",
				"iossdkvip",
				"andsdkvip"
			],
			"enumNames": [
				"App-安卓",
				"App-iOS",
				"SDK-安卓",
				"SDK-iOS",
				"ios至尊",
				"安卓至尊"
			]
		},
		"user": {
			"title": "用户类型",
			"type": "array",
			"items": {
				"type": "string"
			},
			"ui:widget": "checkboxes",
			"enum": [
				"u0",
				"u1",
				"u2",
				"u3",
				"u4",
				"u5",
				"u6",
				"u7",
				"u8",
				"u9",
				"F"
			],
			"enumNames": [
				"默认",
				"新手用户",
				"次新用户",
				"休眠新用户",
				"流失新用户",
				"成长用户",
				"成熟用户",
				"高净值用户",
				"休眠用户",
				"流失用户",
				"F类审核用户"
			]
		}
	}
}

const BANNER_CONFIG = {
	"type": "object",
	"required": ["imgUrl", "jumpUrl", "startDate", "endDate", "platform", "user"],
	"properties": {
		"imgUrl": {
			"title": "背景图片",
			"type": "string",
			"ui:widget": "uploadImg"
		},
		"jumpUrl": {
			"title": "跳转链接",
			"type": "string",
			"pattern": "^https://[^\n ，]*$"
		},
		"startDate": {
            "title": "生效时间",
            "type": "string",
            "format": "dateTime"
        },
		"endDate": {
            "title": "失效时间",
            "type": "string",
            "format": "dateTime"
        },
		"platform": {
			"title": "适用平台",
			"type": "array",
			"items": {
				"type": "string"
			},
			"ui:widget": "checkboxes",
			"enum": [
				"and",
				"ios",
				"andsdk",
				"iossdk",
				"iossdkvip",
				"andsdkvip"
			],
			"enumNames": [
				"App-安卓",
				"App-iOS",
				"SDK-安卓",
				"SDK-iOS",
				"ios至尊",
				"安卓至尊"
			]
		},
		"user": {
			"title": "用户类型",
			"type": "array",
			"items": {
				"type": "string"
			},
			"ui:widget": "checkboxes",
			"enum": [
				"u0",
				"u1",
				"u2",
				"u3",
				"u4",
				"u5",
				"u6",
				"u7",
				"u8",
				"u9",
				"F"
			],
			"enumNames": [
				"默认",
				"新手用户",
				"次新用户",
				"休眠新用户",
				"流失新用户",
				"成长用户",
				"成熟用户",
				"高净值用户",
				"休眠用户",
				"流失用户",
				"F类审核用户"
			]
		}
	}
}

export { REDPAPER_CONFIG, BANNER_CONFIG }