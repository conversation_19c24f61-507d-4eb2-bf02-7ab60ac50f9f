# 黄金宝配置平台接口文档

## 基础配置

采用通用配置接口。

- Key

```
hjbBaseConfig
```

- 响应结果（Response）

响应结果是个 JSON 字符串，需要首先反序列化。

```json
{
    // 黄金基金
    "gold_fund": [
        {
          // 基金代码
          "code": "002611",
          // 要显示的名称文本
          "text": "博时黄金",
          // 换算系数
          "conversion_factor": 285
        }
    ],
    // 活动相关
    "activity": {
        // 活动状态 "offline" | "online"
        "status": "offline",
        // 黄金豆提取门槛
        "golden_bean_extraction_threshold": "1000",
        // 黄金豆提取规则
        "golden_bean_extraction_rule": "",
        // 黄金克数有效时间(小时)
        "gold_valid_time": "72",
        // 首购红包 id
        "first_buy_red_envelope_id": "15975364862",
        // 活动规则
        "rule": ""
    },
    // 首购推荐基金
    "fund": [
        {
          // 首购推荐基金代码
          "fundCode": "002611",
          // 首购推荐基金话术
          "text": "测试",
          // 首购推荐基金标签
          "label": "测试",
          "_id": "kwbw0gb0tdwmiys7pj"
        }
    ],
    // banner 信息
    "banner": [
        {
            // banner 名称
            "name": "测试",
            // banner 链接
            "link": "测试",
            // banner 图片
            "image": "https://trade.5ifund.com:8443/testingimgsrc/fund/e6dbed409e7d4a05a1e764c0070b4181.png",
            "_id": "kwbw5w6d1lg6a1fn89i"
        }
    ]
}
```

## 任务配置

采用通用配置接口。

- Key

```
hjbTasksConfig
```

- 响应结果（Response）

响应结果是个 JSON 字符串，需要首先反序列化。

```json
[{
    "_id": 'kwezazqu6i3s492r6nd',
    // 记录创建时间 时间戳
    "_create_at": 1637846142582,
    // 记录更新时间 时间戳
    "_update_at": 1637894422210,
    // 逻辑删除位
    "_is_deleted": false,
    // 排序 key (sortKey)
    "sort_key": 1,
    // 任务名称
    "name": "",
    // 任务类型 0 - 签到，1 - 首购，2 - 高端理财，3 - 购买，4 - 浏览，5 - 分享，6 - 加自选，7 - 社区类
    "type": 0,
    // 任务触发时间
    "condition_time": null,
    // 任务触发金额
    "condition_amount": null,
    // 是否周期任务 (isCycleTask)
    "is_cycle_task": true,
    // 周期 day - 每日，week - 每周，month - 每月
    "cycle": "day",
    // 周期内可完成次数
    "times_in_cycle": 1,
    // 是否超级悬赏任务 (isSuperTask)
    "is_super_task": false,
    // 奖励类型  0 - 固定奖励，1 - 满赠奖励
    "reward_type": 0,
    // 奖励豆子数量
    "reward_beans": 10,
    // 满赠奖励的条件，每满 xxx 条件
    "reward_every_full": 10,
    // 奖励克数
    "reward_grams": 0.2,
    // 跳转链接
    "link": "",
    // 分享任务 - 文案
    "share_text": "",
    // 分享任务 - 标题
    "share_title": "",
    // 分享任务 - 图片
    "share_image": "",
    // 分享任务 - 链接
    "share_link": "",
}]
```