.aiGenerateModal {
  .modalContent {
    padding: 8px 0;
    
    .formItem {
      margin-bottom: 20px;
      
      .ant-form-item-label {
        font-weight: 500;
        color: #333;
      }
      
      .ant-input,
      .ant-select-selector,
      .ant-input-textarea {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #40a9ff;
        }
        
        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
      
      .ant-input-textarea {
        resize: vertical;
        min-height: 80px;
      }
    }
    
    .tips {
      margin-top: 24px;
      padding: 16px;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 6px;
      
      .tipsTitle {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 500;
        color: #52c41a;
      }
      
      .tipsList {
        margin: 0;
        padding-left: 16px;
        color: #666;
        
        li {
          margin-bottom: 6px;
          font-size: 13px;
          line-height: 1.4;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    text-align: right;
    
    .ant-btn {
      border-radius: 6px;
      font-weight: 500;
      
      & + .ant-btn {
        margin-left: 12px;
      }
    }
    
    .ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;
      
      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }
} 