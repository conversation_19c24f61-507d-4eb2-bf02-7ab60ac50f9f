.authEquityManage {}

.authEquityModal {}

.modal-body-auth-e {
    display: flex;
    justify-content: space-between;
    margin: 12px 0px;

    .top-content {
        flex: 1;
        display: flex;
        align-items: center;

        .red-xi::before {
            display: inline-block;
            margin-right: 4px;
            color: #f5222d;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: '*';
        }

        .m-b-top-input {
            width: 230px;
            margin-left: 10px;
        }
    }
}

.auth-e-second-item {
    margin-top: 20px;
}

.m-b-content-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0px 30px;

    .m-b-content-mid {
        width: 100%;
        margin: 20px 20px;
        border: 1px solid #dddddd;
        padding: 10px 30px;
    }
}