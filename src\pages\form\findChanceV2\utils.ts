import moment from 'moment';
import { ConfigData } from './type';

export const KEY = 'LCTAB_FINDCHANCE_V2';
export const HOTSPOT_KEY = 'LCTAB_FINDCHANCE_V2_HOTSPOT';
export const HOTSPOT_TMP_KEY = 'LCTAB_FINDCHANCE_V2_HOTSPOT_TMP';
export const checkUrl = (str: string, sort: string = '') => {
  if (str) {
    if(str.indexOf(" ")!==-1){
      return {
        isError: true,
        msg: `请检查${sort}空格`,
      };
    }
    if (!/^(http:\/\/|https:\/\/|client.html).+/.test(str)) {
      return {
        isError: true,
        msg: `请填写${sort}正确的跳转链接`,
      };
    }
    if (str.length !== str.trim().length) {
      return {
        isError: true,
        msg: `${sort}跳转链接前后不能有空格`,
      };
    } else {
      return {
        isError: false,
        msg: '',
      };
    }
  } else {
    return {
      isError: false,
      msg: '',
    };
  }
};
export const checkRequiredAndThrow = (data, name) => {
  if (!data) {
    throw new Error(name);
  }
  return true;
};
export const checkUrlAndThrow = (data, name) => {
  if (!data) {
    return true;
  }
  const result = checkUrl(data, name);
  if (result.isError) {
    throw new Error(result.msg);
  }
};
export const checkTagAndThrow=(data,name,sum=3,every=7)=>{
  if (!data) {
    return true;
  }

  const tagArr=data.split(",");
  if(sum&&tagArr.length>sum){
    throw new Error(`${name}不多于${sum}个`)
  }
  tagArr.forEach(item=>{
    if(!item){
      throw new Error(`${name}： 存在空项`)
    }
    if(item.indexOf(' ')!==-1){
      throw new Error(`${name}：（${item} ）存在空格`)
    }
    if(every&&item.length>every){
      throw new Error(`${name}：（${item} ）长度大于${every}`)
    }
  })
}
export const checkLengthAndThrow=(data,name,length=10)=>{
  if (!data) {
    return true;
  }
  if(data.length>length){
    throw new Error(`${name}限制在${length}个字符内`)
  }
}
export const changeZiXunToHotSpot = item => {
  return {
    title: item.title,
    time: moment(item.ctime * 1000).format('YYYY-MM-DD'), //时间点
    link: item.url, //链接

    content: item.abstractInfo, //摘要内容
    tags: '', //机会标签
    analyze: '', //热点剖析
  };
};

export const changeZiXunToInfo = item => {
  return {
    id:item.uniqueId,
    type:item.type,
    name: item.title, //文章标题
    from: item.source, //文章来源
    readAmount: String(item.clicks), //阅读量
    createTime: moment(item.ctime * 1000).format('YYYY-MM-DD HH:mm:ss'), //发文时间
    cover: item.picUrls[0] || '', //封面图
    link: item.url, //文章链接
  };
};

export const mergePoolToBase=(baseData:ConfigData,basePool:any)=>{
      if(!baseData||!basePool){
        return baseData;
      }

         const mergeData={
          ...baseData
         }
        const poolDataHotSpot=basePool.hotspots?.map(changeZiXunToHotSpot)||[];
        const poolDataInfo=basePool.relateInfos?.map(changeZiXunToInfo)||[];
        mergeData.hotspots=[...poolDataHotSpot,...baseData.hotspots]
        mergeData.infomation=[...poolDataInfo,...baseData.infomation]
     return mergeData

}
export const setRandomId=(id)=>{
  if(!window["$$ids"]){
    window["$$ids"]={
      [id]:true
    }
  }else{
    window["$$ids"][id]=true;
  }
}
export const getRandomId=(id)=>{
  return window["$$ids"]?.[id]
}
// 检查角标及转化角标日期
export const checkMarker = (data) => {
  if (data.marker) {
    if(!data.markerStartTime || !data.markerEndTime){
      throw new Error('请选择角标日期');
    }
    const startTimeStamp = new Date(data.markerStartTime).getTime();
    const endTimeStamp = new Date(data.markerEndTime).getTime();
    const nowTimerStamp = new Date().getTime();
    if(endTimeStamp <= startTimeStamp){
      throw new Error('角标开始日期应小于截止日期');
    }
    if(endTimeStamp <= nowTimerStamp){
      throw new Error('角标截止日期应大于当前日期');
    }
  }
  return true;
};
