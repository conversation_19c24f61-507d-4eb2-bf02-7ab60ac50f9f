export const HotOpportunityGroup = 1;

// 编辑类型枚举
export enum EditType {
  // 新增
  ADD = 0,
  // 编辑
  EDIT = 1,
  // 复制
  COPY = 2,
}

export enum PageStatus {
  // AI生成中
  AI_GENERATING = 0,
  // AI生成完成
  AI_GENERATED = 1,
  // AI生成失败
  AI_FAILED = 2,
  // 填写完整
  COMPLETE = 3,
  // 已上线
  ONLINE = 4,
  // 已下线
  OFFLINE = 5,
}

export const statusMap: Record<number, string> = {
  0: 'AI生成中',
  1: 'AI生成完成',
  2: 'AI生成失败',
  3: '待发布',
  4: '已上线',
  5: '已下线',
};

export const DefaultFormData = {
  name: '',
  group: HotOpportunityGroup,
  data: {
    title: '',
    digest: '',
    abTest: false,
    abValue: '',
    newsAppId: '',
    conceptBlock: {
      name: '',
      code: '',
      market: '',
    },
    industryConfig: {
      switch: true,
      tab: '',
      detail: {},
    },
  },
};

// 所属位置类型
export enum PositionType {
  // 适用全部（最相关标签）
  ALL = 'all',
  // 上游
  UPSTREAM = 'upstream',
  // 中游
  MIDSTREAM = 'midstream',
  // 下游
  DOWNSTREAM = 'downstream',
}
