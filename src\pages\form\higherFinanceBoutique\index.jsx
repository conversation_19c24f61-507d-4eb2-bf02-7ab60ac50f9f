/* eslint-disable react-hooks/exhaustive-deps */
import React, {useState, useEffect} from 'react'
import styles from './index.less';
import classnames from 'classnames';
import api from 'api';
import {Collapse,Button,Drawer,Table,Switch,message } from 'antd';
import { DraggableArea } from '@/components/DragTags';
const { getHigherFinanceAllNew, buyPrivateList,fetchBoutiqueList, fetchBoutiqueLists, boutiqueListQuery, fetchClassList, saveClassConfig, boutiquePublish } = api;
const { Panel } = Collapse;
message.config({
  duration: 3, // 持续时间
  maxCount: 3, // 最大显示数, 超过限制时，最早的消息会被自动关闭
  top: 70, // 到页面顶部距离
});

const CLASSTITLE = {
  hqlc: '活钱理财',
  wjyx: '稳健优选',
  jjlc: '进阶理财',
  smjp: '私募精品',
};
const CLASSKEY = {
  hqlc: 'flexibleMoney',
  wjyx: 'steadyInvest',
  jjlc: 'advancedFinance',
  smjp: 'privateFund',
};

export default function () {
  // 是否显示分类配置抽屉
  const [showAddDrawer,setShowAddDrawer] = useState(false);
  // 当前编辑的分类
  const [currentEditClass,setCurrentEditClass] = useState('');
  // 当前分类全部列表查询
  const [currentClassList,setCurrentClassList] = useState([]);
  // 精品列表
  const [boutiqueLists, setBoutiqueLists] = useState([
    {
      key: 'hqlc',
      boutiqueList: []
    },
    {
      key: 'wjyx',
      boutiqueList: []
    },
    {
      key: 'jjlc',
      boutiqueList: []
    },
    {
      key: 'smjp',
      boutiqueList: []
    },
  ]);
  // 高端理财详情列表
  const [detailList,setDetailList] = useState([]);
  // 私募详情列表
  const [privateList,setPrivateList] = useState([]);
  const tableColumns = [
    {
      title: '序号',
      key: 'index',
      render: (row, record, index) => <div>{index + 1}</div>,
    },
    {
      title: '产品代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '选择产品',
      key: 'isPart',
      render: (row, record, index) => (
        <div>
          <Switch checked={row.isPart === '0' ? false : true} onChange={()=>{handleSwitch(row, index,'isPart')}}/>
        </div>
      ),
    },
    {
      title: '首页精品',
      key: 'selected',
      render: (row, record, index) => (
        <div>
          <Switch checked={row.selected === '0' ? false : true} onChange={()=>{handleSwitch(row, index,'selected')}}/>
        </div>
      ),
    },
  ];
  useEffect(() => {
    handleFetchBoutiqueLists();
    handleGetHigherFinanceAllNew();
    handleGetPrivateList();
  }, [])
  
  // 获取高端理财详情列表
  const handleGetHigherFinanceAllNew = () => {
    getHigherFinanceAllNew()
      .then(res => {
        let { code, data } = res;
        if (code === '0000' && data) {
          const temp = data.filter((item, index) => {
            return item.value !== 'null' && item.value !== '';
          });
          const tempResult = temp.map((item, index) => {
            return JSON.parse(item.value);
          });
          const resultMap = {};
          tempResult && tempResult.forEach((item,index)=> {
            let key = item.fundCode;
            resultMap[key] = item;
          })
          setDetailList(resultMap);
        } else {
          message.error(res.msg);
        }
      })
      .catch(e => {
        message.error(e.message);
      });
  }
  // 获取私募详情列表
  const handleGetPrivateList = () => {
    buyPrivateList()
      .then(res => {
        let { code, data } = res;
        if (code === '0000' && data) {
          const resultMap = {};
          data && data.forEach((item,index)=> {
            let key = item.privateFundId;
            resultMap[key] = item;
          })
          setPrivateList(resultMap);
        } else {
          message.error(res.msg);
        }
      })
      .catch(e => {
        message.error(e.message);
      });
  }
  // 获取全部精品列表
  const handleFetchBoutiqueLists = () => {
    fetchBoutiqueLists().then((res)=>{
      if (res.status_code === 0) {
        const _boutiqueLists = deepClone(boutiqueLists);
        const _data = deepClone(res.data);
        
        for (let key in _data) {
          _data[key] = _data[key] && _data[key].filter(Boolean).map((item,index) => {
            item.id = item.code;
            return item;
          })
        }
        _boutiqueLists && _boutiqueLists.map((item,index)=>{
          if (item.key === 'hqlc') {item.boutiqueList = deepClone(_data[CLASSKEY['hqlc']])}
          else if (item.key === 'wjyx') {item.boutiqueList = deepClone(_data[CLASSKEY['wjyx']])}
          else if (item.key === 'jjlc') {item.boutiqueList = deepClone(_data[CLASSKEY['jjlc']])}
          else if (item.key === 'smjp') {item.boutiqueList = deepClone(_data[CLASSKEY['smjp']])}
          return item;
        });
        setBoutiqueLists(_boutiqueLists);
      } else {
        message.error('获取全部精品列表失败');
      }
    }).catch(()=>{
      message.error('获取全部精品列表失败');
    });
  }
  // 获取精品列表
  const handleFetchBoutiqueList = (type) => {
    fetchBoutiqueList({type: CLASSKEY[type]}).then((res)=>{
      if (res.status_code === 0) {
        const _boutiqueLists = deepClone(boutiqueLists);
        _boutiqueLists && _boutiqueLists.map((item,index)=>{
          if (item.key === type) {
            let _data = deepClone(res.data);
            _data = _data && _data.map((item2,index2) => {
              item2.id = item2.code;
              return item2;
            })
            item.boutiqueList = deepClone(_data)
          }
          return item;
        });
        setBoutiqueLists(_boutiqueLists);
      } else {
        message.error('更新列表失败');
      }
    }).catch(()=>{
      message.error('更新列表失败');
    });
  }
  // 渲染折叠面板右上角按钮
  const renderCollapseBtn = (item) => {
    return (
      <>
        <Button type='primary' className={styles['panel-btn']} onClick={(e)=>{handleEdit(e, item)}}>编辑</Button>
        <Button type='danger' className={styles['panel-btn']} onClick={(e)=>{handlePublish(e, item)}}>发布</Button>
      </>
    )
  };
  // 编辑
  const handleEdit = (e, item) => {
    e.stopPropagation();
    fetchClassList({type: CLASSKEY[item.key]}).then((res)=>{
      if (res.status_code === 0) {
        const _currentClassList = deepClone(res.data);
        setCurrentClassList(_currentClassList);
        setCurrentEditClass(item.key);
        setShowAddDrawer(true);
      }
    }).catch(()=>{})
    
  };
  // 切换开关
  const handleSwitch = (row, index, type) => {
    if (type === 'selected' && row[type] === '0' && currentEditClass !== 'smjp') {
      if (!detailList[row.code]?.feature || (!detailList[row.code]?.firstReason && !detailList[row.code]?.secondReason)) {
        message.error('未配置产品特征和推荐理由，无法展示到首页精品');
        return;
      }
    } else if (type === 'selected' && row[type] === '0' && currentEditClass === 'smjp') {
      if (!privateList[row.code]?.feature || (!privateList[row.code]?.firstReason && !privateList[row.code]?.secondReason)) {
        message.error('未配置产品特征和推荐理由，无法展示到首页精品');
        return;
      }
    }
    const _currentClassList = deepClone(currentClassList);
    _currentClassList[index][type] = row[type]=== '0' ? '1' : '0';
    // 关闭选择产品开关时，同时关闭首页精品开关
    if (type === 'isPart' && _currentClassList[index][type] === '0') {
      _currentClassList[index].selected = '0';
    }
    // 打开精品开关时，同时打开选择产品开关
    if (type === 'selected' && _currentClassList[index][type] === '1') {
      _currentClassList[index].isPart = '1';
    }
    setCurrentClassList(_currentClassList);
  };
  // 保存分类配置
  const handleSaveClass = () => {
    saveClassConfig({
      dataList: currentClassList,
      type: CLASSKEY[currentEditClass]
    }).then((res)=>{
      if (res.status_code === 0) {
        message.success('保存成功');
        handleFetchBoutiqueList(currentEditClass);
        closeDrawer();
      } else {
        message.error('保存失败');
      }
    }).catch(()=>{
      message.error('保存失败');
    })
  };
  // 发布
  const handlePublish = (e, item) => {
    e.stopPropagation();
    boutiquePublish({
      type: CLASSKEY[item.key]
    }).then((res)=>{
      if (res.status_code === 0) {
        message.success('发布成功')
      } else {
        message.error('发布失败')
      }
    }).catch(()=>{
      message.error('发布失败')
    })
  };
  // 关闭抽屉
  const closeDrawer = () => {
    setCurrentClassList([]);
    setShowAddDrawer(false);
  };
  // 深拷贝
  const deepClone = (value) => {
    return JSON.parse(JSON.stringify(value));
  };
  return (
    <div className={styles['higher-finance-boutique']}>
      <div className={styles['title']}>首页分类精品配置</div>
      <Collapse>
        {
          boutiqueLists.map((item,index)=>{
            return (
              <Panel header={CLASSTITLE[item.key]} key={index} extra={renderCollapseBtn(item)}>
                <div className={classnames('f-tl', 'u-l-middle')} style={{width: 1604, height:30, fontSize: 12, background: '#FAFAFA'}}>
                  <div style={{width: '10%'}}>序号</div>
                  <div style={{width: '20%'}}>产品代码</div>
                  <div style={{width: '30%'}}>产品名称</div>
              </div>
              <DraggableArea
                isList
                tags={item.boutiqueList}
                render={({tag, index}) => {
                  return (
                    <div className={styles['tag']} style={{width: 1604}}>
                        <div className={classnames(styles['m-row'], 'f-tl', 'u-l-middle')} style={{width: 1604, height: 50}}>
                            <div style={{width: '10%'}}>{index + 1}</div>
                            <div style={{width: '20%'}}>{tag.code || '-'}</div>
                            <div style={{width: '30%'}}>{tag.name || '-'}</div>
                        </div> 
                    </div> 
                  )
                }}
                onChange={(data) => {
                    let _data = deepClone(data);
                    _data = _data && _data.map((item2,index2)=>{
                      delete item2.id;
                      return item2;
                    })
                    boutiqueListQuery({
                      dataList: _data,
                      type: CLASSKEY[item.key],
                    }).then((res)=> {
                      if (res.status_code === 0) {
                      message.success('更新排序成功')
                      } else {
                        message.error('更新排序失败')
                      }
                    }).catch(()=>{
                      message.error('更新排序失败')
                    })
                }}
              />
              </Panel>
            )
          })
        }
      </Collapse>
      <Drawer
          className="higher-finance-boutique-drawer"
          title={`分类配置-${CLASSTITLE[currentEditClass]}`}
          placement="right"
          width="1500"
          maskClosable={false}
          destroyOnClose={true}
          onClose={closeDrawer}
          visible={showAddDrawer}
        >
          <Table
            columns={tableColumns}
            dataSource={currentClassList}
            pagination={false}
            rowKey={record => record.id}
            scroll={{ y: 700 }}
          />
          {/* 确认 */}
          <Button
            type="primary"
            onClick={() => {
              handleSaveClass();
            }}
            style={{marginTop: 30}}
          >
            确认
          </Button>
        </Drawer>
    </div>
  )
}
