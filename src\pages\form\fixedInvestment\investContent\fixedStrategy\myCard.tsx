import {
    Form,
    Input,
    Button,
    message,
    Popconfirm
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import { iFixedStrategyData } from './index';
import styles from '../index.less';

interface StrategyCardProps extends FormComponentProps {
    currentData: iFixedStrategyData;
    handleData: (data: iFixedStrategyData) => void;
    handleDelete: () => void;
}

class StrategyCard extends React.Component<StrategyCardProps, any> {
    constructor(props: StrategyCardProps) {
        super(props);
        this.state = {
            isEdit: false,
        }
    }

    formItemLayout = {
        labelCol: {
            span: 3
        },
        wrapperCol: {
            span: 19
        },
    };
    handleSubmit = () => { 
        const { isEdit } = this.state;
        if (isEdit) {
            const { currentData } = this.props;
            this.props.form.validateFields((err, values) => {
                if (!err) {
                    values = { ...currentData, ...values };
                    this.props.handleData(values);
                    this.setState({
                        isEdit: !isEdit
                    })
                }
            });
        } else {
            this.setState({
                isEdit: !isEdit
            })
        }
        
    };

    render() {
        const { getFieldDecorator } = this.props.form;
        const { handleDelete } = this.props;
        const { isEdit } = this.state;
        return (
            <div className={styles['m-card']}>
                <div className={styles['m-header']}>
                <Button className={styles['m-button']} onClick={this.handleSubmit}>{isEdit === true? '保存' : '编辑'}</Button>
                    <Popconfirm
                        title="确定删除?"
                        onConfirm={handleDelete}
                        okText="是"
                        cancelText="否"
                    >
                        <Button type="danger" className={styles['m-button']}> 删除</Button>
                    </Popconfirm>
                </div>
                <Form {...this.formItemLayout}>
                    <Form.Item label="标题">
                        {getFieldDecorator('title', {
                            initialValue: this.props.currentData?.title,
                            rules: [{ required: true, message: '请输入标题' }],
                        })(
                            <Input disabled={!isEdit} maxLength={16}/>
                        )}
                    </Form.Item>
                    <Form.Item label="跳转链接">
                        {getFieldDecorator('link', {
                            initialValue: this.props.currentData?.link,
                            rules: [{ required: true, message: '请输入跳转链接' }],
                        })(
                            <Input disabled={!isEdit}/>
                        )}
                    </Form.Item>
                </Form>
            </div>
        )
    }
}
const WrappedStrategyCard = Form.create<StrategyCardProps>({ name: 'strictCard' })(StrategyCard);
export default WrappedStrategyCard;