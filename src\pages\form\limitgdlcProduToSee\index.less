.m-filter-container {
  margin-bottom: 10px;
  display: flex;
  .m-filter-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
    span {
      display: inline-block;
      margin-right: 10px;
    }
    :global {
      .ant-select {
        width: 260px;
      }
    }
  }
}
.m-operate {
  .m-publish {
    color: #1890ff;
    &.m-pubished{
      color: grey;
    }
    &:hover {
      cursor: pointer;
    }
  }
  .m-published{
    color: grey !important;
    &:hover{
      cursor: not-allowed;
    }
  }
  .m-del {
    color: red;
    &:hover {
      cursor: pointer;
    }
  }
}


