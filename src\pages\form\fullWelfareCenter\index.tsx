import React, { useEffect, useState } from 'react';
import { Drawer, message, Button, Table, Form, Tabs, Divider, Input } from 'antd';
import { nanoid } from 'nanoid';
import moment from 'moment';
import _ from 'lodash';
import api from 'api';
import store from 'store';
import { ITableItem } from './types';
import { KEY } from './const';
import { getColumns } from './column';
import { isTest, sortByCreateTime } from './utils';
import MemberRights from './components/MemberRights';
import FundRecommend from './components/FundRecommend';
import FloatingWindow from './components/FloatingWindow';
import OtherWelfareForm from './components/OtherWelfare';
import UserCommentsForm from './components/UserComments';

const { fetchHashAll, postHash, postHashDel } = api;
const { TabPane } = Tabs;

const formCol = {
  labelCol: {
    sm: 4,
  },
  wrapperCol: {
    sm: 8,
  },
};

const WelfareCenter = props => {
  const {
    getFieldDecorator,
    validateFields,
    getFieldValue,
    setFieldsValue,
    resetFields,
  } = props.form;
  // 表格数据-数组
  const [tableData, setTableData] = useState<ITableItem[]>([]);
  // 是否展示抽屉
  const [showDrawer, setShowDrawer] = useState(false);
  // 当前编辑或新增策略的key值
  const [editKey, setEditKey] = useState('');
  /**
   * 会员权益区
   */
  // 是否发放红包
  const [isGiveRedPacket, setIsGiveRedPacket] = useState(true);
  // 是否开启条件匹配
  const [isOpenConditionMatching, setIsOpenConditionMatching] = useState(false);
  // 是否展示权益id
  const [showRightsId, setShowRightsId] = useState([true, true, true]);
  /**
   * 基金推荐区
   */
  // 是否是新发基金
  const [isNew, setIsNew] = useState(false);
  // 是否展示基金走势
  const [isShowFundTrend, setIsShowFundTrend] = useState(true);

  /**
   * 其他福利区
   */
  // 是否展示
  const [showOtherWelfare, setShowOtherWelfare] = useState(true);
  // tab位置顺序
  const [tabLocation, setTabLocation] = useState([
    'professionalAnalysis',
    'weeklyLive',
    'investmentTool',
    'investmentSchool',
  ]);
  // 专业分析卡片数量
  const [analysisCardNum, setAnalysisCardNum] = useState(1);
  // 投资工具卡片数量
  const [toolCardNum, setToolCardNum] = useState(1);
  // 投资学堂卡片数量
  const [schoolCardNum, setSchoolCardNum] = useState(1);

  /**
   * 用户精评区
   */
  const [showComments, setShowComments] = useState(true);
  const [commentNum, setCommentNum] = useState(1);

  useEffect(() => {
    fetchHashAll({ key: KEY }).then(res => {
      if (res && res.code === '0000') {
        // 将无序map转化为有序数组放到表格中
        setTableData(sortByCreateTime(res.data));
      } else {
        message.error('获取数据失败');
      }
    });
  }, []);

  // 点击新增，所有状态必须都是新的
  const handleAdd = () => {
    // 设置新增策略的key
    setEditKey(nanoid());
    // 还原默认初始状态
    setIsGiveRedPacket(true);
    setIsOpenConditionMatching(false);
    setShowRightsId([true, true, true]);
    setIsNew(false);
    setIsShowFundTrend(true);
    setShowOtherWelfare(true);
    setTabLocation(['professionalAnalysis', 'weeklyLive', 'investmentTool', 'investmentSchool']);
    setAnalysisCardNum(1);
    setToolCardNum(1);
    setSchoolCardNum(1);
    setShowComments(true);
    setCommentNum(1);
    // 清除表单之前的字段
    resetFields();

    setShowDrawer(true);
  };

  const handleEdit = (tableItem: ITableItem, index: number) => {
    resetFields();
    const temp = JSON.parse(JSON.stringify(tableItem));
    setEditKey(temp.key);
    // 会员权益区
    setIsGiveRedPacket(_.get(temp, 'strategyContent.rights.redPacket.isGiveRedPacket') === '1');
    setIsOpenConditionMatching(
      _.get(temp, 'strategyContent.rights.redPacket.kycConfig.isOpenConditionMatching') === '1',
    );
    // 基金推荐区
    setIsNew(_.get(temp, 'strategyContent.fundRecommend.mainFund.isNew') === '1');
    setIsShowFundTrend(
      _.get(temp, 'strategyContent.fundRecommend.mainFund.isShowFundTrend') === '1',
    );
    // 其他福利区
    setShowOtherWelfare(_.get(temp, 'strategyContent.otherWelfare.isShow') === '1');
    const otherWelfare = _.get(temp, 'strategyContent.otherWelfare');
    const locationArr = [];
    if (otherWelfare.isShow === '1') {
      locationArr[Number(otherWelfare.professionalAnalysis.index)] = 'professionalAnalysis';
      locationArr[Number(otherWelfare.weeklyLive.index)] = 'weeklyLive';
      locationArr[Number(otherWelfare.investmentTool.index)] = 'investmentTool';
      locationArr[Number(otherWelfare.investmentSchool.index)] = 'investmentSchool';
      setTabLocation(locationArr);
      setAnalysisCardNum(
        _.get(temp, 'strategyContent.otherWelfare.professionalAnalysis.cardList')?.length || 0,
      );
      setToolCardNum(
        _.get(temp, 'strategyContent.otherWelfare.investmentTool.cardList')?.length || 0,
      );
      setSchoolCardNum(
        _.get(temp, 'strategyContent.otherWelfare.investmentSchool.cardList')?.length || 0,
      );
    }

    // 时间格式需要对齐
    const redPacket = _.get(temp, 'strategyContent.rights.redPacket');
    try {
      _.set(redPacket, 'kycConfig.deliveryTime', [
        moment(redPacket.kycConfig.startTime),
        moment(redPacket.kycConfig.endTime),
      ]);
    } catch (err) {}
    try {
      _.set(redPacket, 'kycConfig.optional.deliveryTime', [
        moment(redPacket.kycConfig.optional.startTime),
        moment(redPacket.kycConfig.optional.endTime),
      ]);
    } catch (err) {}

    // 用户精评区
    setShowComments(_.get(temp, 'strategyContent.comments.isShow') === '1');
    setCommentNum(_.get(temp, 'strategyContent.comments.commentList')?.length || 1);

    setShowDrawer(true);
    setTimeout(() => {
      setFieldsValue(temp.strategyContent);
    }, 0);
  };

  const handleDelete = (tableItem: ITableItem, deleteIndex: number) => {
    const deletePropName = tableItem.key;
    const newTableData = tableData.filter(
      (_item: ITableItem, index: number) => index !== deleteIndex,
    );
    postHashDel({ key: KEY, propName: deletePropName })
      .then(res => {
        if (res.code === '0000') {
          message.success('删除成功');
          setTableData(newTableData);
        } else {
          message.error(res.message);
        }
      })
      .catch(err => {
        message.error(err);
      });
  };

  const handleCopyUrl = (copyIndex: number) => {
    const copyUrl = `https://${
      isTest() ? 'test' : ''
    }fund.10jqka.com.cn/marketingDomain/fullWelfareCenter.html?key=${tableData[copyIndex].key}`;

    const textarea = document.createElement('textarea');
    textarea.value = copyUrl;
    document.body.appendChild(textarea);
    textarea.select();
    const isSuccess = document.execCommand('copy');
    if (isSuccess){
      message.success('复制成功');
      document.body.removeChild(textarea);
    } else {
      message.error('您的浏览器不支持，请手动复制');
    }
  };

  const handleCopyConfig = (copyIndex: number) => {
    const createAuthor = store.get('name');
    const createTime = moment().format('YYYY-MM-DD HH:mm:ss');
    const copyPropName = nanoid();
    const newTableDataItem = {
      ...JSON.parse(JSON.stringify(tableData[copyIndex])),
      key: copyPropName,
      createAuthor,
      createTime,
      lastEditor: createAuthor,
      lastEditTime: createTime,
    };
    postHash({ key: KEY, propName: copyPropName, value: JSON.stringify(newTableDataItem) })
      .then(res => {
        if (res.code === '0000') {
          const newTableData = [...tableData, newTableDataItem];
          message.success('复制配置成功');
          setTableData(newTableData);
        } else {
          message.error('复制失败');
        }
      })
      .catch(err => {
        message.error(err);
      });
  };

  const handleAbort = (tableItem: ITableItem, abortIndex: number) => {
    const abortItem = JSON.parse(JSON.stringify(tableItem));
    const abortKey = abortItem.key;
    abortItem.isEnable = false;
    postHash({ key: KEY, propName: abortKey, value: JSON.stringify(abortItem) })
      .then(res => {
        if (res.code === '0000') {
          setTableData([
            ...tableData.slice(0, abortIndex),
            abortItem,
            ...tableData.slice(abortIndex + 1),
          ]);
        } else {
          message.error('中止失败');
        }
      })
      .catch(err => {
        message.error(err);
      });
  };

  const handleEnable = (tableItem: ITableItem, enableIndex: number) => {
    const enableItem = JSON.parse(JSON.stringify(tableItem));
    const enableKey = enableItem.key;
    enableItem.isEnable = true;
    postHash({ key: KEY, propName: enableKey, value: JSON.stringify(enableItem) })
      .then(res => {
        if (res.code === '0000') {
          setTableData([
            ...tableData.slice(0, enableIndex),
            enableItem,
            ...tableData.slice(enableIndex + 1),
          ]);
        } else {
          message.error('启用失败');
        }
      })
      .catch(err => {
        message.error(err);
      });
  };

  const handleClose = () => {
    setShowDrawer(false);
  };

  const onSubmit = e => {
    e.preventDefault();
    validateFields((err, values) => {
      try {
        const { kycConfig } = values.rights.redPacket;
        if (kycConfig.isOpenConditionMatching === '1' && !kycConfig.optional.kycFilterId) {
          message.error('请检查kyc配置是否保存');
          return;
        };
        kycConfig.startTime = values.rights.redPacket.kycConfig.deliveryTime[0].format(
          'YYYY-MM-DD HH:mm:ss',
        );
        kycConfig.endTime = values.rights.redPacket.kycConfig.deliveryTime[1].format(
          'YYYY-MM-DD HH:mm:ss',
        );
        delete kycConfig.deliveryTime;
        kycConfig.optional.startTime = values.rights.redPacket.kycConfig.optional.deliveryTime[0].format(
          'YYYY-MM-DD HH:mm:ss',
        );
        kycConfig.optional.endTime = values.rights.redPacket.kycConfig.optional.deliveryTime[1].format(
          'YYYY-MM-DD HH:mm:ss',
        );
        delete kycConfig.optional.deliveryTime;
      } catch (err) {}
      if (!err) {
        const editIndex = tableData.findIndex((item: ITableItem) => item.key === editKey);
        const lastEditor = store.get('name');
        const lastEditTime = moment().format('YYYY-MM-DD HH:mm:ss');
        // 添加index属性
        if (values.otherWelfare.isShow === '1') {
          values.otherWelfare.professionalAnalysis.index = tabLocation
            .indexOf('professionalAnalysis')
            .toString();
          values.otherWelfare.weeklyLive.index = tabLocation.indexOf('weeklyLive').toString();
          values.otherWelfare.investmentTool.index = tabLocation
            .indexOf('investmentTool')
            .toString();
          values.otherWelfare.investmentSchool.index = tabLocation
            .indexOf('investmentSchool')
            .toString();
        }
        // 修改时间属性
        try {
          const { kycConfig } = values.rights.redPacket;
          kycConfig.startTime = values.rights.redPacket.kycConfig.deliveryTime[0].format(
            'YYYY-MM-DD HH:mm:ss',
          );
          kycConfig.endTime = values.rights.redPacket.kycConfig.deliveryTime[1].format(
            'YYYY-MM-DD HH:mm:ss',
          );
          kycConfig.optional.startTime = values.rights.redPacket.kycConfig.optional.deliveryTime[0].format(
            'YYYY-MM-DD HH:mm:ss',
          );
          kycConfig.optional.endTime = values.rights.redPacket.kycConfig.optional.deliveryTime[1].format(
            'YYYY-MM-DD HH:mm:ss',
          );
        } catch (err) {}

        const reqData = {
          isEnable: true,
          key: editKey,
          strategyContent: values,
          lastEditor,
          lastEditTime,
        };
        // 是编辑还是新增
        if (editIndex === -1) {
          postHash({
            key: KEY,
            propName: editKey,
            value: JSON.stringify({
              ...reqData,
              createAuthor: lastEditor,
              createTime: lastEditTime,
            }),
          }).then(res => {
            if (res.code === '0000') {
              message.success('保存成功');
              setShowDrawer(false);
              fetchHashAll({ key: KEY }).then(res => {
                if (res && res.code === '0000') {
                  // 将无序map转化为有序数组放到表格中
                  setTableData(sortByCreateTime(res.data));
                } else {
                  message.error('获取数据失败');
                }
              });
            } else {
              message.error('保存失败');
            }
          })
        } else {
          postHash({
            key: KEY,
            propName: editKey,
            value: JSON.stringify({
              ...reqData,
              createAuthor: tableData[editIndex].createAuthor,
              createTime: tableData[editIndex].createTime,
            }),
          })
            .then(res => {
              if (res.code === '0000') {
                message.success('保存成功');
                setShowDrawer(false);
                fetchHashAll({ key: KEY }).then(res => {
                  if (res && res.code === '0000') {
                    // 将无序map转化为有序数组放到表格中
                    setTableData(sortByCreateTime(res.data));
                  } else {
                    message.error('获取数据失败');
                  }
                });
              } else {
                message.error('保存失败');
              }
            })
            .catch(err => {
              message.error(err);
            });
        }
      } else {
        message.error('请将所有字段输入完整');
      }
    });
  };

  const columns = getColumns(
    handleEdit,
    handleDelete,
    handleCopyUrl,
    handleCopyConfig,
    handleAbort,
    handleEnable,
  );

  return (
    <div>
      <div style={{ textAlign: 'center', marginTop: '10px', fontSize: '30px', fontWeight: 'bold' }}>
        权益中心
      </div>
      <Button
        type="primary"
        style={{ marginTop: '10px', marginBottom: '10px' }}
        onClick={handleAdd}
      >
        新增
      </Button>
      <Table columns={columns} dataSource={tableData}></Table>
      <Drawer visible={showDrawer} onClose={handleClose} width={1000}>
        <Form {...formCol} onSubmit={onSubmit}>
          <div>
            策略名称：
            {_.get(tableData.filter(item => item.key === editKey)[0], 'strategyContent.name')}
            （正式环境地址：
            {`https://www.fund.10jqka.com.cn/marketingDomain/welfarecenter.html?key=${editKey}`}）
          </div>
          <Divider />
          <Form.Item label="策略名称">
            {getFieldDecorator('name', {
              rules: [{ required: true, message: '请输入策略名称' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item style={{ float: 'right' }}>
            <Button type="primary" htmlType="submit">
              保存并发布
            </Button>
          </Form.Item>
          <Tabs defaultActiveKey="1">
            <TabPane tab="权益" key="memberRights" forceRender={true}>
              <MemberRights
                getFieldDecorator={getFieldDecorator}
                isGiveRedPacket={isGiveRedPacket}
                setIsGiveRedPacket={setIsGiveRedPacket}
                isOpenConditionMatching={isOpenConditionMatching}
                setIsOpenConditionMatching={setIsOpenConditionMatching}
                showRightsId={showRightsId}
                setShowRightsId={setShowRightsId}
              />
            </TabPane>
            <TabPane tab="基金推荐区" key="fundRecommend" forceRender={true}>
              <FundRecommend
                getFieldDecorator={getFieldDecorator}
                isNew={isNew}
                setIsNew={setIsNew}
                isShowFundTrend={isShowFundTrend}
                setIsShowFundTrend={setIsShowFundTrend}
              />
            </TabPane>
            <TabPane tab="浮窗区" key="3" forceRender={true}>
              <FloatingWindow getFieldDecorator={getFieldDecorator} />
            </TabPane>
            <TabPane tab="其他福利区" key="4" forceRender={true}>
              <OtherWelfareForm
                getFieldDecorator={getFieldDecorator}
                getFieldValue={getFieldValue}
                setFieldsValue={setFieldsValue}
                showOtherWelfare={showOtherWelfare}
                setShowOtherWelfare={setShowOtherWelfare}
                tabLocation={tabLocation}
                setTabLocation={setTabLocation}
                analysisCardNum={analysisCardNum}
                setAnalysisCardNum={setAnalysisCardNum}
                toolCardNum={toolCardNum}
                setToolCardNum={setToolCardNum}
                schoolCardNum={schoolCardNum}
                setSchoolCardNum={setSchoolCardNum}
              />
            </TabPane>
            <TabPane tab="用户精评区" key="5" forceRender={true}>
              <UserCommentsForm
                getFieldDecorator={getFieldDecorator}
                showComments={showComments}
                setShowComments={setShowComments}
                commentNum={commentNum}
                setCommentNum={setCommentNum}
              />
            </TabPane>
          </Tabs>
        </Form>
      </Drawer>
    </div>
  );
};

export default Form.create({ name: 'new_welfare_center' })(WelfareCenter);
