import React, { useState, useEffect } from 'react';
import { Table, Divider, Select, Button, message, Popconfirm } from 'antd';
import moment from 'moment';
import classnames from 'classnames';

import api from 'api';
import styles from './index.less';
import { Fund, TableRecordItem, OperateType } from './type';

const { Option } = Select;
const LimitgdlcProduToSee = () => {
  const [selectedFund, setSelectedFund] = useState<Fund>({
    fundCode: '',
    fundName: '',
  });
  const [fundList, setFundList] = useState<Fund[]>([]);
  const [dataSource, setDataSource] = useState<TableRecordItem[]>([]);
  const [loading, setLoading] = useState(false);
  // 选择某条基金
  const handleSelectFund = (fundCode: string) => {
    const currentSelectedFund = fundList.find(item => {
      return item.fundCode === fundCode;
    });
    setSelectedFund(currentSelectedFund);
  };
  const init = () => {
    fetchTableData();
    fetchHighFinanceProdu()
  };
  const fetchTableData = async () => {
    setLoading(true);
    fetchGDLCLimitProduToSeeConfig();
  };
  useEffect(() => {
    init();
  }, []);
  const fetchHighFinanceProdu = () => {
    api
    .fetchHighFinanceProdu({type: 'flexibleMoney'})
    .then(res => {
      if (res.status_code === 0 && res.data) {
        const newFundList = (res.data || []).reduce((pre,cur)=>{
          return pre.concat({
            fundCode: cur.code,
            fundName: cur.name
          })
        },[])
        setFundList(newFundList)
      }
    })
    .catch(err => {
      message.error(err.message);
    });
  }
  const fetchGDLCLimitProduToSeeConfig = () => {
    api
      .fetchGDLCLimitProduToSeeConfig()
      .then(res => {
        if (res.code === '0000' && res.data) {
          const valueObj = JSON.parse(res.data);
          const list: TableRecordItem[] = valueObj.list;
          setDataSource([...list]);
        }
        setLoading(false);
      })
      .catch(err => {
        setLoading(false);
        message.error(err.message);
      });
  };
  const dealWithSetData = (list: TableRecordItem[], type: number) => {
    const operateMap = {
      [OperateType.Add]: {
        msg: '添加成功',
      },
      [OperateType.Del]: {
        msg: '删除成功',
      },
      [OperateType.Publish]: {
        msg: '发布成功',
      },
    }[type];
    const fundCodeList = list.reduce((pre: string[], cur, index) => {
      cur.index = index + 1
      if (cur.state === 1) {
        pre.push(cur.fundCode);
      }
      return pre;
    }, []);
    const valueObj = {
      list,
      fundCodeList,
    };
    const callback = () => {
      setDataSource(list);
      message.success(operateMap.msg);
    };
    postGDLCLimitProduToSeeConfig(valueObj, callback);
  };
  const postGDLCLimitProduToSeeConfig = (
    value: { list: TableRecordItem[]; fundCodeList: string[] },
    callback: () => void,
  ) => {
    const jsonSValue = JSON.stringify(value);
    api.postGDLCLimitProduToSeeConfig({ value: jsonSValue }).then(res => {
      if (res.code !== '0000') {
        message.error(res.message);
        return;
      }
      callback();
    });
  };
  const handleAdd = () => {
    if (!selectedFund?.fundCode) {
      message.error('请选择基金代码');
      return;
    }
    const index = dataSource.findIndex(item => item.fundCode === selectedFund.fundCode);
    if (index > -1) {
      message.error('该基金代码已被添加，请勿重复添加');
      return;
    }
    const addTime: string = moment(new Date()).format('YYYY/MM/DD: HH:mm:ss');
    const state = 0;
    const addItem = {
      ...selectedFund,
      addTime,
      state,
    };
    const list = [addItem, ...dataSource];
    dealWithSetData(list, OperateType.Add);
  };
  const handlePublish = (record: TableRecordItem) => {
    const currentRecord = dataSource.find(item => item.fundCode === record.fundCode);
    currentRecord.state = 1;
    dealWithSetData([...dataSource], OperateType.Publish);
  };
  const handleDel = (record: TableRecordItem) => {
    const index = dataSource.findIndex(item => item.fundCode === record.fundCode);
    dataSource.splice(index, 1);
    dealWithSetData([...dataSource], OperateType.Del);
  };
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
    },
    {
      title: '基金名称',
      dataIndex: 'fundName',
      key: 'fundName',
    },
    {
      title: '基金代码',
      dataIndex: 'fundCode',
      key: 'fundCode',
    },
    {
      title: '添加时间',
      dataIndex: 'addTime',
      key: 'addTime',
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      render: text => {
        return <span>{text === 0 ? '待发布' : '已发布'}</span>;
      },
    },
    {
      title: '操作',
      dataIndex: 'operate',
      render: (text, record: TableRecordItem) => {
        return (
          <span className={styles['m-operate']}>
            <Popconfirm
              title="确定要发布吗？"
              onConfirm={() => handlePublish(record)}
              okText="确定"
              cancelText="取消"
              disabled={record.state === 1}
            >
              <span
                className={classnames(
                  styles['m-publish'],
                  record.state === 1 ? styles['m-published'] : '',
                )}
              >
                发布
              </span>
            </Popconfirm>
            <Divider type="vertical" />
            <Popconfirm
              title="您确定要删除该基金吗？删除后将不再限制该高端理财产品的信息展示"
              onConfirm={() => handleDel(record)}
              okText="确定"
              cancelText="取消"
            >
              <span className={styles['m-del']}>删除</span>
            </Popconfirm>
          </span>
        );
      },
    },
  ];

  return (
    <section>
      <section className={styles['m-filter-container']}>
        <div className={styles['m-filter-item']}>
          <span>基金代码</span>
          <Select
            onChange={handleSelectFund}
            showSearch
            allowClear
            optionFilterProp="children"
            filterOption={(input, option) =>
              option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {fundList.map(item => (
              <Option
                value={item.fundCode}
                key={item.fundCode}
                title={`${item.fundCode}-${item.fundName}`}
              >{`${item.fundCode}-${item.fundName}`}</Option>
            ))}
          </Select>
        </div>
        <Button type={'primary'} onClick={handleAdd}>
          添加
        </Button>
      </section>
      <Table columns={columns} dataSource={dataSource} loading={loading} />
    </section>
  );
};

export default LimitgdlcProduToSee;
