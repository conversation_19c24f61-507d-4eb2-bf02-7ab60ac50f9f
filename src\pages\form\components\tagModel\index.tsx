import React, { useState, useEffect } from 'react';
import { Radio, Select, Input, Button, Popconfirm } from 'antd';
import api from 'api';
const { Option } = Select;
const { fetchCBAS, fetchOLAS } = api;
import styles from '@/pages/frontend/frontend.less';

export default function(props: any) {
  const { isEdit, kycTag, olasTag, data, handleChange, handleIdType, olasUserType } = props;
  const [radio, setRadio] = useState('');
  const [kycLogic, setKycLogic] = useState('');
  const [kycs, setKycs] = useState([]);
  const [olasId, setOlasId] = useState({ '1': '', '3': '' });

  useEffect(() => {
    if (data) {
      //判断kyc标签是否在场景内
      console.log('data', data);
      data.kycs &&
        data.kycs.map((val, index) => {
          let target = kycTag.find(item => item.label_code === val.label);
          console.log('target', target, val, kycTag);
          val.available = target && target.app_info && target.app_info.includes('fund0005');
        });
      setRadio(data.targetType);
      setKycLogic(data.kycLogic);
      setKycs(data.kycs);
      setOlasId({
        ...olasId,
        [olasUserType]: data.olasId,
      });
    }
  }, [data]);
  //提交时返回给父组件的回调
  const onSubmit = (radio, kycLogic, kycs, olasId) => {
    let _data = {
      targetType: radio,
      kycLogic: kycLogic,
      olasId: olasId[olasUserType],
      kycs: kycs,
    };
    console.log(_data, data);
    handleChange(_data);
  };
  const onRadioChange = (e: any) => {
    console.log(e.target);
    setRadio(e.target.value);
    onSubmit(e.target.value, kycLogic, kycs, olasId);
  };
  const onKycLogicChange = (val: any) => {
    console.log(val);
    setKycLogic(val);
    onSubmit(radio, val, kycs, olasId);
  };
  const addTag = () => {
    let obj = {
      label: kycTag[0].label_code,
      logic: 'equal',
      value: '',
      available: kycTag[0].app_info && kycTag[0].app_info.includes('fund0005'),
    };
    let data = [].concat(kycs, obj);
    setKycs(data);
    onSubmit(radio, kycLogic, data, olasId);
  };
  const onLabelChange = (val: any, index: any, other: any) => {
    let available = kycTag[other.key].app_info && kycTag[other.key].app_info.includes('fund0005');
    console.log(val, available);

    let _kyc = [...kycs];
    _kyc[index].label = val;
    _kyc[index].available = available;
    setKycs(_kyc);
    onSubmit(radio, kycLogic, _kyc, olasId);
  };
  const onLogicChange = (val: any, index: any) => {
    let _kyc = [...kycs];
    _kyc[index].logic = val;
    setKycs(_kyc);
    onSubmit(radio, kycLogic, _kyc, olasId);
  };
  const onInput = (e: any, index: any) => {
    let _kyc = [...kycs];
    _kyc[index].value = e.target.value;
    setKycs(_kyc);
    onSubmit(radio, kycLogic, _kyc, olasId);
  };
  const onOlasChange = (val: any) => {
    setOlasId({
      ...olasId,
      [olasUserType]: val,
    });
    onSubmit(radio, kycLogic, kycs, {
      ...olasId,
      [olasUserType]: val,
    });
  };
  const handleDelete = (index: number) => {
    console.log(index);
    let _kycs = kycs.filter((item, idx) => idx !== index);
    setKycs(_kycs);
    onSubmit(radio, kycLogic, _kycs, olasId);
  };

  return (
    <section>
      <p className={styles['m-card-label']}>指定用户:</p>
      <Radio.Group onChange={onRadioChange} value={radio} disabled={!isEdit}>
        <Radio value={'kyc'} key="kyc">
          通过KYC标签
        </Radio>
        <Radio value={'olas'} key="olas">
          通过OLAS平台推送
        </Radio>
      </Radio.Group>
      <br />
      {radio === 'kyc' ? (
        <div key="kyc">
          <div className={styles['m-tagModel-row']}>
            <p className={styles['m-card-label']}>标签逻辑关系:</p>
            <Select
              value={kycLogic}
              style={{ width: 120 }}
              onChange={onKycLogicChange}
              disabled={!isEdit}
            >
              <Option value="and">与</Option>
              <Option value="or">或</Option>
            </Select>
          </div>
          <br />
          <div className={styles['m-tagModel-row']}>
            <p className={styles['m-card-label']}>标签值:</p>
            <Button
              type="primary"
              onClick={addTag}
              disabled={!isEdit}
              size="small"
              className={styles['m-tagModel-button']}
            >
              添加
            </Button>
          </div>
          {kycs.map((item: any, index) => {
            return (
              <div key={index} className={'g-mb10 g-ml100'}>
                <Select
                  showSearch
                  style={{ width: 260, marginRight: '10px' }}
                  optionFilterProp="children"
                  onChange={(val, other) => onLabelChange(val, index, other)}
                  maxTagCount={5}
                  filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                  value={item.label}
                  disabled={!isEdit}
                >
                  {kycTag.map((val: any, cnt) => {
                    return (
                      <Option value={val.label_code} key={cnt}>
                        {val.label_name}
                      </Option>
                    );
                  })}
                </Select>
                <Select
                  style={{ width: 260, marginRight: '10px' }}
                  onChange={val => onLogicChange(val, index)}
                  value={item.logic}
                  disabled={!isEdit}
                >
                  <Option value="equal">等于</Option>
                  <Option value="notEqual">不等于</Option>
                  <Option value="in">字符串包含</Option>
                  <Option value="notIn">字符串不包含</Option>
                  <Option value="isSet">有值</Option>
                  <Option value="notSet">没值</Option>
                  <Option value="greater">大于</Option>
                  <Option value="less">小于</Option>
                  <Option value="greaterEqual">大于等于</Option>
                  <Option value="lessEqual">小于等于</Option>
                </Select>
                {item.logic === 'isSet' || item.logic === 'notSet' ? null : (
                  <Input
                    style={{ width: 260 }}
                    onChange={e => onInput(e, index)}
                    value={item.value}
                    disabled={!isEdit}
                  />
                )}
                <span className={'g-ml20'}>
                  <Popconfirm
                    title="确定删除？"
                    onConfirm={() => handleDelete(index)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button type="danger" ghost className={'g-ml10'}>
                      删除
                    </Button>
                  </Popconfirm>
                </span>
                {!item.available ? (
                  <p style={{ color: 'red' }}>
                    此标签未添加进基金的应用场景fund0005，请联系叶志飞（<EMAIL>）手动添加，15:30前添加，T+1生效
                  </p>
                ) : null}
              </div>
            );
          })}
        </div>
      ) : (
        radio === 'olas' && (
          <>
            <div className="u-l-middle" style={{ marginBottom: 20, color: 'black' }}>
              <p className={styles['m-card-label']} style={{ marginBottom: 0 }}>
                手动指定用户名单类型:
              </p>
              <div>
                <Radio.Group value={olasUserType} disabled={!isEdit} onChange={handleIdType}>
                  <Radio value="3">通过Cust-id筛选</Radio>
                  <Radio value="1">通过User-id筛选</Radio>
                </Radio.Group>
              </div>
            </div>
            <div key="olas" className={styles['m-tagModel-row']}>
              <span className={styles['m-card-label']}>用户群体:</span>
              <Select
                showSearch
                style={{ width: 260 }}
                optionFilterProp="children"
                onChange={onOlasChange}
                value={olasId[olasUserType]}
                filterOption={(input, option) => option.props.children.indexOf(input) >= 0}
                disabled={!isEdit}
              >
                {olasTag &&
                  olasTag.map((item: any, index) => {
                    return (
                      <Option value={item.groupid} key={index}>
                        {item.description}
                      </Option>
                    );
                  })}
              </Select>
            </div>
          </>
        )
      )}
    </section>
  );
}
