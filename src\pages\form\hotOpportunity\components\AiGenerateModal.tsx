import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Spin, message } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import { AiGenerateModalProps, AiGenerateParams } from '../types';
import styles from './AiGenerateModal.less';
import { getOperator, createSubmitDebounce } from '../utils';

const { TextArea } = Input;

interface AiGenerateModalInternalProps extends FormComponentProps, AiGenerateModalProps {}

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const AiGenerateModal: React.FC<AiGenerateModalInternalProps> = ({
  form,
  showDialog,
  onClose,
  onSubmit,
  initialData,
}) => {
  const [loading, setLoading] = useState<boolean>(false);

  const { getFieldDecorator, validateFields, resetFields, setFieldsValue } = form;

  // 创建防抖提交函数
  const debouncedSubmit = React.useMemo(() => {
    return createSubmitDebounce(async (formData: AiGenerateParams) => {
      const result = await onSubmit(formData);
      return result;
    }, 1000);
  }, [onSubmit]);

  // 监听initialData变化，进行参数回填
  useEffect(() => {
    if (showDialog && initialData) {
      // 等待弹窗完全打开后再设置字段值
      setTimeout(() => {
        setFieldsValue({
          configId: initialData.configId,
          name: initialData.name || '',
          aiId: initialData.aiId || '1510',
          newsContent: initialData.newsContent || '',
          targetIndustry: initialData.targetIndustry || '',
          initialIndustryChain:
            initialData.initialIndustryChain || '上游：【】；中游：【】；下游：【】；',
        });
      }, 100);
    }
  }, [showDialog, initialData, setFieldsValue]);

  // 处理弹窗关闭
  const handleCancel = () => {
    resetFields();
    onClose();
  };

  // 处理表单提交
  const handleSubmit = () => {
    validateFields(async (err, values) => {
      if (!err) {
        setLoading(true);
        try {
          const formData = {
            configId: initialData?.configId,
            group: 1,
            status: 0,
            useAi: true,
            name: values.name?.trim(),
            aiInfo: {
              type: 'WORK_FLOW',
              id: values.aiId.trim(),
              prompt: JSON.stringify({
                query: values.newsContent?.trim(),
                industry: values.targetIndustry?.trim(),
                stream: values.initialIndustryChain?.trim(),
              }),
            },
            operator: getOperator(),
          };

          debouncedSubmit.submit(formData);
        } catch (error) {
          console.error('AI生成提交失败:', error);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  return (
    <Modal
      visible={showDialog}
      maskClosable={false}
      title="AI智能生成热点机会"
      closable={false}
      width={800}
      onCancel={handleCancel}
      footer={
        <Spin spinning={loading}>
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>
          <Button key="submit" type="primary" onClick={handleSubmit}>
            开始生成
          </Button>
        </Spin>
      }
      className={styles.aiGenerateModal}
    >
      <Spin spinning={loading}>
        <div className={styles.modalContent}>
          <Form {...formItemLayout} labelAlign="left">
            <Form.Item label="页面名称" className={styles.formItem}>
              {getFieldDecorator('name', {
                rules: [{ required: true, message: '请输入页面名称' }],
              })(<Input placeholder="请输入页面名称" />)}
            </Form.Item>

            <Form.Item label="AI标识ID" className={styles.formItem}>
              {getFieldDecorator('aiId', {
                initialValue: '13109',
                rules: [
                  { required: true, message: '请输入AI标识ID' },
                  {
                    pattern: /^[a-zA-Z0-9_-]+$/,
                    message: 'AI标识ID只能包含字母、数字、下划线和短横线',
                  },
                ],
              })(<Input placeholder="请输入AI标识ID" />)}
            </Form.Item>

            <Form.Item label="新闻内容" className={styles.formItem}>
              {getFieldDecorator('newsContent')(
                <TextArea
                  placeholder="请输入相关新闻内容，AI将基于此内容生成热点机会分析..."
                  rows={6}
                  maxLength={2000}
                />,
              )}
            </Form.Item>

            <Form.Item label="目标行业" className={styles.formItem}>
                {getFieldDecorator('targetIndustry')(<Input placeholder="请输入目标行业" />)}
            </Form.Item>

            <Form.Item label="初始产业链" className={styles.formItem}>
              {getFieldDecorator('initialIndustryChain', {
                initialValue: '上游：【】；中游：【】；下游：【】；',
              })(
                <TextArea
                  placeholder="请描述初始产业链结构，包括上中下游主要环节（如：上游-锂矿开采，中游-电池制造，下游-整车组装）"
                  rows={4}
                  maxLength={1000}
                />,
              )}
            </Form.Item>
          </Form>
        </div>
      </Spin>
    </Modal>
  );
};

export default Form.create<AiGenerateModalInternalProps>()(AiGenerateModal); 