{"schema": {"type": "object", "properties": {"title": {"title": "tab标题", "type": "string", "ui:width": "60%", "ui:options": {}}, "mainCondition": {"title": "核心条件设置", "type": "", "ui:widget": "conditionParserWidget"}, "condition": {"title": "条件设置", "type": "", "ui:widget": "conditionParserWidget"}}, "required": ["title", "mainCondition", "condition"]}, "displayType": "row", "showDescIcon": true, "labelWidth": 140}