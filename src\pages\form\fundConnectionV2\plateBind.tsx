import React, { useState, useEffect } from 'react';
import api from 'api';
import {
  Button,
  message,
  Popover,
  Table,
  Row,
  Col,
  Input,
  Select,
  Modal,
  Switch,
  PageHeader,
  List,
  Divider,
  InputNumber,
  Popconfirm,
} from 'antd';
import Search from 'antd/lib/input/Search';
import { toast } from '@/utils/message';
import { addTI, removeTI } from './helper';
import { PlateBindProps } from './type';

const { postHashDel, fetchHashAll, postHash } = api;

//
interface iFundRelation {
  insideRate: number;
  outsideRate: number; // 场外基金占比，小数
  fundRate: number; // 投资占比阈值，小数
  scale: number; // 基金规模阈值，单位：千万
  subjectFundMap: any;
}

const initEditInfo = {
  platId: '',
  index: '',
  outfund1: '',
  outfund2: '',
  outfund3: '',
};

const HASHKEY = 'subject_fund_relation_v2';
export default function() {
  const [nowEditInfo, setNowEditInfo] = useState<PlateBindProps>(initEditInfo);
  const [plateModalShow, setPlateModalShow] = useState<boolean>(false);
  const [columnPlateDateSource, setColumnPlateDateSource] = useState<any[]>([]);
  const [isAdd, setIsAdd] = useState(false);

  const columsForPlateFundBind = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
    },
    {
      title: '板块id',
      dataIndex: 'plateId',
      key: 'plateId',
      editable: true,
    },
    {
      title: '场外基金1',
      dataIndex: 'outFund1',
      key: 'outFund1',
      editable: true,
    },
    {
      title: '场外基金2',
      dataIndex: 'outFund2',
      key: 'outFund2',
      editable: true,
    },
    {
      title: '场外基金3',
      dataIndex: 'outFund3',
      key: 'outFund3',
    },
    {
      title: '操作',
      key: 'button',
      render: (item: any, record, index) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginRight: 20 }}
              onClick={() => {
                console.log('record', record, index);
                setPlateModalShow(true);
                setNowEditInfo({ ...record, index: index });
                setIsAdd(false);
              }}
            >
              修改
            </Button>
            <Popconfirm
              title="确认删除?"
              onConfirm={() => {
                setColumnPlateDateSource(prev => {
                  const tempcolumnPlateDateSource = [...prev];
                  tempcolumnPlateDateSource.splice(index, 1);
                  return tempcolumnPlateDateSource;
                });
                postHashDel({ key: HASHKEY, propName: record.plateId })
                  .then(res => {
                    if (res.code === '0000') {
                      toast.success(`删除成功，id=${record.plateId}`);
                    } else {
                      toast.error('删除失败');
                      throw new Error(res.message);
                    }
                  })
                  .catch(err => {
                    toast.error(err);
                  });
              }}
            >
              <Button type="danger">删除</Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  const getPlateFundTable = () => {
    fetchHashAll({ key: HASHKEY })
      .then((res: any) => {
        if (res.code === '0000') {
          const data = res.data || {};
          const _listData =
            Object.keys(data)?.map((item, index) => {
              let beforeAdapteItem = JSON.parse(data[item]);
              return {
                index: index,
                plateId: item,
                outFund1: beforeAdapteItem?.relationFundList[0] || '',
                outFund2: beforeAdapteItem?.relationFundList[1] || '',
                outFund3: beforeAdapteItem?.relationFundList[2] || '',
              };
            }) || [];
          setColumnPlateDateSource(_listData);
          // for (let key in data) {
          //   _listData.push(JSON.parse(data[key]));
          // }
          // setListData(_listData);
        } else {
          message.error('获取列表失败');
        }
      })
      .catch(() => {
        // setIsListLoading(false);
        message.error('获取列表失败');
      });
  };

  useEffect(() => {
    getPlateFundTable();
  }, []);

  /**处理搜索关联关系回调 */
  const onSearchBond = (val: string) => {
    const findItem = columnPlateDateSource.find(e => {
      return e.plateId === val;
    });
    if (findItem) {
      const { plateId, outFund1, outFund2, outFund3 } = findItem;
      Modal.confirm({
        title: '查询到此项',
        content: `${plateId}已关联${outFund1}、${outFund2}、${outFund3}`,
        okText: '确认',
        cancelButtonProps: { style: { display: 'none' } },
      });
    } else {
      Modal.confirm({
        title: '未查询到此项',
        content: ``,
        okText: '确认',
        cancelButtonProps: { style: { display: 'none' } },
      });
    }
  };

  return (
    <div>
      <section>
        <PageHeader
          style={{ width: '80%' }}
          title={`1. 人工指定行业和概念关联基金`}
          extra={
            <div style={{ display: 'flex', flexDirection: 'row' }}>
              <Button
                type="primary"
                style={{ marginRight: 10 }}
                onClick={() => {
                  setPlateModalShow(true);
                  setNowEditInfo({
                    ...initEditInfo,
                    index: columnPlateDateSource.length
                  });
                  setIsAdd(true);
                }}
              >
                新增
              </Button>
              <Search
                placeholder="查询关联关系"
                allowClear
                enterButton="查询"
                style={{ marginLeft: '24px' }}
                onSearch={onSearchBond}
              />
            </div>
          }
        ></PageHeader>
        <Modal
          title={`人工指定行业和概念关联基金编辑`}
          visible={plateModalShow}
          onOk={() => {
            postHash({
              key: HASHKEY,
              propName: nowEditInfo.plateId,
              value: JSON.stringify({
                relationFundList: [
                  nowEditInfo.outFund1,
                  nowEditInfo.outFund2,
                  nowEditInfo.outFund3,
                ].filter(i => !!i),
              }),
            })
              .then(res => {
                if (res.code === '0000') {
                  toast.success('修改成功');
                  setPlateModalShow(false);
                  setColumnPlateDateSource(
                    isAdd ? [...columnPlateDateSource, nowEditInfo] : columnPlateDateSource.map((i, index) => { return nowEditInfo.index === index ? nowEditInfo : i })
                  );
                } else {
                  toast.error(res.message || '修改失败');
                }
              })
              .catch(err => {
                toast.error(err || '修改失败');
              });
          }}
          onCancel={() => {
            setPlateModalShow(false);
          }}
        >
          <Input
            value={isAdd ? removeTI(nowEditInfo.plateId) : nowEditInfo.plateId}
            onChange={e => {
              e.persist();
              setNowEditInfo(prev => {
                return {
                  ...prev,
                  plateId: isAdd ? addTI(e?.target?.value) : e?.target?.value,
                };
              });
            }}
            disabled={!isAdd}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>板块Id：</span>}
            addonAfter={isAdd ? <>.TI</> : null}
          />
          <Input
            value={nowEditInfo.outFund1}
            onChange={e => {
              e.persist();
              setNowEditInfo(prev => {
                console.log(e);
                return {
                  ...prev,
                  outFund1: e?.target?.value,
                };
              });
            }}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>场外基金1：</span>}
          />
          <Input
            value={nowEditInfo.outFund2}
            onChange={e => {
              e.persist();
              setNowEditInfo(prev => {
                return {
                  ...prev,
                  outFund2: e?.target?.value,
                };
              });
            }}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>场外基金2：</span>}
          />
          <Input
            value={nowEditInfo.outFund3}
            onChange={e => {
              e.persist();
              setNowEditInfo(prev => {
                return {
                  ...prev,
                  outFund3: e?.target?.value,
                };
              });
            }}
            style={{ marginBottom: 10, width: 300 }}
            addonBefore={<span>场外基金3：</span>}
          />
        </Modal>
        <div>
          <Table
            columns={columsForPlateFundBind}
            dataSource={columnPlateDateSource}
            rowKey={record => record.index}
          ></Table>
        </div>
      </section>
    </div>
  );
}
