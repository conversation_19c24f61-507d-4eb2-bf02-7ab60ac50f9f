{"type": "page", "id": "u:e58b7b592600", "body": [{"type": "form", "id": "u:61a1addd6e42", "reload": "crud", "api": {"method": "post", "url": "/common_config/kv_data_save", "adaptor": "return {\r\n  status: payload.code === \"0000\" ? 0:-1,\r\n  msg: '提交成功',\r\n}", "data": {"key": "role_rights_white_list", "value": "${ARRAYFILTER(SPLIT(${custId},'\\n'), item => LEN(item) > 0)}"}, "dataType": "form", "requestAdaptor": "api.body.value = JSON.stringify(api.body.value);\r\nconsole.log(api)\r\nreturn api;", "messages": {}}, "body": [{"type": "flex", "className": "p-1", "items": [{"type": "container", "body": [{"type": "button-group", "buttons": [{"type": "input-excel", "label": "导入Excel", "name": "excel", "id": "u:d1f13f45d90f", "parseMode": "object", "inline": false, "mode": "inline", "onEvent": {"change": {"weight": 0, "actions": [{"componentId": "u:613629fdf121", "groupType": "component", "actionType": "setValue", "args": {"value": "${JOIN(ARRAYFILTER(ARRAYMAP(event.data.value, item => TRIM(item.custId)), item => LEN(item) > 0),\"\\n\")}"}}]}}}, {"label": "下载模板", "type": "button", "actionType": "url", "url": "https://o.thsi.cn/thsjj-yytj-docker-cn.yytj-resource/************************************.xlsx", "level": "primary", "className": "mr-5", "id": "u:8a6520e089be"}, {"type": "button", "label": "清空", "onEvent": {"click": {"actions": [{"componentId": "u:613629fdf121", "groupType": "component", "actionType": "clear"}, {"componentId": "u:d1f13f45d90f", "groupType": "component", "actionType": "clear"}]}}, "id": "u:20183577f41a", "level": "primary", "confirmText": "确定要清空吗"}], "id": "u:cac85ad462f6"}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:7454fa35f004"}, {"type": "container", "body": [{"type": "service", "id": "u:9e5938902978", "api": {"method": "get", "url": "/common_config/kv_data_get?key=role_rights_white_list", "adaptor": "return {\r\n  status: payload.code === '0000' ? 0 : -1,\r\n  data: {\r\n    custId: JSON.parse(payload.data).join('\\n')\r\n  }\r\n}"}, "body": [{"type": "textarea", "label": "用户选择", "name": "custId", "id": "u:613629fdf121", "minRows": 3, "maxRows": 5, "placeholder": "请输入custId（每行只能填写一个）", "size": "lg"}]}], "size": "xs", "style": {"position": "static", "display": "block", "flex": "1 1 auto", "flexGrow": 1, "flexBasis": "auto"}, "wrapperBody": false, "isFixedHeight": false, "isFixedWidth": false, "id": "u:bfb063fa99a8"}], "style": {"position": "relative", "inset": "auto", "flexWrap": "nowrap", "flexDirection": "column", "alignItems": "flex-start"}, "id": "u:99e07a3c31a2", "isFixedHeight": false, "isFixedWidth": false}], "feat": "Insert", "dsType": "api", "actions": [{"type": "submit", "label": "提交", "primary": true, "id": "u:bba65b97811c"}]}], "title": "白名单", "pullRefresh": {"disabled": true}}