// 个基开启资讯tab：
export const IS_OPEN_ZX_TAB = {
    all: 'all',
    some: 'some'
} 

export const NEW_ARTICLE = {
    "type": "object",
    "properties": {
      "contentType": {
        "title": "配置内容",
        "type": "number",
        "enum": [
          1,
          2
        ],
        "enumNames": [
          "资讯文章",
          "同顺号文章"
        ],
        "ui:widget": "radio"
      },
      "itemId": {
        "title": "内容ID",
        "type": "string",
        "ui:options": {}
      },
      "funds": {
        "title": "配置个基",
        "type": "string",
        "description": "个基代码之间用英文逗号分隔",
        "ui:options": {}
      },
      "selectedContent": {
        "title": "设置精选",
        "type": "string",
        "enum": [
          '0',
          '1'
        ],
        "enumNames": [
          "否",
          "是"
        ],
        "ui:widget": "radio"
      },
      "label": {
        "title": "标签",
        "type": "string",
        "ui:options": {},
        "ui:hidden": "{{rootValue.selectedContent != '1'}}"
      },
      "sendSelectedHold": {
        "title": "并分发至持仓页 (默认配置七天)",
        "type": "boolean",
        "ui:hidden": "{{rootValue.selectedContent != '1'}}"
      },
      "startTime": {
        "title": "日期选择",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%",
        "ui:hidden": "{{rootValue.selectedContent != '1' || rootValue.sendSelectedHold != true}}"
      },
      "endTime": {
        "title": "-",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%",
        "ui:hidden": "{{rootValue.selectedContent != '1' || rootValue.sendSelectedHold != true}}"
      }
    }
  }

export const OLD_ARTICLE = {
    "type": "object",
    "properties": {
      "contentType": {
        "title": "配置内容",
        "type": "string",
        "ui:disabled": true,
        "enum": [
          1,
          2
        ],
        "enumNames": [
          "资讯文章",
          "同顺号文章"
        ],
        "ui:widget": "radio"
      },
      "itemId": {
        "title": "内容ID",
        "type": "string",
        "ui:disabled": true
      },
      "funds": {
        "title": "配置个基",
        "type": "string",
        "ui:disabled": true,
        "description": "个基代码之间用英文逗号分隔"
      },
      "selectedContent": {
        "title": "设置精选",
        "type": "string",
        "enum": [
          '0',
          '1'
        ],
        "enumNames": [
          "否",
          "是"
        ],
        "ui:widget": "radio"
      },
      "label": {
        "title": "标签",
        "type": "string",
        "ui:hidden": "{{rootValue.selectedContent != '1'}}"
      },
      "sendSelectedHold": {
        "title": "并分发至持仓页 (默认配置七天)",
        "type": "boolean",
        "ui:hidden": "{{rootValue.selectedContent != '1'}}"
      },
      "startTime": {
        "title": "日期选择",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%",
        "ui:hidden": "{{rootValue.selectedContent != '1' || rootValue.sendSelectedHold != true}}"
      },
      "endTime": {
        "title": "-",
        "type": "string",
        "format": "dateTime",
        "ui:width": "50%",
        "ui:hidden": "{{rootValue.selectedContent != '1' || rootValue.sendSelectedHold != true}}"
      }
    }
}

