// 所有页面信息
export type allCompetitionInfo = {
  infoConfig?: Competition<string>;
  recommendConfig?: {
    value: recommendFund<string>;
    editer?: string;
    time?: string;
  };
  noticeConfig?: NoticeFormData<string>;
  mainOperationConfig?: {
    value: MainVenueFormData<PositioItem<string>>;
    editer?: string;
    time?: string;
  };
  holdOperationConfig?: {
    value: PositionFormData<PositioItem<string>>;
    editer?: string;
    time?: string;
  };
  fundOperationConfig?: OutSideFund<string>;
};
// 通用接口
export interface FirmOfferCompetition {
  code: string;
  message: string;
  data?: string | null; // post成功无data，get成功有data
}
// 大赛相关信息
export type Competition<T> = {
  headImage: T;
  etfRewardImage: T;
  fundRewardImage: T;
  rule: T;
  desSignUpBtn: T;
  baseNumber?: T;
  editer?: T;
  time?: T;
  endList?: any;
  file?: any;
};
// 推荐基金展示
export type recommendFund<T> = {
  value: {
    bsjj: {
      companyId: 'bsjj';
      fundCodes: T;
    };
    ctzg: {
      companyId: 'ctzg';
      fundCodes: T;
    };
    dfhzg: {
      companyId: 'dfhzg';
      fundCodes: T;
    };
    fgjj: {
      companyId: 'fgjj';
      etfCodes: T;
    };
    gyrxjj: {
      companyId: 'gyrxjj';
      etfCodes: T;
    };
    gtjj: {
      companyId: 'gtjj';
      etfCodes: T;
      fundCodes: T;
    };
    hajj: {
      companyId: 'hajj';
      fundCodes: T;
    };
    htbrjj: {
      companyId: 'htbrjj';
      etfCodes: T;
    };
    hxjj: {
      companyId: 'hxjj';
      etfCodes: T;
      fundCodes: T;
    };
    htfjj: {
      companyId: 'htfjj';
      fundCodes: T;
    };
    jsjj: {
      companyId: 'jsjj';
      etfCodes: T;
    };
    jsccjj: {
      companyId: 'jsccjj';
      fundCodes: T;
    };
    nfjj: {
      companyId: 'nfjj';
      fundCodes: T;
    };
    phjj: {
      companyId: 'phjj';
      etfCodes: T;
    };
    pajj: {
      companyId: 'pajj';
      etfCodes: T;
    };
    thjj: {
      companyId: 'thjj';
      etfCodes: T;
      fundCodes: T;
    };
    yfdjj: {
      companyId: 'yfdjj';
      etfCodes: T;
    };
    yhjj: {
      companyId: 'yhjj';
      etfCodes: T;
      fundCodes: T;
    };
    zsjj: {
      companyId: 'zsjj';
      fundCodes: T;
    };
    zojj: {
      companyId: 'zojj';
      fundCodes: T;
    };
    wjjj: {
      companyId: 'wjjj';
      fundCodes: T;
    };
  };
  editer?: T;
  time?: T;
};
// 公告及榜单
export type NoticeFormData<T> = {
  noticeContent: T;
  noticeUrl: T;
  startTime: T;
  endTime: T;
  totalListRewardImage: T;
  monthListRewardImage: T;
  weekListRewardImage: T;
  editer?: T;
  time?: T;
};
// 主会场运营位 - 场内ETF
export type MainVenueFormData<T> = {
  def: T;
  cczq: T;
  czzq: T;
  dwzq: T;
  dxzq: T;
  gjzq: T;
  hczq: T;
  zszq: T;
};
// 持仓运营位 - 场内ETF
export type PositioItem<T> = {
  endTime: T;
  operationImage: T;
  startTime: T;
  text: T;
  type: T;
  url: T;
};
// 持仓运营位 - 场内ETF
export type PositionFormData<T> = {
  def: T;
  cczq: T;
  czzq: T;
  dwzq: T;
  dxzq: T;
  gjzq: T;
  hczq: T;
  zszq: T;
};
// 相关运营位 - 场外基金
export type OutSideFund<T> = {
  noticeContent: T;
  noticeUrl: T;
  startTime: T;
  endTime: T;
  mainOperation: {
    endTime: T;
    operationImage: T;
    startTime: T;
    text: T;
    url: T;
  };
  holdOperation: {
    endTime: T;
    operationImage: T;
    startTime: T;
    text: T;
    url: T;
  };
  totalListRewardImage: T;
  monthListRewardImage: T;
  weekListRewardImage: T;
  editer?: T;
  time?: T;
};
// router
export type CusRouter = {
  hash: string;
  pathname: string;
  query: Object;
  search: string;
  state?: allCompetitionInfo;
};
