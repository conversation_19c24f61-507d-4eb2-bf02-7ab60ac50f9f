/**
 * 工具函数
 */
import { TASK_TYPE, TASK_TYPE_TEXT } from './const';

/**
 * 购买类任务，金额限制要求
 */
export function isBuyType(type: keyof typeof TASK_TYPE_TEXT) {
  return !![TASK_TYPE.BUY, TASK_TYPE.FIRST_PURCHASE, TASK_TYPE.HIGH_END_FINANCIAL_MANAGEMENT].find(
    key => key == type,
  );
}

/**
 * 浏览类任务，时长要求
 */
export function isBrowseType(type: keyof typeof TASK_TYPE_TEXT) {
  return !![TASK_TYPE.BROWSE].find(key => key == type);
}

/**
 * 分享类任务
 */
export function isShareType(type: keyof typeof TASK_TYPE_TEXT) {
  return !![TASK_TYPE.SHARE].find(key => key == type);
}

/**
 * 奖励克数
 */
export function isRewardGram(type: keyof typeof TASK_TYPE_TEXT) {
  return !![TASK_TYPE.FIRST_PURCHASE, TASK_TYPE.HIGH_END_FINANCIAL_MANAGEMENT].find(
    key => key == type,
  );
}
