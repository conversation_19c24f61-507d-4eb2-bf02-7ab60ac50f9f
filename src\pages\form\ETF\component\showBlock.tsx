import React from 'react';
import { Button, Popconfirm, Input } from 'antd';
import FundSelect from './fundSelect'

export default function ({
    obj,
    index,
    setHotBlock,
    hotBlock,
    hotBlockItem,
    disabled
}: {
    obj: any
    index: number
    setHotBlock: any
    hotBlock: any
    hotBlockItem: any
    disabled: boolean
}) {

    /**
     * 选择ETF
     * @param code 交易代码
     */
    function handleSelect(code: string): void {
        let _hotBlock = [...hotBlock]
        _hotBlock[index] = obj.filter((item: any) => item.tradecode === code)[0]
        setHotBlock(_hotBlock)
        console.log(_hotBlock)
    }

    /**
     * 清空
     */
    function clearSelect(): void {
        let _hotBlock = [...hotBlock]
        _hotBlock[index] = {}
        setHotBlock(_hotBlock)
    }


    return (
        <div style={{
            display: 'inline-block',
            width: '400px',
            marginBottom: '20px'
        }}>
            <FundSelect 
            placeholder='ETF基金 ETF名称'
            obj = {obj}
            handleSelect={handleSelect}
            item={hotBlockItem}
            width={300}
            disabled={disabled}
            />
            <Popconfirm
                title="请问是否清空"
                okText="确定"
                cancelText="取消"
                onConfirm={clearSelect}
                placement="bottom"
            >
                <Button type="primary" className="g-center g-mt20" disabled={disabled}>清空</Button>
            </Popconfirm>
            
        </div>
    )
}