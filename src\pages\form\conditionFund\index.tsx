import React,{useState,useEffect,FC} from 'react'
import ConfigSelect from './components/selectModel';
import api from 'api'
import { KEY,STRICT_PROPNAME,VIP_PROPNAME } from './const';
import Card from './components/card';
import moment from 'moment';
import { Button, message, Spin } from 'antd';
import { checkForm as strictSelectionCheckForm } from './pageConfig/strictSelection/check';
import { checkForm as vipSelectionCheckForm } from './pageConfig/vipSelection/check';
import configHelper from './pageConfig/configHelper';
import Item from 'antd/lib/list/Item';
const { fetchCBAS, fetchOLAS,postHash,fetchHashAll } = api;
export interface IProps{

}
const conditionFund:FC<IProps>=()=>{
    const [init, setInit] = useState(false);
    const [config, setConfig] = useState<{ utype: string[], platform: string[] }>({ utype: [], platform: [] });
    const [kycTag, setKycTag] = useState([]);
    const [olasTag, setOlasTag] = useState([]);
    const [isModify, setModify] = useState(false);
    const [allData, setAllData] = useState({})

    const [strictConditionData,setStrictConditionData]=useState([])
    const [vipConditionData,setVipConditionData]=useState([])
    const [loading,setLoading]=useState(false);
    const handleChange = (data: any) => {
        console.log('configData', data);
        setConfig(data);
    }

    // useEffect(() => {
    //     fetchCBAS().then((res: any) => {
    //         const { code, data } = res;
    //         if (code === '0000') {
    //             if (data) {
    //                 setKycTag(data)
    //             }
    //         } else {
    //             message.error(res?.message || '系统繁忙');
    //         }
    //     }).catch((e: Error) => {
    //         message.error(e?.message || '系统繁忙');
    //     })
    // }, [])
    const initData=()=>{
        setLoading(true);
        fetchHashAll({
            key:KEY
        }).then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
              
                let initData: any = data , _data: any = [];
                console.log(initData);
                function processData(floorType,item){
                    let obj: any = {
                        formData: configHelper(floorType).fetchData(item),
                        // configData: {
                        //     platform: item.platform || [],
                        //     utype: item.utype || [],
                        // },
                        
                        // relationData: {
                            
                        //     targetType: item.targetType,
                        //     kycLogic: item.kycLogic,
                        //     kycs: item.kycs,
                        //     olasId: item.olasId,
                           
                        // },
                        // other: {
                        //     sv: item.sv,
                        //     needLogin: item.needLogin,
                        //     updateTime: item.updateTime
                        // }
                    }
                   
                    return obj
                }

                const normalStrictConditionData=initData?.[STRICT_PROPNAME]?JSON.parse(initData?.[STRICT_PROPNAME]).value?.map((item)=>{
                    return processData(STRICT_PROPNAME,item)
                })||[]:[];
                const normalVipConditionData=initData?.[VIP_PROPNAME]?JSON.parse(initData?.[VIP_PROPNAME]).value?.map((item)=>{
                    return processData(VIP_PROPNAME,item)
                })||[]:[];
                // initData?.confs?.forEach((item: any) => {
                //     let obj: any = {
                //         formData: fetchData(item, floorType),
                //         configData: {
                //             platform: item.platform || [],
                //             utype: item.utype || [],
                //         },
                        
                //         relationData: {
                            
                //             targetType: item.targetType,
                //             kycLogic: item.kycLogic,
                //             kycs: item.kycs,
                //             olasId: item.olasId,
                           
                //         },
                //         other: {
                //             sv: item.sv,
                //             needLogin: item.needLogin,
                //             updateTime: item.updateTime
                //         }
                //     }
                   
                //     _data.push(obj);
                // })
                setStrictConditionData(normalStrictConditionData);
                setVipConditionData(normalVipConditionData)
                // setOriginData(_data); 
                setAllData(res.data||{});
               
                console.log('initdata:', _data);
                setInit(true);
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            
            message.error(e?.message || '系统繁忙');
        }).finally(()=>{
           
            setLoading(false);
        })
    }
    // useEffect(() => {
    //     fetchOLAS().then((res: any) => {
    //         const { code, data } = res;
    //         if (code === '0000') {
    //             if (data) {
    //                 let _olasTag: any = [];
    //                 for (let prop in data) {
    //                     _olasTag.push({ "groupid": prop, "description": data[prop].description });
    //                 }
    //                 setOlasTag(_olasTag);
    //             }
    //         } else {
    //             message.error(res?.message || '系统繁忙');
    //         }
    //     }).catch((e: Error) => {
    //         message.error(e?.message || '系统繁忙');
    //     })
    // }, []) 

    useEffect(() => {
        initData();
    }, [])

    const onSubmit = async () => {
        // let _value: any = [];
        // let _sendData: any = {};
        
        console.log(strictConditionData,vipConditionData)
        // for (let i = 0, len = originData?.length; i < len; i++) {
        //     const { formData, configData, relationData, commentList } = originData[i];
            
        //      if (floorType === 'coupon') {
        //         if (!formData.name) {
        //             message.error(`请填写第${i+1}项优惠券运营位名称`)
        //             return;
        //         }
        //         if (!formData.page) {
        //             message.error(`请上传第${i+1}项优惠券图标`)
        //             return;
        //         }
        //         if (!formData.content) {
        //             message.error(`请填写第${i+1}项优惠券展示文案`)
        //             return;
        //         }
        //         if (!formData.packetId) {
        //             message.error(`请填写第${i+1}项优惠券ID`)
        //             return;
        //         }
        //         if (!formData.receiveButton) {
        //             message.error(`请填写第${i+1}项领取按钮文描`)
        //             return;
        //         }
        //         if (!formData.watchButton) {
        //             message.error(`请填写第${i+1}项查看按钮文描`)
        //             return;
        //         }
        //         if (!formData.watchUrl) {
        //             message.error(`请填写第${i+1}项跳转链接`)
        //             return;
        //         }
        //         try {
        //             let result = await checkCoupon({}, formData.packetId);
        //             const { status_code, status_msg } = result;
        //             if (status_code === 0) {
        //                 if (result?.data?.length > 0) {
        //                     for (let i = 0, len = result.data.length; i < len; i++) {
        //                         const { couponStartDate, couponEndDate, couponId } = result.data[i];
        //                         if ( couponStartDate && couponEndDate ) {
        //                             let time = timeFormat2();
        //                             time = time.replace(/[^\d]/g, '');
        //                             if (time < couponStartDate || time > couponEndDate) {
        //                                 message.error(`当前优惠券ID-${couponId}未处于生效状态`);
        //                                 return;
        //                             }
        //                             if (formData.startTime) {
        //                                 let startTime = formData.startTime.replace(/[^\d]/g, '');
        //                                 if (startTime < couponStartDate) {
        //                                     message.error('模块的时间范围必须在优惠券的时间范围之内');
        //                                     return;
        //                                 }
        //                             }
        //                             if (formData.endTime) {
        //                                 let endTime = formData.endTime.replace(/[^\d]/g, '');
        //                                 if (endTime >= couponEndDate) {
        //                                     message.error('模块的时间范围必须在优惠券的时间范围之内');
        //                                     return;
        //                                 }
        //                             }
        //                         } else {
        //                             message.error(`接口未返回优惠券ID-${couponId}生效时间`);
        //                             return;
        //                         }
        //                     }
        //                 } else {
        //                     message.error('请检查优惠券ID是否正确');
        //                     return;
        //                 }
        //             } else {
        //                 message.error(status_msg || '网络请求错误，请稍后再试');
        //                 return;
        //             }
        //         } catch(e) {
        //             message.error(e.message);
        //             return;
        //         }
        //     }
          
        //      else if (floorType === 'threesteps') {
        //         if (!formData.name) {
        //             message.error(`请填写第${i+1}项三步走策略名称`)
        //             return;
        //         }
        //         if (!formData.page) {
        //             message.error(`请上传第${i+1}项小图标`)
        //             return;
        //         }
        //         if (!formData.title) {
        //             message.error(`请填写第${i+1}项模块标题`)
        //             return;
        //         }

        //         let activityIdObj: any = {
        //             coupon: '',
        //             gold: []
        //         }
        //         for (let j = 0; j < 3; j++) {
        //             let obj = formData?.steps[j] ?? {};
        //             const { type, activityId, page, status, url, button, buttonUrl,rightTime } = obj;
        //             if (!type) {
        //                 message.error(`请选择第${i+1}项步骤${j+1}权益类型`)
        //                 return;
        //             }
        //             if (['1','2', '3'].includes(type) && !activityId) {
        //                 message.error(`请填写第${i+1}项步骤${j+1}${type === '1' ? '体验金活动' : type === '2' ? '优惠券' : '活动'}ID`)
        //                 return;
        //             }
        //             if (!page) {
        //                 message.error(`请填写第${i+1}项步骤${j+1}图片`)
        //                 return;
        //             }
        //             if (type === '3' && !rightTime) {
        //                 message.error(`请填写第${i+1}项步骤${j+1}金牛会员权益时间`)
        //                 return;
        //             }
        //             if (!status) {
        //                 message.error(`请填写第${i+1}项步骤${j+1}状态文描`)
        //                 return;
        //             }
        //             if (!url) {
        //                 message.error(`请填写第${i+1}项步骤${j+1}权益查看跳转链接`)
        //                 return;
        //             }
        //             let result = checkUrl(url, `第${i+1}项步骤${j+1}`);
        //             if (result.isError) {
        //                 message.error(result.msg);
        //                 return;
        //             }
        //             if (!button) {
        //                 message.error(`请填写第${i+1}项步骤${j+1}按钮文描`)
        //                 return;
        //             }
        //             if (j === 2 ) {
        //                 if (!buttonUrl) {
        //                     message.error(`请填写第${i+1}项步骤${j+1}跳转链接`)
        //                     return;
        //                 } else {
        //                     let result = checkUrl(buttonUrl, `第${i+1}项步骤${j+1}`);
        //                     if (result.isError) {
        //                         message.error(result.msg);
        //                         return;
        //                     }
        //                 }
                        
        //             }
        //             if (type === '1') {
        //                 activityIdObj.gold.push(activityId);
        //             }
        //             if (type === '2') {
        //                 activityIdObj.coupon += activityId + ',';
        //             }
        //         }
        //         if (!formData.finishButton) {
        //             message.error(`请填写第${i+1}项完成按钮文描`)
        //             return;
        //         }
        //         try {
        //             let promiseAll = []
        //             if (activityIdObj.coupon) {
        //                 let promiseFace = await checkCoupon({}, activityIdObj.coupon);
        //                 promiseAll.push(promiseFace);
        //             }
        //             if (activityIdObj.gold.length > 0) {
        //                 for (let i = 0, len = activityIdObj.gold.length; i < len; i++ ) {
        //                     let promiseFace = await postActivityDetails({
        //                         type: 'query',
        //                         activityIndex: activityIdObj.gold[i]?.trim()
        //                     });
        //                     promiseAll.push(promiseFace);
        //                 }
        //             }
        //             let resArr = await Promise.all(promiseAll);
        //             if (activityIdObj.coupon) {
        //                 let res0 = resArr[0];
        //                 const { status_code, status_msg } = res0;
        //                 if (status_code === 0) {
        //                     if (res0?.data?.length > 0) {
        //                         for (let i = 0, len = res0.data.length; i < len; i++) {
        //                             const { couponStartDate, couponEndDate, couponId } = res0.data[i];
        //                             if ( couponStartDate && couponEndDate ) {
        //                                 let time = timeFormat2();
        //                                 time = time.replace(/[^\d]/g, '');
        //                                 if (time < couponStartDate || time > couponEndDate) {
        //                                     message.error(`当前优惠券ID-${couponId}未处于生效状态`);
        //                                     return;
        //                                 }
        //                                 if (formData.startTime) {
        //                                     let startTime = formData.startTime.replace(/[^\d]/g, '');
        //                                     if (startTime < couponStartDate) {
        //                                         message.error('模块的时间范围必须在优惠券的时间范围之内');
        //                                         return;
        //                                     }
        //                                 }
        //                                 if (formData.endTime) {
        //                                     let endTime = formData.endTime.replace(/[^\d]/g, '');
        //                                     if (endTime >= couponEndDate) {
        //                                         message.error('模块的时间范围必须在优惠券的时间范围之内');
        //                                         return;
        //                                     }
        //                                 }
        //                             } else {
        //                                 message.error(`接口未返回优惠券ID-${couponId}生效时间`);
        //                                 return;
        //                             }
        //                         }
        //                     } else {
        //                         message.error('请检查优惠券ID是否正确');
        //                         return;
        //                     }
        //                 } else {
        //                     message.error(status_msg || '网络请求错误，请稍后再试');
        //                     return;
        //                 }
        //             }
        //             if (activityIdObj.gold.length > 0) {
        //                 let start = activityIdObj.coupon ? 1 : 0;
        //                 for (let i = start, len = resArr.length; i < len; i++ ) {
        //                     const { code } = resArr[i];
        //                     if (code === '0000') {
        //                         if (resArr[i]?.data) {
        //                             let { startTime, endTime, indexStr } = resArr[i]?.data;
        //                             if ( startTime && endTime ) {
        //                                 let time = timeFormat2();
        //                                 time = time.replace(/[^\d]/g, '');
        //                                 startTime = startTime.replace(/[^\d]/g, '');
        //                                 endTime = endTime.replace(/[^\d]/g, '');
        //                                 if (time < startTime || time > endTime) {
        //                                     message.error(`当前体验金活动ID-${indexStr}未处于生效状态`);
        //                                     return;
        //                                 }
        //                                 if (formData.startTime) {
        //                                     let formStartTime = formData.startTime.replace(/[^\d]/g, '');
        //                                     if (formStartTime < startTime) {
        //                                         message.error('模块的时间范围必须在体验金活动的时间范围之内');
        //                                         return;
        //                                     }
        //                                 }
        //                                 if (formData.endTime) {
        //                                     let formEndTime = formData.endTime.replace(/[^\d]/g, '');
        //                                     if (formEndTime >= endTime) {
        //                                         message.error('模块的时间范围必须在体验金活动的时间范围之内');
        //                                         return;
        //                                     }
        //                                 }
        //                             } else {
        //                                 message.error(`接口未返回体验金活动ID-${indexStr}生效时间`);
        //                                 return;
        //                             }
        //                         } else {
        //                             message.error('请检查体验金活动ID是否正确');
        //                             return;
        //                         }
        //                     } else {
        //                         message.error(resArr[i]?.message || '网络请求错误，请稍后再试');
        //                         return;
        //                     }
        //                 }
        //             }
        //         }catch(err) {
        //             message.error(err.message);
        //             return;
        //         }
        //     }
          
        //     if (formData.startTime && formData.endTime) {
        //         let startTime = formData.startTime.replace(/[^\d]/g, '');
        //         let endTime = formData.endTime.replace(/[^\d]/g, '');
        //         if (startTime >= endTime) {
        //             message.error(`第${i+1}项开始时间应早于结束时间`);
        //             return;
        //         }
        //     } 
           
        //     if ([ 'coupon'].includes(floorType)) {
        //         let result = checkUrl(formData.jumpAction, `第${i+1}项`);
        //         if (result.isError) {
        //             message.error(result.msg);
        //             return;
        //         }
        //     } else if (['threesteps'].includes(floorType)) {
        //         let result = checkUrl(formData.url, `第${i+1}项`);
        //         if (result.isError) {
        //             message.error(result.msg);
        //             return;
        //         }
        //     } 
           
        //     if (configData.platform.length === 0) {
        //         message.error(`请选择第${i+1}项适用平台`)
        //         return
        //     } else if (configData.utype.length === 0) {
        //         message.error(`请选择第${i+1}项用户类型`)
        //         return
        //     }
        //     if (!relationData) {
        //         message.error(`请先保存第${i+1}项指定用户数据配置`)
        //         return
        //     }
        // }
        //校验
        
        //校验-1  严选条件
        if(!strictConditionData.every((item)=>{
            return configHelper(STRICT_PROPNAME)?.checkForm(item.formData)
        })){
            return ;
        }
        //校验-2  牛人选基
        if(!vipConditionData.every((item)=>{
            return configHelper(VIP_PROPNAME)?.checkForm(item.formData)
        })){
            return ;
        }

        //数据整理
        function formatConditionData(floorType,val){
            console.log("val",val)
            // let _data: any = {};
            // let realData={};
            // if (val.relationData?.userType === '1') {
            //     const {blackCustId, whiteCustId, ...other} = val.relationData;
            //     realData = {...other}
            // } else {
            //     const {blackUserId, whiteUserId, blackCustId, whiteCustId, ...other} = val.relationData;;
            //     realData = {
            //         ...other,
            //         blackUserId: blackCustId,
            //         whiteUserId: whiteCustId
            //     }
            // }
            // if (val.relationData?.targetType === 'kyc') {
            //     realData.olasId = '';
            //     realData.olasType = '';
            // } else if (val.relationData?.targetType === 'olas') {
            //     realData.kycLogic = '';
            //     realData.kycs = [];
            // }
            
            return  { ...configHelper(floorType)?.parseData(val.formData)};
        }
        //数据整理-1  严选条件
        const strictSelectionSubmitData={
            value:strictConditionData.map((item)=>{
                return formatConditionData(STRICT_PROPNAME,item)
            }),
            lastEditor:localStorage.name,
            updateTime: moment().format("YYYY-MM-DD HH:mm:ss"),
        }
        //数据整理-2  牛人选基
        const vipSelectionSubmitData={
            value:vipConditionData.map((item)=>{
                return formatConditionData(VIP_PROPNAME,item)
            }),
            lastEditor:localStorage.name,
            updateTime: moment().format("YYYY-MM-DD HH:mm:ss"),
        }
        console.log(strictConditionData,vipConditionData)
        //提交数据
        setLoading(true);
        //提交数据-1  严选条件
       try{
        const strictRes=  await postHash({
            key:KEY,
            propName:STRICT_PROPNAME,
            value:JSON.stringify(strictSelectionSubmitData)
        })
        if(strictRes?.code!=="0000"){
            throw new Error(`严选条件保存失败 msg:${strictRes?.message}`)
        }
    //提交数据-2 牛人选基
        const vipRes= await postHash({
                key:KEY,
                propName:VIP_PROPNAME,
                value:JSON.stringify(vipSelectionSubmitData)
            })

            if(vipRes?.code!=="0000"){
                throw new Error(`牛人选基保存失败 msg:${vipRes?.message}`)
            }

            message.success("保存成功")
        }catch(e){
            message.error(e.message);
        }
        setLoading(false);
        initData();
       
    }
    if (!init) {
        return (
            <div>加载中</div>
        );
    }
    console.log("isModify",isModify)
    return <div>
         {/* <ConfigSelect handleChange={handleChange} isHead={true} /> */}
        <Spin spinning={loading}>
        <Button type="primary" style={{marginBottom: 20}} onClick={onSubmit} disabled={!isModify}>保存</Button>
            <h3>严选条件</h3>
         <Card floorType={STRICT_PROPNAME} config={config} isModify={isModify} setModify={setModify} kycTag={kycTag} olasTag={olasTag} originData={strictConditionData} setOriginData={setStrictConditionData}/>
         <h3>牛人选基</h3>
         <Card floorType={VIP_PROPNAME} config={config} isModify={isModify} setModify={setModify} kycTag={kycTag} olasTag={olasTag} originData={vipConditionData} setOriginData={setVipConditionData}/>
        </Spin>
    </div>
}
export default conditionFund
