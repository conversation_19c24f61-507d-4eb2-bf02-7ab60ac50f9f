export function getGridSchema (isEdit) {
	console.log(isEdit, 'isEdit');
	return {
		"type": "object",
		"required": [
			"endTime",
			"beginTime",
			"status",
			"url",
			"urlIos",
			"urlAndroid",
			"urlType",
			"iconNight",
			"iconDay",
			"gridName"
		],
		"properties": {
			"gridId": {
				"title": "宫格id",
				"type": "string",
				"pattern": /^\w+$/,
				"description": "需要用英文输入，唯一标识符，不能重复",
				"ui:options": {},
				"ui:disabled": true,
				"default": (new Date()).valueOf(),
			},
			"gridName": {
				"title": "宫格名称",
				"type": "string",
				// "pattern": /^\w+$/,
				"description": "",
				"ui:options": {},
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"iconDay": {
				"title": "图标-白天",
				"type": "string",
				"ui:widget": "uploadImg",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"iconNight": {
				"title": "图标-黑夜",
				"type": "string",
				"ui:widget": "uploadImg",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"urlType": {
				"title": "链接类型",
				"type": "string",
				"enum": [
					"1",
					"2"
				],
				"enumNames": [
					"H5",
					"客户端"
				],
				"ui:widget": "radio",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"urlPlatform": {
				"title": "ios",
				"type": "string",
				"ui:widget": "checkbox",
				"ui:className": "checkbox-zone",
				'ui:hidden': (formData: any) => formData.urlType === '1',
				"ui:disabled": isEdit === '3' ? true: false,
				"displayType": "row",
				"ui:width": "20%",
				"default": true,
				"labelWidth": 50,
			},
			"urlPlatformAndroid": {
				"title": "安卓",
				"type": "string",
				"ui:className": "checkbox-zone",
				"ui:widget": "checkbox",
				'ui:hidden': (formData: any) => formData.urlType === '1',
				"ui:disabled": isEdit === '3' ? true: false,
				"ui:displayType": "row",
				"ui:width": "20%",
				"labelWidth": 50,
			},
			"urlVer": {
				"title": 'ios版本号',
				"description": '最低版本号控制',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatform !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"urlVerAndroid": {
				"title": '安卓版本号',
				"description": '最低版本号控制',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatformAndroid !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			'urlAndroid': {
				"title": '安卓跳转链接',
				"description": '',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatformAndroid !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			'urlIos': {
				"title": 'ios跳转链接',
				"description": '',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatform !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			'url': {
				"title": '跳转链接',
				"description": '',
				'ui:hidden': (formData: any) => formData.urlType === '2',
				"type": 'string',
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"tag": {
				"title": "角标",
				"pattern": /^[\u4e00-\u9fa5]{1,2}$|^[A-Za-z0-9]{1,4}$/,
				"description": "可配置new、hot等不超过4个字母，或者两个文字如快速、首发、便捷、活动等",
				"type": "string",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"status": {
				"title": "启用状态",
				"type": "string",
				"enum": [
					"1",
					"2",
					'3'
				],
				"enumNames": [
					"启用",
					"不启用",
					"自定义启用时间"
				],
				"ui:widget": "radio",
				"default": "0",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"beginTime": {
				"ui:labelWidth": 150,
				"title": "开始时间",
				"type": "string",
				"format": "dateTime",
				// "ui:width": "46%",
				'ui:hidden': (formData: any) => formData.status !== '3',
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"endTime": {
				"ui:labelWidth": 150,
				"title": "结束时间",
				"type": "string",
				"format": "dateTime",
				// "ui:width": "46%",
				'ui:hidden': (formData: any) => formData.status !== '3',
				"ui:disabled": isEdit === '3' ? true: false,
			},

		}
	}
}

export function getCardSchema (isEdit) {
	console.log(isEdit, 'isEdit');
	return {
		"type": "object",
		"required": [
			"endTime",
			"beginTime",
			"status",
			"url",
			"urlIos",
			"urlAndroid",
			"urlType",
			"iconDay",
			"iconNight",
			"imageType",
			"cardName",
			"cardId",
		],
		"properties": {
			"cardId": {
				"title": "卡片id",
				"type": "string",
				"pattern": /^\w+$/,
				"description": "需要用英文输入，唯一标识符，不能重复",
				"ui:options": {},
				"ui:disabled": true,
				"default": (new Date()).valueOf(),
			},
			"cardName": {
				"title": "卡片名称",
				"type": "string",
				// "pattern": /^\w+$/,
				"description": "",
				"ui:options": {},
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"iconDay": {
				"title": "背景图片-白天",
				"type": "string",
				"ui:widget": "uploadImg",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"iconNight": {
				"title": "背景图片-黑夜",
				"type": "string",
				"ui:widget": "uploadImg",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"imageType": {
				"title": "图片类型",
				"type": "string",
				"enum": [
					"1",
				],
				"enumNames": [
					"图片",
				],
				"ui:widget": "radio",
				"default": "1",
				"ui:disabled": true
			},
			"urlType": {
				"title": "链接类型",
				"type": "string",
				"enum": [
					"1",
					"2"
				],
				"enumNames": [
					"H5",
					"客户端"
				],
				"ui:widget": "radio",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"urlPlatform": {
				"title": "ios",
				"type": "string",
				"ui:widget": "checkbox",
				"ui:className": "checkbox-zone",
				'ui:hidden': (formData: any) => formData.urlType === '1',
				"ui:disabled": isEdit === '3' ? true: false,
				"displayType": "row",
				"ui:width": "20%",
				"default": true,
				"labelWidth": 50,
			},
			"urlPlatformAndroid": {
				"title": "安卓",
				"type": "string",
				"ui:className": "checkbox-zone",
				"ui:widget": "checkbox",
				'ui:hidden': (formData: any) => formData.urlType === '1',
				"ui:disabled": isEdit === '3' ? true: false,
				"ui:displayType": "row",
				"ui:width": "20%",
				"labelWidth": 50,
			},
			"urlVer": {
				"title": 'ios版本号',
				"description": '最低版本号控制',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatform !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"urlVerAndroid": {
				"title": '安卓版本号',
				"description": '最低版本号控制',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatformAndroid !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			'urlAndroid': {
				"title": '安卓跳转链接',
				"description": '',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatformAndroid !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			'urlIos': {
				"title": 'ios跳转链接',
				"description": '',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '1' || formData.urlPlatform !== true,
				"ui:disabled": isEdit === '3' ? true: false,
			},
			'url': {
				"title": '跳转链接',
				"description": '',
				"type": 'string',
				'ui:hidden': (formData: any) => formData.urlType === '2',
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"status": {
				"title": "启用状态",
				"type": "string",
				"enum": [
					"1",
					"2",
					'3'
				],
				"enumNames": [
					"启用",
					"不启用",
					"自定义启用时间"
				],
				"ui:widget": "radio",
				"default": "0",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"beginTime": {
				"ui:labelWidth": 150,
				"title": "开始时间",
				"type": "string",
				"format": "dateTime",
				// "ui:width": "46%",
				'ui:hidden': (formData: any) => formData.status !== '3',
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"endTime": {
				"ui:labelWidth": 150,
				"title": "结束时间",
				"type": "string",
				"format": "dateTime",
				// "ui:width": "46%",
				'ui:hidden': (formData: any) => formData.status !== '3',
				"ui:disabled": isEdit === '3' ? true: false,
			},

		}
	}
}

export function getPageSchema (isEdit) {
	console.log(isEdit, 'isEdit');
	return {
		"type": "object",
		"required": [
			"endTime",
			"beginTime",
			"status",
			"pageName",
			"pageId",
		],
		"properties": {
			"pageId": {
				"title": "页面id",
				"type": "string",
				"pattern": /^\w+$/,
				"description": "需要用英文输入，唯一标识符，不能重复",
				"ui:options": {},
				"ui:disabled": true,
				"default": (new Date()).valueOf(),
			},
			"pageName": {
				"title": "方案名称",
				"type": "string",
				// "pattern": /^\w+$/,
				"description": "",
				"ui:options": {},
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"status": {
				"title": "启用状态",
				"type": "string",
				"enum": [
					"1",
					"2",
					'3'
				],
				"enumNames": [
					"启用",
					"不启用",
					"自定义启用时间"
				],
				"ui:widget": "radio",
				"default": "0",
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"beginTime": {
				"ui:labelWidth": 150,
				"title": "开始时间",
				"type": "string",
				"format": "dateTime",
				// "ui:width": "46%",
				'ui:hidden': (formData: any) => formData.status !== '3',
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"endTime": {
				"ui:labelWidth": 150,
				"title": "结束时间",
				"type": "string",
				"format": "dateTime",
				// "ui:width": "46%",
				'ui:hidden': (formData: any) => formData.status !== '3',
				"ui:disabled": isEdit === '3' ? true: false,
			},
			"grid": {
				"title": "宫格区域",
				"type": "string",
				"ui:widget": "dragComponent",
				"ui:width": "100%",
				// "ui:displayType": "row"
			},
			"card": {
				"title": "卡片区域",
				"type": "string",
				"ui:widget": "dragComponent",
				"ui:width": "100%",
				// "ui:displayType": "row"
			},
		},
		"displayType": "column"
	}
}

export const KEYS = {
	grid: 'etf-zone-grid',
	card: 'etf-zone-card',
	page: 'etf-zone-page'
}

export const PROPKEYS = {
	grid: 'gridId',
	card: 'cardId',
	page: 'pageId'
}

export const TITLES = {
	grid: {
		title: '宫格id',
		name: '宫格名称',
		nameKey: 'gridName',
		add: '增加宫格',
		drawerTitle: '宫格',
	},
	card: {
		title: '卡片id',
		name: '卡片名称',
		nameKey: 'cardName',
		add: '增加卡片',
		drawerTitle: '卡片',
	},
	page: {
		title: '方案id',
		name: '方案名称',
		nameKey: 'pageName',
		add: '增加方案',
		drawerTitle: '方案'
	}
}