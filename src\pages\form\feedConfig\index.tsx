import React, { useState, useEffect } from 'react';
import styles from './index.less';
import classnames from 'classnames';
import api from 'api';
import { Button, message, Table, Pagination, Select, ConfigProvider, Popconfirm } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import UploadImg from '@/pages/frontend/compoment/uploadImg';
import { checkoutInput } from '../organGroup/fn';
const { Option } = Select;
import { EditData, tableData, Col, editKey } from './type';
const { fetchFeedConfig, addFeedConfig, offFeedConfig, fetchFeedTagList } = api;

const mockTableData: tableData[] = [
  {
    key: '0',
    fundPoolId: '物品id-1',
    icon: 'https://trade.5ifund.com:8443/testingimgsrc/fund/a05d4d3e47c44d998fea5021fc30c126.png',
    marketReason: '标题',
    buttonReason: '去购买',
    
    poolStatus: '0',
    createUser: 'admin',
    updateUser: 'hkt',
    updateTime: '2021-10-29 10:38:10',
  },
  {
    key: '1',
    fundPoolId: '物品id-1',
    icon: 'https://trade.5ifund.com:8443/testingimgsrc/fund/a05d4d3e47c44d998fea5021fc30c126.png',
    marketReason: '标题',
    buttonReason: '去购买',
    
    poolStatus: '1',
    createUser: 'admin',
    updateUser: 'hkt',
    updateTime: '2021-10-29 10:38:10',
  },
]

const keyList = ['fundPoolId', 'icon', 'marketReason', 'buttonReason'];
const defaultEdit: EditData = { fundPoolId: '', icon: '', marketReason: '', buttonReason: '' };

export default function FeedConfig () {
  const [init, setInit] = useState(false);
  const [editData, setEditData] = useState<EditData>(defaultEdit);
  const [tableData, setTableData] = useState<tableData[]>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [tableTotal, setTableTotal] = useState(0);
  const [tagList, setTagList] = useState<string[]>([]);

  useEffect(() => {
    Init();
  }, [])
  useEffect(() => {
    if (init) fetchTable();
  }, [pageNum, pageSize])

  const Init = () => {
    fetchTable();
    fetchTagList();
    setInit(true);
  }
  // 选择器选项
  const tagOption = () => {
    return tagList.map((tag, index) => (
      <Option key={index} value={tag}>{tag}</Option>
    ))
  }
  // 渲染 操作列
  const renderOption = (text: string, row: any, index: number) => {
    const ifUp = row.poolStatus === '1';
    return (
      <Button
        type="primary"
        disabled={!ifUp}
        onClick={() => handleOptionClick(index)}
      >
        下线
      </Button>
    )
  }
  // 渲染 icon
  const renderIcon = (text: string, row: tableData) => {
    return <img className={styles['m-table-icon']} src={row.icon || ''} />
  }

  let columns: Col[] = [
    { dataIndex: 'fundPoolId', key: 'fundPoolId', title: '物品id' },
    { dataIndex: 'icon', key: 'icon', title: '运营icon', render: renderIcon },
    { dataIndex: 'marketReason', key: 'marketReason', title: '营销文案', len: 14 },
    { dataIndex: 'buttonReason', key: 'buttonReason', title: '按钮文案', len: 3 },
    { dataIndex: 'createUser', key: 'createUser', title: '创建人', tableItem: true },
    { dataIndex: 'updateUser', key: 'updateUser', title: '下线人', tableItem: true },
    { dataIndex: 'updateTime', key: 'updateTime', title: '最近修改时间', tableItem: true },
    { dataIndex: 'option', key: 'option', title: '操作', render: renderOption, tableItem: true },
  ]

  // 获取策略
  const fetchTable = () => {
    fetchFeedConfig({pageNum, pageSize}).then((res: any) => {
      console.log('fetchFeedConfig', res)
      if (res?.status_code === 0) {
        res = res.data;
        const _tableData = (res?.feedInfoList || []).map((item: any, index: number) => {
          item.key = index.toString();
          return item;
        })
        setTableData(_tableData);
        setTableTotal(Number(res?.size || ''));
      } else {
        console.log(res?.status_msg);
        message.error(res?.status_msg);
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error(e.message);
    })
  }
  // 获取tag列表
  const fetchTagList = () => {
    fetchFeedTagList().then((res: any) => {
      if (res?.status_code === 0) {
        res = res.data;
        const _tagList = res.map((item: any) => {
          return item.resultPageTag;
        })
        setTagList(_tagList);
      } else {
        message.error(res?.status_msg);
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error(e.message);
    })
  }
  // 新增策略
  const addTable = (data: EditData) => {
    let _data: tableData = {
      ...data,
      createUser: JSON.parse(localStorage.getItem('name') || ''),
    };
    addFeedConfig(_data).then((res: any) => {
      console.log('addFeedConfig', res)
      if (res?.status_code === 0) {
        message.success('提交成功');
        fetchTable();
        setEditData({...defaultEdit, icon: editData.icon});
      } else if (res?.status_code === 1031) {
        message.warning('线上已存在相同策略！');
      } else {
        console.log(res?.message);
        message.error(res?.message);
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error(e.message);
    })
  }
  // 策略下线
  const optionTable = (fundPoolId: string) => {
    const data: tableData = {
      fundPoolId,
      updateUser: JSON.parse(localStorage.getItem('name') || ''),
    }
    offFeedConfig(data).then((res: any) => {
      console.log('optionTable', res);
      if (res?.status_code === 0) {
        message.success('下线成功');
        fetchTable();
      } else {
        console.log(res?.message);
        message.error(res?.message);
      }
    }).catch((e: Error) => {
      console.log(e.message);
      message.error(e.message);
    })
  }

  // 渲染 输入项
  const renderItem = (col: Col) => {
    if (col.tableItem) return;
    if (col.key === 'icon') {
      return (
        <div key={col.key}>
          <UploadImg
            size={['24px*24px']}
            handleChange={(url: string) => handleEditChange(col.key as editKey, url)}
            imageUrl={editData[col.key]}
            title={col.title}
            isEdit={true}
          />
        </div>
      )
    } else if (col.key === 'fundPoolId') {
      return (
        <div className="m-value-item m-type-title" key={col.key}>
          <em className="m-required-icon">*</em> <span>{col.title}</span>
          <Select
            value={editData[col.key]}
            className={classnames('m-select', editData[col.key] ? '' : 'm-required')}
            showSearch
            placeholder="请选择基金池tag"
            // onSearch={handleSearch}
            onChange={(value: any) => {handleEditChange(col.key as editKey, value)}}
          >
            {tagOption()}
          </Select>
        </div>
      )
    } else {
      const required = !editData[col.key as editKey] || (col.len && (editData[col.key as editKey] || '')?.length > col.len);
      return (
        <div className="m-value-item m-type-title" key={col.key}>
          <em className="m-required-icon">*</em> <span>{col.title}</span>
          <input
            type="text"
            className={classnames('m-input', required ? 'm-required' : '')}
            value={editData[col.key as editKey]}
            onChange={() => {}}
            onInput={(e: any) => handleEditChange(col.key as editKey, e.target.value, col.len)}
          />
          {
            col.len ? 
            <span className={classnames('m-tip', required ? '' : 'z-hide')}>限{col.len}个字</span>
            : null
          }
        </div>
      )
    }
  }
  // 检查 填入项
  const checkoutForm = () => {
    // 遍历表单数据
    const formList: Col[] = columns.filter(col => !col.tableItem);
    let checked = true;
    for (let i = 0; i < formList.length; i++) {
      const col = formList[i];
      // 如果未填
      if (!editData[col.key as editKey]) {
        checked = false;
        break;
      }
      // 如果长度溢出
      if (col.len && (editData[col.key as editKey] || '')?.length > col.len) {
        checked = false;
        break;
      }
    }
    return checked;
  }

  // 编辑 控制器
  const handleEditChange = (type: editKey, value: string, len?: number) => {
    const reg = /[ ]/g;
    if (value && reg.test(value)) return;
    const _editData: EditData = JSON.parse(JSON.stringify(editData));
    // 字数校验
    if (len) _editData[type] = checkoutInput(value, len);
    else _editData[type] = value;
    setEditData(_editData);
  }
  // 添加 控制器
  const handleAddClick = () => {
    console.log('新增策略', editData);
    const checked = checkoutForm();
    if (!checked) message.warning('请正确填写各字段！');
    else addTable(editData);
  }
  // 页码 控制器
  const handlePageNum = (current: number) => {
    console.log('页码控制器', current);
    setPageNum(current);
  };
  // 页容量 控制器
  const handlePageSize = (pageNum: number, pageSize: number) => {
    console.log('页容量控制器', pageNum, pageSize);
    setPageSize(pageSize);
  };
  // 上下线 控制器
  const handleOptionClick = (index: number) => {
    const fundPoolId = tableData[index]?.fundPoolId || '';
    optionTable(fundPoolId);
  }

  return (
    <section className={styles['m-feed-config']}>
      <p>新增基金池卡片运营策略：</p>
      <section className={styles['m-edit']}>
        {columns.map(item => renderItem(item))}
        <ConfigProvider locale={zhCN}>
          <Popconfirm title="确认新增策略吗？" onConfirm={handleAddClick}>
            <Button type="primary">确认新增</Button>
          </Popconfirm>
        </ConfigProvider>
      </section>
      <p>已上线运营方案：</p>
      <section className={styles['m-table']}>
        <ConfigProvider locale={zhCN}>
          <Table columns={columns as any} dataSource={tableData} pagination={false} />
        </ConfigProvider>
        <div className={styles['m-pagination']}>
          <ConfigProvider locale={zhCN}>
            <Pagination
              current={pageNum}
              pageSize={pageSize}
              total={tableTotal}
              showSizeChanger
              pageSizeOptions={['10', '20', '30', '50']}
              onChange={handlePageNum}
              onShowSizeChange={handlePageSize}
              hideOnSinglePage={tableTotal < 10}
            />
          </ConfigProvider>
        </div>
      </section>
    </section>
  )
}