import { ConfigData } from '../type';
import {
  checkLengthAndThrow,
  checkRequiredAndThrow,
  checkUrlAndThrow,
  getRandomId,
  setRandomId,
  checkMarker
} from '../utils';
import { bannerHelper } from './Banner/helper';
import { chanceLiftHelper } from './ChanceLift/helper';
import { fundRecommendHelper } from './FundRecommend/helper';
import { hotSpotHelper } from './HotSpot/helper';
import { informationHelper } from './Infomation/helper';
import { managerAnalyzeHelper } from './ManagerAnalyze/helper';
import { screemStreamHelper } from './ScreemStream/helper';
import { sectorAnalyzeHelper } from './SectorAnalyze/helper';
import { streamCardHelper } from './StreamCard/helper';


export const mapPropToName = {
  sectorTitle: '机会板块名',
  chanceTitle: '最新机会',
  topTag: '顶部标签',
  hotLogic: '热点逻辑',
  detailLink: '全部事件跳转链接',
  tabTag: '顶部tab标签',
  marker: '角标',
  markerStartTime: '角标开始时间',
  markerEndTime: '角标截止时间',
};
const mapPropToHelper = {
  hotspots: hotSpotHelper,
  screenStream: screemStreamHelper,
  sectorAnalyze: sectorAnalyzeHelper,
  managerAnalyze: managerAnalyzeHelper,
  fundRecommend: fundRecommendHelper,
  streamCard: streamCardHelper,
  banner: bannerHelper,
  infomation: informationHelper,
  chanceLift: chanceLiftHelper,
};

export const generateId = (prefix = '', length = 6) => {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length);
    result += chars[randomIndex];
  }
  const myid = `${prefix}${result}`;
  if (getRandomId(myid)) {
    return generateId(prefix, length);
  }
  setRandomId(myid);
  return myid;
};
export const configItemhelper = {
  addInit: function(index = 0): ConfigData {
    return {
      id: generateId(),
      index,
      sectorTitle: '',
      chanceTitle: '',
      topTag: '',
      hotLogic: '',
      tabTag: '',
      detailLink: '',
      hotspots: mapPropToHelper['hotspots'].addInit(),
      screenStream: mapPropToHelper['screenStream'].addInit(),
      sectorAnalyze: mapPropToHelper['sectorAnalyze'].addInit(),
      managerAnalyze: mapPropToHelper['managerAnalyze'].addInit(),
      fundRecommend: mapPropToHelper['fundRecommend'].addInit(),
      streamCard: mapPropToHelper['streamCard'].addInit(),
      banner: mapPropToHelper['banner'].addInit(),
      infomation: mapPropToHelper['infomation'].addInit(),
      chanceLift: mapPropToHelper['chanceLift'].addInit(),
    };
  },

  checkData: function(data: ConfigData) {
    checkRequiredAndThrow(data.sectorTitle, mapPropToName['sectorTitle']);
    checkRequiredAndThrow(data.chanceTitle, mapPropToName['chanceTitle']);
    checkRequiredAndThrow(data.detailLink, mapPropToName['detailLink']);
    checkUrlAndThrow(data.detailLink, mapPropToName['detailLink']);
    checkLengthAndThrow(data.tabTag, mapPropToName['tabTag'], 4);
    checkLengthAndThrow(data.marker, mapPropToName['marker'], 4);
    checkMarker(data);
    for (const key in data) {
      const helper = mapPropToHelper[key];
      if (helper) {
        helper.checkData(data[key]);
      }
    }
  },
};
