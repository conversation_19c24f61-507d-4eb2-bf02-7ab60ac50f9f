import React, { useState, useEffect, useCallback } from 'react';
import { useHistory } from 'react-router-dom';
import api from 'api';
import { Table, Button, message, Popconfirm } from 'antd';
import { ClientRiskData, DeleteClientRisk, QueryAllClientRisk, ClientRisk } from '../tsFile';
// 运营平台指令配置接口
const { postCommandConfig } = api;

export default function() {
  const columns = [
    {
      title: 'Id',
      dataIndex: 'id',
      render: (text: unknown, record: ClientRiskData<string>) => (record.id ? record.id : '--'),
    },
    {
      title: '名称',
      dataIndex: 'commandListName',
      render: (text: unknown, record: ClientRiskData<string>) =>
        record.commandListName ? record.commandListName : '--',
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      render: (text: unknown, record: ClientRiskData<string>) =>
        record.startDate ? record.startDate : '--',
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      render: (text: unknown, record: ClientRiskData<string>) =>
        record.startDate ? record.startDate : '--',
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      render: (text: unknown, record: ClientRiskData<string>) => {
        switch (record.status) {
          case '0':
            return '待发布';
          case '1':
            return '已发布';
          default:
            return '--';
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      render: (text: unknown, record: ClientRiskData<string>) => {
        return (
          <span>
            <Popconfirm
              disabled={record.status !== '0'}
              title="是否确认发布？"
              okText="确认"
              cancelText="取消"
              onConfirm={() => {
                publishCommandItem(record.id);
              }}
            >
              <Button disabled={record.status !== '0'}>发布</Button>
            </Popconfirm>
            <Button
              style={{ margin: '0 30px' }}
              onClick={() => {
                history.push(`detail/?id=${record.id}`);
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="是否确认删除？"
              okText="确认"
              cancelText="取消"
              onConfirm={() => {
                deleteCommandItem(record.id);
              }}
            >
              <Button type="danger">删除</Button>
            </Popconfirm>
          </span>
        );
      },
    },
  ];
  const history = useHistory();
  // 加载动画
  const [isLoading, setIsLoading] = useState<boolean>();
  // 表格数据
  const [dataSource, setDataSource] = useState<ClientRiskData<string>[]>();
  // 当前页码
  const [current, setCurrent] = useState<number>(1);
  // 数据总数
  const [total, setTotal] = useState<number>();

  useEffect(() => {
    queryAllCommandConfig(1);
  }, []);
  // 获取表格数据
  const queryAllCommandConfig = useCallback((pageNum: number) => {
    setIsLoading(true);
    postCommandConfig({
      type: 'queryAll',
      pageNum: pageNum,
    })
      .then((res: QueryAllClientRisk) => {
        if (res.status_code === 0) {
          // 表格数据配置
          setDataSource(res.data);
          // 数据总数配置
          setTotal(res.total);
        } else {
          message.error(res.status_msg);
        }
        setIsLoading(false);
      })
      .catch((err: unknown) => {
        console.log(err);
        message.error('网络请求错误，请稍后再试');
      });
  }, []);
  // 页码改变
  const onPaginationChange = useCallback((page: number) => {
    setCurrent(page);
    queryAllCommandConfig(page);
  }, []);
  // 删除
  const deleteCommandItem = (id: string) => {
    postCommandConfig({
      type: 'delete',
      id,
    })
      .then((res: DeleteClientRisk) => {
        if (res.status_code === 0) {
          message.success(`${res.data}删除成功`);
          queryAllCommandConfig(current);
        } else {
          message.error(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
        message.error('网络请求错误，请稍后再试');
      });
  };
  // 发布
  const publishCommandItem = (id: string) => {
    postCommandConfig({
      type: 'change',
      id,
    })
      .then((res: ClientRisk) => {
        if (res.status_code === 0) {
          message.success(`${res.data?.id}发布成功`);
          queryAllCommandConfig(current);
        } else {
          message.error(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
        message.error('网络请求错误，请稍后再试');
      });
  };
  return (
    <div>
      <div>
        <Button
          style={{ marginBottom: '20px' }}
          onClick={() => {
            history.push('detail');
          }}
        >
          添加
        </Button>
        <Table
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 'max-content' }}
          loading={isLoading}
          pagination={{
            pageSize: 20,
            onChange: onPaginationChange,
            total: total,
          }}
        ></Table>
      </div>
    </div>
  );
}
