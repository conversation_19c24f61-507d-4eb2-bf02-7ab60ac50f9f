export const checkoutInput = (value: string, len: number) => {
  value = value || '';
  len = len > 0 ? len : 0;
  let _value1 = value.split(/[a-zA-Z\d]/g).join('');
  let _value2 = value.split(/[\u4e00-\u9fa5]/g).join('');
  if (_value1.length <= len) return value;
  else return value.slice(0, len + _value2.length);
}

export const setSearch = (val: any, ids: string[]) => {
  let flag = 0;
  let _options: any[] = ids.filter((item, index) => {
    let result = ~item.indexOf(val);
    if (result) flag++;
    return result && flag <= 10;
  });
  return _options.map(item => {
    return { value: item, disabled: false }
  })
}