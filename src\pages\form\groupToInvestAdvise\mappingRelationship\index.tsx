import React, {useState, useEffect} from 'react';
import classnames from 'classnames';
import styles from './index.less';
import { Input, Button, Divider, Select, Table, Drawer, message, Spin, Popconfirm } from 'antd';
import api from 'api'

const { Option } = Select;
const { getMappingList, saveMappingList, getGroupToInvestAdviseRedpackage, saveGroupToInvestAdviseRedpackage, getAllGroupList, getAllConsultList,getGroupToInvestAdviseRiskConfig } = api;

// 转出组合类型下拉选项
const outTypeOptions = [
  {key: 'all', label: '全部'},
  {key: '0', label: '0'},
  {key: '3', label: '3'},
  {key: '4', label: '4'},
  {key: '5', label: '5'},
  {key: '6', label: '6'},
  {key: '7', label: '7'},
  {key: '8', label: '8'},
  {key: '9', label: '9'}
];

export default function () {
  const [redPackageId, setRedPackageId] = useState<string>('');
  const [formData, setFormData] = useState<any>({
    groupId: '',
    groupName: '',
    vcTacode: '',
    vcConsulttacticsid: '',
    thresholdAmount: '',
    eliminateAmount: ''
  }); // 表单数据
  const [allListData,setAllListData] = useState<any>([]); //全部的列表内容
  const [listData,setListData] = useState<any>([]); //显示的列表内容
  const [mappingList, setMappingList] = useState<any>([]); // 映射列表
  const [mappingListMap, setMappingListMap] = useState<any>([]); // 映射列表map
  const [successInitMappingList, setSuccessInitMappingList] = useState<any>(false); // 映射列表是否初始化完毕
  const [configInfoList, setConfigInfoList] = useState<any>([]); // 投顾机构对应配置信息
  const [configInfoListMap, setConfigInfoListMap] = useState<any>([]); // 投顾机构对应配置信息map
  const [successInitConfigInfoList, setSuccessInitConfigInfoList] = useState<any>(false); // 配置信息列表是否初始化完毕
  const [intoStrategyOptions,setIntoStrategyOptions] = useState<any>([]); // 转入投顾策略名称下拉选项
  const [intoConsultOptions,setIntoConsultOptions] = useState<any>([]); // 转入投顾机构名称下拉选项
  const [showEdit, setShowEdit] = useState<boolean>(false); // 是否显示编辑面板
  const [isSaveLoading, setIsSaveLoading] = useState<boolean>(false); // 保存按钮loading
  const [isListLoading, setIsListLoading] = useState<boolean>(false); // 列表加载loading
  const [filterParams, setFilterParams] = useState<any>({
    groupType: 'all',
    vcTacode: 'all'
  });
  const tableColumns = [
    {
      title: '转出组合类型',
      dataIndex: 'groupType',
      key: 'groupType',
    },
    {
      title: '转出组合id',
      dataIndex: 'groupId',
      key: 'groupId',
    },
    {
      title: '转出组合名称',
      dataIndex: 'groupName',
      key: 'groupName',
    },
    {
      title: '转入投顾TA名称',
      dataIndex: 'vcTaname',
      key: 'vcTaname',
    },
    {
      title: '转入投顾ID',
      dataIndex: 'vcConsulttacticsid',
      key: 'vcConsulttacticsid',
    },
    {
      title: '转入投顾名称',
      dataIndex: 'vcInvestconsulttacticsname',
      key: 'vcInvestconsulttacticsname',
    },
    {
      title: '操作',
      key: 'action',
      render: (row:any, record:any, index:number) => (
        <div>
          <span
            style={{ color: '#1890FF', cursor: 'pointer', marginRight: '10px' }}
            onClick={()=>{openEditDrawer(row)}}
          >
            编辑
          </span>
        </div>
      ),
    },
  ]; // 列表列
  useEffect(() => {
    handleGetRedpackage();
    handleGetMappingList();
    handleGetConfigInfo();
  }, []);
  useEffect(() => {
    if (successInitMappingList && successInitConfigInfoList) {
      handleGetAllConsult();
      handleGetAllGroup();
    }
  }, [successInitMappingList,successInitConfigInfoList, mappingList])

  // 获取红包ID配置
  const handleGetRedpackage = () => {
    getGroupToInvestAdviseRedpackage().then((res:any)=>{
      if (res.code === '0000') {
        setRedPackageId(res.data);
      } else {
        message.error('获取红包活动ID配置失败');
      }
    }).catch(()=>{
      message.error('获取红包活动ID配置失败');
    })
  }
  // 保存红包ID配置
  const handleSaveRedpackage = () => {
    saveGroupToInvestAdviseRedpackage({
      value: redPackageId
    }).then((res:any)=> {
      if (res.code === '0000') {
        message.success('提交成功');
      } else {
        message.error('提交失败');
      }
    }).catch(()=>{
      message.error('提交失败');
    })
  }
  // 获取投顾机构对应配置信息
  const handleGetConfigInfo = () => {
    getGroupToInvestAdviseRiskConfig().then((res:any)=>{
      if (res.code === '0000') {
        const _configInfoListMap:any = new Map();
        const data = res.data || '[]';
        JSON.parse(data) && JSON.parse(data).forEach((item:any,index:number) => {
          if (!_configInfoListMap.has(item.vcTacode)) {
            _configInfoListMap.set(item.vcTacode, item);
          }
        });
        setConfigInfoList(JSON.parse(data));
        setConfigInfoListMap(_configInfoListMap);
        setSuccessInitConfigInfoList(true);
      } else {
        message.error('获取投顾机构对应配置信息失败');
      }
    }).catch(()=>{
      message.error('获取投顾机构对应配置信息失败');
    });
  }
  // 获取组合投顾映射列表
  const handleGetMappingList = () => {
    getMappingList().then((res: any)=>{
      if (res.code === '0000') {
        const _mappingListMap:any = new Map();
        const data = res.data || '[]';
        JSON.parse(data) && JSON.parse(data).forEach((item:any,index:number) => {
          if (!_mappingListMap.has(item.groupId)) {
            _mappingListMap.set(item.groupId, item);
          }
        });
        setMappingList(data);
        setMappingListMap(_mappingListMap);
        setSuccessInitMappingList(true);
      } else {
        message.error('获取映射列表失败');
      }
    }).catch(()=>{
      message.error('获取映射列表失败');
    })
  };
  // 获取所有组合列表
  const handleGetAllGroup = () => {
    setIsListLoading(true);
    getAllGroupList().then((res:any)=>{
      setIsListLoading(false);
      if (res.status_code === 0) {
        const _listData = res.data.map((item:any,index:number)=>{
          let configInfo = {
            vcTaname: '',
            vcTacode: '',
            vcConsulttacticsid: '',
            vcInvestconsulttacticsname: '',
            vcIctrisk: '',
            vcProdtype: ''
          };
          if (mappingListMap.has(item.groupId)) {
            const mappingItem = mappingListMap.get(item.groupId);
            configInfo.vcTaname = mappingItem.vcTaname;
            configInfo.vcTacode = mappingItem.vcTacode;
            configInfo.vcConsulttacticsid = mappingItem.vcConsulttacticsid;
            configInfo.vcInvestconsulttacticsname = mappingItem.vcInvestconsulttacticsname;
            configInfo.vcIctrisk = mappingItem.vcIctrisk;
            configInfo.vcProdtype = mappingItem.vcProdtype;
          }
          return {
            ...item,
            ...configInfo
          }
        })
        setAllListData(_listData);
        handleSearch(_listData);
      } else {
        message.error('获取组合列表失败');
      }
    }).catch(()=>{
      message.error('获取组合列表失败');
      setIsListLoading(false);
    })
  };
  // 获取所有投顾列表(初始化投顾策略和投顾机构名称下拉选项)
  const handleGetAllConsult = () => {
    getAllConsultList().then((res:any)=>{
      if (res.status_code === 0) {
        let intoStrategyOptionsMap = new Map(); // 投顾策略列表map
        let intoConsultOptionsMap = new Map(); // 投顾机构列表map
        res.data.forEach((item:any,index:number)=> {
          // if (!intoStrategyOptionsMap.has(item.vcConsulttacticsid)) {
          //   intoStrategyOptionsMap.set(item.vcConsulttacticsid, {
          //     vcConsulttacticsid: item.vcConsulttacticsid, 
          //     vcInvestconsulttacticsname: item.vcInvestconsulttacticsname,
          //     vcIctrisk: item.vcIctrisk,
          //     vcProdtype: item.vcProdtype,
          //   });
          // };
          let _listItem = {
            vcConsulttacticsid: item.vcConsulttacticsid, 
            vcInvestconsulttacticsname: item.vcInvestconsulttacticsname,
            vcIctrisk: item.vcIctrisk,
            vcProdtype: item.vcProdtype,
            index
          }
          if (!intoConsultOptionsMap.has(item.vcTacode)) {
            intoConsultOptionsMap.set(item.vcTacode, {vcTacode: item.vcTacode, vcTaname: item.vcTaname || item.vcTacode, list:[_listItem]});
          } else {
            let _list = [...intoConsultOptionsMap.get(item.vcTacode).list]
            _list.push(_listItem)
            intoConsultOptionsMap.set(item.vcTacode, {vcTacode: item.vcTacode, vcTaname: item.vcTaname || item.vcTacode, list:_list});
          }
        })
        // setIntoStrategyOptions([...intoStrategyOptionsMap.values()]);
        setIntoConsultOptions([...intoConsultOptionsMap.values()]);
      } else {
        message.error('获取投顾机构列表失败');
      }
    }).catch(()=>{
      message.error('获取投顾机构列表失败');
    })
  };
  // 选择过滤条件
  const changeFilterParams = (type:string, value:string) => {
    let _filterParams = {...filterParams};
    _filterParams[type] = value;
    setFilterParams(_filterParams);
  };
  // 确认搜索
  const handleSearch = (allListData:any) => {
    const _listData = allListData.filter((item:any, index:number)=>{
      return (item.groupType === filterParams.groupType || filterParams.groupType === 'all') && (item.vcTacode === filterParams.vcTacode || filterParams.vcTacode === 'all');
    })
    setListData(_listData);
  };
  // 打开编辑面板
  const openEditDrawer = (row:any) => {
    let _formData = {
      ...row,
      vcTacode: '',
      vcConsulttacticsid: '',
      thresholdAmount: '420',
      eliminateAmount: '400'
    };
    let _intoStrategyOptions = []
    if (mappingListMap.has(row.groupId)) {
      const mappingItem = mappingListMap.get(row.groupId);
      _formData.vcTacode = mappingItem.vcTacode;
      _formData.vcConsulttacticsid = mappingItem.vcConsulttacticsid;
      _formData.thresholdAmount = mappingItem.thresholdAmount;
      _formData.eliminateAmount = mappingItem.eliminateAmount;
      _intoStrategyOptions = intoConsultOptions.find((val:any) => val.vcTacode === mappingItem.vcTacode)
    }
    setShowEdit(true);
    setFormData(_formData);
    setIntoStrategyOptions(_intoStrategyOptions?.list || [])
  };
  // 关闭编辑面板
  const closeEditDrawer = () => {
    setShowEdit(false);
  };
  // 更改配置项
  const changeFormData = (type:string,value:any) => {
    const _formData = {...formData};
    _formData[type] = value;
    if (type === 'vcConsulttacticsid') {
      intoStrategyOptions.forEach((item: any,index:number)=> {
        if (item.vcConsulttacticsid === value) {
          _formData.vcInvestconsulttacticsname = item.vcInvestconsulttacticsname;
          _formData.vcIctrisk = item.vcIctrisk;
          _formData.vcProdtype = item.vcProdtype;
        }
      })
    } else if (type === 'vcTacode') {
      let _intoStrategyOptions = intoConsultOptions.find((val:any) => val.vcTacode ===value).list
      intoConsultOptions.forEach((item: any,index:number)=> {
        if (item.vcTacode === value) {
          _formData.vcTaname = item.vcTaname;
          _formData.vcConsulttacticsid = _intoStrategyOptions[0].vcConsulttacticsid;
          _formData.vcInvestconsulttacticsname = _intoStrategyOptions[0].vcInvestconsulttacticsname;
          _formData.vcIctrisk = _intoStrategyOptions[0].vcIctrisk;
          _formData.vcProdtype = _intoStrategyOptions[0].vcProdtype;
        }
      })
      // let _intoStrategyOptions = intoConsultOptions.find((val:any) => val.vcTacode ===value)
      setIntoStrategyOptions(_intoStrategyOptions|| [])
    }
    console.log(_formData)
    setFormData(_formData);
  };
  // 检查当前投顾机构是否允许转入组合
  const isAgreeInto = () => {
    // let result = true;
    return !configInfoList.some((item:any,index:number)=> {
      // if (formData.vcTacode === item.vcTacode && item.agreeInto === 'no') {
      //   result = false;
      // }
      return formData.vcTacode === item.vcTacode && item.agreeInto === 'no';
    })
    // return result;
  }
  // 保存校验
  const checkFormData = () => {
    let isPass = false;
    switch (true) {
      case formData.vcTacode === '':
        message.error('请选择投顾机构');
        break;
      case formData.vcConsulttacticsid === '':
        message.error('请选择投顾策略');
        break;
      case formData.thresholdAmount === '':
        message.error('请填写可转出组合起始金额');
        break;
      case formData.eliminateAmount === '':
        message.error('请填写可转出组合最低金额');
        break;
      case Number(formData.thresholdAmount) && Number(formData.eliminateAmount) && (Number(formData.thresholdAmount) <= Number(formData.eliminateAmount)):
        message.error('可转出组合起始金额需大于可转出组合最低金额');
        break;
      case !isAgreeInto():
        message.error('该投顾机构不允许组合转入');
        break;
      default:
        isPass = true;
        break;
    }
    return isPass;
  };
  // 保存
  const handleSave = () => {
    if (checkFormData()) {
      setIsSaveLoading(true);
      mappingListMap.set(formData.groupId,formData);
      saveMappingList({
        value: JSON.stringify([...mappingListMap.values()])
      }).then((res:any)=>{
        if (res.code === '0000') {
          setIsSaveLoading(false);
          message.success('保存成功');
          handleGetMappingList();
          closeEditDrawer();
        } else {
          message.error('保存失败');
        } 
      }).catch(()=>{
        message.error('保存失败');
        setIsSaveLoading(false);
      })
    }
  };
  return (
    <div className={classnames(styles['mapping-relationship'])}>
      <Spin spinning={isListLoading}>
        {/* 红包活动id配置 */}
        <div style={{display: 'inline-block'}}>红包活动ID配置：</div>
        <Input placeholder='请输入红包活动ID' value={redPackageId} onChange={(e)=>{setRedPackageId(e.target.value)}} style={{width: '400px',marginRight: '20px'}} />
        <Popconfirm
          title="确定要提交吗"
          onConfirm={()=>{handleSaveRedpackage()}}
          okText="确定"
          cancelText="取消"
        >
          <Button type='primary'>提交</Button>
        </Popconfirm>
        <Divider />
        {/* 组合-投顾映射关系配置 */}
        <div style={{fontSize: '20px',marginBottom: '20px'}}>组合-投顾映射关系配置</div>
        <div style={{display: 'inline-block'}}>转出组合类型：</div>
        <Select style={{ width: '250px',marginRight: '40px' }} value={filterParams.groupType} onChange={(e:any) => {changeFilterParams('groupType',e)}}>
          {
            outTypeOptions.map((item,index)=>{
              return (
                <Option key={item.key}>{item.label}</Option>
              )
            })
          }
        </Select>
        <div style={{display: 'inline-block'}}>转入投顾TA名称：</div>
        <Select style={{ width: '250px',marginRight: '20px' }} value={filterParams.vcTacode} onChange={(e:any) => {changeFilterParams('vcTacode',e)}}>
          <Option key={'all'}>全部</Option>
          {
            intoConsultOptions.map((item:any ,index: number)=>{
              return (
                <Option key={item.vcTacode}>{item.vcTaname}</Option>
              )
            })
          }
        </Select>
        <Button type='primary' onClick={()=>{handleSearch(allListData)}}>确定</Button>
        {/* 列表 */}
        <Table
          style={{marginTop: '20px'}}
          columns={tableColumns}
          dataSource={listData}
          pagination={false}
          rowKey={record => record.id}
        />
        {/* 编辑面板 */}
        <Drawer
          className="mapping-relationship-drawer"
          title='编辑'
          placement="right"
          width="1000"
          maskClosable={false}
          destroyOnClose={true}
          onClose={closeEditDrawer}
          visible={showEdit}
        >
          {/* 转出组合Id */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>转出组合ID：</div>
            <div style={{display: 'inline-block'}}>{formData.groupId}</div>
          </div>
          {/* 转出组合名称 */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>转出组合名称：</div>
            <div style={{display: 'inline-block'}}>{formData.groupName}</div>
          </div>
          {/* 转入投顾机构名称 */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>转入投顾机构名称：</div>
            <Select style={{ width: '250px',marginRight: '40px' }} value={formData.vcTacode} onChange={(e:any) => {changeFormData('vcTacode',e)}}>
              {
                intoConsultOptions.map((item:any,index:number)=>{
                  return (
                    <Option key={item.vcTacode}>{item.vcTaname}</Option>
                  )
                })
              }
          </Select>
          </div>
          {/* 转入投顾策略名称 */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>转入投顾策略名称：</div>
            <Select style={{ width: '250px',marginRight: '40px' }} value={formData.vcConsulttacticsid} onChange={(e:any) => {changeFormData('vcConsulttacticsid',e)}}>
              {
                intoStrategyOptions.map((item:any,index:number)=>{
                  return (
                    <Option key={item.vcConsulttacticsid}>{item.vcInvestconsulttacticsname}</Option>
                  )
                })
              }
            </Select>
          </div>
          {/* 门槛金额 */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>可转出组合起始金额(元)：</div>
            <Input style={{width: '250px'}} value={formData.thresholdAmount} onChange={e => {changeFormData('thresholdAmount', e.target.value)}}></Input>
          </div>
          {/* 剔除金额 */}
          <div className={'row'}>
            <div style={{display: 'inline-block'}}>可转出组合最低金额(元)：</div>
            <Input style={{width: '250px'}} value={formData.eliminateAmount} onChange={e => {changeFormData('eliminateAmount', e.target.value)}}></Input>
          </div>
          {/* 保存 */}
          <Popconfirm
            title="确定要保存吗"
            onConfirm={()=>{handleSave()}}
            okText="确定"
            cancelText="取消"
          >
            <Button style={{marginTop:'15px'}} type='primary' loading={isSaveLoading}>保存</Button>
          </Popconfirm>
        </Drawer>
      </Spin>
    </div>
  )
}
