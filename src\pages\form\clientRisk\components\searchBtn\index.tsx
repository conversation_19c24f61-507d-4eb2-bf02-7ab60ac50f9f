import React from 'react';
import { Button } from 'antd';
import { CommandItem } from '../../tsFile';
import api from 'api'
import { toast } from '@/utils/message';
const {fetchCommandResult} = api
export default function({ rootValue }: { rootValue: CommandItem<string> }) {
  // 查看指令结果
  const searchCommandResult = () => {
  
    fetchCommandResult({commandId:rootValue.commandId}).then(data =>{
      
      if(data.data){
        const {success,fail}=data.data
        toast.success(`成功${success}条，失败${fail}条`)
      }
    })
    // getDataExcel({}, `activityIndex=${index}`, '', { responseType: 'blob' })
    //   .then((res: any) => {
    //     if (!res.success) message.error(res.message);
    //   })
    //   .catch((err: unknown) => {
    //     message.warn('网络请求错误，请稍后重试');
    //     console.warn(err);
    //   });
  };
  return (
    <section>
      <Button onClick={searchCommandResult}>查看指令结果</Button>
    </section>
  );
}
