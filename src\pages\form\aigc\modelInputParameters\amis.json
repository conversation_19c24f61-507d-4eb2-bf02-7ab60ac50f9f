{"type": "page", "title": "模型执行", "body": {"type": "form", "className": "h-full", "wrapWithPanel": false, "initApi": {"url": "/common_config/kv_data_get", "method": "get", "dataType": "form", "data": {"key": "aigc_model_config"}, "adaptor": "return {\r\n  status: payload.code === 0000 ? 0 : payload.code,\r\n  msg: payload.status_msg,\r\n  data: JSON.parse(payload.data)\r\n};"}, "api": {"url": "/common_config/kv_data_save", "method": "post", "dataType": "form", "data": {"key": "aigc_model_config", "value": {"imgLength": "${imgLength}", "imgHigh": "$imgHigh", "modelVersion": "$modelVersion", "runCount": "$runCount", "textLength": "$textLength", "img_select": "$img_select", "content_select": "$content_select"}}, "sendOn": "${!ISEMPTY(imgLength) && !ISEMPTY(imgHigh) && (!ISEMPTY(img_select) || !ISEMPTY(content_select))}", "requestAdaptor": "const value = JSON.parse(JSON.stringify(api.data.value));\r\nconst imgSplitArray = value.img_select.split(',');\r\nconst contentSplitArray = value.content_select.split(',');\r\n\r\nlet imgResultArr = imgSplitArray.map(item => {\r\n  let [userTag, fundTag] = item.split('|');\r\n  return { \"userTag\": userTag, \"fundTag\": fundTag };\r\n});\r\nlet contentResultArr = contentSplitArray.map(item => {\r\n  let [userTag, fundTag] = item.split('|');\r\n  return { \"userTag\": userTag, \"fundTag\": fundTag };\r\n});\r\nvalue['imgTagList'] = imgResultArr;\r\nvalue['contentTagList'] = contentResultArr;\r\ndelete value.img_select;\r\ndelete value.content_select;\r\napi.data.value = JSON.stringify(value);\r\nreturn api;\r\n", "adaptor": "return {\r\n  status: payload.code === 0000 ? 0 : payload.code,\r\n  msg: payload.message\r\n};"}, "body": [{"type": "select", "name": "runCount", "label": "跑模型次数", "mode": "horizontal", "labelWidth": 160, "size": "sm", "required": true, "multiple": false, "options": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}], "value": "1"}, {"type": "select", "mode": "horizontal", "labelWidth": 160, "name": "modelVersion", "label": "模型版本", "required": true, "multiple": false, "options": [{"label": "完整版", "value": "3Drendering_SDXL"}, {"label": "fast版", "value": "3Drendering_SDXL-fast"}], "value": "3Drendering_SDXL", "size": "sm"}, {"type": "group", "label": "图片长宽（单位px）", "labelWidth": 160, "mode": "inline", "required": true, "body": [{"type": "input-text", "label": "长", "name": "imgLength", "placeholder": "图片长度", "size": "sm"}, {"type": "input-text", "label": "宽", "name": "imgHigh", "placeholder": "图片宽度", "size": "sm"}]}, {"type": "select", "mode": "horizontal", "labelWidth": 160, "name": "textLength", "label": "文本长度", "required": true, "multiple": false, "options": [{"label": "短（7~12）", "value": "短"}, {"label": "中（13~16）", "value": "中"}, {"label": "长（20~30）", "value": "长"}], "value": "短", "size": "sm"}, {"type": "group", "mode": "horizontal", "body": [{"label": "图片使用词", "type": "select", "name": "img_select", "extractValue": true, "maxTagCount": 20, "clearable": true, "multiple": true, "checkAll": true, "labelField": "value", "hideSelected": true, "searchable": true, "selectMode": "table", "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "let imgUserList = payload.data.imgUserList;\r\nlet imgFundList = payload.data.imgFundList;\r\n\r\nlet imgMergeList = [];\r\n\r\nlet index = 0;\r\nfor (let i = 0; i < imgUserList.length; i++) {\r\n  for (let j = 0; j < imgFundList.length; j++) {\r\n    imgMergeList.push({ id: index, value: imgUserList[i] + '|' + imgFundList[j], imgUser: imgUserList[i], imgFund: imgFundList[j] });\r\n  }\r\n};\r\nconsole.log('imgMergeList', imgMergeList);\r\nreturn {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    items: imgMergeList\r\n  }\r\n}", "cache": 2000}, "columns": [{"name": "imgUser", "label": "图片使用词（用户侧）"}, {"name": "imgFund", "label": "图片使用词（基金侧）"}]}, {"label": "文本使用词", "type": "select", "name": "content_select", "maxTagCount": 20, "clearable": true, "multiple": true, "checkAll": true, "labelField": "value", "hideSelected": true, "searchable": true, "selectMode": "table", "source": {"url": "/aigc/audit/prompts", "method": "get", "adaptor": "let contentUserList = payload.data.contentUserList;\r\nlet contentFundList = payload.data.contentFundList;\r\n\r\nlet contentMergeList = [];\r\n\r\nfor (let i = 0; i < contentUserList.length; i++) {\r\n  for (let j = 0; j < contentFundList.length; j++) {\r\n    contentMergeList.push({ value: contentUserList[i] + '|' + contentFundList[j], contentUser: contentUserList[i], contentFund: contentFundList[j] });\r\n  }\r\n};\r\nconsole.log('contentMergeList', contentMergeList);\r\nreturn {\r\n  status: payload.status_code,\r\n  msg: payload.status_msg,\r\n  data: {\r\n    items: contentMergeList\r\n  }\r\n}", "cache": 2000}, "columns": [{"name": "contentUser", "label": "文本使用词（用户侧）"}, {"name": "contentFund", "label": "文本使用词（基金侧）"}]}]}, {"type": "submit", "label": "批量执行"}]}}