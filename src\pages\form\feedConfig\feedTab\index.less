.m-feed-tab {
  .m-feed-list {
    margin-bottom: 20px;
    display: flex;
    flex-flow: column nowrap;
    .m-tab-row {
      margin: 0 !important;
      [class*="ant-col"] {
        padding: 12px 16px !important;
      }
    }
    > div:nth-child(1) {
      padding-left: 16px;
    }
  }
  .m-feed-option {
    width: 100%;
    display: flex;
    > button {
      margin-right: 16px;
    }
  }
}
:global {
  .m-value-item {
    &.m-type-title {
      > input {
        width: 190px;
      }
    }
    &.m-required::after {
      content: '限制7字';
      color: red;
      position: absolute;
    }
    .m-required-icon {
      color: red;
    }
  }
  .m-input {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-variant: tabular-nums;
    list-style: none;
    font-feature-settings: 'tnum';
    position: relative;
    display: inline-block;
    height: 32px;
    padding: 4px 11px;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 1.5;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
    overflow: visible;
    &.m-required {
      border-color: #e21c1c;
    }
    &.m-required:hover {
      border-color: #e21c1c;
    }
    &.m-required:focus {
      border-color: #e21c1c;
      box-shadow: 0 0 0 2px rgba(226, 28, 28, 0.2);
    }
    &:hover {
      border-color: #40a9ff;
      border-right-width: 1px !important;
    }
    &:focus {
      border-color: #40a9ff;
      border-right-width: 1px !important;
      outline: 0;
      box-shadow: 0 0 0 2px rgba(24,144,255,.2);
    }
  }
  .m-select {
    width: 300px;
    &.m-required > div {
      border-color: #f16a6a;
    }
    &.m-required:hover > div {
      border-color: #f16a6a;
    }
    &.m-required:focus > div {
      border-color: #f16a6a;
      box-shadow: 0 0 0 2px rgba(226, 28, 28, 0.2);
    }
  }
}