import React, { useState } from 'react';
import { Button, Collapse, message, Popconfirm } from 'antd';

// 使用 Ant Design 体系
import FormRender from 'form-render/lib/antd';
import FROM_JSON from './form';


export default function PacketForm({ data, submit }) {
  const [formData, setFormData] = useState(data || {});
  const [valid, setValid] = useState([]);

  const onSubmit = () => {
    // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
    if (valid.length > 0) {
      message.info(`校验未通过字段：${valid.toString()}`);
    } else {
      submit(formData);
    }
  };

  return (
    <section>
      <Collapse
        style={{ marginBottom: 30, width: 800 }}>
        <Collapse.Panel
          header={`${formData.activityId + formData.activityName}` || '--'}
          key="1">
          <div style={{ padding: 30 }}>
            <FormRender
              propsSchema={FROM_JSON}
              formData={formData}
              onChange={setFormData}
              onValidate={setValid}
            />
            <Popconfirm
              title="确认呢要提交么？如果是修改会覆盖线上配置！"
              onConfirm={onSubmit}
              okText="确定"
              cancelText="取消"
            >
              < Button type={'primary'}>提交</Button>
            </Popconfirm>
          </div>
        </Collapse.Panel>
      </Collapse>
    </section>
  );
}
