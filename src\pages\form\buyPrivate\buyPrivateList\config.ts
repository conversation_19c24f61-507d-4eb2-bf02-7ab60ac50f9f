export const initData = {
  "privateFundId": "",
  "privateFundName": "",
  "riskLevel": "",
  "investStrategy": "",
  "fundManagerName": "",
  "fundManagerId": null,
  "manageOrganization": "",
  "publicityName": "",
  "publicityUrl": "",
  "publicityStart": "",
  "publicityEnd": "",
  "custodian": "",
  "warningLine": "",
  "stopLossLine": "",
  // "raiseStart": "",
  // "raiseEnd": "",
  // "preSgStart": "",
  // "preSgEnd": "",
  // "preRedeemStart": "",
  // "preRedeemEnd": "",
  "tag": "",
  "limitSale": "",
  "limitNum": "",
  "closePeriod": "",
  "publicityPictureList": [
    {
      "title": "",
      "pictureUrl": ""
    }
  ],
  "publicityVideo": "",
  operateMethod: '',
  fundAgreementFile: { name: '', url: '' },
  fundRiskPointFile: { name: '', url: '' },
  fundRelationFile: { name: '', url: '' },
  fundRaiseFile: { name: '', url: '' },
  openRule: '',
  incomeDescribe: '',
  expectIncomeRate: '',
  setEstabDate: '',
  curOpenNum: '',
  openPeriod: ''
}
export const STRATEGYS = ['股票策略', '宏观策略', '管理期货', '事件驱动', '相对价值', '固定收益', '组合策略', '复合策略']
export const FILES = [
  {
    label: '基金合同',
    val: 'fundAgreementFile',
    required: true
  },
  {
    label: '基金风险揭示书',
    val: 'fundRiskPointFile',
    required: true
  },
  {
    label: '关联关系说明书',
    val: 'fundRelationFile',
    required: false
  },
  {
    label: '基金募集说明书',
    val: 'fundRaiseFile',
    required: true
  }
]
