export const CARD_JSON = {
  "type": "object",
  "properties": {
    "groupCards": {
      "title": "组合运营卡片",
      "type": "array",
      "maxItems": 3,
      "items": {
        "type": "object",
        "required": ["groupId", "reason", "buttonText"],
        "properties": {
          "groupId": {
            "title": "组合产品ID",
            "type": "string",
            "ui:options": {}
          },
          "reason": {
            "title": "精选理由",
            "type": "string",
            "description": "精选理由字数，限制在25字以内",
            "ui:options": {},
            "maxLength": 25
          },
          "buttonText": {
            "title": "按钮文案",
            "type": "string",
            "ui:options": {}
          },
          "liveInfo": {
            "title": "直播卡片",
            "type": "object",
            "properties": {
              "isOpen": {
                "title": "是否开启直播",
                "type": "boolean"
              },
              "liveUrl": {
                "title": "直播链接",
                "pattern": /^\S+$/,
                "type": "string",
                "ui:options": {}
              },
              "liveId": {
                "title": "直播Id",
                "type": "string",
                "ui:options": {}
              }
            }
          }
        }
      },
      "ui:options": { "foldable": true }
    }
  }
}

export const LIST_JSON = {
  "type": "object",
  "required": [
    "groupIntro",
    "ownerName",
    "ownerImage",
    "groupTarget",
    "platform"
  ],
  "properties": {
    "groupId": {
      "type": "string",
      "ui:hidden": true
    },
    "groupIntro": {
      "title": "组合说明",
      "type": "string",
      "ui:width": "100%",
      "ui:options": {},
      "maxLength": 100
    },
    "ownerName": {
      "title": "主理人名称",
      "type": "string",
      "ui:options": {}
    },
    "ownerImage": {
      "title": "主理人头像",
      "type": "string",
      "ui:widget": "uploadImg"
    },
    "ownerUrl": {
      "title": "主理人链接",
      "type": "string",
      "pattern": /^\S+$/,
      "ui:options": {},
      "description": "链接为组合主理人的同顺号主页或圈子主页，视链接指向而定。"
    },
    "productUrl": {
      "title": "产品落地页链接",
      "type": "string",
      "pattern": /^\S+$/,
      "ui:options": {},
      "description": "链接为组合说明的运营落地页。"
    },
    "operateEntrance": {
      "type": "boolean",
      "title": "是否显示运营入口",
      "description": "若选择<是>，则在组合详情页中显示运营配置banner"
    },
    "hideArticle": {
      "title": "是否隐藏主理人文章",
      "type": "boolean",
      "description": "若选择<是>，则在组合详情页中隐藏主理人文章模块"
    },
    "hot": {
      "title": "是否爆款组合",
      "type": "boolean",
      "description": "若选择<是>，则该组合将在组合列表页的<爆款组合>中进行展示，排列顺序为运营后台配置的顺序"
    },
    "operateIntro": {
      "type": "string",
      "title": "运营入口说明",
      "description": "运营位文案。",
      "ui:disabled": "{{!formData.operateEntrance}}"
    },
    "operateUrl": {
      "type": "string",
      "title": "运营入口链接",
      "description": "运营位跳转链接。",
      "ui:disabled": "{{!formData.operateEntrance}}"
    },
    "ownerThsid": {
      "type": "string",
      "title": "主理人同顺号ID",
      "ui:disabled": "{{formData.hideArticle === true ? true : false}}"
    },
    "groupTarget": {
      "title": "组合目标",
      "description": "",
      "type": "string",
      "enum": [
        "pyhj",
        "pylc",
        "pytz",
        "pydp",
        "pygz",
        "pyfj",
        "pybft"
      ],
      "enumNames": [
        "跑赢货基（3%-4%）",
        "跑赢理财（4%-6%）",
        "跑赢通胀（6%-8%）",
        "跑赢大盘（8%-11%）",
        "跑赢工资涨幅（11%-15%）",
        "跑赢一线房价（15%-20%）",
        "跑赢巴菲特（20%以上）"
      ]
    },
    "investment": {
      "type": "string",
      "title": "投资理念",
      "pattern": /^\S{0,8}$/,
    },
    "platform": {
      "title": "上线平台",
      "description": "点击多选",
      "type": "array",
      "items": {
        "type": "string"
      },
      "enum": [
        "all",
        "ajjList",
        "ajjTszt",
        "tc",
        "xm"
      ],
      "enumNames": [
        "全部",
        "爱基金-组合列表",
        "爱基金-同顺智投",
        "甜橙",
        "小米"
      ]
    },
    "groupLabels": {
      "title": "组合标签",
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "label": {
            "title": "",
            "type": "string",
            "pattern": /^\S{0,8}$/,
          }
        }
      },
      "maxItems": 3
    },
    "holderHave": {
      "title": "持有人专享",
      "type": "array",
      "items": {
        "type": "object",
        "required": [
          "title",
          "description"
        ],
        "properties": {
          "title": {
            "title": "标题",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {},
            "maxLength": 10
          },
          "buttonText": {
            "title": "按钮文案",
            "type": "string",
            "ui:width": "50%",
            "ui:options": {},
            "maxLength": 6
          },
          "description": {
            "title": "描述",
            "type": "string",
            "format": "textarea",
            "maxLength": 100
          },
          "url": {
            "title": "链接",
            "type": "string",
            "pattern": /^\S+$/,
            "ui:width": "100%",
            "ui:options": {}
          },
          "imageUrl": {
            "title": "图标",
            "type": "string",
            "ui:widget": "uploadImg"
          }
        }
      },
      "ui:options": { "foldable": true }
    }
  }
}