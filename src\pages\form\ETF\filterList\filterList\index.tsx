import React, { useEffect } from 'react';
import 'moment/locale/zh-cn';
import amisJSON from './amis.json';
import amisEnv from 'functions/amisEnv';
import api from 'api';

export default function() {
    let amisScoped:any;
    let indicList:any = [];
    const init = () => {
      api.fetchHashAll({
        'key': 'normal_config_etf_hqtab_filter'
      }).then((res:any) => {
        if (res && res.statusCode === 200 && res.code === '0000' && res.data) {
          for (const [, value] of Object.entries(res.data)) {
            indicList.push(JSON.parse(value));
          }
        }
        let amis = amisRequire('amis/embed');
        amisScoped = amis.embed('#etfFilterList', {
          ...amisJSON,
          data:{
            indicList: indicList
          }}, {}, amisEnv());
        })
    }
    useEffect(() => {
      init();
      return () => {
        amisScoped.unmount();
      }
    }, [])
    
    return (
      <div id='etfFilterList'></div>
    )
}

