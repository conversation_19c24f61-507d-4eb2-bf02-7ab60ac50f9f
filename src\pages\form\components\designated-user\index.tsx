/**
 * kyc 指定用户通用业务组件
 */

import React, { useEffect, useState } from 'react';
import { Row, Col, Radio, Button, message } from 'antd';
import api from 'api';

import { timeFormat2 } from '@/utils/utils';
import ConfigSelect from './selectModel/index';
import TagModel from './tagModel/index';
import UploadFile from './uploadFile';
import styles from './index.less';

const { updateUserType } = api;
interface dataProps {
  data: any;
  kycTag: any;
  olasTag: any;
  isEdit?: boolean;
  setDesignatedData: (data: any) => void;
}
enum USER_TYPE {
  'user' = '1',
  'cust' = '3',
}
const userText = {
  '1': 'userid',
  '3': 'custid',
};
export default function({ setDesignatedData, data, kycTag, olasTag, isEdit }: dataProps) {
  const [configData, setConfig] = useState<any>({});
  const [relationData, setRelation] = useState<any>({});
  const [userType, setUserType] = useState<'1' | '3'>(USER_TYPE['user']); //手动指定用户名单类型
  const [olasUserType, setOlasUserType] = useState<'1' | '3'>(USER_TYPE['user']); //手动指定用户名单类型
  const [isCanEdit, setIsCanEdit] = useState<boolean>(false); //是否可修改

  useEffect(() => {
    setConfig(data.configData);
    setRelation(data.relationData);
    setUserType(data.relationData?.userType ?? USER_TYPE['user']);
    setOlasUserType(data.relationData?.olasType ?? USER_TYPE['user']);
  }, [data]);

  const handleSelect = (data: any) => {
    console.log('data', data);
    setConfig(data);
  };
  const handleRelation = (data: any) => {
    console.log('tag', data);
    setRelation({
      ...relationData,
      ...data,
      olasType: olasUserType,
    });
  };
  /**
   * 保存指定黑白名单文件
   */
  const saveFile = (obj: any) => {
    if (userType === '1') {
      setRelation({
        ...relationData,
        ...obj,
      });
    } else {
      setRelation({
        ...relationData,
        blackCustId: obj.blackUserId,
        whiteCustId: obj.whiteUserId,
      });
    }
  };
  const handleListType = (e: any) => {
    setUserType(e.target.value);
    setRelation({
      ...relationData,
      userType: e.target.value,
    });
  };
  const handleIdType = (e: any) => {
    setOlasUserType(e.target.value);
    setRelation({
      ...relationData,
      olasType: e.target.value,
    });
  };
  const startEdit = () => {
    setIsCanEdit(true);
  };

  // 上传用户类型
  const uploadUserType = () => {
    let time = timeFormat2();
    let realData: any = {};
    if (userType === '1') {
      // userid
      const { blackCustId, whiteCustId, ...other } = relationData;
      realData = { ...other };
    } else {
      // custid
      const { blackUserId, whiteUserId, blackCustId, whiteCustId, ...other } = relationData;
      realData = {
        ...other,
        blackUserId: blackCustId,
        whiteUserId: whiteCustId,
      };
    }
    if (relationData?.targetType === 'kyc') {
      realData.olasId = '';
      realData.olasType = '';
    } else if (relationData?.targetType === 'olas') {
      realData.kycLogic = '';
      realData.kycs = [];
    }
    let obj = {
      ...configData,
      ...realData,
      userType,
      updateTime: time,
    };
    if (configData.platform?.length === 0) {
      message.error('请选择使用平台');
      return;
    }
    if (configData.utype?.length === 0) {
      message.error('请选择用户类型');
      return;
    }
    updateUserType(obj)
      .then((res: any) => {
        if (res.code !== '0000') return message.error(res.message);
        message.success('提交用户类型成功');
        let id = res.data;
        setIsCanEdit(false);
        setRelation({
          ...relationData,
          id,
        });
        setDesignatedData({
          configData,
          relationData: {
            ...relationData,
            id,
            updateTime: time,
          },
        });
      })
      .catch((e: Error) => {
        message.error(e.message || '系统错误');
      });
  };
  return (
    <div className={styles['m-card']}>
      <div>
        <h1 className="g-fs20 f-bold">指定用户</h1>
        <div className="g-mb20 f-tr">
          {isCanEdit ? (
            <Button disabled={!isEdit} type="primary" onClick={uploadUserType}>
              提交用户类型
            </Button>
          ) : (
            <Button disabled={!isEdit} type="primary" onClick={startEdit}>
              编辑用户类型
            </Button>
          )}
        </div>
      </div>
      <ConfigSelect
        handleChange={handleSelect}
        isHead={false}
        isEdit={isEdit && isCanEdit}
        data={configData}
      />
      <TagModel
        olasUserType={olasUserType}
        handleIdType={handleIdType}
        handleChange={handleRelation}
        data={relationData}
        kycTag={kycTag}
        olasTag={olasTag}
        isEdit={isEdit && isCanEdit}
      />
      <Row style={{ width: '100%' }}>
        <Col span={3} style={{ width: '135px' }}>
          <p className={styles['m-card-label']}>手动指定用户名单类型:</p>
        </Col>
        <Radio.Group onChange={handleListType} value={userType} disabled={!isEdit || !isCanEdit}>
          <Radio value={'1'}>同花顺账号user_id</Radio>
          <Radio value={'3'}>基金客户号cust_id</Radio>
        </Radio.Group>
      </Row>
      <UploadFile
        name={`黑名单（${userText[userType]}）`}
        userType={userType}
        relationData={relationData}
        fileType={'black'}
        saveFile={saveFile}
        isEdit={isEdit && isCanEdit}
        describe={`请上传xlsx文档，每行填入一个基金客户号（${userText[userType]}），第一行不要填数据`}
        isExist={userType === '1' ? relationData['blackUserId'] : relationData['blackCustId']}
      />
      <UploadFile
        name={`手动上传用户名单（${userText[userType]}）`}
        userType={userType}
        relationData={relationData}
        fileType={'white'}
        saveFile={saveFile}
        isEdit={isEdit && isCanEdit}
        describe={`请上传xlsx文档，每行填入一个基金客户号（${userText[userType]}），第一行不要填数据`}
        isExist={userType === '1' ? relationData['whiteUserId'] : relationData['whiteCustId']}
      />
    </div>
  );
}
