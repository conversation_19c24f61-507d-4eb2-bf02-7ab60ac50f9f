import FORM_JSON from './form.json';
import ReactDOM from 'react-dom';
import React, { useState, useEffect } from 'react';
import { Button, message, Switch } from  'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';

export default function() {
    const { fetchfundConfig, postfundConfig } = api;
    const [init, setInit] = useState(false);
    const [formData, setData] = useState({});
    const [checked, setChecked] = useState(true);
    const [valid, setValid] = useState([]);

    useEffect(() => {
        getItem();
    }, [getItem, init])

    const getItem = () => {
        fetchfundConfig().then( (res) => {
            let _data = FORM_JSON.formData;
            console.log(_data)
            try {
                res = JSON.parse(res.data);
                if(res) {
                    _data = res;
                }
            } catch(e) {
                console.warn(e)
            }
            setChecked(_data.activityEnable);
            setInit(true);
            setData(_data);
        }).catch((e) => {
            message.error(e.message);
        })
    }

    // 表单数据检查
    const checkForm = (obj) => {
        let codes = [],
            regCode = /[0-9]{6}/,
            regHttp = /http(s)?:\/\//;
        // 遍历所有类目
        for(let i = 0;  i < obj.configArray.length; i++) {
            let item = obj.configArray[i],
                // 非法字符检查
                codeArr = item.fundCode.toString().replace(/[，\r\n.*\/ ]+/g, ",").split(","),
                _fundCode = [],
                images = item.bannerImage.toString().replace(/\.(?=jpg|png)/g, ",").split(","),
                urls = item.bannerURL.toString().replace(/[，\r\n]/g, ",").split(",");
            // 该类目基金代码去重
            for(let j = 0; j < codeArr.length; j++) {
                // 格式检查
                let code = codeArr[j],
                    rightCode = regCode.exec(code);
                if(rightCode) {
                    if(codes.indexOf(code) === -1) {
                        if(_fundCode.indexOf(code) === -1) {
                            _fundCode.push(code);
                        }
                    } else {
                        alert("有重复基金代码！")
                        return false;
                    }
                } else {
                    alert("基金代码格式错误！")
                    return false;
                }
            }
            codes = [...codes, ..._fundCode]
            //设置基金代码
            item.fundCode = _fundCode;

            //该类目banner是否启用
            item.bannerEnable = item.bannerSwitch && !(item.bannerImage === "")

            //该类目图片去重
            if(images.length > 2) {
                alert("图片重复！")
                return false;
            }

            //该类目链接去重
            if(urls.length === 1) {
                let str = item.bannerURL;
                str = regHttp.exec(str) ? str : ("http://" + str);
            } else {
                alert("banner跳转链接重复！")
                return false;
            }
        }
        return true;
    }

    function getJsonp() {
        let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/rabbitmq/newyypushlish?key=fundConfig&time=${new Date().getTime()}&return=jsonp&jsonp=jsonp`
        console.log(_script)
        document.body.appendChild(_script);
    }

    const onSubmit = () => {
        if(valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
            return;
        }
        let _formData = JSON.parse(JSON.stringify(formData));
        //严选活动开启
        _formData.activityEnable = checked;
        //配置数据
        let trueForm = checkForm(_formData);
        if(trueForm) {
            console.log(_formData)
            postfundConfig({
                value: JSON.stringify(_formData)
            }).then( (res) => {
                try {
                    if(res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        window.jsonp = function (data) {
                            if (data.error.id == 0) { message.success('提交成功！'); }
                        }
                        getJsonp();
                    }
                } catch(e) {
                    message.error(e.message);
                }
            })
        }
    }

    return (
        <article>
            基金配置页
            {
                init ?
                <article>
                    <div>
                        <span>严选活动开启</span>
                        <Switch defaultChecked={checked} onChange={setChecked} />
                    </div>
                    <FormRender 
                        propsSchema={FORM_JSON.propsSchema}
                        uiSchema={FORM_JSON.uiSchema}
                        onValidate={setValid}
                        formData={formData}
                        onChange={setData}
                        displayType="row"
                        showDescIcon={true}
                        lableWidth={90}
                    />
                    <Button onClick={onSubmit}>发布</Button>
                </article>
                :
                ''
            }
        </article>
    )
}