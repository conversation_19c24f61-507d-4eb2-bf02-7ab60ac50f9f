import {
    Form,
    Input,
    But<PERSON>,
    Popconfirm,
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React, { forwardRef } from 'react';
import UploadImg from './uploadImg';

const { TextArea } = Input;
interface baseDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: any;
    handleData: (data: any) => void;
}
class baseDrawer extends React.Component<baseDrawerProps, any> {
    constructor(props: baseDrawerProps) {
        super(props);
    }

    formItemLayout = {
        labelCol: {
            span: 4
        },
        wrapperCol: {
            span: 19
        },
    };
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { currentData } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                values = { 
                    ...currentData,
                    ...values,
                };
                console.log(values);
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;
        const { onEditClose } = this.props;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="TA机构名称：">
                        <span>{this.props.currentData?.taName}</span>
                    </Form.Item>
                    <Form.Item label="顶部文案：" >
                        {getFieldDecorator('topText', {
                            initialValue: this.props.currentData?.topText,
                        })(
                            <TextArea />
                        )}
                    </Form.Item>
                    <Form.Item label="底部文案：">
                        {getFieldDecorator('bottomText', {
                            initialValue: this.props.currentData?.bottomText,
                        })(
                            <TextArea />
                        )}
                    </Form.Item>
                    <span style={{marginLeft: '16.67%'}}>{`顶部文案和底部文案若需要加样式，例：<p style="font-weight: bold;margin-bottom: 4.26667vw">你好</p><p>中国</p>`}</span>
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                            <Button type="primary">
                                提交
                            </Button>
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedBaseDrawer = Form.create<baseDrawerProps>({ name: 'baseDrawer' })(baseDrawer);
export default WrappedBaseDrawer