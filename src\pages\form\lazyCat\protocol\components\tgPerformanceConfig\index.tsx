import React, { useState, useEffect, FC } from 'react';

import { Button, Drawer, message, Popconfirm } from 'antd';
import {  STRATEGYTable, TATable } from '../../utiles';
import FormRender from 'form-render/lib/antd';
export interface IProps {
  visible: boolean;
  data: STRATEGYTable;
  onSubmit: (data: object) => void;
  onClose: () => void;
}
const TGStrategyAlterConfig: FC<IProps> = ({ visible, data, onSubmit, onClose }) => {
    const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  useEffect(()=>{
    if(visible){
      console.log(data)
      setData({
        showPerformance: data?.strategy?.showPerformance || ''
      })
    }
  },[visible,data])
  const schema = {
    type: 'object',
    required: ['showPerformance'],
    properties:{
      showPerformance: {
        title: '业绩表现是否展示',
        type: 'string',
        enum: [
          'Y',
          'N'
        ],
        enumNames: [
          '是',
          '否'
        ]
      },
    }
   
  };
  const handleSave=()=>{
    if (valid.length > 0) {
      message.error(`输入校验未通过，请检查`)
      return ;
    }
    onSubmit(formData);
  }
  return (
    <Drawer title={<>{`投顾机构：${data?.taName||'--'}`}<span style={{margin:'0 12px'}}></span>{`策略名称：${data?.investConsultTacticsName||'--'}`}</>} width={1000} visible={visible} onClose={onClose} >
      <div style={{ paddingTop: '40px' }}>
        <FormRender formData={formData} 
         onChange={setData}
         onValidate={setValid}
        labelWidth={160} displayType={'row'} propsSchema={schema} />
        <div style={{display:'flex',justifyContent:'flex-end'}}>
        <Button style={{marginRight:'12px'}} onClick={onClose}>取消</Button>
        <Popconfirm title={'确定要提交吗？'} cancelText={'取消'} okText={'确定'} onConfirm={handleSave}>
        <Button type='primary' >提交</Button>
        </Popconfirm>
        </div>
      </div>
    </Drawer>
  );
};
export default TGStrategyAlterConfig;
