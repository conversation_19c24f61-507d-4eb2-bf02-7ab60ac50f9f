import React, { useState } from 'react';
import {But<PERSON>, Popconfirm, Drawer, Table, Tag } from 'antd';
import WrappedInvestDrawer from './investDrawer';
import { timeFormat2 } from '@/utils/utils';

export interface iPopularityData {
  fundCode?: string;
  modifyDate?: string;
  synDate?: string;
  key?: string;
  description?: string;
  fundName?: string;
  oneMonthPopularity?: string;
  [key: string]: any;
}

interface iPopularityProps {
  isModify: boolean;
  handleModify: (flag: boolean) => void;
  handleData: (data: iPopularityData[]) => void;
  popularityData: iPopularityData[];
}
function PopularityInvestment({handleData, popularityData, isModify, handleModify}: iPopularityProps) {
    const [edit, setEdit] = useState(0); // 1 新增 2 编辑
    const [currentData, setCurrentData] = useState<iPopularityData>({});
    const columns = [
      {
        title: '基金名称',
        dataIndex: 'fundName',
        key: 'fundName',
      },
      {
        title: '基金ID',
        dataIndex: 'fundCode',
        key: 'fundCode',
      },
      {
        title: '更新时间',
        dataIndex: 'synDate',
        key: 'synDate',
        render: (synDate: string) => {
          return (
          <span>{synDate ?? '--'}</span>
          )
        }
      },
      {
        title: '修改时间',
        dataIndex: 'modifyDate',
        key: 'modifyDate',
        render: (modifyDate: string) => {
          return (
          <span>{modifyDate ?? '--'}</span>
          )
        }
      },
      {
        title: '实际人数',
        dataIndex: 'oneMonthPopularity',
        key: 'oneMonthPopularity',
        render: (oneMonthPopularity: string) => {
          return (
          <span>{oneMonthPopularity ?? '--'}</span>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text: any, record: iPopularityData) => {
          let key: any = record.key;
          return (
            <>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => handleEdit(record)}>编辑</Button>
            </>
          )
        }
      },
    ];

    const handleEdit = (val: iPopularityData) => {
      setCurrentData(val);
      setEdit(2);
    }
    const modifyPopularityData = (data: iPopularityData) => {
      if (!isModify) handleModify(true);
      let arr = [...popularityData];
      let time = timeFormat2();
      if (data?.key) {
        let index: any = data.key;
        data = {
          ...data,
          modifyDate: time,
        }
        arr[index] = data;
      } else {
        data = {
          ...data,
          modifyDate: time,
        }
        arr.push(data);
      }
      arr?.forEach((item: iPopularityData, index: number) => {
        item.key = index.toString();
      })
      handleData(arr);
    }
    const onEditClose = () => {
      setEdit(0);
    }

    return (
      <div>
        <h1 className="g-fs28 f-bold">人气定投：</h1>
        <Table
          columns={columns}
          dataSource={popularityData}
        />
        <Drawer
          width={1000}
          title="产品信息配置"
          placement="right"
          closable
          onClose={onEditClose}
          visible={Boolean(edit)}
          >
          { Boolean(edit) && <WrappedInvestDrawer currentData={currentData} onEditClose={onEditClose} handleData={(data) => modifyPopularityData(data)}></WrappedInvestDrawer> }
        </Drawer>
      </div>
    )
}

export default React.memo(PopularityInvestment);