import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import MessageForm from './message';
import StockForm from './stock';
import styles from './index.less';

const tabs = [
    '基本信息',
    '运营入口',
    '主理人微信文章',
    '主理人答疑',
    '用户精评',
    '组合调仓',
]
function investDrawer(props: any) {
    const { edit, queryAllData, currentData, onEditClose } = props;
    const [groupId, setGroupId] = useState('');
    const [choose, setChoose] = useState(0);
    const [remainShares, setRemainShares] = useState(currentData.remainShares ?? null);
    
    useEffect(() => {
        changeGroupId(props.currentData.groupId)
    }, [props.currentData.groupId])

    useEffect(() => {
        setRemainShares(props.currentData.remainShares)
    }, [props.currentData.remainShares])

    const changeGroupId = (val: string) => {
        setGroupId(val);
    }
    const selectTab = (num: number) => {
        if (edit === 1 && [1, 2, 3, 4, 5].includes(num)) {
            message.info('请先填写产品基本信息');
            return;
        }
        setChoose(num);
    }
    const handleRemainShares = (val: any) => {
        setRemainShares(val ?? remainShares);
    }
    return (
        <>
            <ul className={styles['m-cj-tabs']}>
                { tabs?.map((item, index) => {
                    return (
                        <li key={item} className={index === choose ? styles['m-cj-tab'] : ''} onClick={() => selectTab(index)}>
                            <span>{item}</span>
                        </li>
                    )
                }) }
            </ul>
            {  [0, 1, 2, 3, 4].includes(choose) && <MessageForm 
                currentData={currentData} 
                queryAllData={queryAllData} 
                edit={edit} 
                remainShares={remainShares}
                chooseType={choose} 
                onEditClose={onEditClose}
                />  
            }
            { choose === 5 && <StockForm queryAllData={queryAllData} groupId={groupId} handleRemainShares={handleRemainShares}/> }
        </>
    )
}
export default investDrawer;