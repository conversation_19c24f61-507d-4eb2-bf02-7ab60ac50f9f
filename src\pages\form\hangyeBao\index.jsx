/*
* 行业宝
* <AUTHOR>
* @time 2020.07
*/

import React from 'react';
import { autobind } from 'core-decorators';
import { Button, Table, message, Drawer, Popconfirm, Tag, Icon } from 'antd';
import FormRender from 'form-render/lib/antd';
import FORM_JSON from './form.json';
import api from 'api';
import Item from 'antd/lib/list/Item';

const { fetchHangYeBao, postHangYeBao, postHangYeBaoHash, fetchHangYeBaoHash } = api;

const schema = FORM_JSON.propsSchema;
const formData = FORM_JSON.formData;

@autobind
class hangyeBao extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            formData: formData,
            synStatus: true,
            visible: false,
            topList: [],
            upList: [],
            downList: []
        };
        this.topCol = [{
            title: '行业名称',
            dataIndex: 'name',
            key: 'name',
            },
            {
                title: '组合名称',
                dataIndex: 'groupname',
                key: 'groupname',
            },
            {
                title: '组合ID',
                dataIndex: 'groupid',
                key: 'groupid',
            },
            {
                title: '排序',
                dataIndex: 'rank',
                key: 'rank',
            },
            {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
            },
            {
                title: '修改时间',
                dataIndex: 'date',
                key: 'date',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (_, record) => (
                    <div>
                        <span style={{marginRight: 5}}>
                            <Button type="primary" onClick={() => this.handleEdit(record)} style={{ marginLeft: 4 }}>编辑</Button>
                        </span>
                        <span style={{marginRight: 5}}>
                            <Button onClick={() => this.handleTopToUp(record)}>下强推</Button>
                        </span>
                        <span style={{marginRight: 5}}>
                            <Button onClick={() => this.handleTopToDown(record)}>下架</Button>
                        </span>
                        <span>
                        <Popconfirm
                            title={`确定要删除${record.name}吗？`}
                            onConfirm={() => this.handleDeleteInTop(record)}
                            okText="是"
                            cancelText="取消"
                        >
                            <Button type="danger">删除</Button>
                            </Popconfirm>
                        </span>
                    </div>
                ),
            }
        ];
        
        this.upCol = [
            {
                title: '排序',
                dataIndex: 'sort',
                key: 'sort',
                render: (_, record) => (
                    <div>
                        <Icon type="up" onClick={() => this.handleMoveUp(record)} />
                        <Icon type="down" onClick={() => this.handleMoveDown(record)} />
                    </div>
                )
            },
            {
                title: '行业名称',
                dataIndex: 'name',
                key: 'name',
            },
            {
                title: '组合名称',
                dataIndex: 'groupname',
                key: 'groupname',
            },
            {
                title: '组合ID',
                dataIndex: 'groupid',
                key: 'groupid',
            },
            {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
            },
            {
                title: '修改时间',
                dataIndex: 'date',
                key: 'date',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (_, record) => (
                    <div>
                        <span style={{marginRight: 5}}>
                            <Button type="primary" onClick={() => this.handleEdit(record)}>编辑</Button>
                        </span>
                        <span style={{marginRight: 5}}> 
                            <Button onClick={() => this.handleUpToTop(record)}>上强推</Button>
                        </span>
                        <span style={{marginRight: 5}}>
                            <Button onClick={() => this.handleUpToDown(record)}>下架</Button>
                        </span>
                        <span>
                            <Popconfirm
                                title={`确定要删除${record.name}吗？`}
                                onConfirm={() => this.handleDeleteInUp(record)}
                                okText="是"
                                cancelText="取消"
                            >
                                <Button type="danger">删除</Button>
                            </Popconfirm>
                        </span>
                    </div>
                ),
            }
        ];

        this.downCol = [{
            title: '行业名称',
            dataIndex: 'name',
            key: 'name',
            },
            {
                title: '组合名称',
                dataIndex: 'groupname',
                key: 'groupname',
            },
            {
                title: '组合ID',
                dataIndex: 'groupid',
                key: 'groupid',
            },
            {
                title: '排序',
                dataIndex: 'rank',
                key: 'rank',
            },
            {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
            },
            {
                title: '修改时间',
                dataIndex: 'date',
                key: 'date',
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (_, record) => (
                    <div>
                        <span style={{marginRight: 5}}>
                            <Button type="primary" onClick={() => this.handleEdit(record)}>编辑</Button>
                        </span>
                        <span style={{marginRight: 5}}>
                            <Button onClick={() => this.handleDownToUp(record)}>上架</Button>
                        </span>
                        <span style={{marginRight: 5}}>
                            <Popconfirm
                                    title={`确定要删除${record.name}吗？`}
                                    onConfirm={() => this.handleDeleteInDown(record)}
                                    okText="是"
                                    cancelText="取消"
                                >
                                <Button type="danger">删除</Button>
                            </Popconfirm>
                        </span>
                    </div>
                ),
            }
        ]    
    }

    componentDidMount() {
        fetchHangYeBao().then((res) => {    
            if(res.code === '0000') {
                let data = JSON.parse(res.data);
                console.log(data);
                let _topList = data.topList;
                let _upList = data.upList;
                let _downList = data.downList;
                this.setState({
                    topList: _topList,
                    upList: _upList,
                    downList: _downList
                });
                // 判断是否已经同步
                fetchHangYeBaoHash().then((res) => {    
                    if(res.code === '0000') {
                        let data = JSON.parse(res.data);
                        let _topList = data.topList;
                        let _upList = data.upList;
                        console.log("fetch data", _topList, _upList)
                        console.log("local data", this.state.topList, this.state.upList)
                        if (JSON.stringify(_topList) === JSON.stringify(this.state.topList) && JSON.stringify(_upList) === JSON.stringify(this.state.upList)) {
                            this.setState({synStatus: true});
                        } else {
                            this.setState({synStatus: false});
                        }
                    } else {
                    message.error(res.status_msg)
                    }
                }).catch(e => console.log(e));
            } else {
              message.error(res.status_msg)
            }
        }).catch(e => console.log(e));
    }

    // 处理编辑按钮
    handleEdit = (record) => {
        console.log(record)
        this.setState({formData: record, visible: true});
    };

    // 处理下强推按钮
    handleTopToUp = (record) => {
        let _topList = this.state.topList;
        let _upList = this.state.upList;
        record.status = "上架";
        delete record.rank;
        _upList.push(record);
        _topList = _topList.filter(t => record.key !== t.key);
        this.setState({upList: _upList, topList: _topList});
    };

    // 处理置顶状态的下架按钮
    handleTopToDown = (record) => {
        let _topList = this.state.topList;
        let _downList = this.state.downList;
        record.rank = 0;
        record.status = "下架";
        _downList.push(record);
        _topList = _topList.filter(t => record.key !== t.key);
        this.setState({downList: _downList, topList: _topList});
    };

    // 处理置顶状态的删除按钮
    handleDeleteInTop = (record) => {
        let _topList = this.state.topList;
        _topList = _topList.filter(t => record.key !== t.key);
        this.setState({topList: _topList});
    };

    // 处理上强推按钮
    handleUpToTop = (record) => {
        let _upList = this.state.upList;
        let _topList = this.state.topList;
        record.rank = "置顶强推";
        record.status = "置顶强推";
        _topList.push(record);
        _upList = _upList.filter(t => record.key !== t.key);
        this.setState({upList: _upList, topList: _topList});
    };

    // 处理上架状态的下架按钮
    handleUpToDown = (record) => {
        let _downList = this.state.downList;
        let _upList = this.state.upList;
        record.rank = 0;
        record.status = "下架";
        _downList.push(record);
        _upList = _upList.filter(t => record.key !== t.key);
        this.setState({downList: _downList, upList: _upList});
    };

    // 处理上架状态的删除按钮
    handleDeleteInUp = (record) => {
        let _upList = this.state.upList;
        _upList = _upList.filter(t => record.key !== t.key);
        this.setState({upList: _upList});
    };

    // 处理上架按钮
    handleDownToUp = (record) => {
        let _downList = this.state.downList;
        let _upList = this.state.upList;
        record.status = "上架";
        delete record.rank;
        _upList.push(record);
        _downList = _downList.filter(t => record.key !== t.key);
        this.setState({downList: _downList, upList: _upList});
    };

    // 处理下架状态的删除按钮
    handleDeleteInDown = (record) => {
        let _downList = this.state.downList;
        _downList = _downList.filter(t => record.key !== t.key);
        this.setState({downList: _downList});
    };

    // 数组移动方法
    swapArray = (arr, index1, index2) => {
        arr[index1] = arr.splice(index2, 1, arr[index1])[0];
        return arr;
    }

    // 处理上移操作
    handleMoveUp = (record) => {
        let _upList = this.state.upList;
        let index = _upList.indexOf(record);
        if (index > 0) {
            this.swapArray(_upList, index, index - 1);
            this.setState({upList: _upList});
        } else {
            message.info("已经在第一位")
        }
    }

    // 处理下移操作
    handleMoveDown = (record) => {
        let _upList = this.state.upList;
        let index = _upList.indexOf(record);
        if (index + 1 < _upList.length) {
            this.swapArray(_upList, index, index + 1);
            this.setState({upList: _upList});
        } else {
            message.info("已经在最后一位")
        }
    }

    // 处理表单输入
    handleChange = formData => {
        this.setState({formData});
    };

    // 处理提交按钮
    handleClick = () => {
        let _topList = this.state.topList;
        let _upList = this.state.upList;
        let _downList = this.state.downList;
        let _formData = this.state.formData;
        let mDate = new Date(),
            mYear = mDate.getFullYear(),
            mMonth = mDate.getMonth() + 1,
            mDay = mDate.getDate(),
            mHour = mDate.getHours(),
            mMin = mDate.getMinutes(),
            mSec = mDate.getSeconds(),
            mTime = mYear + '-' + mMonth + '-' + mDay + ' ' + mHour + ':' + mMin + ':' + mSec;
        _formData.date = mTime;
        if (_formData.status === "置顶强推") {
            _topList = _topList.map((record, index) => record.key === _formData.key ? _formData : record);
            this.setState({topList: _topList, formData: formData, visible: false});
        } else if (_formData.status === "上架") {
            _upList = _upList.map((record, index) => record.key === _formData.key ? _formData : record);
            this.setState({upList: _upList, formData: formData, visible: false});
        } else if (_formData.status === "下架") {
            _downList = _downList.map((record, index) => record.key === _formData.key ? _formData : record);
            this.setState({downList: _downList, formData: formData, visible: false});
        } else {
            _formData.rank = 0;
            _formData.status = "下架";
            _formData.key = _formData.name;
            _downList.push(_formData);
            this.setState({downList: _downList, formData: formData, visible: false});
        }
    };

    // 处理保存按钮
    handleSave = () => {
        let _topList = this.state.topList;
        let _upList = this.state.upList;
        let _downList = this.state.downList;
        let _hangyeBao = {topList: _topList, upList: _upList, downList: _downList};
        if (_topList.length !== 1) {
            message.error("置顶强推区域必须有且只有一个行业推荐");
        } else if (_upList.length < 1 || _upList.length > 4) {
            message.error("上架区域行业推荐数量限定1～4个");
        } else {
            postHangYeBao({value:JSON.stringify(_hangyeBao)}).then((res)=>{
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success("保存成功");
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        }
    };

    // 处理同步按钮
    handlePost = () => {
        fetchHangYeBao().then((res) => {    
            if(res.code === '0000') {
                let data = JSON.parse(res.data);
                let _topList = data.topList;
                let _upList = data.upList;
                let _hangyeBao = {topList: _topList, upList: _upList}
                postHangYeBaoHash({value:JSON.stringify(_hangyeBao)}).then((res)=>{
                    try {
                        if (res.code !== '0000') {
                            message.error(res.message);
                        } else {
                            message.success("同步成功");
                            this.setState({synStatus: true});
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        }
                    } catch (e) {
                        message.error(e.message);
                    }
                })
            } else {
              message.error(res.status_msg)
            }
        }).catch(e => console.log(e));
    }

    // 处理新增按钮
    showModal = () => {
        this.setState({visible: true, formData: formData});
    };
    
    // 处理取消按钮
    handleClose = e => {
        this.setState({visible: false});
    };

    render () {
        let _topCol = this.topCol;
        let _upCol = this.upCol;
        let _downCol = this.downCol;
        return (
            <div>
                <div style={{marginBottom: 30}}>
                    <h3>置顶状态</h3>
                    <Table dataSource={this.state.topList} columns={_topCol} pagination={ false } />
                </div>     
                <div style={{marginBottom: 30}}>
                    <h3>上架状态</h3>
                    <Table dataSource={this.state.upList} columns={_upCol} pagination={ false } />
                </div>
                <div style={{marginBottom: 30}}>
                    <h3>下架状态</h3>
                    <Table dataSource={this.state.downList} columns={_downCol} pagination={ false } />
                </div>
                <Drawer
                    title="添加行业"
                    width={720}
                    visible={this.state.visible}
                    onClose={this.handleClose}
                    bodyStyle={{ paddingBottom: 20 }}
                >
                    <FormRender
                        propsSchema={schema}
                        formData={this.state.formData}
                        onChange={this.handleChange}
                        showDescIcon={true}
                        displayType="row"
                    />
                    <div style={{textAlign: 'right'}}>
                        <Button onClick={this.handleClose} style={{ marginRight: 20 }}>
                            取消
                        </Button>
                        <Button onClick={this.handleClick} type="primary">
                            提交
                        </Button>
                    </div>
                </Drawer>
                <Tag color={this.state.synStatus ?  "green" : "red"}>{this.state.synStatus ?  "已同步" : "未同步"}</Tag>
                <span style={{marginRight: 10}}>
                    <Button type="primary" onClick={this.showModal}>新增</Button>
                </span>
                <span style={{marginRight: 10}}>
                    <Button type="primary" onClick={this.handleSave}>保存</Button>
                </span>
                <Popconfirm
                    title="确定已经保存完毕，要进行同步上线操作？"
                    onConfirm={this.handlePost}
                    okText="是"
                    cancelText="取消"
                >
                    <Button type="primary">同步</Button>
                </Popconfirm>
            </div>
        )
    }
}

export default hangyeBao;
