import React from 'react';
import DragSort from './DragSort.jsx';
import { FormComponentProps } from 'antd/es/form';
import api from 'api';
import moment from 'moment';
import {Button, Popconfirm, Form, Input, message, DatePicker } from 'antd';

const { TextArea } = Input;
const { fetchIndexFreeRide, postIndexFreeRide } = api;

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18
  }
};
interface RideFormProps extends FormComponentProps {
  [key: string]: any
}
interface iRideDetail {
  key?: number,
  index: string,
  event: string,
  chances: string,
  fundCode: string,
  fundName: string,
  fundMarketid?: string,
  tradeMarketid?: string,
  fundta: string,
  tradeCode: string,
  tradeName: string,
  buttonText: string,
  [key: string]: any
}
class FreeRide extends React.Component<RideFormProps, any> {
    state = {
      init: false,
      isFirst: true,
      dataSource: [], // 上架数据
      dataSource1: [], // 下架数据
      currentIndex: {
        index: '',
        event: '',
        chances: '',
        fundCode: '',
        fundName: '',
        fundMarketid: '',
        tradeMarketid: '',
        fundta: '',
        tradeCode: '',
        tradeName: '',
        buttonText: ''
      },
      type: '0', // 上架 0 下架 1,
      key: 0, // 数组索引
      isAdd: null, // 0 新增 1 编辑
      date: null, // 最新车次
      pushId: '', // push id
      etfs: '' // 上期发车ETF
    }
    
    columns = [
      {
        title: '指数',
        dataIndex: 'index',
        key: 'index',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => {
          return (
          <span>{status === '0' ? '上架推荐' : '已下架'}</span>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text: any, record: any) => {
          const { key, status } = record;
          return (
            <>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => this.editFund(record)}>编辑</Button>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => this.setFund(key, status)}>下架</Button>
              <Popconfirm title="确定删除?" onConfirm={() => this.deleteFund(key, status)}>
                  <Button type="primary">删除</Button>
              </Popconfirm>
            </>
          )
        }
      },
    ];
    columns1 = [
      {
        title: '指数',
        dataIndex: 'index',
        key: 'index',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => {
          return (
          <span>{status === '0' ? '上架推荐' : '已下架'}</span>
          )
        }
      },
      {
        title: '操作',
        dataIndex: 'operation',
        render: (text: any, record: any) => {
          const { key, status } = record;
          return (
            <>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => this.editFund(record)}>编辑</Button>
              <Button type="primary" style={{ marginRight: '20px' }} onClick={() => this.setFund(key, status)}>上架</Button>
              <Popconfirm title="确定删除?" onConfirm={() => this.deleteFund(key, status)}>
                  <Button type="primary">删除</Button>
              </Popconfirm>
            </>
          )
        }
      },
    ];

    init = () => {
      fetchIndexFreeRide().then((res: any) => {
        let { code, data } = res;
        if ( code === '0000') {
          data = JSON.parse(data);
          const { upData, downData, date, pushId, etfs } = data;
          this.setState({
            init: true,
            dataSource: upData || [],
            dataSource1: downData || [],
            date: date || null,
            pushId: pushId || '',
            etfs: etfs || ''
          })
        }
      }).catch((e: Error) => {
        message.error(e?.message);
      })
    }
    handleDataSource = (dragIndex: number, hoverIndex: number, index: number) => {
      const { dataSource, dataSource1 } = this.state;
      let arr = index === 0 ? [...dataSource] : [...dataSource1];
      const dragRow = arr[dragIndex];
      arr.splice(dragIndex, 1);
      arr.splice(hoverIndex, 0, dragRow);
      this.setState({
        [`dataSource${index === 0 ? '' : index}`]: [...arr]
      })
    }
    // 保存编辑项 
    handleSubmit = () => {
      this.props.form.validateFields((err, values) => {
        if (!err) {
          const { dataSource, dataSource1, isAdd, type, key } = this.state;
          let obj = {
            key: 0,
            status: '0',
            ...values
          }
          let arr = [];
          if (isAdd === 0) {
            // 新增
            arr = JSON.parse(JSON.stringify(dataSource));
          } else if (isAdd === 1) {
            // 编辑
            arr = JSON.parse(JSON.stringify(type === '0' ? dataSource : dataSource1));
            obj.status = type;
            arr.splice(key, 1);
          }
          arr.unshift({...obj});
          arr?.length > 0 && arr.forEach((item: iRideDetail, num: number) => {
            item.key = num;
          })
          this.setState({
            isFirst: true,
            dataSource: (isAdd === 0 || type === '0') ? arr : dataSource,
            dataSource1:  (isAdd === 1 && type === '1') ? arr : dataSource1
          });
        }
      });
    } 
    cancel = () => {
      this.setState({
        isFirst: true,
      })
    }
    // 新增指数
    addFreeRide = () => {
      const { dataSource } = this.state;
      if (dataSource?.length >= 3) {
        message.info('上架状态的顺风车最多3个');
        return;
      }
      this.setState({
        isFirst: false,
        isAdd: 0,
        currentIndex: {
          index: '',
          event: '',
          chances: '',
          fundCode: '',
          fundName: '',
          fundMarketid: '',
          tradeMarketid: '',
          fundta: '',
          tradeCode: '',
          tradeName: '',
          buttonText: ''
        }
      })
    }
    // 同步数据
    saveFreeRide = () => {
      const { dataSource, dataSource1, date, pushId, etfs } = this.state;
      dataSource?.length > 0 && dataSource.forEach((item: iRideDetail, num: number) => {
        item.key = num;
      })
      dataSource1?.length > 0 && dataSource1.forEach((item: iRideDetail, num: number) => {
        item.key = num;
      })
      let params = {
        upData: dataSource,
        downData: dataSource1,
        date,
        pushId,
        etfs
      };
      postIndexFreeRide({
        value: JSON.stringify(params),
      }).then((res: any) => {
          if (res.code !== '0000') {
              message.error(res?.msg || '同步失败');
          } else {
              message.success('同步成功！');
          }
      }).catch((e: Error) => {
          message.error(e?.message || '同步失败');
      });
    }
    // 编辑数据
    editFund = (record: iRideDetail) => {
      const { key, status, ...rest } = record;
      this.setState({
        isFirst: false,
        isAdd: 1,
        currentIndex: {...rest},
        type: status,
        key
      })
    }
    // 上下架
    setFund = (key: number, status: string) => {
      const { dataSource, dataSource1 } = this.state;
      let arr = JSON.parse(JSON.stringify(dataSource));
      let arr1 = JSON.parse(JSON.stringify(dataSource1));
      if (status === '0') {
        // 下架
        let obj = arr[key];
        arr.splice(key, 1);
        obj.status = '1';
        arr1.unshift(obj);
      } else if (status === '1') {
        // 上架
        if (dataSource?.length === 3) {
          message.info('上架状态的顺风车最多3个, 请先下架一个');
          return;
        }
        let obj = arr1[key];
        arr1.splice(key, 1);
        obj.status = '0';
        arr.unshift(obj);
      }
      arr?.length > 0 && arr.forEach((item: iRideDetail, num: number) => {
        item.key = num;
      })
      arr1?.length > 0 && arr1.forEach((item: iRideDetail, num: number) => {
        item.key = num;
      })
      this.setState({
        dataSource: arr,
        dataSource1: arr1
      })
    }
    // 删除数据
    deleteFund = (key: number, status: string) => {
      const { dataSource, dataSource1 } = this.state;
      let arr = JSON.parse(JSON.stringify(status === '0' ? dataSource : dataSource1));
      arr.splice(key, 1);
      arr?.length > 0 && arr.forEach((item: iRideDetail, num: number) => {
        item.key = num;
      })
      this.setState({
        [`dataSource${status === '0' ? '' : status}`]: [...arr]
      });
    }
    handleDate = (date: any, dateString: string) => {
      this.setState({
        date: dateString || null
      })
    }
    handlePushId = (e: any) => {
      this.setState({
        pushId: e.target.value
      })
    }
    handleETFs = (e: any) => {
      this.setState({
        etfs: e.target.value
      })
    }
    componentDidMount() {
      this.init();
    }
    render() {
      const { isFirst, dataSource, dataSource1, init, currentIndex, date, pushId, etfs } = this.state;
      const { getFieldDecorator } = this.props.form;
      if (!init) {
        return '加载中'
      }
      return (
        <div style={{ padding: 40 }}>
            <h1 className="g-fs28 f-bold">顺风车配置页面</h1>
            { isFirst ? <>
              <section>
                <h3 className="g-fs20 u-block_il" style={{marginRight: 20}}>最新车次</h3>
                <DatePicker onChange={this.handleDate} value={date ? moment(date, 'YYYY-MM-DD') : null}/>
              </section>
              <section>
                <h3 className="g-fs20 u-block_il" style={{marginRight: 20}}>pushId</h3>
                <div className="u-block_il">
                  <Input onChange={this.handlePushId} value={pushId}/>
                </div>
              </section>
              <section>
                <h3 className="g-fs20 u-block_il" style={{marginRight: 20}}>上期发车ETF</h3>
                <div className="u-block_il" style={{marginRight: 20, width: '190px'}}>
                  <Input onChange={this.handleETFs} value={etfs}/>
                </div>
                <span>例：159841,159997,512880</span>
              </section>
              <section>
                  <h3 className="g-fs24">上架状态</h3>
                  <DragSort index={0} columns={this.columns} dataSource={dataSource} handleDataSource={(dragIndex: number, hoverIndex: number, index: number) => this.handleDataSource(dragIndex, hoverIndex, index)}></DragSort>
              </section>
              <section>
                  <h3 className="g-fs24">下架状态</h3>
                  <DragSort index={1} columns={this.columns1} dataSource={dataSource1} handleDataSource={(dragIndex: number, hoverIndex: number, index: number) => this.handleDataSource(dragIndex, hoverIndex, index)}></DragSort>
              </section>
              <div style={{marginTop: 20}}>
                  <Button type="primary" style={{marginRight: 20}} onClick={this.addFreeRide}>新增</Button>
                  <Button type="primary" style={{marginRight: 20}} onClick={this.saveFreeRide}>同步</Button>
              </div>
            </> : <>
                <p>例：机会解读  
                  <span className="u-block">券商基本面增速较好，信用风险不断下降#titleEnd#</span>
                  <span className="u-block">#content#从券商基本面来看，无论是20年还是21Q1业绩增速均在30%左右，自身信用风险也在不断下降，券商股价持续低迷与市场对不确定性较高的股票偏好程度较低有关。#contentEnd#</span>
                  <span className="u-block">券商基本面增速较好，信用风险不断下降#titleEnd#</span>
                  <span className="u-block">#content#从券商基本面来看，无论是20年还是21Q1业绩增速均在30%左右，自身信用风险也在不断下降，券商股价持续低迷与市场对不确定性较高的股票偏好程度较低有关。</span>
                </p>
                <Form {...formItemLayout} onSubmit={this.handleSubmit}>
                    <Form.Item label="指数" wrapperCol={{span: 3}}>
                        {getFieldDecorator('index', {
                            initialValue: currentIndex.index || '',
                            rules: [{ required: true, message: '请输入指数名称' }],
                        })(
                            <Input maxLength={6}/>
                        )}
                    </Form.Item>
                    <Form.Item label="利好事件">
                        {getFieldDecorator('event', {
                            initialValue: currentIndex.event,
                            rules: [{ required: true, message: '请输入利好事件文案' }],
                        })(
                            <TextArea rows={4}/>
                        )}
                    </Form.Item>
                    <Form.Item label="机会解读">
                        {getFieldDecorator('chances', 
                        {
                            initialValue: currentIndex.chances,
                            rules: [{ required: true, message: '请输入机会解读文案' }],
                        }
                        )(
                            <TextArea rows={6}/>
                        )}
                    </Form.Item>
                    <Form.Item label="推荐基金代码" wrapperCol={{span: 3}}>
                        {getFieldDecorator('fundCode', {
                            initialValue: currentIndex.fundCode,
                            rules: [{ required: true, message: '请输入推荐基金代码' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="推荐基金名称" wrapperCol={{span: 3}}>
                        {getFieldDecorator('fundName', {
                            initialValue: currentIndex.fundName,
                            rules: [{ required: true, message: '请输入推荐基金名称' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="推荐基金市场" wrapperCol={{span: 3}}>
                        {getFieldDecorator('fundMarketid', {
                            initialValue: currentIndex.fundMarketid,
                            rules: [{ required: true, message: '请输入推荐基金市场' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="推荐基金埋点" wrapperCol={{span: 3}}>
                        {getFieldDecorator('fundta', {
                            initialValue: currentIndex.fundta,
                            rules: [{ required: true, message: '请输入推荐基金埋点' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="跟踪指数代码" wrapperCol={{span: 3}}>
                        {getFieldDecorator('tradeCode', {
                            initialValue: currentIndex.tradeCode,
                            rules: [{ required: true, message: '请输入跟踪指数代码' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="跟踪指数名称" wrapperCol={{span: 4}}>
                        {getFieldDecorator('tradeName', {
                            initialValue: currentIndex.tradeName,
                            rules: [{ required: true, message: '请输入跟踪指数名称' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="跟踪指数市场代码" wrapperCol={{span: 3}}>
                        {getFieldDecorator('tradeMarketid', {
                            initialValue: currentIndex.tradeMarketid,
                            rules: [{ required: true, message: '请输入跟踪指数市场代码' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="按钮文案" wrapperCol={{span: 3}}>
                        {getFieldDecorator('buttonText', {
                            initialValue: currentIndex.buttonText,
                            rules: [{ required: true, message: '请输入按钮文案' }],
                        })(
                            <Input maxLength={6}/>
                        )}
                    </Form.Item>
                    <Form.Item className="f-tc">
                        <Button type="primary" htmlType="submit" style={{marginRight: 20}}>
                            保存
                        </Button>
                        <Button type="primary" onClick={this.cancel}>
                            取消
                        </Button>
                    </Form.Item>
                </Form>
            </>}
        </div>
    )
    }
    
}

const WrappedFreeRide = Form.create<RideFormProps>({ name: 'freeRide' })(FreeRide);
export default WrappedFreeRide;