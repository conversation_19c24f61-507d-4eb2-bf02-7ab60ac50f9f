import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';
import { ConfigData } from '../../type';
import { Input, Switch, DatePicker } from 'antd';
import InputTitle from '../InputTitle';
import UploadImg from '../UploadImg';
import { mapPropToName } from './helper';
import moment from 'moment';

export interface IProps {
  data: ConfigData['banner'];
  onChange: (data: ConfigData['banner']) => void;
  disabled: boolean;
}
const Banner: FC<IProps> = ({ data, onChange, disabled }) => {
  const handleChange = (name, value) => {
    onChange({
      ...data,
      [name]: value,
    });
  };
  return (
    <div>
      <h3>
        活动banner{' '}
        <Switch
          disabled={disabled}
          checked={data.open}
          onChange={v => handleChange('open', v)}
        ></Switch>
      </h3>
      <UploadImg
        value={data.img}
        disabled={disabled}
        onChange={v => handleChange('img', v)}
        title={mapPropToName['img']}
        isRequired
        size={['343*88']}
      ></UploadImg>
      <Input
        value={data.name}
        onChange={e => handleChange('name', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10, width: 300 }}
        addonBefore={<InputTitle title={mapPropToName['name']} />}
      />
      <Input
        value={data.link}
        onChange={e => handleChange('link', e.target.value)}
        disabled={disabled}
        style={{ marginBottom: 10 }}
        addonBefore={<InputTitle title={mapPropToName['link']} />}
      />

      <div style={{ marginBottom: 10, width: 300 }}>
        <InputTitle title={mapPropToName['startTime']} isRequired={false} />
        <DatePicker
          value={data.startTime ? moment(data.startTime) : ''}
          onChange={(date, dateString) => handleChange('startTime', dateString)}
          showTime
          disabled={disabled}
          placeholder={'请选择日期'}
        />
      </div>
      <div style={{ marginBottom: 10, width: 300 }}>
        <InputTitle title={mapPropToName['endTime']} isRequired={false} />
        <DatePicker
          value={data.endTime ? moment(data.endTime) : ''}
          onChange={(date, dateString) => handleChange('endTime', dateString)}
          showTime
          disabled={disabled}
          placeholder={'请选择日期'}
        />
      </div>
    </div>
  );
};
export default Banner;
