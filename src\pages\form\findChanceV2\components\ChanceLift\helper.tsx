import { FundRecommend, ChanceLiftType, ChanceLift } from '../../type';
import { checkRequiredAndThrow } from '../../utils';
import { fundRecommendHelper, mapPropToName as fmapPropToName } from '../FundRecommend/helper';
import { streamCardHelper, mapPropToName as smapPropToName } from '../StreamCard/helper';
export const mapPropToName = {
  ...fmapPropToName,
  ...smapPropToName,
};
export const chanceLiftHelper = {
  mapPropToName: function(type) {
    if (type === ChanceLiftType.FUND) {
      return fmapPropToName;
    } else {
      return smapPropToName;
    }
  },
  addArrayData: function(type): ChanceLift {
    if (type === ChanceLiftType.FUND) {
      return fundRecommendHelper.addArrayData();
    } else {
      return streamCardHelper.addArrayData();
    }
  },
  addInit: function(): ChanceLift[] {
    return [];
  },

  checkData: function(data: ChanceLift[]) {
    if(data?.length>3){
      throw new Error(`机会顺风车不得超过3项`);
    }
    data.forEach((item, index) => {
      try {
        if (item.$type === ChanceLiftType.FUND) {
          return fundRecommendHelper.checkSingleData(item);
        } else {
          return streamCardHelper.checkSingleData(item);
        }
      } catch (e) {
        throw new Error(`机会顺风车-${e.message}(第${index + 1}项)`);
      }
    });
    return true;
  },
};
