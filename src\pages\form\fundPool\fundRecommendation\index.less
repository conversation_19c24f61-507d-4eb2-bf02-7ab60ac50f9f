.fund-recommendation {
    width: 100%;
    padding: 10px 20px;
    .fund-header {
        position: relative;
        margin-bottom: 76px;
        &-left {
            position: absolute;
            top: 0;
            left: 0;
            span {
                display: block;
                color: #FF0000
            }
        }
        &-middle {
            font-size: 28px;
            font-weight: bold;
            text-align: center;
        }
    }
    .remark {
        span {
            display: block;
            &:first-child, &:nth-child(2) {
                color: red;
            }
        }
    }
    .f-center {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    textarea{
        resize:none
    }
}
:global {
    .recommendation-wrapper {
        .ant-modal-body {
            padding: 20px;
            .ant-form {
                .ant-form-item {
                    margin-bottom: 16px;
                }
            }
        }
    }
}