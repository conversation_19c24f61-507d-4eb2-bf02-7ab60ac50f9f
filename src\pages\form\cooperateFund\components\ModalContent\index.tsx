import React, { useEffect, useState } from 'react';
import { Form, Input, Radio, Row, Col, message, Button, Spin, Modal, Select } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import { getFundDetail, getFundPopularity, getFundSelfSelect, getPlateData, saveFundData } from '../../services';
import { FundFormData, PlateData, CooperateStatus } from '../../types';
import './index.less';
const { Option } = Select;

/**
 * 判断数值是否有效（不为undefined、null、空对象）
 * @param value 要判断的值
 * @returns boolean
 */
const isValidValue = (value: any): boolean => {
  return value !== undefined && value !== null;
};

interface ModalContentProps extends FormComponentProps {
  showDialog: boolean;
  onEditClose: () => void;
  currentData: FundFormData;
  edit: number;
  queryList: () => void;
  children?: React.ReactNode;
}

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 18,
  },
};
const marginStyle = { marginBottom: '10px' };

const ModalContent: React.FC<ModalContentProps> = ({
  showDialog,
  onEditClose,
  edit,
  currentData,
  form,
  queryList,
}) => {
  const { getFieldDecorator } = form;
  const [loading, setLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<FundFormData>({} as FundFormData);
  const [optionList, setOptionList] = useState<PlateData[]>([]);

  // 百分比校验规则（只允许整数）
  const getPercentageValidationRules = () => [
    {
      pattern: /^\d+%?$/,
      message: '请输入有效的整数百分比格式'
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value) {
          const numValue = parseInt(value.toString().replace('%', ''));
          if (numValue < 0) {
            callback('百分比不能小于0%');
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    }
  ];

  useEffect(() => {
    if (edit === 0) {
      form.resetFields();
    }
  }, []);

  useEffect(() => {
    // 处理编辑时百分比字段的显示，将小数转换为百分比
    const processedData = { ...currentData };
    if (processedData.popularityPercent !== undefined && processedData.popularityPercent !== null && processedData.popularityPercent !== '') {
      processedData.popularityPercent = (parseFloat(processedData.popularityPercent) * 100).toString();
    }
    if (processedData.selfSelectCountPercent !== undefined && processedData.selfSelectCountPercent !== null && processedData.selfSelectCountPercent !== '') {
      processedData.selfSelectCountPercent = (parseFloat(processedData.selfSelectCountPercent) * 100).toString();
    }
    setFormData({ ...processedData });
    
    // 编辑状态下，如果有基金代码，自动获取当前热度值和自选值
    if (edit !== 0 && processedData.fundCode) {
      setLoading(true);
      
      // 并行获取热度值和自选值
      Promise.all([
        getFundPopularity(processedData.fundCode),
        getFundSelfSelect(processedData.fundCode)
      ])
        .then(([popularityData, selfSelectData]) => {
          const updates: Partial<FundFormData> = {};
          
          // 处理热度数据
          if (popularityData && isValidValue(popularityData.rate)) {
            updates.currentPopularity = popularityData.rate;
          }
          
          // 处理自选值数据
          if (selfSelectData && isValidValue(selfSelectData.rate)) {
            updates.currentSelfSelectCount = selfSelectData.rate;
          }
          
          // 将获取到的值设置到表单和状态中
          if (Object.keys(updates).length > 0) {
            form.setFieldsValue(updates);
            setFormData((preForm: FundFormData) => ({ 
              ...preForm, 
              ...updates
            }));
          }
        })
        .catch((error) => {
          console.error('获取数据失败:', error);
          Modal.error({ content: error.message || '获取数据失败，请稍后重试' });
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [currentData, edit]);

  const onSubmit = () => {
    form.validateFields((err: any, values: any) => {
    //   console.log(values);
      for (let i in formData.nickNameList) {
        if (!new RegExp(/^[A-Za-z0-9\u4e00-\u9fa5]+$/).test(formData.nickNameList[i])) {
          message.error(`别名${Number(i) + 1}:${formData.nickNameList[i]}只能为汉字、字母、数字!`);
          return;
        }
      }
      if (Array.from(new Set(formData.nickNameList)).length !== formData.nickNameList.length) {
          message.error('别名有重复项，请检查')
          return;
      }
      if (!err) {
        handleSave(values);
      } else {
        message.error('请检查必填项');
      }
    });
  };

  /**
   * @description: 新增或编辑保存调用ajax方法
   */

  const handleSave = (values: FundFormData) => {
    const processedValues = { ...values };
    if (processedValues.popularityPercent) {
      const percentValue = parseFloat(processedValues.popularityPercent.toString().replace('%', ''));
      processedValues.popularityPercent = percentValue / 100;
    }
    if (processedValues.selfSelectCountPercent) {
      const percentValue = parseFloat(processedValues.selfSelectCountPercent.toString().replace('%', ''));
      processedValues.selfSelectCountPercent = percentValue / 100;
    }
    
    const _val = { ...processedValues, ...{ nickNameList: formData.nickNameList, themeList: formData.themeList } };
    setLoading(true);
    
    saveFundData(_val, _val.fundCode, edit !== 0)
      .then(() => {
        queryList();
        onEditClose();
      })
      .catch((error) => {
        Modal.error({ content: error.message || '保存失败，请稍后重试' });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * @description: 别名相关操作函数（新增，change，删除）
   */
  const addNickName = () => {
    const _nickNameList = formData?.nickNameList || [];
    _nickNameList.push('');
    setFormData((preForm: any) => ({ ...preForm, ...{ nickNameList: _nickNameList } }));
  };
  const changeNickName = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const _nickNameList = formData?.nickNameList;
    _nickNameList[index] = e.target.value;
    setFormData((preForm: any) => ({ ...preForm, ...{ nickNameList: _nickNameList } }));
  };
  const deleteNickName = (e: any, index: number) => {
    const _nickNameList = formData?.nickNameList;
    _nickNameList.splice(index, 1);
    setFormData((preForm: any) => ({ ...preForm, ...{ nickNameList: _nickNameList } }));
  };

  /**
   * @description: 输入基金代码失焦点查询中台数据
   */

  const onBlur = () => {
    if (!form.getFieldValue('fundCode')) return;
    if (form.getFieldValue('fundCode').length < 6) {
      message.warning('请检查基金代码');
      return;
    }
    setLoading(true);
    
    // 并行获取基金详情、热度值和自选值
    Promise.all([
      getFundDetail(form.getFieldValue('fundCode')),
      getFundPopularity(form.getFieldValue('fundCode')),
      getFundSelfSelect(form.getFieldValue('fundCode'))
    ])
      .then(([detailData, popularityData, selfSelectData]) => {
        // 设置基金基本信息
        form.setFieldsValue({
          simpleFundName: detailData.simpleFundName,
          jumpCode: detailData.jumpCode,
          expSecurityName: detailData.expSecurityName,
          subMarket: detailData.subMarket
        });
        
        const updates: Partial<FundFormData> = {};
        
        // 处理热度数据
        if (popularityData && isValidValue(popularityData.rate)) {
          updates.currentPopularity = popularityData.rate;
        }
        
        // 处理自选值数据
        if (selfSelectData && isValidValue(selfSelectData.rate)) {
          updates.currentSelfSelectCount = selfSelectData.rate;
        }
        
        // 更新表单和状态
        if (Object.keys(updates).length > 0) {
          form.setFieldsValue(updates);
          setFormData((preForm: FundFormData) => ({ 
            ...preForm, 
            ...updates
          }));
        }
      })
      .catch((error) => {
        console.error('获取数据失败:', error);
        Modal.error({ content: error.message || '获取数据失败，请稍后重试' });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /** 操作 */
  const renderPlate = (text: string, record: any) => {
    const themeList = formData?.themeList || [];
    const defaultValue = themeList.map((item: any) => `${item.name} ${item.thscodeHq}`);
    return <Form.Item label="相关板块">
      <Row type="flex" align="middle">
        <Col span={23}>
          <Select
            mode="multiple"
            style={{ width: '100%' }}
            placeholder="查询板块"
            defaultValue={defaultValue}
            optionLabelProp="label"
            onSearch={onSearch}
            onDeselect={onDeselect}
            onSelect={onSelect}
          >
            {
              optionList.map((item: any) => (
                <Option key={item.thscodeHq} value={`${item.name} ${item.thscodeHq}`} label={`${item.name} ${item.thscodeHq}`}>
                  {item.name} {item.thscodeHq}
                </Option>
              ))
            }
          </Select>
        </Col>
      </Row>
    </Form.Item>
  }

  let timeout: NodeJS.Timeout | null = null;
  const onSearch = (value: string) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      getPlateData(value)
        .then(data => {
          data && setOptionList([data]);
        })
        .catch(error => {
          console.error('获取板块数据失败:', error);
        })
        .finally(() => {
          setLoading(false);
        });
    }, 400);
  };

  const onSelect = (value: string) => {
    const themeList = formData?.themeList || [];
    const selectItem = optionList.find((item: PlateData) => value.indexOf(item.thscodeHq) > -1);
    if (selectItem) {
      const newList = [...themeList, selectItem];
      setFormData((preForm: FundFormData) => ({ ...preForm, ...{ themeList: newList } }));
    }
  }

  const onDeselect = (value: string) => {
    const themeList = formData?.themeList || [];
    const index = themeList.findIndex((item: PlateData) => value.indexOf(item.thscodeHq) > -1);
    const newList = [...themeList];
    newList.splice(index, 1);
    setFormData((preForm: FundFormData) => ({ ...preForm, ...{ themeList: JSON.parse(JSON.stringify(newList)) } }));
  }

  return (
    <Modal
      visible={showDialog}
      maskClosable={false}
      title={edit === 0 ? '添加' : '编辑'}
      closable={false}
      onCancel={onEditClose}
      footer={
        <Spin spinning={loading}>
          <Button key="back" onClick={onEditClose}>
            取消
          </Button>
          <Button htmlType="submit" key="submit" type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Spin>
      }
    >
      <Spin spinning={loading}>
        <Form {...formItemLayout} onSubmit={onSubmit} labelAlign="left">
          <Form.Item label="基金代码" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('fundCode', {
              initialValue: formData.fundCode,
              rules: [{ required: true, message: '请输入基金代码' }],
            })(<Input onBlur={onBlur} />)}
          </Form.Item>
          <Form.Item label="跳转代码" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('jumpCode', {
              initialValue: formData.jumpCode,
              rules: [{ required: true, message: '请输入跳转代码' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="市场代码" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('subMarket', {
              initialValue: formData.subMarket,
              rules: [{ required: true, message: '请输入市场代码' }],
            })(<Input />)}
          </Form.Item>
          <Form.Item label="扩展证券名称" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('expSecurityName', {
              initialValue: formData.expSecurityName,
              rules: [{ required: true, message: '请输入扩展证券名称' }],
            })(<Input disabled />)}
          </Form.Item>
          <Form.Item label="基金名称">
            <Row>
              <Col span={17}>
                {getFieldDecorator('simpleFundName', {
                  initialValue: formData.simpleFundName,
                  rules: [{ required: true, message: '请输入基金名称' }],
                })(<Input disabled />)}
              </Col>
              <Col offset={1} span={6}>
                <Button type="primary" onClick={addNickName}>
                  添加
                </Button>
              </Col>
            </Row>
          </Form.Item>
          {formData?.nickNameList?.length > 0 &&
            formData.nickNameList.map((item: string, index: number) => {
              return (
                <Form.Item label={`别名${index + 1}`} key={index}>
                  <Row type="flex" align="middle">
                    <Col span={17}>
                      <Input
                        value={item}
                        onChange={e => {
                          changeNickName(e, index);
                        }}
                      />
                    </Col>
                    <Col offset={1} span={6}>
                      <Button
                        type="danger"
                        onClick={e => {
                          deleteNickName(e, index);
                        }}
                      >
                        删除
                      </Button>
                    </Col>
                  </Row>
                </Form.Item>
              );
            })}
          {renderPlate('', {})}
          <Form.Item label="是否合作">
            {getFieldDecorator('isCooperate', {
              initialValue: formData.isCooperate,
              rules: [{ required: true, message: '请选择是否合作' }],
            })(
              <Radio.Group>
                <Radio style={marginStyle} value={'1'}>
                  是
                </Radio>
                <Radio style={marginStyle} value={'0'}>
                  否
                </Radio>
              </Radio.Group>,
            )}
          </Form.Item>
          <Form.Item label="当前热度值" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('currentPopularity', {
              initialValue: formData.currentPopularity,
            })(<Input disabled placeholder="输入基金代码后自动获取" />)}
          </Form.Item>
          <Form.Item label="ETF热度(数值)" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('popularity', {
              initialValue: formData.popularity,
            })(<Input />)}
          </Form.Item>
          <Form.Item label="ETF热度(百分比)" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('popularityPercent', {
              initialValue: formData.popularityPercent,
              rules: getPercentageValidationRules(),
            })(<Input placeholder="例：10" addonAfter="%" />)}
          </Form.Item>
          <Form.Item label="当前自选值" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('currentSelfSelectCount', {
              initialValue: formData.currentSelfSelectCount,
            })(<Input disabled placeholder="输入基金代码后自动获取" />)}
          </Form.Item>
          <Form.Item label="ETF自选数(数值)" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('selfSelectCount', {
              initialValue: formData.selfSelectCount,
            })(<Input />)}
          </Form.Item>
          <Form.Item label="ETF自选数(百分比)" wrapperCol={{ span: 12 }}>
            {getFieldDecorator('selfSelectCountPercent', {
              initialValue: formData.selfSelectCountPercent,
              rules: getPercentageValidationRules(),
            })(<Input placeholder="例：10" addonAfter="%" />)}
          </Form.Item>
          <div className="modal-content-tip-container">
            <div className="tip-title">温馨提示：</div>
            <div>1. 输入基金代码，点击弹窗空白位置，查询最新热度值和自选人数</div>
            <div>2. 当前热度/自选值 = 实际热度/自选值 × ETF热度/自选(百分比) + ETF热度/自选(数值)</div>
          </div>
        </Form>
      </Spin>
    </Modal>
  );
};

export default Form.create<ModalContentProps>()(ModalContent);
