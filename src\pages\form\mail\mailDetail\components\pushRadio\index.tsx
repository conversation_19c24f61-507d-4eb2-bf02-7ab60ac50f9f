import React, { useEffect, useRef, useState } from 'react';
import { Radio } from 'antd';

type FormRender = {
  name: string;
  onChange: Function;
  value: string;
  formData: any;
};

export default function({ name, onChange, value, formData }: FormRender) {
  const [radioValue, setValue] = useState('0');
  const count = useRef(0);
  useEffect(() => {
    sessionStorage.removeItem('mailPushs3Url');
    sessionStorage.removeItem('mailPushuploadHashKey');
    if (formData.SendPushInfo.pushTxtUrl || formData.SendPushInfo.pushUgroup) {
      onChange('pushTxtUrl', '');
      onChange('pushUgroup', '');
    }
  }, [formData.SendPushInfo.pushTagType]);
  return (
    <>
      <div
        style={{
          marginLeft: '66px',
          display: location.href.match(/\?id=.+/g) !== null ? 'none' : 'block',
        }}
      >
        <span style={{ color: '#333',marginRight:'12px' }}>是否同步发送PUSH:</span>
        <Radio.Group
          name="radiopush"
          value={radioValue}
          disabled={location.href.match(/\?id=.+/g) !== null}
          onChange={e => {
            setValue(e.target.value);
            onChange(name, e.target.value);
          }}
        >
          <Radio value="0">否</Radio>
          <Radio value="1">是</Radio>
        </Radio.Group>
      </div>
    </>
  );
}
