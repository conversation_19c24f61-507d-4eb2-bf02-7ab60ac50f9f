import React from 'react';
import { Button } from 'antd';

import HotBlock from './../component/hotBlock'

function hotTabs () {
    return (
        <p>
            <span style={{display: 'inline-block', width: 100}}>序号</span>
            <span style={{display: 'inline-block', width: 500}}>基金</span>
            <span style={{display: 'inline-block', width: 300}}>操作</span>
        </p>
    )
}

interface iProps {
    items: any
    fundList: any
    addFunc: any
    deleteFunc: Function
    setItems: any
    disabled: boolean
}

export default function ({
    items,
    fundList,
    addFunc,
    deleteFunc,
    setItems,
    disabled
}: iProps) {

    return (
        <div>
            {hotTabs()}
            <div style={{width: 900}}>
                <div>
                    {
                        items.map((item: any, index: number) => {
                            return(
                                <HotBlock 
                                obj={fundList}
                                key={index}
                                index={index}
                                deleteFunc={() => {deleteFunc(index)}}
                                item={item}
                                items={items}
                                setItems={setItems}
                                disabled={disabled}
                                />
                            )
                        })
                    }
                    
                </div>
                <Button type="primary" onClick={addFunc} disabled={disabled}>添加</Button>
            </div>
        </div>
    )
}