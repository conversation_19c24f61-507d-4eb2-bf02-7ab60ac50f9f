import React, { useState, useEffect } from 'react';
import { Modal, Input } from  'antd';

export default React.memo(function({
    isShow,
    setIsShow,
    offlineTshArticles,
    offlineZxArticles,
    modifiedData,
    save
}: any) {

    const [tsh, setTsh] = useState('')
    const [zx, setZx] = useState('')

    useEffect(() => {
        setTsh(offlineTshArticles.join(','))
    }, [offlineTshArticles])

    useEffect(() => {
        setZx(offlineZxArticles.join(','))
    }, [offlineZxArticles])

    function handleTsh(e: any) {
        let _value = e.target.value
        setTsh(_value)
    }

    function handleZx(e: any) {
        let _value = e.target.value
        setZx(_value)
    }

    function okFunc() {
        let addOutlineList = [...modifiedData.addOutlineList],
            delOutlineList = [...modifiedData.delOutlineList],
            _tempOfflineList = [...offlineTshArticles, ...offlineZxArticles, ...addOutlineList],
            newTsh = tsh.split(','),
            newZx = zx.split(','),
            nowList = [...newTsh, ...newZx],
            addList = [],
            delList = []
        for (let i = 0; i < nowList.length; i ++) {
            let _item = nowList[i]
            if (!~_tempOfflineList.indexOf(_item)) {
                addList.push(_item)
            }
            if (~delOutlineList.indexOf(_item)) {
                delOutlineList.splice(delOutlineList.indexOf(_item), 1)
            }
        }

        for (let i = 0; i < _tempOfflineList.length; i++) {
            let _item = _tempOfflineList[i]
            if (!~nowList.indexOf(_item)) {
                delList.push(_item)
            }
        }

        // for (let i = 0; i < newZx.length; i ++) {
        //     let _item = newZx[i]
        //     if (!~_tempZxOffline.indexOf(_item)) {
        //         addList.push(_item)
        //     }
        //     if (~delOutlineList.indexOf(_item)) {
        //         delOutlineList.splice(delOutlineList.indexOf(_item), 1)
        //     }
        // }

        // for (let i = 0; i < _tempZxOffline.length; i++) {
        //     let _item = _tempZxOffline[i]
        //     if (!~newZx.indexOf(_item)) {
        //         delList.push(_item)
        //     }
        // }

        let _modifiedData = { ...modifiedData }
        _modifiedData.addOutlineList = [...addList]
        _modifiedData.delOutlineList = [...delList]
        setIsShow(false)
        save(_modifiedData)
        // setOfflineTshArticles(newTsh)
        // setOfflineZxArticles(newZx)
    }

    return (
        <Modal 
        title="已下线文章" 
        visible={isShow} 
        width={800}
        okText="确认"
        cancelText="取消"
        onCancel={() => {setIsShow(false)}}
        onOk={okFunc}
        >
            <p>本界面仅展示对本只基金下线的文章并进行操作如想对针对全量基金下线文章操作请跳转基金配置主页面</p>
            <p className="g-mt20">同顺号</p>
            <Input.TextArea 
            value={tsh}
            onChange={handleTsh}
            rows={5}
            ></Input.TextArea>
            <p className="g-mt20">资讯</p>
            <Input.TextArea 
            value={zx}
            onChange={handleZx}
            rows={5}
            ></Input.TextArea>
        </Modal>
    )
})