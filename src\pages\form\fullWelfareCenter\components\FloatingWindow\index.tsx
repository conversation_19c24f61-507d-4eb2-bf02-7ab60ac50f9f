import React from 'react';
import { Form, Input } from 'antd';
import UploadForm from '../Upload';

const FloatingWindow = props => {
  const { getFieldDecorator } = props;
  return (
    <div>
      <UploadForm
        getFieldDecorator={getFieldDecorator}
        fieldLocation="floatingWindow.image"
        label="浮窗图片"
        required={false}
      />
      <Form.Item label="浮窗跳转链接">
        {getFieldDecorator('floatingWindow.jumpUrl')(<Input />)}
      </Form.Item>
    </div>
  );
};

export default FloatingWindow;
