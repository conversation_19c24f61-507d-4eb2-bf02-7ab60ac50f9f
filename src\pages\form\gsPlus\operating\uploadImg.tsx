import React from 'react';
import ImgUpload from '../../../frontend/compoment/uploadImg'

interface dataProps {
    name: string,
    onChange: Function,
    value: string
}

export default function (props:dataProps) {

    return (
        <div>
            <ImgUpload 
                handleChange={(value: any) => {
                    props.onChange(props.name, value)
                }}
                imageUrl={props.value}
                limit={0.2}
                isEdit={true}
                title=''
            />
        </div>
    )
}