import React, { useEffect, useState } from 'react';
import { Checkbox, Button } from 'antd';
import styles from './index.less'
interface selectProps {
    isHead?: boolean; //顶部筛选与内部配置项公用一个组件
    isEdit?: boolean; //内部配置项是否可用
    data?: {
        utype:Array<string>,
        platform:Array<string>
    };
    isOpenAccount?: boolean;
    handleChange: (data: any) => void;
    type?: string
}

export default function (props: selectProps) {
    const { handleChange, type } = props;
    const [platform, setPlatform] = useState<Array<string>>([]);
    const [utype, setUser] = useState<Array<string>>([]);
    const [checkAllPlatform, setCheckAllPlatform] = useState(false);
    const [checkAllUser, setCheckAllUser] = useState(false);
    const [indeterminateUser, setiUser] = useState(true)
    const [indeterminatePlat, setiPlat] = useState(true)
    const platformOptions = [ 
        { label: 'App-安卓', value: 'and' },
        { label: 'App-iOS', value: 'ios' },
        { label: 'SDK-安卓', value: 'andsdk' },
        { label: 'SDK-iOS', value: 'iossdk' },
    ]
    if (type !== 'my') {
        platformOptions.push(
            { label: '手抄环境', value: 'ths' },
            { label: '站外网站', value: 'outsideweb' }
        )
    }
    const userOptions = [
        { label: '默认', value: 'u0' },
        { label: '新手用户', value: 'u1' },
        { label: '次新用户', value: 'u2' },
        { label: '休眠新用户', value: 'u3' },
        { label: '流失新用户', value: 'u4' },
        { label: '成长用户', value: 'u5' },
        { label: '成熟用户', value: 'u6' },
        { label: '高净值用户', value: 'u7' },
        { label: '休眠用户', value: 'u8' },
        { label: '流失用户', value: 'u9' },
        { label: 'F类审核用户', value: 'F' },
    ]
    useEffect(() => {
        
        let _getAll1: string[] = platformOptions.map((item) => item.value);
        let _getAll2: string[] = userOptions.map((item) => item.value);
        setPlatform(_getAll1);
        setUser(_getAll2);
        setCheckAllPlatform(true);
        setiPlat(false);
        let data = {
            platform: _getAll1,
            utype: _getAll2
        }
        handleChange(data)
    },[])
    useEffect(() => {
        if (props.data) {
            if (JSON.stringify(props.data) === "{}") {
                return;
            }
            setUser(props.data.utype)
            setPlatform(props.data.platform)
            setiPlat(!!props.data.platform.length && props.data.platform.length < platformOptions.length)
            setCheckAllPlatform(props.data.platform.length === platformOptions.length)
            setiUser(!!props.data.utype.length && props.data.utype.length < userOptions.length)
            setCheckAllUser(props.data.utype.length === userOptions.length)
        }
    }, [props.data])
    const onChangePlatform = (platform: string[]) => {
        console.log(platform)
        setiPlat(!!platform.length && platform.length < platformOptions.length)
        setPlatform(platform)
        setCheckAllPlatform(platform.length === platformOptions.length)
        if (!props.isHead) {
            let data = {
                platform: platform,
                utype: utype
            }
            handleChange(data)
        }
    }
    const onChangeUser = (_user: string[]) => {
        setiUser(!!_user.length && _user.length < userOptions.length)
        setUser(_user)
        setCheckAllUser(_user.length === userOptions.length)
        if (!props.isHead) {
            let data = {
                platform: platform,
                utype: _user
            }
            handleChange(data)
        }
    }
    const onCheckAllChangePlatform = (e: any) => {
        setiPlat(false)
        setCheckAllPlatform(e.target.checked);
        let _getAll: string[] = platformOptions.map((item, index) => item.value);
        setPlatform(e.target.checked ? _getAll : [])
        if (!props.isHead) {
            let data = {
                platform: e.target.checked ? _getAll : [],
                utype: utype
            }
            handleChange(data)
        }
    }
    const onCheckAllChangeUser = (e: any) => {
        setiUser(false)
        setCheckAllUser(e.target.checked);
        let _getAll: string[] = userOptions.map((item) => item.value);
        setUser(e.target.checked ? _getAll : [])
        if (!props.isHead) {
            let data = {
                platform: platform,
                utype: e.target.checked ? _getAll : []
            }
            handleChange(data)
        }
    }
    const handleSelect = () => {
        let data = {
            platform: platform,
            utype: utype
        }
        handleChange(data)
    }
    return (
        <section className={styles['m-config']}>
            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>适用平台:</p>
            <Checkbox
                indeterminate={indeterminatePlat}
                onChange={onCheckAllChangePlatform}
                checked={checkAllPlatform}
                disabled={!props.isHead && !props.isEdit}
            >
                全选
            </Checkbox>
            <br />
            <Checkbox.Group
                options={platformOptions}
                onChange={onChangePlatform}
                value={platform}
                style={{ marginLeft: '110px', marginBottom: '20px' }}
                disabled={!props.isHead && !props.isEdit} />
            <br />
            <p className={styles['m-card-label']}><span className={styles['m-required']}>*</span>用户类型:</p>
            <Checkbox
                indeterminate={indeterminateUser}
                onChange={onCheckAllChangeUser}
                checked={checkAllUser}
                disabled={!props.isHead && !props.isEdit}
            >
                全选
            </Checkbox>
            <br />
            <Checkbox.Group
                options={userOptions}
                onChange={onChangeUser}
                value={utype}
                style={{ marginLeft: '110px' }}
                disabled={!props.isHead && !props.isEdit} />
            <br />
            {props.isHead ? <Button onClick={handleSelect} type='primary' style={{ marginTop: '20px' }}>查询</Button> : null}
        </section>
    )
}

