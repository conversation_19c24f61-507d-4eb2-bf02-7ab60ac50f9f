import React, { useState } from 'react';
import { Form, Button, Select, Input, message, TreeSelect } from 'antd';
import { FormComponentProps } from 'antd/lib/form/Form';
import { PositionType } from '../consts';
import { TrackItem, SelectedETF, ETFCustomConfig, ETFCustomItem, OutsideTrackItem } from '../types';
import { useFieldArray } from '../hooks';
import { getApiPrefix, getYesterday, cleanFundConfigData } from '../utils';
import axios from 'axios';
import styles from './FundConfig.less';

const { Option } = Select as any;

// 赛道名称搜索过滤函数
const filterTrackByName = (input: string, option: any): boolean => {
  const trackName = option?.props?.children;
  if (typeof trackName === 'string') {
    return trackName.toLowerCase().includes(input.toLowerCase());
  }
  return String(trackName || '').toLowerCase().includes(input.toLowerCase());
};

// 辅助函数：获取已选择ETF的完整信息列表
const getSelectedETFList = (
  selectedEtfCodes: string[],
  trackProductMap: Record<string, SelectedETF[]>,
  trackCode: string
): SelectedETF[] => {
  if (!selectedEtfCodes || !trackCode || !trackProductMap[trackCode]) {
    return [];
  }
  
  const trackProducts = trackProductMap[trackCode];
  return selectedEtfCodes
    .map(code => trackProducts.find(item => item.code === code))
    .filter(Boolean) as SelectedETF[];
};

// ETF自定义配置编辑器组件
interface ETFCustomEditorProps {
  fieldPath: string;
  itemKey: number;
  form: FormComponentProps['form'];
  trackProductMap: Record<string, SelectedETF[]>;
  onProductInfoUpdate?: (itemKey: number) => void;
}

const ETFCustomEditor: React.FC<ETFCustomEditorProps> = ({
  fieldPath,
  itemKey,
  form,
  trackProductMap,
  onProductInfoUpdate,
}) => {
  const { getFieldValue, setFieldsValue, getFieldDecorator } = form;
  const [selectedEditEtf, setSelectedEditEtf] = React.useState<string>('');
  
  // 获取当前选中的ETF代码列表
  const selectedEtfCodes = getFieldValue(`${fieldPath}[${itemKey}].selectedEtf`) || [];
  const trackCode = getFieldValue(`${fieldPath}[${itemKey}].trackCode`);
  const etfCustomConfig = getFieldValue(`${fieldPath}[${itemKey}].etfCustomConfig`) || {};
  
  // 获取已选择ETF的完整信息
  const selectedETFList = getSelectedETFList(selectedEtfCodes, trackProductMap, trackCode);
  
  // 处理自定义配置变更
  const handleCustomConfigChange = (etfCode: string, field: keyof ETFCustomItem, value: string) => {
    const newConfig = {
      ...etfCustomConfig,
      [etfCode]: {
        ...etfCustomConfig[etfCode],
        [field]: value,
      },
    };
    setFieldsValue({
      [`${fieldPath}[${itemKey}].etfCustomConfig`]: newConfig,
    });
    
    // 自定义配置更新后，同步更新 selectedProductInfo
    if (onProductInfoUpdate) {
      // 需要延时执行，确保自定义配置已经更新到表单中
      setTimeout(() => {
        onProductInfoUpdate(itemKey);
      }, 50);
    }
  };
  
  // 清理无效的自定义配置
  React.useEffect(() => {
    if (selectedEtfCodes.length === 0) {
      setSelectedEditEtf('');
      if (Object.keys(etfCustomConfig).length > 0) {
        setFieldsValue({
          [`${fieldPath}[${itemKey}].etfCustomConfig`]: {},
        });
      }
      return;
    }
    
    const validCodes = new Set(selectedEtfCodes);
    const configCodes = Object.keys(etfCustomConfig);
    const needClean = configCodes.some(code => !validCodes.has(code));
    
    if (needClean) {
      const cleanedConfig: ETFCustomConfig = {};
      configCodes.forEach(code => {
        if (validCodes.has(code)) {
          cleanedConfig[code] = etfCustomConfig[code];
        }
      });
      
      setFieldsValue({
        [`${fieldPath}[${itemKey}].etfCustomConfig`]: cleanedConfig,
      });
    }
    
    // 如果当前选中的编辑ETF不在已选择列表中，重置选择
    if (selectedEditEtf && !validCodes.has(selectedEditEtf)) {
      setSelectedEditEtf('');
    }
  }, [selectedEtfCodes, etfCustomConfig, selectedEditEtf, fieldPath, itemKey, setFieldsValue]);
  
  if (selectedETFList.length === 0) {
    return null;
  }
  
  const currentEditEtf = selectedETFList.find(item => item.code === selectedEditEtf);
  const currentConfig = etfCustomConfig[selectedEditEtf] || {};
  
  return (
    <div className={styles['etf-custom-config']}>
      <Form.Item label="自定义配置">
        <Select
          placeholder="选择要配置的ETF"
          value={selectedEditEtf || undefined}
          onChange={setSelectedEditEtf}
          allowClear
        >
          {selectedETFList.map(item => (
            <Option key={item.code} value={item.code}>
              {item.code} - {item.name}
              {etfCustomConfig[item.code] && (etfCustomConfig[item.code].customName || etfCustomConfig[item.code].customHighlight) && 
                <span style={{ color: '#52c41a', marginLeft: 8 }}>●</span>
              }
            </Option>
          ))}
        </Select>
      </Form.Item>
      
      {selectedEditEtf && currentEditEtf && (
        <div className={styles['etf-custom-fields']}>
          <Form.Item label="自定义名称">
            <Input
              placeholder={`默认：${currentEditEtf.name}`}
              value={currentConfig.customName || ''}
              onChange={e => handleCustomConfigChange(selectedEditEtf, 'customName', e.target.value)}
            />
          </Form.Item>
          <Form.Item label="自定义亮点">
            <Input.TextArea
              placeholder="请输入该ETF的亮点介绍"
              rows={3}
              value={currentConfig.customHighlight || ''}
              onChange={e => handleCustomConfigChange(selectedEditEtf, 'customHighlight', e.target.value)}
            />
          </Form.Item>
        </div>
      )}
    </div>
  );
};

interface FundConfigProps {
  type: 'inside' | 'outside';
  form: FormComponentProps['form'];
  insideTrackList: TrackItem[];
  outsideTrackList?: OutsideTrackItem[];
  calculateProbability: (selectedCodes: string[], allProductList: SelectedETF[], currentCode: string) => string;
  initialLength?: number;
  formData?: any;
}

const FundConfig: React.FC<FundConfigProps> = ({
  type,
  form,
  insideTrackList,
  outsideTrackList = [],
  calculateProbability,
  initialLength = 1,
  formData,
}) => {
  const { getFieldDecorator, getFieldValue, setFieldsValue } = form;
  const fieldPath = `data.fundConfig.${type}`;

  // 内部状态管理
  const [trackProductMap, setTrackProductMap] = useState<Record<string, SelectedETF[]>>({});

  // 根据选中的value在树形数据中构建完整路径
  const buildPathFromValue = (value: string, treeData: OutsideTrackItem[], parentPath: string = ''): string => {
    for (const node of treeData) {
      const currentPath = parentPath ? `${parentPath}/${node.label}` : node.label;
      
      if (node.value === value) {
        return currentPath;
      }
      
      if (node.children && node.children.length > 0) {
        const childPath = buildPathFromValue(value, node.children, currentPath);
        if (childPath) {
          return childPath;
        }
      }
    }
    return '';
  };

  // 为树形数据设置selectable属性，只有第3级节点可选，并生成唯一的value
  const setSelectableForTreeData = (treeData: OutsideTrackItem[], level: number = 1, parentPath: string = ''): any[] => {
    return treeData.map((node, index) => {
      const currentPath = parentPath ? `${parentPath}/${node.label}` : node.label;
      const uniqueValue = currentPath; // 使用完整路径作为唯一value
      const isLeafLevel = level === 3; // 第3级才能选择
      
      const processedNode: any = {
        ...node,
        value: uniqueValue, // 使用路径作为唯一value
        title: node.label, // TreeSelect使用title字段显示
        selectable: isLeafLevel && (!node.children || node.children.length === 0), // 第3级且没有子节点才可选
        disabled: !isLeafLevel || (node.children && node.children.length > 0), // 非第3级或有子节点则禁用
      };
      
      if (node.children && node.children.length > 0) {
        processedNode.children = setSelectableForTreeData(node.children, level + 1, currentPath);
      }
      
      return processedNode;
    });
  };

  // 从formData中提取已选择的赛道代码
  const extractTrackCodesFromFormData = (): string[] => {
    if (!formData?.data?.fundConfig?.[type] || !Array.isArray(formData.data.fundConfig[type])) {
      return [];
    }
    
    // 清理formData中的null值
    const cleanedFormData = cleanFundConfigData(formData);
    const configArray = cleanedFormData?.data?.fundConfig?.[type];
    
    if (!Array.isArray(configArray)) {
      return [];
    }
    
    const trackCodes: string[] = [];
    configArray.forEach((item: any) => {
      if (item?.trackCode && typeof item.trackCode === 'string') {
        trackCodes.push(item.trackCode);
      }
    });
    
    // 返回去重后的赛道代码列表
    return [...new Set(trackCodes)];
  };

  // 编辑模式下预加载已选择赛道的数据
  React.useEffect(() => {
    const trackCodes = extractTrackCodesFromFormData();
    
    if (trackCodes.length === 0) {
      return;
    }
    
    // 过滤出尚未获取数据的赛道
    const needFetchCodes = trackCodes.filter(code => !trackProductMap[code] || trackProductMap[code].length === 0);
    
    if (needFetchCodes.length === 0) {
      return;
    }
    
    // 并发获取所有需要的赛道数据，根据类型调用相应的API
    const fetchPromises = needFetchCodes.map(async (trackIdentifier) => {
      try {
        await fetchTrackData(trackIdentifier);
      } catch (error) {
        console.warn(`获取赛道 ${trackIdentifier} 的${type === 'inside' ? 'ETF' : '基金'}数据失败:`, error);
      }
    });
    
    Promise.all(fetchPromises).catch(error => {
      console.warn(`批量获取${type === 'inside' ? 'ETF' : '基金'}赛道数据时发生错误:`, error);
    });
  }, [formData, type]); // 当formData或type变化时重新执行

  // 计算数组初始长度
  const calculateInitialLength = (): number => {
    // 优先使用formData中的实际数组长度
    if (formData?.data?.fundConfig?.[type] && Array.isArray(formData.data.fundConfig[type])) {
      // 清理formData中的null值，确保数组长度计算正确
      const cleanedFormData = cleanFundConfigData(formData);
      if (cleanedFormData?.data?.fundConfig?.[type] && Array.isArray(cleanedFormData.data.fundConfig[type])) {
        return cleanedFormData.data.fundConfig[type].length;
      }
    }
    // 如果没有formData，使用传入的initialLength
    return initialLength;
  };

  const arrayManager = useFieldArray({ arrNum: calculateInitialLength() });

  // 获取指定赛道ETF列表
  const getETFList = async (trackCode: string) => {
    try {
      const res = await axios.post(`${getApiPrefix('fund')}/ranking/list/v1/etf_track_result`, {
        trackCode,
        cycleTime: getYesterday(),
      });
      console.log('res', res);
      if (res?.data?.status_code === 0) {
        // 对返回的列表根据result字段大小降序排序
        const trackDataList = res.data.data?.trackDataList || [];
        const sortedTrackDataList = trackDataList.sort((a: any, b: any) => {
          const resultA = parseFloat(a.result) || 0;
          const resultB = parseFloat(b.result) || 0;
          return resultB - resultA; // 降序排序
        });

        setTrackProductMap(prev => ({
          ...prev,
          [trackCode]: sortedTrackDataList,
        }));
      }
    } catch (error: any) {
      message.error(error?.message || '获取ETF列表失败');
    }
  };

  // 获取指定赛道场外基金列表
  const getFundList = async (trackName: string) => {
    try {
      const res = await axios.post(`${getApiPrefix('fund')}/ranking/list/v1/trackflowranking`, {
        classification: [trackName],
        cycleTime: getYesterday(),
        page: 1,
        limit: 1,
        rankType: 'RANKING',
        scene: 'FUND',
      });
      console.log('res', res);
      if (res?.data?.status_code === 0) {
        const trackDataList = res.data.data?.dataList?.[0]?.dataList || [];
        const sortedTrackDataList = trackDataList.sort((a: any, b: any) => {
          const resultA = parseFloat(a.result) || 0;
          const resultB = parseFloat(b.result) || 0;
          return resultB - resultA; // 降序排序
        });

        setTrackProductMap(prev => ({
          ...prev,
          [trackName]: sortedTrackDataList,
        }));
      }
    } catch (error: any) {
      message.error(error?.message || '获取基金列表失败');
    }
  };

  // 统一数据获取函数，根据类型自动选择API
  const fetchTrackData = async (trackIdentifier: string) => {
    if (type === 'inside') {
      await getETFList(trackIdentifier);
    } else {
      await getFundList(trackIdentifier);
    }
  };

  // 处理赛道选择
  const handleTrackChange = async (trackIdentifier: any) => {
    // 确保trackIdentifier是字符串
    const identifier = String(trackIdentifier);

    // 已获取过数据，跳过
    if (trackProductMap[identifier]?.length > 0) {
      return;
    }
    await fetchTrackData(identifier);
  };

  // 处理场外基金赛道选择
  const handleOutsideTrackChange = async (selectedValue: any, itemKey: number) => {
    // 清空基金产品选择、自定义配置和产品完整信息
    setFieldsValue({
      [`${fieldPath}[${itemKey}].selectedEtf`]: [],
      [`${fieldPath}[${itemKey}].etfCustomConfig`]: {},
      [`${fieldPath}[${itemKey}].selectedProductInfo`]: [],
    });
    
    // 由于现在value就是完整路径，直接使用selectedValue作为trackName
    const trackPath = selectedValue;
    
    // 将路径格式的trackName存储到表单字段中
    setFieldsValue({
      [`${fieldPath}[${itemKey}].trackCode`]: trackPath,
    });

    // 已获取过数据，跳过
    if (trackProductMap[trackPath]?.length > 0) {
      return;
    }
    await fetchTrackData(trackPath);
  };

  // 处理赛道变更，同时清空相关的产品和自定义配置
  const handleTrackCodeChange = async (trackIdentifier: any, itemKey: number) => {
    // 清空产品选择、自定义配置和产品完整信息
    setFieldsValue({
      [`${fieldPath}[${itemKey}].selectedEtf`]: [],
      [`${fieldPath}[${itemKey}].etfCustomConfig`]: {},
      [`${fieldPath}[${itemKey}].selectedProductInfo`]: [],
    });
    
    // 调用赛道变更处理函数
    await handleTrackChange(trackIdentifier);
  };

  // 处理产品选择变更，同时生成 selectedProductInfo 字段
  const handleProductChange = (selectedCodes: string[], itemKey: number) => {
    updateSelectedProductInfo(itemKey, selectedCodes);
  };
  
  // 更新指定项目的 selectedProductInfo 字段
  const updateSelectedProductInfo = (itemKey: number, selectedCodes?: string[]) => {
    const codes = selectedCodes || getFieldValue(`${fieldPath}[${itemKey}].selectedEtf`) || [];
    const trackCode = getFieldValue(`${fieldPath}[${itemKey}].trackCode`);
    const etfCustomConfig = getFieldValue(`${fieldPath}[${itemKey}].etfCustomConfig`) || {};
    
    if (!trackCode || !trackProductMap[trackCode]) {
      // 如果没有赛道信息或产品映射，只设置空数组
      setFieldsValue({
        [`${fieldPath}[${itemKey}].selectedProductInfo`]: [],
      });
      return;
    }
    
    // 获取赛道产品列表
    const trackProducts = trackProductMap[trackCode];
    
    // 根据选中的 code 生成完整的产品信息数组
    const selectedProductInfo = codes
      .map((code: string) => {
        const baseProduct = trackProducts.find(item => item.code === code);
        if (!baseProduct) {
          console.warn(`未找到代码为 ${code} 的产品`);
          return null;
        }
        
        // 合并自定义配置
        const customConfig = etfCustomConfig[code] || {};
        return {
          ...baseProduct,
          customName: customConfig.customName,
          customHighlight: customConfig.customHighlight,
        };
      })
      .filter(Boolean);
    
    // 将完整的产品信息存储到 selectedProductInfo 字段
    setFieldsValue({
      [`${fieldPath}[${itemKey}].selectedProductInfo`]: selectedProductInfo,
    });
  };

  return (
    <>
      {arrayManager.arr.map((key: number, index: number) => {
        return (
          <div className={styles['industry-item-group']} key={key}>
            <div className={styles['industry-item-group-header']}>
              标的 {index + 1}
              <Button
                type="link"
                size="small"
                style={{ float: 'right', color: '#ff4d4f' }}
                onClick={() => arrayManager.onRemove(key)}
              >
                删除
              </Button>
            </div>
            <Form.Item label="所属类型">
              {getFieldDecorator(`${fieldPath}[${key}].position`, {
                rules: [{ required: true, message: '请选择所属类型' }],
              })(
                <Select placeholder="请选择所属类型">
                  <Option value={PositionType.ALL}>最相关</Option>
                  <Option value={PositionType.UPSTREAM}>上游</Option>
                  <Option value={PositionType.MIDSTREAM}>中游</Option>
                  <Option value={PositionType.DOWNSTREAM}>下游</Option>
                </Select>,
              )}
            </Form.Item>
            <Form.Item label="选择赛道">
              {getFieldDecorator(`${fieldPath}[${key}].trackCode`, {
                rules: [{ required: true, message: '请选择赛道' }],
              })(
                type === 'inside' ? (
                  <Select 
                    placeholder="请选择赛道（支持搜索）" 
                    showSearch
                    filterOption={filterTrackByName}
                    onChange={value => handleTrackCodeChange(value, key)}
                  >
                    {insideTrackList.map(item => (
                      <Option key={item.trackCode} value={item.trackCode}>
                        {item.trackName}
                      </Option>
                    ))}
                  </Select>
                ) : (
                  <TreeSelect
                    placeholder="请选择赛道（只能选择第3级赛道）"
                    searchPlaceholder="搜索赛道名称"
                    showSearch
                    treeNodeFilterProp="title"
                    treeData={setSelectableForTreeData(outsideTrackList)}
                    onChange={value => handleOutsideTrackChange(value, key)}

                    style={{ width: '100%' }}
                    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                    allowClear
                  />
                ),
              )}
            </Form.Item>
            <Form.Item label="产品">
              {getFieldDecorator(`${fieldPath}[${key}].selectedEtf`, {rules: [{ required: true, message: '请选择产品' }]})(
                <Select 
                  mode="multiple" 
                  placeholder="请选择产品"
                  onChange={(selectedCodes: string[]) => handleProductChange(selectedCodes, key)}
                >
                  {trackProductMap[getFieldValue(`${fieldPath}[${key}].trackCode`)]?.map(item => {
                    const selectedEtfs = getFieldValue(`${fieldPath}[${key}].selectedEtf`) || [];
                    const hasSelection = selectedEtfs.length > 0;
                    
                    return (
                      <Option key={item.code} value={item.code}>
                        代码：{item.code} 名称：{item.name}
                        {hasSelection && (
                          <span> 概率：
                            {calculateProbability(
                              selectedEtfs,
                              trackProductMap[getFieldValue(`${fieldPath}[${key}].trackCode`)] || [],
                              item.code,
                            )}
                          </span>
                        )}
                      </Option>
                    );
                  })}
                </Select>,
              )}
            </Form.Item>
            
            {/* 产品完整信息隐藏字段 */}
            {getFieldDecorator(`${fieldPath}[${key}].selectedProductInfo`, {
              initialValue: [],
            })(<div style={{ display: 'none' }} />)}
            
            {/* ETF自定义配置隐藏字段 */}
            {getFieldDecorator(`${fieldPath}[${key}].etfCustomConfig`, {
              initialValue: {},
            })(<div style={{ display: 'none' }} />)}
            
            {/* ETF自定义配置编辑器 */}
            <ETFCustomEditor
              fieldPath={fieldPath}
              itemKey={key}
              form={form}
              trackProductMap={trackProductMap}
              onProductInfoUpdate={updateSelectedProductInfo}
            />
          </div>
        );
      })}
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          size="large"
          style={{ marginBottom: 8 }}
          onClick={() => arrayManager.onAdd()}
        >
          {type === 'inside' ? '新增ETF' : '新增基金'}
        </Button>
      </div>
    </>
  );
};

export default FundConfig; 