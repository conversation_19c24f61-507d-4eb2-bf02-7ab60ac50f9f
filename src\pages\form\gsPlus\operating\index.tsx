import React, { useState, useEffect } from 'react';
import FormRender from 'form-render/lib/antd';
import api from 'api';
import { REDPAPER_CONFIG, BANNER_CONFIG } from './form.js';
import { Button, Collapse, Tag, Radio, message, Spin, Popconfirm } from 'antd';
import uploadImg from './uploadImg';

import BigCard from './bigCard';

const { Panel } = Collapse;

export default function() {
  const { fetchGsPlusOperating, postGsPlusOperating, fetchGsPlusProdList } = api;

  const [init, set_init] = useState(false);

  // 红包配置
  const [redpaperConfigStatus, set_redpaperConfigStatus] = useState('0'); // 0: 开启 1: 关闭
  const [redpaperFormData, set_redpaperFormData] = useState<any>({});
  const [redpaperValid, set_redpaperValid] = useState([]);

  // 运营banner配置
  const [bannerConfigStatus, set_bannerConfigStatus] = useState('0'); // 0: 开启 1: 关闭
  const [bannerFormData, set_bannerFormData] = useState<any>({});
  const [bannerValid, set_bannerValid] = useState([]);

  // 大卡配置
  const [bigCardConfigStatus, set_bigCardConfigStatus] = useState(['0', '0', '0']); // 0: 开启 1: 关闭
  const [bigCardFormData, set_bigCardFormData] = useState<any[]>([{}, {}, {}]);

  const [prodObj, set_prodObj] = useState<any>({}); // 全部产品

  /**
   * 获取数据
   */
  const fetchData = () => {
    fetchGsPlusProdList().then((res: any) => {
      try {
        res = JSON.parse(res.data);
        console.log(res);
        if (res) {
          let _prodList: any[] = res.prodList;
          let _prodObj: any = {};
          for (let i = 0; i < _prodList.length; i++) {
            _prodObj[_prodList[i].fundCode] = _prodList[i];
          }
          set_prodObj(_prodObj);
        }
      } catch (e) {
        console.warn(e);
      }
    });
    fetchGsPlusOperating()
      .then((res: any) => {
        try {
          res = JSON.parse(res.data);
          console.log(res);
          if (res) {
            set_redpaperConfigStatus(res.redpaperConfigStatus || '0');
            set_redpaperFormData(res.redpaperFormData || {});
            set_bannerConfigStatus(res.bannerConfigStatus || '0');
            set_bannerFormData(res.bannerFormData || {});
            set_bigCardConfigStatus(res.bigCardConfigStatus || ['0', '0', '0']);
            set_bigCardFormData(res.bigCardFormData || [{}, {}, {}]);
          }
        } catch (e) {
          console.warn(e);
        }
        set_init(true);
      })
      .catch((e: Error) => {
        message.error(e.message);
      });
  };

  /**
   * 提交数据
   */
  const onSubmit = () => {
    // 判断红包
    if (redpaperConfigStatus === '0') {
      // 开启了，需要判断
      const { startDate, endDate } = redpaperFormData;
      if (
        !redpaperFormData ||
        JSON.stringify(redpaperFormData) === '{}' ||
        redpaperValid.length > 0
      ) {
        message.error('红包配置异常');
        return;
      }
      if (new Date(startDate) >= new Date(endDate)) {
        message.error('红包时间配置异常');
        return;
      }
    }
    // 判断banner
    if (bannerConfigStatus === '0') {
      // 开启了，需要判断
      const { startDate, endDate } = bannerFormData;
      if (!bannerFormData || JSON.stringify(bannerFormData) === '{}' || bannerValid.length > 0) {
        message.error('banner配置异常');
        return;
      }
      if (new Date(startDate) >= new Date(endDate)) {
        message.error('banner时间配置异常');
        return;
      }
    }
    // 判断产品
    const checkProd = (data: any) => {
      if (
        data.showType &&
        data.fundCode &&
        data.fundCode.length === 6 &&
        !data.fundCode.includes(' ') &&
        data.fundName &&
        data.timeRange &&
        data.dataIndicator &&
        data.platform.length &&
        data.user.length &&
        data.tagText &&
        data.buyText &&
        data.recommendText &&
        data.signText
      ) {
        return true;
      }
      return false;
    };
    let _fundCode: string[] = [];
    for (let i = 0; i < bigCardConfigStatus.length; i++) {
      if (bigCardConfigStatus[i] === '0' && !checkProd(bigCardFormData[i])) {
        message.error(`大卡配置-产品${i + 1}异常`);
        return;
      }
			if (bigCardConfigStatus[i] === '0') {
				let _tag = bigCardFormData[i].tagText;
				_tag = _tag.split(',');
				let _len = 0;
				for (let i = 0; i < _tag.length; i++) {
					_len += _tag[i].length;
					if (i == 2) break;
				}
				if (_len > 12) {
					message.error(`大卡配置-产品${i + 1} 标签文案前三个不可超过12个字符`);
        	return;
				}
			}
      if (bigCardConfigStatus[i] === '0' && _fundCode.includes(bigCardFormData[i].fundCode)) {
        message.error(`大卡配置-产品${i + 1} 基金代码重复`);
        return;
      }
      if (bigCardConfigStatus[i] == '0') {
        _fundCode.push(bigCardFormData[i].fundCode);
      }
    }
    let _postData = {
      redpaperConfigStatus,
      redpaperFormData,
      bannerConfigStatus,
      bannerFormData,
      bigCardConfigStatus,
      bigCardFormData,
    };
    postGsPlusOperating({
      value: JSON.stringify(_postData),
    }).then((res: any) => {
      try {
        if (res.code !== '0000') {
          message.error(res.message);
        } else {
          message.success('发布成功！');
          setTimeout(() => {
            location.reload();
          }, 500);
        }
      } catch (e) {
        message.error(e.message);
      }
    });
  };

  /**
   * 改变大卡配置
   * @param data
   * @param index
   */
  const changeBidCardData = (data: any, index: number) => {
    let _bigCardFormData = [...bigCardFormData];
    _bigCardFormData[index] = data;
    set_bigCardFormData(_bigCardFormData);
    let _bigCardConfigStatus = [...bigCardConfigStatus];
    _bigCardConfigStatus[index] = data.showType;
    set_bigCardConfigStatus(_bigCardConfigStatus);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <Spin size="large" spinning={!init}>
      <Collapse defaultActiveKey={['redpaperConfig', 'bannerConfig', 'bigCardConfig']}>
        <Panel
          header={
            <div>
              红包配置
              <Tag
                color={redpaperConfigStatus === '0' ? '#fe5d4e' : '#ccc'}
                style={{ marginLeft: 10 }}
              >
                {redpaperConfigStatus === '0' ? '开启' : '关闭'}
              </Tag>
            </div>
          }
          key="redpaperConfig"
        >
          <Radio.Group
            onChange={e => {
              set_redpaperConfigStatus(e.target.value);
            }}
            value={redpaperConfigStatus}
            style={{ marginBottom: 10 }}
          >
            <Radio value={'0'}>开启</Radio>
            <Radio value={'1'}>关闭</Radio>
          </Radio.Group>
          {redpaperConfigStatus === '0' ? (
            <FormRender
              propsSchema={REDPAPER_CONFIG}
              formData={redpaperFormData}
              onChange={set_redpaperFormData}
              onValidate={set_redpaperValid}
              showDescIcon={true}
              displayType="row"
            />
          ) : null}
        </Panel>
        <Panel
          header={
            <div>
              banner配置
              <Tag
                color={bannerConfigStatus === '0' ? '#fe5d4e' : '#ccc'}
                style={{ marginLeft: 10 }}
              >
                {bannerConfigStatus === '0' ? '开启' : '关闭'}
              </Tag>
            </div>
          }
          key="bannerConfig"
        >
          <Radio.Group
            onChange={e => {
              set_bannerConfigStatus(e.target.value);
            }}
            value={bannerConfigStatus}
            style={{ marginBottom: 10 }}
          >
            <Radio value={'0'}>开启</Radio>
            <Radio value={'1'}>关闭</Radio>
          </Radio.Group>
          {bannerConfigStatus === '0' ? (
            <FormRender
              propsSchema={BANNER_CONFIG}
              formData={bannerFormData}
              onChange={set_bannerFormData}
              onValidate={set_bannerValid}
              showDescIcon={true}
              displayType="row"
              widgets={{ uploadImg }}
            />
          ) : null}
        </Panel>
        <Panel
          header={
            <div>
              大卡配置
              {bigCardConfigStatus.map((item, index) => (
                <Tag
                  key={index}
                  color={item === '0' ? '#fe5d4e' : '#ccc'}
                  style={{ marginLeft: 10 }}
                >
                  {item === '0' ? '开启' : '关闭'}
                </Tag>
              ))}
            </div>
          }
          key="bigCardConfig"
        >
          {bigCardFormData.map((item, index) => (
            <BigCard
              key={index}
              baseData={item}
              title={`产品${index + 1}`}
              allProd={prodObj}
              onChange={(data: any) => {
                changeBidCardData(data, index);
              }}
            />
          ))}
        </Panel>
      </Collapse>
      <Popconfirm title="发布后将修改线上配置" onConfirm={onSubmit} okText="确定" cancelText="取消">
        <Button type="primary" style={{ marginTop: 20 }}>
          发布
        </Button>
      </Popconfirm>
    </Spin>
  );
}
