import React, {useEffect, useState} from 'react'
import FormRender from 'form-render/lib/antd';
import moment from 'moment';
import {Button, Popconfirm, message, Modal} from 'antd';
// import UploadImg from './components/UploadImg';
import UploadImg from '../../../components/uploadFile';
import {getGridSchema, getCardSchema, getPageSchema, KEYS, PROPKEYS} from './config';
import api from 'api';
import styles from './index.less';
import { DraggableArea } from '@/components/DragTags';
import Item from './item';
import classnames from 'classnames';
const { confirm } = Modal;

const {
    getEtfEarningConfig,
    postEtfEarningConfig,
    postHashDel
} = api;

const MESSAGE = {
    '0': '添加完成',
    '1': '',
    '2': '修改完成',
    '3': '审批完成',
}

export default function({
    type,
    modify,
    isCardtype,
    isShowAdd,
    activities = [],
    activity,
    setActivity,
    setIsShowAdd = () => {},
    handleFetchETFActivity = () => {}
}: {
    type: string
    modify: string
    isCardtype: string
    isShowAdd: boolean
    activities: any
    activity?: any
    setActivity: any
    setIsShowAdd: Function
    handleFetchETFActivity: Function
}) {

    // const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    const [file, setFile] = useState<File | null>(null)
    const [funds, setFunds] = useState([]);
    const [grids, setGrids] = useState([]);
    const [cards, setCards] = useState([]);
    const [cardList, setCardList] = useState([]);

    useEffect(() => {
        if (!isShowAdd) {
            setFile(null)
        }
    }, [isShowAdd])
    // 深拷贝对象/数组
    const deepClone = (obj) => {
        return JSON.parse(JSON.stringify(obj));
    };
	//handleVerify
	const handleVerify = (status) => {
        if (status === '2') {
            const roles = JSON.parse(localStorage.roles);
            const userId = JSON.parse(localStorage.userInfos).userId;
            if (userId === activity.personId) {
                return message.warn('审核人和创建人不可以是同一个人');
            }
            if (type === 'page') {
                if (roles.indexOf('ETF管理员') < 0) {
                    return message.warn('审核人必须是ETF管理员');
                }
            } else {
                if (roles.indexOf('ETF产品运营') < 0 &&  roles.indexOf('ETF管理员') < 0) {
                    return message.warn('审核人必须具备ETF产品运营以上权限');
                }
            }
        }
        let _form: any = {
            ...activity,
            audit: status,
            auditPerson: JSON.parse(localStorage.userInfos).username,
        }

        if (status === '3') {// 驳回
            confirm({
              title: '提示',
              content: status === '2' ? '确定审核通过吗？' : '确定驳回吗？',
              okText: '确定',
              cancelText: '取消',
              onOk() {
                saveConfig(_form, 'false');
                setIsShowAdd(false)
              },
            });
        } else {
            saveConfig(_form, 'true');
        }
    }

    /**
     * 提交
     */
    function postConfig() {
        if (activities.filter((item) => item[PROPKEYS[type]] === activity[PROPKEYS[type]]).length > (modify === '0' ? 0 : 1)) return message.error('gridId重复，需为唯一')
        let obj = {}
        if (type === 'page') {
            if (!funds.length || !cards.length || funds.filter(item => !item.select).length > 0 || cards.filter(item => !item.select).length > 0) {
                return message.error('宫格和卡片必须填写')
            }
            obj = {
                cards: cards,
                grids: funds,
            }
        }
        if (valid.length > 0) return message.error('必填项未填');
        let _form: any = {
            ...activity,
            personId: JSON.parse(localStorage.userInfos).userId,
            person: JSON.parse(localStorage.userInfos).username,
            modifyTime: moment().format('YYYY-MM-DD HH:mm:ss'),
            audit: '1',
            ...obj,
        }
        saveConfig(_form);
    }

    function saveConfig(_form, audited) {
        let propName;
        if (modify === '0') { // 新增
            propName = _form[PROPKEYS[type]];
        } else if (audited)  {
            propName = String(_form[PROPKEYS[type]]).replace('@', '');
        } else {
            propName = `@${_form[PROPKEYS[type]]}`;
        }
        postEtfEarningConfig({
            key: KEYS[type],
            propName,
            value: JSON.stringify(_form)
        }).then((res: any) => {
            _.hideFundLoading()
            if (res.code === '0000') {
                setIsShowAdd(false);
                message.success(MESSAGE[modify]);
                if (audited) {
                    // 删除修改副本
                    postHashDel({
                        key: KEYS[type],
                        propName: `@${_form[PROPKEYS[type]]}`,
                    }).then(() => {
                        handleFetchETFActivity();
                    });
                } else {
                    handleFetchETFActivity();
                }
            }
        }).catch(() => {
            _.hideFundLoading()
        })
    }


    //checkImageWH  返回一个promise  检测通过返回resolve  失败返回reject阻止图片上传
    function checkImageWH(file, width, height) {
        return new Promise(function(resolve, reject) {
        let filereader = new FileReader()
        filereader.onload = e => {
            let src = e.target.result
            const image = new Image()
            image.onload = function() {
            console.log(this.width, this.height, 'this.height');
            if (this.width / width !== 1 || this.height / height !== 1) {
                // debugger
                Modal.error({
                title: '上传图片的宽高不符合要求，请重传',
                })
                reject(false)
            } else {
                resolve(true)
            }
            }
            image.onerror = reject
            image.src = src
        }
        filereader.readAsDataURL(file)
        })
    }

    const beforeUpload = (file, width, height) => {
        console.log(file, width, height);
        return checkImageWH(file, width, height)
    }

    const LogoUpload = ({ value, onChange, name}) => {
        const uploadCallback = (fileName: string, size: number, url: string) => {
            onChange(name, url);
        }
        const imgCardObj = {
            width: '534',
            height: '565',
        }
        const imgGridObj = {
            width: '84',
            height: '84',
        }
        let imgObj = {};
        if (type === 'card') {
            imgObj = imgCardObj;
        } else {
            imgObj = imgGridObj
        }
        return (
            <>
                {
                    activity[name] && <img style={{width: `100px`, height: '100px'}} src={activity[name]} />
                }
                <span className={styles['upload-logo']}>
                    <UploadImg text="选择文件" callback={uploadCallback} beforeUpload={(file) => beforeUpload(file, imgObj.width, imgObj.height)}/>
                </span>
                <span>
                    图片要求：1. 格式为.png 2.底色透明,3.尺寸长宽{imgObj.width}px * {imgObj.height}px
                </span>
            </>
        )
    }

    const filterList = (hashObj) => {
        const copyKeys = [];
        const onlineKey = [];
        Object.keys(hashObj).forEach(key => {
          if (key.indexOf('@') !== 0) {
            onlineKey.push(key)
          } else {
            copyKeys.push(key);
          }
        });
        const listMap = onlineKey.map(key => {
            const copyKey = `@${key}`;
            if (copyKeys.indexOf(copyKey) > -1) {
              return JSON.parse(hashObj[copyKey]);
            } else {
              return JSON.parse(hashObj[key]);
            }
        });
        return listMap;
    }

    const initSelect = () => {
        getSelectList(KEYS['card']).then(res => {
            const filterCards = res.filter(item => item.audit === '2' && cards.filter(itemCard => item.cardId === itemCard.select).length < 1);
            setCardList(filterCards);
            getSelectList(KEYS['grid']).then(resSel => {
                const filterGrids = resSel.filter(item => item.audit === '2' && funds.filter(itemFund => item.gridId === itemFund.select).length < 1);
                setGrids(filterGrids);
            });
        });
    }

    const getSelectList = async (key) => {
        try {
            const data = await getEtfEarningConfig({ key })
            if (data.code === '0000') {
              if (!data.data) {
                return [];
              }
              const list = filterList(data.data);
            //   const list = Object.values(data.data).map(item => JSON.parse(item));
            //   console.log(list, 'list card');
              return list;
            } else {
              message.error(data.message);
            }
        } catch(err) {
            message.error(err.message);
        }
    }

    useEffect(() => {
        const grids = activity.grids || [];
        const cards = activity.cards || [];
        setCards(cards);
        setFunds(grids);
        initSelect();
    }, [])

    function filterFunds() {
        const filterGrids = grids.filter(item => item.audit === '2' && item.status !== '2').map(item=> {
            if (funds.filter(itemFund => item.gridId === itemFund.select).length < 1) {
                item.disabled = false;
            } else {
                item.disabled = true;
            }
            return item;
        });
        setGrids(filterGrids);
    }

    useEffect(() => {
        filterFunds();
    }, [funds]);

    function filterCards() {
        const filterCard = cardList.filter(item => item.audit === '2'  && item.status !== '2').map(item => {
            if (cards.filter(itemCard => item.cardId === itemCard.select).length < 1) {
                item.disabled = false;
            } else {
                item.disabled = true;
            }
            return item;
        });
        setCardList(filterCard);
    }

    useEffect(() => {
        filterCards();
    }, [cards]);

    // 基金
    const handleFundChange = (index: number, fund: FundItem, nameArea) => {
        console.log('基金列表变化', index, fund, nameArea);
        let _funds = JSON.parse(JSON.stringify(nameArea === 'grid' ? funds : cards));
        _funds[index] = fund;
        nameArea === 'grid' ? setFunds(_funds) : setCards(_funds);
    }
    const handleFundAdd = (nameArea) => {
        let _funds: FundItem[] = JSON.parse(JSON.stringify(nameArea === 'grid' ? funds : cards));
        _funds.push({
            id: (_funds?.length || 0).toString(),
        })
        nameArea === 'grid' ? setFunds(_funds) : setCards(_funds);
    }
    const handleFundDel = (index: number, nameArea) => {
        let _funds: FundItem[] = JSON.parse(JSON.stringify(nameArea === 'grid' ? funds : cards));
        _funds.splice(index, 1);
        _funds.forEach((item, index) => {
            item.id = index.toString();
        })
        // setFunds(_funds);
        nameArea === 'grid' ? setFunds(_funds) : setCards(_funds);
    }

    const dragComponent = ({ value, onChange, name}) => {
        return (<section className={classnames(styles['m-funds'], styles['m-module'])}>
        {/* <p className={classnames(styles['m-title'])}>热门基金</p> */}
        <div className={classnames(styles['m-list'])}>
          <DraggableArea
            isList
            tags={ name === 'grid' ? funds : cards}
            render={({tag, index}: {tag: FundItem, index: number}) => (
              <Item
                key={index}
                name={name}
                index={index}
                dataItem={tag}
                onChange={handleFundChange}
                onDel={handleFundDel}
                titleLength={14}
                ids={name === 'grid' ? grids : cardList}
              />
            )}
            onChange={(data:any) => {
                name === 'grid' ? setFunds(data) : setCards(data)
            }}
          >
          </DraggableArea>
        </div>
        {(modify === '0' || modify === '2') && <Button type="primary" onClick={() => handleFundAdd(name)} disabled={funds.length >= 9}>新增{name === 'grid' ? '宫格' : '卡片'}</Button>}
      </section>)
    }

    function onChangeForm(activityN: any) : any{
        if (modify !== '3' && JSON.stringify(activityN) !== JSON.stringify(activity)) {
            setActivity(activityN)
        }
    }

    const schemaFunc = {
        page: getPageSchema,
        grid: getGridSchema,
        card: getCardSchema,
    }

    let SCHEMA = modify === '0' ? schemaFunc[type](false) : schemaFunc[type](modify);

    return (
        <div>
            <FormRender
                propsSchema={SCHEMA}
                onValidate={setValid}
                formData={activity}
                onChange={onChangeForm}
                showDescIcon={true}
                widgets={{ uploadImg: LogoUpload, dragComponent: dragComponent }}
            />

            {  (modify === '0' || modify === '2') && <div className="u-r-middle" style={{margin: 20}}>
                <Button type="primary" onClick={() => {setIsShowAdd(false)}} style={{marginRight: 20}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={postConfig}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                    >
                        提交
                    </Button>
                </Popconfirm>
            </div>}
            {
                modify === '3' && <div className="u-r-middle" style={{margin: 20}}>
                <Button type="primary" onClick={() => {handleVerify('3'); }} style={{marginRight: 20}}>驳回</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要通过吗？'}
                    onConfirm={() => handleVerify('2')}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                    >
                        通过
                    </Button>
                </Popconfirm>
            </div>
            }

        </div>
    )
}