import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Table, message, Popconfirm } from  'antd';

import api from 'api'
const {
    fetchTshBlackList,
    fetchArticleBlackList,
    postThsBlackList,
    postArticleBlackList
} = api

export default React.memo(function({
    isShow,
    setIsShow,
    refresh
}: any) {
    const [tag, setTag] = useState('tsh') //具体为哪个tag 同顺号：tsh 文章：article
    const [articleList, setArticleList] = useState([]) //文章黑名单
    const [tshList, setTshList] = useState([]) //同顺号黑名单
    const [tshPagination, setTshPagination] = useState<Pagination>({current: 1, pageSize: 10})     
    const [articlePagination, setArticlePagination] = useState<Pagination>({current: 1, pageSize: 10})     
    const [loading, setLoading] = useState(false) //表格加载

    const [outArticleList, setOutArticleList] = useState<string[]>([]) //移除文章黑名单列表
    const [outTshList, setOutTshList] = useState<string[]>([]) //移除同顺号黑名单列表

    useEffect(() => {
        if (!isShow) return
        if (tag === 'tsh') handleTshTableChange(tshPagination)
        else handleArticleTableChange(articlePagination)
    }, [isShow, tag])

    function handleTshTableChange(pagination: Pagination) {
        setLoading(true)
        fetchTshBlackList({
            offer: pagination.current,
            limit: pagination.pageSize
        }).then((data: any) => {
            setLoading(false)
            if (data.status_code === 0) {
                setTshList(data?.data?.blackThsList || [])
                let _pagination: Pagination = {...pagination}
                _pagination.total = data.data.totalNum
                setTshPagination(_pagination)
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            setLoading(false)
            console.log(e.message)
            message.error('系统错误请稍候再试～')
        })
    }

    function handleArticleTableChange(pagination: Pagination) {
        setLoading(true)
        fetchArticleBlackList({
            offer: pagination.current,
            limit: pagination.pageSize
        }).then((data: any) => {
            setLoading(false)
            if (data.status_code === 0) {
                setArticleList(data?.data?.blackContentList || [])
                let _pagination: Pagination = {...pagination}
                _pagination.total = data.data.totalNum
                setArticlePagination(_pagination)
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            setLoading(false)
            console.log(e.message)
            message.error('系统错误请稍候再试～')
        })
    }

    function handleTshList(id: string) {
        let _outTshList: string[] = [...outTshList]
        if (outTshList.indexOf(id) > -1) {
            _outTshList.splice(outTshList.indexOf(id), 1)
        } else {
            _outTshList.push(id)
        }
        setOutTshList(_outTshList)
    }

    function handleArticleList(id: string) {
        let _outArticleList: string[] = [...outArticleList]
        if (outArticleList.indexOf(id) > -1) {
            _outArticleList.splice(outArticleList.indexOf(id), 1)
        } else {
            _outArticleList.push(id)
        }
        setOutArticleList(_outArticleList)
    }

    function save() {
        outTshList.length > 0 && postThsBlackList({
            data: outTshList.join(',')
        }).then((data: any) => {
            if (data.status_code === 0) {
                setIsShow(false)
                refresh && refresh()
                message.success('更新同顺号黑名单成功')
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            console.log(e.message)
            message.error('系统错误请稍候再试～')
        })
        outArticleList.length > 0 && postArticleBlackList({
            data: outArticleList.join(',')
        }).then((data: any) => {
            if (data.status_code === 0) {
                setIsShow(false)
                refresh && refresh()
                message.success('更新文章黑名单成功')
            } else {
                message.error(data.status_msg || '系统错误请稍候再试～')
            }
        }).catch((e: any) => {
            console.log(e.message)
            message.error('系统错误请稍候再试～')
        })
    }

    return (
        <Modal 
        title="黑名单" 
        visible={isShow} 
        width={800}
        onCancel={() =>{setIsShow(false)}}
        footer={
            <div className="u-r-middle">
                <Button type="primary" onClick={() =>{setIsShow(false)}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要保存么'}
                    onConfirm={save}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button type="danger" >保存</Button>
                </Popconfirm>
            </div>
        }
        >
            <p>本界面仅展示对<span style={{color: 'red'}}>全量基金</span>拉黑的文章或同顺号并进行操作如想对针对个基下线文章操作请跳转对应个基配置页</p>
            <p className="g-mt20 g-mb20">
                <span onClick={() => {setTag('article')}} style={{
                    fontWeight: tag === 'article' ? 'bold' : 'normal',
                    textDecoration: tag === 'article' ? 'underline' : 'none'
                }}>文章</span>
                <span className="g-ml20" onClick={() => {setTag('tsh')}} style={{
                    fontWeight: tag === 'tsh' ? 'bold' : 'normal',
                    textDecoration: tag === 'tsh' ? 'underline' : 'none'
                 }}>同顺号</span>
            </p>
            <Table 
            dataSource={articleList} 
            style={{display: tag === 'tsh' ? 'none' : ''}}
            // rowKey={(item: article) => item.itemId}
            pagination={articlePagination}
            loading={loading}
            onChange={handleArticleTableChange}
            >
                <Table.Column title="文章id" dataIndex={'itemId'} key="itemId"></Table.Column>
                <Table.Column title="文章标题" dataIndex={'title'} key="title"></Table.Column>
                <Table.Column title="操作时间" dataIndex={'opTime'} key="opTime"></Table.Column>
                <Table.Column title="操作" key="action" render={(text, record: article, index) => {
                    return (
                        <a key={index} style={{color: 'blue'}} onClick={() => {
                            handleArticleList(record.itemId)
                        }}>
                            {
                                outArticleList.indexOf(record.itemId) > -1
                                ?
                                '取消上线'
                                :
                                '恢复上线'
                            }
                        </a>
                    )
                }}></Table.Column>
            </Table>

            <Table 
            dataSource={tshList} 
            // rowKey={(item: TSH) => item.id}
            pagination={tshPagination}
            loading={loading}
            onChange={handleTshTableChange}
            style={{display: tag === 'tsh' ? '' : 'none'}}
            >
                <Table.Column title="同顺号id" dataIndex={'id'} key="id"></Table.Column>
                <Table.Column title="同顺号名称" dataIndex={'name'} key="name"></Table.Column>
                <Table.Column title="操作时间" dataIndex={'opTime'} key="opTime"></Table.Column>
                <Table.Column title="操作" key="action" render={(text, record: TSH, index) => {
                    return (
                        <a key={index} style={{color: 'blue'}} onClick={() => {
                            handleTshList(record.id)
                        }}>
                            {
                                outTshList.indexOf(record.id) > -1
                                ?
                                '取消移出黑名单'
                                :
                                '移出黑名单'
                            }
                        </a>
                    )
                }}></Table.Column>
            </Table>
        </Modal>
    )
})