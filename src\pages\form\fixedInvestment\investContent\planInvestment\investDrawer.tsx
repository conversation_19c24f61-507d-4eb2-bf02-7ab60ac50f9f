import {
    Form,
    Input,
    Button,
    Select,
    Popconfirm,
    InputNumber
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import { iPlanData } from './index';

const { Option } = Select;

interface planDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: iPlanData;
    handleData: (data: iPlanData) => void;
}

class planDrawer extends React.Component<planDrawerProps, any> {
    constructor(props: planDrawerProps) {
        super(props);
        this.state = {
            fundName: ''
        }
    }

    formItemLayout = {
        labelCol: {
            span: 4
        },
        wrapperCol: {
            span: 19
        },
    };
    handleCode = (e: any) => {
        let val = e.target.value;
        val = val.replace(/\s/g, '');
        val = val.replace(/[^\d]/g, '');
        if (val?.length === 6) {
            this.getJsonp(val);
        } else {
            this.setState({
                fundName: ''
            })
        }
    }
    handleName= (e: any) => {
        let val = e.target.value;
        this.setState({
            fundName: val
        })
    }
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { fundName } = this.state;
        const { currentData } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                values = { ...currentData, ...values, fundName };
                console.log(values);
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    getJsonp(code: string) {
        let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/fund/multiFundInfo/${code}_name?return=jsonp&jsonp=jsonp`
        console.log(_script)
        document.body.appendChild(_script);
        _script.onload = function() {
            document.body.removeChild(_script);
        };
    }

    componentDidMount() {
        const _this = this;
        const { fundName } = this.props.currentData;
        this.setState({
            fundName
        })
        window.jsonp = function (res: any) {
            console.log(res);
            const { error, data } = res;
            if (error?.id === 0 && data) {
                Object.keys(data)?.forEach((key: string) => {
                    console.log(data[key]);
                    _this.setState({
                        fundName: data[key]?.name
                     })
                })
            }
        }
    }
    render() {
        const { getFieldDecorator } = this.props.form;
        const { onEditClose } = this.props;
        const { fundName } = this.state;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="基金ID" wrapperCol={{span: 4}}>
                        {getFieldDecorator('fundCode', {
                            initialValue: this.props.currentData?.fundCode,
                            rules: [{ required: true, message: '请输入基金ID' }],
                        })(
                            <Input onChange={this.handleCode}/>
                        )}
                    </Form.Item>
                    <Form.Item label="基金名称" wrapperCol={{span: 6}}>
                        <Input maxLength={200} onChange={this.handleName} value={fundName}/>
                    </Form.Item>
                    <Form.Item label="适用人群">
                        {getFieldDecorator('peopleType', {
                            initialValue: this.props.currentData?.peopleType,
                            rules: [{ required: true, message: '请选择适用人群' }],
                        })(
                            <Select style={{ width: 120 }}>
                                <Option key={'xsd'} value={'xsd'}>学生党</Option>
                                <Option key={'zcxr'} value={'zcxr'}>职场新人</Option>
                                <Option key={'zclr'} value={'zclr'}>职场老炮</Option>
                                <Option key={'zrz'} value={'zrz'}>责任族</Option>
                                <Option key={'txz'} value={'txz'}>退休族</Option>
                            </Select>
                        )}
                    </Form.Item>
                    <Form.Item label="标签1" wrapperCol={{span: 4}}>
                        {getFieldDecorator('label1', {
                            initialValue: this.props.currentData?.label1,
                        })(
                            <Input maxLength={6}/>
                        )}
                    </Form.Item>
                    <Form.Item label="标签2" wrapperCol={{span: 4}}>
                        {getFieldDecorator('label2', {
                            initialValue: this.props.currentData?.label2,
                        })(
                            <Input  maxLength={6}/>
                        )}
                    </Form.Item>
                    <Form.Item label="收益率">
                        {getFieldDecorator('profitType', {
                            initialValue: this.props.currentData?.profitType,
                            rules: [{ required: true, message: '请选择收益率' }],
                        })(
                            <Select style={{ width: 180 }}>
                                <Option key={10} value={10}>成立以来年化收益率</Option>
                                <Option key={9} value={9}>近一年收益率</Option>
                                <Option key={8} value={8}>7日年化收益率</Option>
                                <Option key={0} value={0}>近一年定投收益率</Option>
                                <Option key={2} value={2}>近三年定投收益率</Option>
                            </Select>
                        )}
                    </Form.Item>
                    <Form.Item label="购买人数基数" wrapperCol={{span: 4}}>
                        {getFieldDecorator('baseNum', {
                            initialValue: this.props.currentData?.baseNum,
                            rules: [{ required: true, message: '请输入购买人数基数' }],
                        })(
                            <InputNumber />
                        )}
                    </Form.Item>
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button type="primary" style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                            <Button type="danger">
                                提交
                            </Button>
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedplanDrawer = Form.create<planDrawerProps>({ name: 'planDrawer' })(planDrawer);
export default WrappedplanDrawer