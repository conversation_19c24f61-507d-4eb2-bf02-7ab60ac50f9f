import React, { useEffect, useState, useRef } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { Table, Button, Modal, message, Drawer, Spin } from 'antd';
import { EditContentType, ContentProps, columnsProps, Status } from './types';
import { initData } from './config';
import api from 'api';
import DrawerContent from './content';

// 状态
const renderStatus = (text: Status) => text === '0' ? '下架' : '上架';

const Index = () => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [currentData, setCurrentData] = useState<any>({}); // 单条数据
  const [edit, setEdit] = useState<number>(0); //0 新增 1编辑
  const [showDrawer, setShowDrawer] = useState<boolean>(false);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [noticeInfo, setNoticeInfo] = useState<{[index:string]: {name:string;url:string}[]}>({})
  const queryList = () => {
    setTableLoading(true)
    const promise1 = api.buyPrivateList()
    const promise2 = api.fetchGDLCPrivateFundNoticeConfig()
    Promise.all([promise1, promise2]).then(([res,res2])=>{
      if (res.code !== '0000' || res2.code !== '0000') {
        Modal.error({ content: '获取数据失败' });
        return;
      }
      if(res2.data){
        setNoticeInfo(JSON.parse(res2.data))
      }
      setTableLoading(false)
      setDataSource(res.data)
    })
  }


  const showDeleteConfirm = (item: Partial<ContentProps>) => {
    Modal.confirm({
      title: '确定删除?',
      okText: '确认',
      okType: 'danger',
      cancelText: '返回',
      onOk() {
        const key = item.privateFundId
        delete noticeInfo[key]
        const promise1 = api.buyPrivateDel({
          data: { privateFundId: item.privateFundId }
        })
        const promise2 = api['postGDLCPrivateFundNoticeConfig']({ value: JSON.stringify({...noticeInfo}) })
        Promise.all([promise1,promise2]).then(([res])=>{
          if (res?.code !== '0000') {
            message.error('操作失败');
            return;
          }
          message.success('删除成功');
          queryList();
        })
      }
    });
  }
  const showStatusConfirm = (item: Partial<ContentProps>) => {
    const _message = item.status == '0' ? '上架' : '下架',
      _status = item.status == '0' ? '1' : '0';
    Modal.confirm({
      title: `确定${_message}?`,
      okText: '确认',
      cancelText: '返回',
      onOk() {
        api.buyPrivateStatus({ privateFundId: item.privateFundId, status: _status }).then((res: any) => {
          const { code } = res;
          if (code !== '0000') {
            message.error('操作失败');
            return;
          }
          message.success(`${_message}成功`);
          queryList();
        })
      }
    });
  }

  const ModifyButton = ({ record, children, type, className }: any) => {
    const [buttonLoading, setButtonLoading] = useState<boolean>(false);

    const showDrawerContent = (editContentType: EditContentType, record?: any) => {
      if (editContentType === 0) {
        setShowDrawer(true)
        setCurrentData(initData)
        setEdit(editContentType)
      } else if (editContentType === 1) {
        setButtonLoading(true)
        api.buyPrivateGet({ privateFundId: record.privateFundId }).then((res: any) => {
          setButtonLoading(false)
          if (res?.code !== '0000') {
            Modal.error({ content: '获取数据失败' });
            return;
          }
          unstable_batchedUpdates(() => {
            setShowDrawer(true)
            setCurrentData(res.data)
            setEdit(editContentType)
          })
        })
      }
    };

    return (
      <Button type="primary" className={className} loading={buttonLoading} onClick={() => { showDrawerContent(type, record) }}>{children}</Button>
    )
  }

  /** 操作 */
  const renderOperate = (text: string, record: any) => {
    return <div className="u-l-middle">
      <ModifyButton type={EditContentType.Edit} record={record}>编辑</ModifyButton>
      <Button type="primary" className="g-ml10" onClick={() => { showStatusConfirm(record) }}>{record.status == '0' ? '上架' : '下架'}</Button>
      <Button type="danger" className="g-ml10" onClick={() => { showDeleteConfirm(record) }}>删除</Button>
    </div>
  }

  const columns: columnsProps[] = [
    { title: '产品代码', dataIndex: 'privateFundId', key: 'privateFundId' },
    { title: '投资策略', dataIndex: 'investStrategy', key: 'investStrategy' },
    { title: '预约申购开始日', dataIndex: 'backPreSubsceStart', key: 'backPreSubsceStart' },
    { title: '预约申购结束日', dataIndex: 'backPreSubsceEnd', key: 'backPreSubsceEnd' },
    { title: '私募基金名称', dataIndex: 'privateFundName', key: 'privateFundName' },
    { title: '剩余名额', dataIndex: 'curOpenNum', key: 'curOpenNum' },
    { title: '修改时间', dataIndex: 'editTime', key: 'editTime' },
    { title: '状态', dataIndex: 'status', key: 'status', width: '80px', render: renderStatus },
    { title: '操作', key: '_operate', width: '220px', render: renderOperate }

  ]
  useEffect(queryList, []);
  const onEditClose = () => {
    setShowDrawer(false)
  }
  return <div>
    <header>
      <h2>
        私募基金列表
        <ModifyButton className="f-fr" type={EditContentType.Add}>新增</ModifyButton>
      </h2>
    </header>
    <Spin spinning={tableLoading}>
      <Table
        rowKey='privateFundId'
        columns={columns as any}
        dataSource={dataSource}
        pagination={false}
      />
    </Spin>
    <Drawer
      width={1000}
      title="私募基金详情"
      placement="right"
      closable
      onClose={onEditClose}
      visible={showDrawer}
    >
      {
        showDrawer &&
        <DrawerContent
          currentData={currentData}
          queryAllData={queryList}
          noticeInfo={noticeInfo}
          edit={edit}
          onEditClose={onEditClose}
        />
      }
    </Drawer>
  </div>
}

export default Index;
