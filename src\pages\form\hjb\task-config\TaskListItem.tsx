/**
 * 黄金宝 - 任务配置 - 任务列表项
 */
import React from 'react';
import styles from './index.less';
import clsx from 'classnames';
import { Checkbox, Button, Tag, Typography, Tooltip } from 'antd';
import moment from 'moment';
import {
  TASK_TYPE,
  TASK_TYPE_TEXT,
  REWARD_TYPE,
  REWARD_TYPE_TEXT,
  TASK_CYCLE_TEXT,
} from '../const';
import { isBuyType, isBrowseType, isShareType, isRewardGram } from '../helper';

type Props = {
  provided: unknown;
  data: { [k: string]: string };
  onToggle: (ids: string[]) => void;
  checked: boolean;
  onChecked: (ids: string[]) => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
};

//
const TaskListItem: React.FunctionComponent<Props> = ({
  provided,
  data,
  onToggle,
  checked,
  onChecked,
  onEdit,
  onDelete,
}) => {
  //

  // prettier-ignore
  return (
    <div
      ref={ provided.innerRef }
      { ...provided.draggableProps }
      { ...provided.dragHandleProps }
      className={ clsx(styles['task-list-item'], styles['task-list-row'], data.is_super_task && styles['task-list-item-super']) }
    >
      <div className={ styles['task-list-column'] }>
        <Checkbox checked={ checked } onChange={ () => onChecked([data._id]) } />
      </div>
      <div className={ styles['task-list-column'] }>{ Number(data.sort_key) + 1 }</div>
      <div className={ styles['task-list-column'] }>
        { data.name }
        { data.is_super_task && <Tag className={ styles['tag'] } color="gold">今日重金悬赏</Tag> }
        { data._is_deleted && <Tag className={ styles['tag'] } color="red">已下线</Tag> }
      </div>
      <div className={ styles['task-list-column'] }>{ TASK_TYPE_TEXT[data.type] || '' }</div>
      <div className={ styles['task-list-column'] }>
        { data.is_cycle_task ? TASK_CYCLE_TEXT[data.cycle] + '/' : '' }
        { data.is_cycle_task ? (data.times_in_cycle == 0 ? '无限' : data.times_in_cycle) + '次' : '' }
        { !data.is_cycle_task ? <Typography.Text type="warning">仅能完成一次</Typography.Text> : '' }
      </div>
      <div className={ styles['task-list-column'] }>
        {
          data.condition_amount
            ? data.condition_amount != 0 ? `购买 ${ data.condition_amount } 元` : '完成' + TASK_TYPE_TEXT[data.type] + '购买'
            : ''
        }
        {
          data.condition_time ? `浏览 ${data.condition_time} 秒` : ''
        }
        {
          !data.condition_amount && !data.condition_time ? '完成' + TASK_TYPE_TEXT[data.type] : ''
        }
      </div>
      <div className={ styles['task-list-column'] }>
        {
          !isRewardGram(data.type)
            ? (data.reward_type == REWARD_TYPE.FULL_REWARD ? ('每满' + data.reward_every_full + '元') : '') + (data.reward_beans + '颗')
            : (data.reward_grams ? (data.reward_grams) + '克黄金(等值黄金豆)' : '') }
      </div>
      {/* <div className={ styles['task-list-column'] }></div> */}
      <div className={ styles['task-list-column'] }>{ moment(data._update_at).format('YYYY-MM-DD HH:mm:ss') }</div>
      <div className={ styles['task-list-column'] }>
        <Tooltip title="编辑">
          <Button size="small" type="default" icon="edit" onClick={ () => onEdit(data._id) }></Button>
        </Tooltip>
        <Tooltip title={ data._is_deleted ? "上线" : "下线" }>
          <Button size="small" type="default" icon={ data._is_deleted ? "check" : "stop" } onClick={ () => onToggle([data._id]) }></Button>
        </Tooltip>
        {
          !!~location.href.search('hjbAdmin=true') &&
          <Tooltip title="删除">
            <Button size="small" type="default" icon="delete" onClick={ () => onDelete(data._id) }></Button>
          </Tooltip>
        }
      </div>
    </div>
  )
};

export default TaskListItem;
