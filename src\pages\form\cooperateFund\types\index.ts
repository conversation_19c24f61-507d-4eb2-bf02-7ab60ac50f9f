/**
 * 基金详情接口返回值类型
 */
export interface FundDetail {
  simpleFundName: string;
  jumpCode: string;
  expSecurityName: string;
  subMarket: string;
}

/**
 * 基金热度/自选值接口返回值类型
 */
export interface FundRate {
  rate: number;
}

/**
 * 板块数据接口返回值类型
 */
export interface PlateData {
  name: string;
  thscodeHq: string;
}

/**
 * 基金表单数据类型
 */
export interface FundFormData {
  fundCode: string;
  simpleFundName: string;
  jumpCode: string;
  expSecurityName: string;
  subMarket: string;
  isCooperate: string;
  popularity: number;
  popularityPercent: number | string;  // 可能是数字或字符串（带%）
  selfSelectCount: number;
  selfSelectCountPercent: number | string;  // 可能是数字或字符串（带%）
  nickNameList: string[];
  themeList: Array<{
    name: string;
    thscodeHq: string;
  }>;
  // 当前值字段（用于显示）
  currentPopularity?: number;
  currentSelfSelectCount?: number;
  // 其他可选字段
  [key: string]: any;  // 允许其他字段
}

/**
 * API响应类型
 */
export interface ApiResponse<T> {
  code: string;
  message?: string;
  data: T;
}

/**
 * 合作状态类型
 */
export type CooperateStatus = '0' | '1'; 