import { Select, Table, Button, Checkbox,message, Modal, Upload, Spin} from 'antd';
import React, { useState, useEffect, FC } from 'react';
import {ConfigData, InfoNameMap} from '../type'
import { HOTSPOT_KEY, HOTSPOT_TMP_KEY, KEY } from '../utils';
import api from 'api'
import moment from 'moment';
const {fetchHashAll,postHash,fetchHash,getHotTheme,getThemeNews,getTopicFunds} = api;
const {Option} = Select
export interface IProps {}

// const testThemeList=[
//   {id:"1",name:"科技"},
//   {id:"2",name:"农业"},
//   {id:"3",name:"医药"},
//   {id:"4",name:"养老"},
//   {id:"5",name:"耕田"},
//   ...Array.from(new Array(10),(item,index)=>({id:"v"+index,name:"测试"+index}))
// ]

//const testDataSource=[{"author":"交易者日记","ctime":1605857781,"intro":null,"topStatus":false,"rtime":1605857781,"source":"同顺号","title":"了解分析方法的本质，助你突破基本面与技术面的死循环","type":1,"uniqueId":128453197,"url":"https://t.10jqka.com.cn/pid_128453197.shtml","id":5305640,"picUrls":["https://u.thsi.cn/imgsrc/sns/aed81ddb3f557a0464e3c0afe9af1eaf_266_199_small.png"],"abstractInfo":"本文来自交易者日记，作者：淡淡禅风，谢绝转载基本面分析和技术面分析，是交易中两种不同的分析体系，都是辅助我们交易很好的帮手。问题来了，当基本面分析与技术面分析的结果出现冲突的时候，你会怎么做？基本面分析和技术面分析的结果会产生冲突，这是因为对基本面分析和技术面分析理解的不够透彻，当能够深入的理解基本......","clicks":4906},{"author":"欧奈尔杯柄","ctime":1605857774,"intro":null,"topStatus":false,"rtime":1605857774,"source":"同顺号","title":"如何利用基本面和技术面来获得超额收益？","type":1,"uniqueId":128860814,"url":"https://t.10jqka.com.cn/pid_128860814.shtml","id":5305639,"picUrls":["https://u.thsi.cn/imgsrc/sns/14d22ee12566cceab1d95fed765dcd6a_720_540_small.jpg"],"abstractInfo":"技术面和基本面分的那么清楚并不是好事，你分的这么清楚，我敢肯定，你必然遗漏了某些事情，因为确定代表着清晰、而炒股这件事上、有几个能那么清晰的把所有模糊的事情给分析出来，这大概是只有神才能办到的事情。","clicks":2430},{"author":"期货通学院","ctime":1605857768,"intro":null,"topStatus":false,"rtime":1605857768,"source":"同顺号","title":"基本面分析的精髓","type":1,"uniqueId":121606331,"url":"https://t.10jqka.com.cn/pid_121606331.shtml","id":5305633,"picUrls":["https://u.thsi.cn/imgsrc/sns/a6610e65cce5b950cf4a5362c1fc71d3_284_213_small.png"],"abstractInfo":"传统的基本面分析大都采取自上而下的方式，即从宏观分析到产业分析，再到具体品种分析。对于绝大多数期货交易者来说，他们不愿意花费很多时间用于基本面分析，一方面可能是因为相关知识的匮乏，另一方面可能是因为基本面分析需要消耗太多的时间和精力。其实期货的基本面分析并不像你想象的那么难，我们只需要抓住主要矛盾即......","clicks":2123},{"author":"基本面","ctime":1605593666,"intro":null,"topStatus":false,"rtime":1605593666,"source":"SNS","title":"政策对股市的影响","type":16,"uniqueId":101870054,"url":"http://t.10jqka.com.cn/newcircle/post/pkgPostDetail/?pid=101870054&packageid=681","id":5257691,"picUrls":["https://u.thsi.cn/fileupload/data/Blog/2018/03f5a66c461ddc107e3a0c61a0417404.jpg"],"abstractInfo":"1．货币政策对股市的影响。货币政策是政府调控宏观经济的基本手段之一。由于社会总供给和总需求的平衡与货币供给总量与货币需求总量的平衡相辅相成。因而宏观经济调控之重点必然立足于货币供给量。货币政策主要针对货币供给量的调节和控制展开,进而实现诸如稳定货币、增加就业、平衡国际收支、发展经济等宏观经济目标。货...","clicks":8563},{"author":"基本面","ctime":1605593658,"intro":null,"topStatus":false,"rtime":1605593658,"source":"SNS","title":"利率变动对股市的影响","type":16,"uniqueId":101870076,"url":"http://t.10jqka.com.cn/newcircle/post/pkgPostDetail/?pid=101870076&packageid=681","id":5257688,"picUrls":["https://u.thsi.cn/fileupload/data/Blog/2018/1edf6fb6ea302d456fa0344a904bddd0.jpg"],"abstractInfo":"对股票市场及股价产生影响的种种因素中最敏锐者莫过于金融因素。在金融因素中,利率水准的变动对股市行情的影响又最为直接和迅速。一般来说,利率下降时,股票的价格就上涨；利率上升时,股票的价格就会下跌。因此,利率的高低以及利率同股票市场的关系,也成为股票投资者据以买进和卖出股票的重要依据。为什么利率的升降与...","clicks":2928},{"author":"基本面","ctime":1605593650,"intro":null,"topStatus":false,"rtime":1605593650,"source":"SNS","title":"经济周期对股市的影响","type":16,"uniqueId":101870072,"url":"http://t.10jqka.com.cn/newcircle/post/pkgPostDetail/?pid=101870072&packageid=681","id":5257686,"picUrls":["https://u.thsi.cn/fileupload/data/Blog/2018/85e48f455c7172f775792bc4d1d051c4.jpg"],"abstractInfo":"股市是反映国民经济状况的一个窗口，股市的兴衰反过来也影响着国民经济发展的好坏与快慢。但是，从根本上来说，国民经济的发展决定着股市的发展，而不是相反。因此，国民经济发展的状况、对国民经济发展有重要影响的一些因素都将对股市及股市中存在着的各种股票发生显著作用。对这些作用，股票投资者和分析者必须做到了然于...","clicks":2236},{"author":"基本面","ctime":1605593641,"intro":null,"topStatus":false,"rtime":1605593641,"source":"SNS","title":"通货膨胀对股市的双重作用","type":16,"uniqueId":101870071,"url":"http://t.10jqka.com.cn/newcircle/post/pkgPostDetail/?pid=101870071&packageid=681","id":5257684,"picUrls":["https://u.thsi.cn/fileupload/data/Blog/2018/ff17348da94e15428b251062808fa918.jpg"],"abstractInfo":"通货膨胀是影响股票市场以及股票价格的一个重要宏观经济因素。这一因素对股票市场的影响比较复杂。它既有刺激股票市场的作用，又有压抑股票市场的作用。通货膨胀主要是由于过多地增加货币供应量造成的。货币供应量与股票价格一般是呈正比关系，即货币供应量增大使股票价格上升，反之，货币供应量缩小则使股票价格下降，但在...","clicks":2471},{"author":"基本面","ctime":1605593631,"intro":null,"topStatus":false,"rtime":1605593631,"source":"SNS","title":"财政政策对股价的影响","type":16,"uniqueId":101870067,"url":"http://t.10jqka.com.cn/newcircle/post/pkgPostDetail/?pid=101870067&packageid=681","id":5257679,"picUrls":["https://u.thsi.cn/fileupload/data/Blog/2018/669578c3123bfbc673775789ab707f41.jpg"],"abstractInfo":"财政是国家为实现其职能的需要对一部分社会产品进行的分配活动,它体现着国家与其有关各方面发生的经济关系。国家财政资金的来源,主要来自于企业的纯收人。其大小取决于物质生产部门以及其他事业的发展状况、经济结构的优化、经济效益的高低、以及财政政策的正确与否,财政支出主要用于经济建设、公共事业、教育、国防以及...","clicks":1353},{"author":"恒泰武工队","ctime":1604995936,"intro":null,"topStatus":false,"rtime":1604995936,"source":"同顺号","title":"股票投资中，择时和择股哪个更重要？","type":1,"uniqueId":139305971,"url":"https://t.10jqka.com.cn/pid_139305971.shtml","id":5187162,"picUrls":["https://u.thsi.cn/imgsrc/sns/8af48d3bf9b68430191182ff11c9ec7f_233_175_small.png"],"abstractInfo":"去掉干扰，就会离成功更进一步！","clicks":1224}]
//const testDataSource2=Array.from(testDataSource,item=>({...item,id:item.id+"copy",title:item.title+"copy"}))
const initPagination={
  current:1,
  pageSize:20,
  
}
const HotSpotPool: FC<IProps> = () => {
    // 已经配置的板块数据
    const [configDataList, setConfigDataList] = useState<ConfigData[]>([]);
    const [themeList,setThemeList]=useState([])
    const [dataSource,setDataSource]=useState([]);
    const [hotSpotMap,setHotSpotMap]=useState<Map<string,any>>(new Map());
    const [relateInfomationMap,setRelateInfomationMap]=useState<Map<string,any>>(new Map());
    const [selectedRowKeys,setSelectedRowKeys]=useState([]);
    const [pagination,setPagination]=useState(initPagination)
    //热门主题选择
    const [selectThemes,setSelectThemes]=useState([])
    //板块选择
    const [currentSector,setCurrentSector]=useState();
    const [loading,setLoading]=useState(false);
    const [isEnd,setIsEnd]=useState(false);
    const addHotSpotSet=(keys:string[],cb)=>{
        const nset=new Map(hotSpotMap);
        keys.forEach(key=>{
            nset.set(key,dataSource.find(item=>item.uniqueId===key))
        }) 
        if(nset.size>1){
          message.info("最多选择一个作为重磅热点。");
         
          return;
        }
        setHotSpotMap(nset)
        cb&&cb();
    }

    const addRelateInfomationSet=(keys:string[],cb)=>{
        const nset=new Map(relateInfomationMap);
        keys.forEach(key=>{
          nset.set(key,dataSource.find(item=>item.uniqueId===key))
        }) 
        if(nset.size>5){
          message.info("最多选择五个作为相关资讯。");
          return;
        }
        setRelateInfomationMap(nset)
        cb&&cb()
    }
    const removeHotSpotSet=(keys:string[])=>{
        const nset=new Map(hotSpotMap);
        keys.forEach(key=>{
            nset.delete(key)
        }) 

        
        setHotSpotMap(nset)
    }

    const removeRelateInfomationSet=(keys:string[])=>{
        const nset=new Map(relateInfomationMap);
        keys.forEach(key=>{
            nset.delete(key)
        }) 
        setRelateInfomationMap(nset)
    }
    const getConfigDataList=async()=>{
      
        try{
          const res= await fetchHashAll({key:KEY})
          if(res.code!=="0000"){
            throw new Error(res.message)
          }
          const {data}=res;
          if(!data){
            return;
          }
          const list=[];
          for(const key in data){
            list.push(JSON.parse(data[key]))
          }
          list.sort((a,b)=>a.index-b.index);
          setConfigDataList(list);
          
        }catch(e){
          message.error(e.message||"未知异常")
        }
      }
      const getThemeList=async()=>{
       
        try{
          const res= await getHotTheme({sortDate:moment().format("YYYYMMDD")})
          
          if(res.status_code!==0){
            throw new Error(res.status_msg)
          }

          
          setThemeList(res.data);
          // setThemeList([{
          //   themeId:"TZ-10133",
          //   themeName:"测试用"
          // }])
        }catch(e){
          message.error(e.message||"未知异常")
        }
      }
      
    const columns = [
        {
        title: '序号',
        dataIndex: 'uniqueId',
        },
        {
        title: '标题',
        dataIndex: 'title',
        },
        {
        title: '置顶情况',
        dataIndex: 'topStatus',
        render:(_)=>{
            if(_){
              return '是'
            }else{
              return '否'
            }
        }
        },
        {
        title: '文章类型',
        dataIndex: 'type',
        render:(_)=>{
         return InfoNameMap[_]||"其他" ;
        }
        },
        {
        title: '来源',
        dataIndex: 'source',
        },
        {
        title: '阅读量',
        dataIndex: 'clicks',
        },
        {
        title: '发文时间',
        dataIndex: 'ctime',
        render:(_)=>{
          
          return moment(_*1000).format("YYYY-MM-DD HH:mm:ss")
        }
        },
        {
        title: '封面图',
        dataIndex: 'picUrls',
        render:(_)=>{
            if(_&&_[0]){
              return <img src={_[0]} style={{height:"88px"}}/>            }else{
              return "空"
            }
        }
        },
        {
        title: '标识',
        dataIndex: '',
        render: (_,record) => {
          const isHotSpot=hotSpotMap.has(record.uniqueId)
          const isRelateInfo=relateInfomationMap.has(record.uniqueId);
          const red={color:"red"}
            return <>
                <Checkbox onChange={(e)=>{
                  
                  if(e.target.checked){
                    addHotSpotSet([record.uniqueId])
                  }else{
                    Modal.confirm({
                      content:"是否取消",
                      onOk:()=>{
                        removeHotSpotSet([record.uniqueId])
                      }
                    })
                  }
                }} style={isHotSpot?red:{}} checked={isHotSpot}>重磅热点</Checkbox>
                <Checkbox  onChange={(e)=>{
                  
                  if(e.target.checked){
                    addRelateInfomationSet([record.uniqueId])
                  }else{
                    Modal.confirm({
                      content:"是否取消",
                      onOk:()=>{
                        removeRelateInfomationSet([record.uniqueId])
                      }
                    })
                  }
                }} style={isRelateInfo?red:{}} checked={isRelateInfo}>相关资讯</Checkbox>
            </>
        },
        },
    ];

    //列为重磅热点
    const onHotSpot=()=>{
     
        
        addHotSpotSet(selectedRowKeys,()=>{
          setSelectedRowKeys([])
        });
        
    }

    // 列为相关资讯
    const onRelateInfo=()=>{
      
      addRelateInfomationSet(selectedRowKeys,()=>{
        setSelectedRowKeys([])
      })
      
    }
    const rowSelection = {
      selectedRowKeys,
      onChange: (keys)=>{
        console.log(keys)
        setSelectedRowKeys(keys)
      },
    };
    const getTopicList=async (page,pageSize)=>{
      setLoading(true);
      try{
        if(!selectThemes||selectThemes.length===0){
          throw new Error("请选择热门主题");
        }
        const res=await getThemeNews({themeIdListStr:selectThemes.join(","),pageId:page,pageSize})
        if(res.status_code!==0){
          throw new Error(res.status_msg);
        }
      setDataSource(res.data);
      if(res.data.length<pageSize){
        setIsEnd(true);
      }else{
        setIsEnd(false);
      }
      setPagination({
        current:page,
        pageSize,
      })
      }catch(e){
        message.error(e.message||"查询列表失败")
      }finally{
        setLoading(false);
      }
    }
    const onSave=async ()=>{
       //保存板块主题关联信息
     try{
      if(!currentSector){
        message.error("请选择板块!")
        return;
       }
      const tmpValues={
        tempThemes:selectThemes
      }
      console.log(tmpValues);
      const res = await postHash({key:HOTSPOT_TMP_KEY,propName:currentSector,
      value:JSON.stringify(tmpValues)})
       //保存板块配置的重磅热点与相关资讯
       if(res.code!=="0000"){
        throw new Error(`保存主题-板块对应数据失败：${res.message}`)
       }
       console.log(hotSpotMap,relateInfomationMap,hotSpotMap.values())
       const sectorValues={
        hotspots:Array.from(hotSpotMap.values(),item=>item),
        relateInfos:Array.from(relateInfomationMap.values(),item=>item)
       }
       console.log(sectorValues);
       const res2 = await postHash({key:HOTSPOT_KEY,propName:currentSector,value:JSON.stringify(sectorValues)})
       if(res2.code!=="0000"){
        throw new Error(`保存数据失败：${res2.message}`)
       }
       message.success("保存成功")
      }catch(e){
      message.error(e.message||"未知错误")
     }
    }
    const onSearch=()=>{
     
      getTopicList(1,pagination.pageSize);
    }
    //获取暂存的板块-热点主题关联内容
    const getTmpData=async (id)=>{
      try{
        const res = await fetchHash({key:HOTSPOT_TMP_KEY,propName:id})

      if(res.code!=="0000"){
        throw new Error(`获取板块配置数据失败：${res.message}`)
      }
      if(res.data){
        const data=JSON.parse(res.data);
        setSelectThemes(data.tempThemes);
      }
      }catch(e){
        message.error(e.message);
      }
    }
    // 获取当前板块已配置的数据
    const getSectorPoolData=async(id)=>{
      setHotSpotMap(new Map());
      setRelateInfomationMap(new Map());
      setSelectedRowKeys([]);
      try{
        const res = await fetchHash({key:HOTSPOT_KEY,propName:id})

      if(res.code!=="0000"){
        throw new Error(`获取板块配置数据失败：${res.message}`)
      }
      if(res.data){
        const data=JSON.parse(res.data);
        const hmap=new Map(data.hotspots.map(item=>([item.uniqueId,item])))
        const rmap=new Map(data.relateInfos.map(item=>([item.uniqueId,item])))
        setHotSpotMap(hmap)
        setRelateInfomationMap(rmap)
      }
      }catch(e){
        message.error(e.message);
      }
    }
    useEffect(()=>{

         getConfigDataList();
         getThemeList();
    },[])
    useEffect(()=>{
      if(currentSector){
        getSectorPoolData(currentSector).then(()=>{
          getTmpData(currentSector);
        });
        
      }
    },[currentSector])
  return (
    <Spin spinning={loading}>
      <Table
    
    rowKey={"uniqueId"}
    rowSelection={rowSelection}
    footer={()=>{
      return <div style={{
        display:"flex",
        justifyContent:"flex-end",
        alignItems:"center"
      }}><span style={{marginRight:"12px"}}>当前页:{pagination.current}</span><Button  style={{marginRight:"12px"}} disabled={pagination.current===1} onClick={()=>{
        getTopicList(pagination.current-1,pagination.pageSize)
      }}>上一页</Button><Button disabled={isEnd} onClick={()=>{
        getTopicList(pagination.current+1,pagination.pageSize)
      }}>下一页</Button></div>

    }}
    pagination={
     false
    }
    dataSource={dataSource}
      title={() => (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <Select mode='multiple' style={{width:"300px"}} placeholder={'请选择热门主题'}
            value={selectThemes}
            onChange={setSelectThemes}
            optionFilterProp={"children"}
            
            >
              {themeList?.map(item=>{
                    return <Option value={item.themeId}>{item.themeName}</Option>
                })}
            </Select>
            <Select  style={{width:"300px"}} placeholder={'请选择板块'}
            value={currentSector}
            onChange={setCurrentSector}
            >
                {configDataList?.map(item=>{
                    return <Option value={item.id}>{item.sectorTitle}</Option>
                })}
            </Select>
          </div>
          <div>
            <Button type="primary" style={{ marginRight: '8px' }} onClick={onHotSpot}>
              列为重磅热点
            </Button>
            <Button type="primary" style={{ marginRight: '8px' }} onClick={onRelateInfo}>
              列为相关资讯
            </Button>

            <Button  style={{ marginRight: '8px' }} onClick={onSearch}>查询</Button>
            <Button type="primary" onClick={onSave} >
              保存
            </Button>
          </div>
        </div>
      )}
      columns={columns}
    ></Table>
    </Spin>
  );
};
export default HotSpotPool;
