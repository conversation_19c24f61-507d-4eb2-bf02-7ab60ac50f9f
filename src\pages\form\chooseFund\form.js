export default {
  'type': 'object',
  'properties': {
    'listTab': {
      'type': 'string',
      'title': '榜单配置',
      'enum': [
        'zrdtj',
        'thsxj',
        'dnbd',
        'rqzwj_week_zqx',
      ],
      'enumNames': [
        '热门定投',
        '土豪销量榜',
        '高手销量榜',
        '安稳债基'
      ],
      'ui:width': '40%',
    },
    'configs': {
      'type': 'object',
      'properties': {
        'zrdtj': {
          'type': 'object',
          'properties': {
            'des': {
              'type': 'string',
              'title': '榜单说明',
              'ui:width': '50%',
            },
            'btnText': {
              'type': 'string',
              'title': '按钮文案',
              'ui:width': '50%',
              'maxLength':8
            },
          },
          'ui:hidden': (formData) => formData.listTab !== 'zrdtj',
        },
        'thsxj': {
          'type': 'object',
          'properties': {
            'des': {
              'type': 'string',
              'title': '榜单说明',
              'ui:width': '50%',
            },
            'btnText': {
              'type': 'string',
              'title': '按钮文案',
              'ui:width': '50%'
            },
          },
          'ui:hidden': (formData) => formData.listTab !== 'thsxj',
        },
        'dnbd': {
          'type': 'object',
          'properties': {
            'des': {
              'type': 'string',
              'title': '榜单说明',
              'ui:width': '50%',
            },
            'btnText': {
              'type': 'string',
              'title': '按钮文案',
              'ui:width': '50%'
            },
          },
          'ui:hidden': (formData) => formData.listTab !== 'dnbd',
        },
        'rqzwj_week_zqx': {
          'type': 'object',
          'properties': {
            'des': {
              'type': 'string',
              'title': '榜单说明',
              'ui:width': '50%',
            },
            'btnText': {
              'type': 'string',
              'title': '按钮文案',
              'ui:width': '50%',
              'maxLength':8
            },
          },
          'ui:hidden': (formData) => formData.listTab !== 'rqzwj_week_zqx',
        },
      },
    },
    'lists': {
      'type': 'array',
      'title': '自定义榜单',
      'minItems': 0,
      'maxItems': 2,
      'items': {
        'type': 'object',
        'properties': {
          'name': {
            'type': 'string',
            'title': '榜单名称',
            "maxLength": 5,
            "ui:width": "25%"
          },
          'key': {
            "type": "string",
            "title": "key值",
            "description": "字母组成",
            "maxLength": 5,
            "ui:width": "25%"
          },
          'btnText': {
            'type': 'string',
            'title': '按钮文案',
            "ui:width": "25%"
          },
          'des': {
            'type': 'string',
            'title': '榜单说明',
            "ui:width": "50%"
          },
          'funds': {
            'type': 'array',
            'title':"基金列表",
            "items":{
              'type': 'object',
              'properties': {
                'fundCode': {
                  'type': 'string',
                  'title': '基金代码',
                  'ui:width': '30%',
                  'maxLength':6,
                },
                'tags': {
                  'type': 'string',
                  'title': '标签',
                  "description":'中文逗号隔开（不要超2个）',
                  'ui:width': '30%',
                },
                "profits": {
                  "type": "string",
                  "title": "收益区间",
                  "enum": [
                    "rate",
                    "week",
                    "month",
                    "tmonth",
                    "hyear",
                    "year",
                    "tyear",
                    "now"
                  ],
                  "enumNames": [
                    "昨日涨幅",
                    "近一周涨幅",
                    "近一月涨幅",
                    "近三月涨幅",
                    "近六月涨幅",
                    "近一年涨幅",
                    "近三年涨幅",
                    "成立以来"
                  ],
                  "ui:width": "30%"
                },
                "text": {
                  "type": "string",
                  "title": "收益文案",
                  "description":'代替收益率展示',
                  'maxLength':4,
                  "ui:width": "30%"
                },
              },
              'required': ['fundCode','profits','tags'],
            }
          },
        },
        'required': ['name','key','btnText'],
      },
    },
  },
  'required': [],
};
