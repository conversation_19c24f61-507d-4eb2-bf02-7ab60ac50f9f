import React, { useState, useEffect } from 'react';
import { Input, Button, Popconfirm, message } from 'antd';
import styles from './index.less'
import classNames from 'classnames'

import api from 'api'
const { getETFFundList, fetchETFRankIndustry, postETFRankIndustry } = api;
import FundSelect from '../component/fundSelect';

interface iProp {
    index: number, // 当前项索引
    item: ETFItem, // 当前项data
    onDelete: (index: number) => void, // 删除当前项
    onChange: (index: number, value: ETFItem) => void,
    obj: any, // ETFList
}
interface ETFItem {
    secname_pub205: string
    submarket_hq: string
    thscode_hq: string
    tradecode: string
    collection?: number,
    nickname?: string,
}
interface ETFData {
    secname_pub205: string
    submarket_hq: string
    thscode_hq: string
    tradecode: string
}

const funcBlock = function(intervalTime: number, f1: Function, f2: Function) {
	let lastFunc = 0;
	let timer: any;
	// 连续执行结束，重置lastFunc，等待新的首次执行
	const funcEnd: Function =  function() {
        f2 && f2(...arguments);   
		lastFunc = 0;
	}
	// 首次执行和连续执行调用。
	return function() {
		if(lastFunc !== 0) {
			// 连续执行需清理定时器
			clearTimeout(timer);		
		}
        lastFunc = new Date().getTime();
		f1 && f1(...arguments);
		// 每次执行，启用定时器，定时器若执行，判断为连续执行结束。
		timer = setTimeout(() => {
			clearTimeout(timer);
			funcEnd(...arguments);
		}, intervalTime, ...arguments)
	}
}
const setNickName: Function = funcBlock(
    200,
    function(target: any, value: string) {
        target['value'] = value;
    },
    function(target: any, value: string, update: Function) {
        update();
    }
)

function ETFItem({
    index, // 当前项索引
    item, // 当前项data
    onDelete, // 删除当前项
    onChange, // 当前项被修改
    obj, // ETFList
}: iProp) {
    const handleItemChange = (value: string, type: 'data'|'collection'|'nickname', target?: any) => {
        let _item = item;
        switch(type) {
            case 'data': {
                let results = obj.filter( (etf: ETFData) => etf.tradecode === value)
                let _data = !!results && results.length > 0 ? results[0] : null;
                _item = {..._item, ..._data}
                onChange(index, _item)
                break;
            }
            case 'nickname': {
                let update = () => {
                    _item.nickname = value;
                    onChange(index, _item);
                }
                setNickName(target, value, update)
                break;
            }
            // case 'collection': {
            //     _item.collection = Number(value);
            //     break;
            // }
        }
    }

    return (
        <div className={classNames(styles['m-list-item'])}>
            <span>{index + 1}</span>
            <FundSelect
                obj={obj}
                item={item}
                placeholder="ETF基金代码 ETF基金名称"
                handleSelect={(value: any) => {handleItemChange(value, 'data')}}
                disabled={false}
                width={400}
            />
            <input
                className={classNames(styles['m-nickname'])}
                defaultValue={item.nickname}
                onInput={(event: any) => {handleItemChange(event.target['value'], 'nickname', event.target)}}
            />
            {/* <Input
                className={classNames(styles['m-collection'])}
                value={item.collection}
                onChange={(event) => {handleItemChange(event.target.value, 'collection')}}
            /> */}
            <Popconfirm title="确认删除吗？" okText="确认" cancelText="取消" onConfirm={() => onDelete(index)}>
                <Button type='danger' ghost>删除</Button>
            </Popconfirm>
        </div>
    )
}

export default function() {
    const [ETFList, setETFList] = useState([]); // etf列表
    const [currentIndex, setCurrentIndex] = useState(0); // 当前操作配置项索引
    const [formData, setFormData] = useState<ETFItem[]>([]); // 配置项列表

    useEffect( () => {
        getETFFundList().then( (res: any) => {
            setETFList(res.data)
        })
        getItem();
    }, [])

    const getItem = () => {
        fetchETFRankIndustry().then( (res: any) => {
            let _data = [];
            try {
                res = JSON.parse(res.data);
                console.log('列表数据', res)
                if(res) {
                    _data = res;
                }
            } catch(e) {
                message.error(e.message)
            }

            setFormData(_data);
        })
    }

    const handleDeleteItem = (index: number) => {
        let _formData = [...formData];
        _formData.splice(index, 1);
        setFormData(_formData);
        setCurrentIndex(index);
    }
    const handleAddItem = () => {
        let _data: ETFData = ETFList[0];
        let _item: ETFItem = {
            ..._data,
            // collection: 0,
        }
        let _formData = [...formData];
        _formData.push(_item);
        setFormData(_formData)
    }
    const handleChangeItem = (index: number, value: ETFItem) => {
        console.log(index, value);
        let _formData = [...formData];
        _formData[index] = value;
        setFormData(_formData);
        setCurrentIndex(index);
    }

    /**
     * 跨域jsonp
     */
    function getJsonp() {
        let _script = document.createElement("script");
        _script.type = "text/javascript";
        _script.src = `http://${window.location.href.indexOf('8080') !== -1 || window.location.href.indexOf('8000') !== -1 ? 'test' : ''}fund.10jqka.com.cn/interface/Rabbitmq/newyypushlish?key=ETFRank`;
        document.body.appendChild(_script);
        _script.onload = function (){
            document.body.removeChild(_script)
        } 
     }
    /**
     * 列表校验
     */
    const checkForm = () => {
        let formMap: any = {};
        for(let i = 0; i < formData.length; i++) {
            let etf = formData[i];
            if(!etf.nickname) {
                message.warning(`第${i + 1}项“简称”为空`)
                return false;
            }
            if(!!formMap[etf.tradecode]) {
                message.warning(`第${i + 1}项${etf.tradecode + etf.secname_pub205}重复，请检查！`)
                return false;
            } else {
                formMap[etf.tradecode] = true;
            }
        }
        return true;
    }
    const onSubmit = () => {
        if(checkForm()) {
            postETFRankIndustry({
                value: JSON.stringify(formData)
            }).then( (res: any) => {
                console.log(res)
                try {
                    if(res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        getJsonp();
                        message.success('提交成功');
                    }
                } catch(e) {
                    message.error(e.message);
                }
            })
        }
    }

    return (
        <section>
            <p className={classNames(styles['m-title'])}>热门行业ETF</p>
            <p className={classNames(styles['m-list-header'])}>
                <span>序号</span>
                <span>基金</span>
                <span>简称</span>
                {/* <span>加自选人数</span> */}
                <span>操作</span>
            </p>
            <div className={classNames(styles['m-list'])}>
                {
                    formData.map( (item: any, index: number) => {
                        return (
                        <ETFItem
                            key={index}
                            obj={ETFList}
                            index={index}
                            item={item}
                            onDelete={handleDeleteItem}
                            onChange={handleChangeItem}
                        />)
                    })
                }
            </div>
            <Button type='primary' className={classNames(styles['m-add'])} onClick={handleAddItem}>添加</Button>
            <Button type='danger' className={classNames(styles['m-submit'])} onClick={onSubmit}>提交</Button>
        </section>
    )
}