import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { Table, Button, Modal, Select, Input, DatePicker, ConfigProvider, message } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import api from 'api';
import moment from 'moment';

const { RangePicker } = DatePicker;
const { fetchHashAll, postHash } = api;

const FollowTrend = () => {
  // 经典案例配置弹窗控制
  const [classModel, setClassModel] = useState(false);
  // 调研纪要配置弹窗控制
  const [rearchModel, setRearchModel] = useState(false);
  // 经典案例配置编辑控制
  const [editClass, setEditClass] = useState({});
  // 调研纪要配置编辑控制
  const [editRearch, setEditRearch] = useState({});
  // 经典案例配置表格内容
  const [classicTable, setClassicTable] = useState([]);
  // 调研纪要配置表格内容
  const [rearchTable, setRearchTable] = useState([]);
  // 经典案例配置编辑索引
  const [classIndex, setClassIndex] = useState(-1);
  // 调研纪要配置索引
  const [rearchIndex, setRearchIndex] = useState(-1);
  const classicColumns = [
    {
      title: '分类',
      dataIndex: 'surveyType',
      render: (text, record, index) => {
        const surveyType = {
          new: '最新调研',
          stock: '个股调研',
          theme: '行业调研',
          manager: '经理调研'
        }
        return surveyType[text];
      }
    },
    {
      title: '文案',
      dataIndex: 'content',
    },
    {
      title: '跳转链接',
      dataIndex: 'jumpUrl',
    },
    {
      title: '操作',
      render: (_, record, index) => (
        <>
          <Button style={{ marginRight: '10px' }} type="primary" onClick={() => classicEdit(record, index)}>
            编辑
          </Button>
          <Button type="danger" onClick={() => deleteClassic(index)}>删除</Button>
        </>
      ),
    },
  ];

  const researchColumns = [
    {
      title: '股票代码',
      dataIndex: 'stockCode',
    },
    {
      title: '调研纪要',
      dataIndex: 'summary',
    },
    {
      title: '直播链接',
      dataIndex: 'liveUrl',
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
    },
    {
      title: '操作',
      render: (t, record, index) => (
        <>
          <Button style={{ marginRight: '10px' }} type="primary" onClick={() => rearchEdit(record, index)}>
            编辑
          </Button>
          <Button type="danger" onClick={(index) => deleteRearch(index)}>删除</Button>
        </>
      ),
    },
  ];

  useEffect(() => {
    initData();
  }, []);

  const classicEdit = (record, index) => {
    setEditClass({ ...record });
    setClassModel(true);
    setClassIndex(index);
  }

  const rearchEdit = (record, index) => {
    setEditRearch({ ...record });
    setRearchModel(true);
    setRearchIndex(index);
  }

  const initData = () => {
    fetchHashAll({
      key: 'followTrend'
    }).then(data => {
      if (data.code === '0000') {
        setClassicTable(JSON.parse(data.data.classicConfig));
        setRearchTable(JSON.parse(data.data.RearchConfig))
      }
    })
  }

  const deleteClassic = (index) => {
    const arr = [...classicTable];
    arr.splice(index, 1);
    postHash({
      key: 'followTrend',
      propName: 'classicConfig',
      value: JSON.stringify([...arr])
    }).then(data => {
      if (data.code === '0000') {
        initData();
      }
    })
  }

  const deleteRearch = (index) => {
    const arr = [...rearchTable];
    arr.splice(index, 1);
    postHash({
      key: 'followTrend',
      propName: 'RearchConfig',
      value: JSON.stringify([...arr])
    }).then(data => {
      if (data.code === '0000') {
        initData();
      }
    })
  }

  const handleClassModel = () => {
    const symbol = Object.keys(editClass).length === 3 && Object.keys(editClass)?.every(item => !!editClass[item])
    if (symbol) {
      const arr = [...classicTable];
      if (~classIndex) {
        arr[classIndex] = editClass;
      } else {
        arr.push(editClass)
      }
      postHash({
        key: 'followTrend',
        propName: 'classicConfig',
        value: JSON.stringify([...arr])
      }).then(data => {
        if (data.code === '0000') {
          setClassModel(false);
          setEditClass({});
          setClassIndex(-1);
          initData();
        }
      })
    } else {
      message.warning('您有项目未填写')
    }
  };

  const handleInputChange = (e: any, type: string, main: string) => {
    if (main === 'class') {
      setEditClass({
        ...editClass,
        [type]: e.target.value.trim()
      })
    } else {
      setEditRearch({
        ...editRearch,
        [type]: e.target.value.trim()
      })
    }
  };

  const handleRearchModel = () => {
    const symbol = Object.keys(editRearch).length === 5 && Object.keys(editRearch)?.every(item => !!editRearch[item])
    if (symbol) {
      const arr = [...rearchTable];
    if (~rearchIndex) {
      arr[rearchIndex] = editRearch;
    } else {
      arr.push(editRearch)
    }
    postHash({
      key: 'followTrend',
      propName: 'RearchConfig',
      value: JSON.stringify([...arr])
    }).then(data => {
      if (data.code === '0000') {
        setRearchModel(false);
        setEditRearch({});
        setRearchIndex(-1);
        initData();
      }
    })
    } else {
      message.warning('您有项目未填写');
    }
  };

  const handleRangePicker = (value: any) => {
    setEditRearch({
      ...editRearch,
      startTime: moment(value[0]).format('YYYY-MM-DD HH:mm'),
      endTime: moment(value[1]).format('YYYY-MM-DD HH:mm')
    })
  };

  const handleRangePickerChange = (dates, dataString) => {
    setEditRearch({
      ...editRearch,
      startTime: dataString[0],
      endTime: dataString[1]
    })
  }

  return (
    <ConfigProvider locale={zhCN}>
      <div className={styles['m-follow-trend']}>
        <span>经典案例配置</span>
        <Button
          type="primary"
          style={{ width: '80px', marginBottom: '10px' }}
          onClick={() => setClassModel(true)}
        >
          新增
        </Button>
        <Table dataSource={classicTable} columns={classicColumns} />
        <span>调研纪要配置</span>
        <Button
          type="primary"
          style={{ width: '80px', marginBottom: '10px' }}
          onClick={() => setRearchModel(true)}
        >
          新增
        </Button>
        <Table dataSource={rearchTable} columns={researchColumns} />
        <Modal
          title="经典案例配置"
          visible={classModel}
          destroyOnClose={true}
          onCancel={() => { setClassModel(false); setEditClass({}) }}
          onOk={handleClassModel}
        >
          <div className={styles['m-model-item']}>
            <span>分类:</span>
            <Select
              style={{ width: '120px' }}
              value={editClass['surveyType']}
              onChange={value => setEditClass({ ...editClass, surveyType: value })}
            >
              <Select.Option value="new">最新调研</Select.Option>
              <Select.Option value="stock">个股调研</Select.Option>
              <Select.Option value="theme">行业调研</Select.Option>
              <Select.Option value="manager">经理调研</Select.Option>
            </Select>
          </div>
          <div className={styles['m-model-item']}>
            <span>文案:</span>
            <Input
              onChange={e => handleInputChange(e, 'content', 'class')}
              style={{ width: '200px' }}
              value={editClass['content']}
            />
          </div>
          <div className={styles['m-model-item']}>
            <span>跳转链接:</span>
            <Input
              onChange={e => handleInputChange(e, 'jumpUrl', 'class')}
              style={{ width: '200px' }}
              value={editClass['jumpUrl']}
            />
          </div>
        </Modal>
        <Modal
          title="调研纪要"
          visible={rearchModel}
          destroyOnClose={true}
          onCancel={() => { setRearchModel(false); setEditRearch({}) }}
          onOk={handleRearchModel}
        >
          <div className={styles['m-model-item']}>
            <span>股票代码:</span>
            <Input
              onChange={e => handleInputChange(e, 'stockCode', 'rearch')}
              style={{ width: '200px' }}
              value={editRearch['stockCode']}
            />
          </div>
          <div className={styles['m-model-item']}>
            <span>调研纪要:</span>
            <Input
              onChange={e => handleInputChange(e, 'summary', 'rearch')}
              style={{ width: '200px' }}
              value={editRearch['summary']}
            />
          </div>
          <div className={styles['m-model-item']}>
            <span>直播链接:</span>
            <Input
              onChange={e => handleInputChange(e, 'liveUrl', 'rearch')}
              style={{ width: '200px' }}
              value={editRearch['liveUrl']}
            />
          </div>
          <div className={styles['m-model-item']}>
            <span>开始时间~结束时间:</span>
            <RangePicker
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
              style={{ width: '300px' }}
              onOk={handleRangePicker}
              onChange={handleRangePickerChange}
              value={[
                editRearch['startTime'] ? moment(editRearch['startTime'], 'YYYY-MM-DD HH:mm') : null,
                editRearch['endTime'] ? moment(editRearch['endTime'], 'YYYY-MM-DD HH:mm') : null,
              ]}
            />
          </div>
        </Modal>
      </div>
    </ConfigProvider>
  );
};

export default FollowTrend;
