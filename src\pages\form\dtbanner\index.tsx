import React, { useState, useEffect } from "react";
import Form<PERSON><PERSON> from 'form-render/lib/antd';
import axios from 'axios'
import api from 'api';
import { Button, message } from 'antd'

const {fetchDtbanner, postDtbanner} = api;

let FORM_CONFIG: any = {
    propsSchema: {
        type: 'object',
        properties: {
            title: {
                title: '主标题(1~10个字符)',
                type: 'string',
                maxLength: 10
            },
            subTitle: {
                title: '副标题(0～18个字符)',
                type: 'string',
                maxLength: 18,
            },
            icon: {
                title: 'icon地址(必填,https图片地址)',
                type: 'string',
            },
            url: {
                title: '跳转地址(必填,https地址)',
                type: 'string',
            }
        },
        required: ['title', 'icon', 'url']
    }
};

// 
export default function () {
    const [init, setInit] = useState(false);
    const [formConfig, setFormConfig] = useState({});
    const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    
    useEffect(() => {
        fetchDtbanner().then((res: any) => {
            try {
                res = JSON.parse(res.data);
                if (res) {
                    FORM_CONFIG.formData = {
                        ...res
                    };
                }
            } catch (e) {
                console.warn(e)
            }
            
            setInit(true);
            setFormConfig(FORM_CONFIG);
            setData(FORM_CONFIG.formData);
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [init]);

    const onSubmit = () => {
        if (valid.length > 0) {
            message.error(`校验未通过字段：${valid.toString()}`);
        } else {
            let _postData = {
                ...formData
            }
            postDtbanner({
                value: JSON.stringify(_postData)
            }).then((res: any) => {
                try {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('发布成功！')
                    }
                } catch (e) {
                    message.error(e.message);
                }
            })
        }
    };

    if (!init) return '加载中'
    return (
        <div style={{ padding: 60 }}>
        <FormRender
            propsSchema={FORM_CONFIG.propsSchema}
            formData={formData}
            onChange={setData}
            onValidate={setValid}
            showDescIcon={true}
        />
        <Button type="primary" onClick={onSubmit}>提交</Button>
        </div>
    );
}
