{"type": "object", "ui:className": "m-position-form", "properties": {"def": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "默认"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "def"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}, "cczq": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "长城证券"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "cczq"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}, "cjzq": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "长江证券"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "cjzq"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}, "dwzq": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "东吴证券"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "dwzq"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}, "dxzq": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "东兴证券"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "dxzq"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}, "gjzq": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "国金证券"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "gjzq"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}, "hczq": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "华创证券"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "hczq"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}, "zszq": {"type": "object", "ui:className": "m-position-form-item", "properties": {"text": {"ui:width": "51%", "ui:labelWidth": 200, "ui:className": "f-position-title", "type": "string", "ui:widget": "html", "default": "浙商证券"}, "type": {"ui:className": "f-position-type", "type": "string", "ui:widget": "html", "default": "zszq"}, "operationImage": {"title": "持仓运营位宣传图", "type": "string", "ui:labelWidth": 200, "ui:widget": "UploadImg", "ui:className": "u-position-image"}, "url": {"title": "跳转链接", "type": "string", "ui:width": "51%", "ui:labelWidth": 200}, "startTime": {"title": "开始时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}, "endTime": {"title": "结束时间", "type": "string", "ui:width": "51%", "ui:labelWidth": 200, "format": "dateTime"}}}}}