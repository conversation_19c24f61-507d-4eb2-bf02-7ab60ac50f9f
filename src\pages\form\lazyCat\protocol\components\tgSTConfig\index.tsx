import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';

import { Button, Drawer, message, Popconfirm } from 'antd';
import { ProtoListToForm, ProtoType, TATable } from '../../utiles';
import FormRender from 'form-render/lib/antd';
export interface IProps {
  visible: boolean;
  data: TATable;
  onSubmit: (data: object) => void;
  onClose: () => void;
}
const TGSTConfig: FC<IProps> = ({ visible, data, onSubmit, onClose }) => {
    const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);
  const schema = {
    type: 'object',
    properties: {
      riskProtoName: {
        title: '小目标风险揭示书名称',
        type: 'string',
      },
      riskProtoLink: {
        title: '小目标风险揭示书链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$"
      },
      serveProtoName: {
        title: '小目标服务协议名称',
        type: 'string',
      },
      serveProtoLink: {
        title: '小目标服务协议链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$"
      },
      ruleProtoName: {
        title: '小目标业务规则名称',
        type: 'string',
      },
      ruleProtoLink: {
        title: '小目标业务规则链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$"
      },
      strategyProtoName: {
        title: '小目标策略说明书名称',
        type: 'string',
      },
      strategyProtoLink: {
        title: '小目标策略说明书链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$"
      },
      fundAlterPoolName:{
        title: '小目标基金备选库名称',
          type: 'string',
          "ui:hidden":true,
          
      },
      fundAlterPoolLink: {
        title: '小目标基金备选库链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$"
      },
    },
   
  };
  useEffect(()=>{
    if(visible){
    //   setData({
    //     protoName:data?.strategy?.fundAlterPool.name,
    //     protoLink:data?.strategy?.fundAlterPool.link
    //   })
    const _formData={};
    setData(ProtoListToForm(data?.share?.stProto))

    }
  },[visible,data])
  const handleSave=()=>{
    if (valid.length > 0) {
    message.error(`输入校验未通过，请检查`)
    return ;
    }
    onSubmit(formData);
  }
  return (
    <Drawer title={`投顾机构：${data?.taName||'--'}`} width={1000} visible={visible} onClose={onClose} >
      <div style={{ paddingTop: '40px' }}>
        <FormRender formData={formData} 
         onChange={setData}
         onValidate={setValid}
        labelWidth={180} displayType={'row'} propsSchema={schema} />
        <div style={{display:'flex',justifyContent:'flex-end'}}>
        <Button style={{marginRight:'12px'}} onClick={onClose}>取消</Button>
        <Popconfirm title={'确定要提交吗？'} cancelText={'取消'} okText={'确定'} onConfirm={handleSave}>
        <Button type='primary' >提交</Button>
        </Popconfirm>
        </div>
      </div>
    </Drawer>
  );
};
export default TGSTConfig;
