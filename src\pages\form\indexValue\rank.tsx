import React, { useEffect, useState } from 'react';
import { Table, But<PERSON>, Popconfirm, Drawer } from 'antd';
import MyDrawer from './rankDrawer'

interface dataProps {
    flag?: number,
    fundCode?: string,
    fundName?: string,
    name?: string,
    type?: string,
    introduce?: string,
    innerFund?: string,
    innerName?: string,
    innerText?: string,
    outterFund?: string,
    outterName?: string,
    outterText?: string,
    applyArea?: string,
    pe?: string,
    pb?: string,
}
interface iProps {
    saveRank: Function,
    data: Array<dataProps>
}

export default function ({ saveRank, data }: iProps) {
    const [list, setList] = useState<Array<dataProps>>([]) //数据数组
    const [visible, setVisible] = useState(false); //修改窗口展示
    const [editData, setEditData] = useState<dataProps>({}); //正在编辑的数据
    useEffect(() => {
        console.log('rankcomponent', data)
        if (data.length === 0) return
        setList(data)
    }, [data])
    /**
     * 
     * @param record 修改的数据条目
     */
    const showDrawer = (record:any) => {
        console.log(record)
        setVisible(true);
        setEditData(record)
    };
    const closeDrawer = () => {
        setVisible(false)
    }
    const columns = [
        {
            title: '指数代码',
            dataIndex: 'fundCode',
            key: 'fundCode',
            editable: true,

        },
        {
            title: '指数名称',
            dataIndex: 'fundName',
            key: 'fundName',
        },
        {
            title: '展示名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '指数类型',
            dataIndex: 'type',
            key: 'type',
        },
        {
            title: '指数介绍',
            dataIndex: 'introduce',
            key: 'introduce',
            width:'18%'
        },
        {
            title: '场内基金代码',
            dataIndex: 'innerFund',
            key: 'innerFund',
        },
        {
            title: '场内基金名称',
            dataIndex: 'innerName',
            key: 'innerName',
            width:'8%'
        },
        {
            title: '场内展示文案',
            dataIndex: 'innerText',
            key: 'innerText',
            width:'8%'
        },
        {
            title: '场外基金代码',
            dataIndex: 'outterFund',
            key: 'outterFund',
        },
        {
            title: '场外基金名称',
            dataIndex: 'outterName',
            key: 'outterName',
            width:'8%'
        },
        {
            title: '场外展示文案',
            dataIndex: 'outterText',
            key: 'outterText',
            width:'8%'
        },
        {
            title: '适用指标',
            dataIndex: 'applyArea',
            key: 'applyArea',
        },
        {
            title: 'PE',
            dataIndex: 'pe',
            key: 'pe',
        },
        {
            title: 'PB',
            dataIndex: 'pb',
            key: 'pb',
        },
        {
            title: 'operation',
            dataIndex: 'operation',
            render: (text: any, record: any) =>
                <div style={{display:'flex', flexDirection:'column'}}>
                    <Popconfirm title="确定删除?" onConfirm={() => deleteItem(record.flag)}>
                        <Button type="danger" ghost>删除</Button>
                    </Popconfirm>
                    <Button onClick={() => showDrawer(record)} className={'g-mt10'} >编辑</Button>

                </div>
        },

    ]
    function addItem() {
        let _temp: dataProps = {
            flag: new Date().getTime(),
            fundCode: '',
            fundName: '',
            name: '',
            type: '',
            introduce: '',
            innerFund: '',
            innerName: '',
            innerText: '',
            outterFund: '',
            outterName: '',
            outterText: '',
            applyArea: '',
            pe: '',
            pb: '',
        }
        const _list: Array<dataProps> = [...list]
        _list.push(_temp)
        saveRank(_list)
    }
    function deleteItem(flag: any) {
        const _list = [...list]
        saveRank(_list.filter(item => item.flag !== flag))
    }
    function save() {
        saveRank(list)
    }
    /**
     * 
     * @param item 修改后的数据
     * @description 将修改后的数据保存至展示的table
     */
    function itemSave(item:dataProps) {
        closeDrawer()
        console.log(item)
        let _list = [...list]
        let target:number = _list.findIndex((val:any) => val.flag === item.flag)
        console.log(target)
        _list[target] = item
        saveRank(_list)
        

    }
    return <div>
        <p className={'g-fs30'}>估值排行配置</p>
        <Button onClick={addItem} type="primary" style={{ margin: 16 }}>
            增加
        </Button>
       
        <Table
            columns={columns}
            dataSource={list}
            size="small"
        >

        </Table>
        <MyDrawer
        show={visible}
        closeDrawer = {closeDrawer}
        data = {editData}
        save = {itemSave}
        />

    </div>

}