.m-fundConfigCheck {
  p {
    margin: 0;
  }
  .m-filters {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;
    .m-option {
      > span {
        margin-right: 10px;
        display: inline-block;
      }
      > input {
        width: 400px;
        margin-right: 30px;
      }
      :global {
        .ant-select {
          min-width: 250px;
          margin-right: 30px;
        }
      }
    }
  }
  .m-tabs {
    border-top: 1px #e8e8e8 solid;
    margin-top: 10px;
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: flex-end;
    .m-tab {
      width: 94px;
      height: 46px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      cursor: pointer;
      .m-underline {
        bottom: 0;
        position: absolute;
        display: none;
      }
    }
    .m-tab.m-now {
      font-size: 22px;
      .m-underline {
        width: 100%;
        height: 4px;
        display: block;
        background: #40a9ff;
      }
    }
  }
  .m-table-pagination {
    display: flex;
    align-items: center;
    > p {
      > span {
        color: #40a9ff;
      }
    }
    .m-pagination {
      margin-left: 10px;
    }
    &.m-top {
      margin-left: auto;
      margin-bottom: 6px;
    }
    &.m-bottom {
      margin-top: 10px;
    }
  }
}
