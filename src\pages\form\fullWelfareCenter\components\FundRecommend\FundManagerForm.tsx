import React from 'react';
import { Form, Input } from 'antd';
import UploadForm from '../Upload';

const FundManagerForm = props => {
  const { getFieldDecorator } = props;
  return (
    <div>
      <Form.Item label="基金代码">
        {getFieldDecorator('fundRecommend.mainFund.fundCode', {
          rules: [{ required: true, message: '请输入基金代码' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="经理姓名">
        {getFieldDecorator('fundRecommend.mainFund.optional.isNewFund.managerName', {
          rules: [{ required: true, message: '请输入经理姓名' }],
        })(<Input />)}
      </Form.Item>
      <UploadForm
        getFieldDecorator={getFieldDecorator}
        fieldLocation="fundRecommend.mainFund.optional.isNewFund.managerPhoto"
        label="经理照片"
        required={true}
      />
      <Form.Item label="经理标签">
        {getFieldDecorator('fundRecommend.mainFund.optional.isNewFund.managerTag', {
          rules: [{ required: true, message: '请输入经理标签' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="突出亮点1-主">
        {getFieldDecorator('fundRecommend.mainFund.mainHighLight1', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="突出亮点1-副">
        {getFieldDecorator('fundRecommend.mainFund.subHighLight1', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="突出亮点2-主">
        {getFieldDecorator('fundRecommend.mainFund.optional.isNewFund.mainHighLight2', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="突出亮点2-副">
        {getFieldDecorator('fundRecommend.mainFund.optional.isNewFund.subHighLight2', {
          rules: [{ required: true, message: '请输入突出亮点' }],
        })(<Input />)}
      </Form.Item>
      <Form.Item label="初始申购人数">
        {getFieldDecorator('fundRecommend.mainFund.optional.isNewFund.buyPeople', {
          rules: [{ required: true, message: '请输入初始申购人数' }],
        })(<Input />)}
      </Form.Item>
    </div>
  );
};

export default FundManagerForm;
