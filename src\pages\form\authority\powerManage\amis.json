{"title": "权限管理", "type": "page", "id": "u:348396df0b74", "body": [{"label": "新增", "type": "button", "actionType": "drawer", "level": "primary", "id": "u:68172a731726", "drawer": {"title": "新增", "size": "lg", "body": {"type": "form", "reload": "manageCrud", "api": {"method": "post", "url": "/role/rights/config/save_or_update", "data": {"roleId": "${roleId}", "equityArray": "${equityArray}", "equityMap": "${equityMap}", "&": "$$"}, "dataType": "json", "requestAdaptor": "const { url, data } = api;\r\nlet flatArr = []\r\ndata.equityArray.forEach(item=>{\r\nconst equ = item.rightsId.split(',')?.map(select=>({roleId:data.roleId,rightsId:select,rightsTypeId:data.equityMap[select],rightsTime:item.renewal?String(item.rightsTime):null,renewal:item.renewal?'1':'0'}))\r\nflatArr.push(...equ)})\r\nconsole.log(flatArr)\r\nreturn { ...api, data:flatArr  };"}, "body": [{"type": "select", "name": "roleId", "label": "角色名称", "source": "${roleOption}"}, {"type": "combo", "name": "equityArray", "multiple": true, "multiLine": true, "addButtonText": "增加权益", "value": [{}], "items": [{"type": "tpl", "tpl": "权益<%= this.index + 1%>", "className": "p-t-xs"}, {"type": "nested-select", "name": "rightsId", "label": "级联选择器多选", "checkAll": false, "onlyLeaf": true, "onlyChildren": true, "withChildren": true, "source": "${nestedSelectEquityOption}"}, {"name": "renewal", "label": "支持续期", "type": "switch", "value": "", "option": "不支持续期可能会导致线上问题，请谨慎选择"}, {"name": "rightsTime", "disabledOn": "!(Number(renewal))", "visibleOn": "Number(renewal)", "label": "期限设置", "type": "input-number", "placeholder": "期限设置", "value": "", "suffix": "天", "option": ""}]}], "id": "u:eddda5c21d91"}}}, {"type": "crud", "syncLocation": false, "name": "manageCrud", "api": {"method": "get", "url": "/role/rights/config/page", "autoRefresh": "true", "adaptor": "return {\r\n  status: payload.status_code === 0 ? 0 : 1,\r\n  msg: payload.message,\r\n  data: payload.data.configList\r\n}", "data": {"&": "$$"}}, "columns": [{"name": "roleLevelConfig", "label": "角色名称", "type": "text", "id": "u:949473c06746", "tpl": "<%= this.roleLevelConfig.roleName %>"}, {"type": "table", "label": "权限内容", "source": "${roleRightsConfigList}", "columns": [{"name": "rightsType", "type": "text", "label": "一级权益"}, {"name": "rightsName", "type": "text", "label": "二级权益名称"}, {"name": "renewal", "label": "支持续期", "type": "text", "tpl": "<%= this.renewal==='0'?'否':'是' %>"}, {"name": "rightsTime", "label": "续期期限", "type": "text", "tpl": "<%= this.renewal==='0'?'/':this.rightsTime %>"}]}, {"type": "operation", "label": "操作", "buttons": [{"label": "编辑", "type": "button", "level": "link", "actionType": "drawer", "drawer": {"title": "编辑", "body": {"type": "form", "reload": "manageCrud", "api": {"method": "post", "url": "/role/rights/config/save_or_update", "data": {"roleId": "${roleLevelConfig.id}", "equityArray": "${roleRightsConfigList}", "equityMap": "${equityMap}", "&": "$$"}, "dataType": "json", "requestAdaptor": "const { url, data } = api;\r\nlet flatArr = []\r\ndata.equityArray.forEach(item=>{\r\nconst equ = item.rightsId.split(',')?.map(select=>({roleId:data.roleId,rightsId:select,rightsTypeId:data.equityMap[select],rightsTime:Number(item.renewal)?String(item.rightsTime):null,renewal:Number(item.renewal)?'1':'0'}))\r\nflatArr.push(...equ)})\r\nconsole.log(flatArr)\r\nreturn { ...api, data:flatArr  };"}, "body": [{"type": "select", "name": "roleLevelConfig.roleName", "label": "角色名称", "source": "${roleOption}", "disabled": "true"}, {"type": "combo", "name": "roleRightsConfigList", "multiple": true, "multiLine": true, "addButtonText": "增加权益", "value": [{}], "items": [{"type": "tpl", "tpl": "权益<%= this.index + 1%>", "className": "p-t-xs"}, {"type": "nested-select", "name": "rightsId", "label": "级联选择器多选", "checkAll": false, "onlyLeaf": true, "onlyChildren": true, "withChildren": true, "source": "${nestedSelectEquityOption}"}, {"name": "renewal", "label": "支持续期", "type": "switch", "value": "", "option": "不支持续期可能会导致线上问题，请谨慎选择"}, {"name": "rightsTime", "disabledOn": "!(Number(renewal))", "visibleOn": "Number(renewal)", "label": "期限设置", "type": "input-number", "placeholder": "期限设置", "value": "", "option": "", "suffix": "天"}]}], "id": "u:eddda5c21d91"}, "position": "right", "size": "lg"}, "id": "u:f652330a4b18"}, {"label": "删除", "type": "button", "actionType": "ajax", "level": "link", "confirmText": "确认要删除角色名称[${roleLevelConfig.roleName}]？", "api": {"url": "/role/rights/config/delete?idList=${roleLevelConfig.id}", "data": {"idList": "${[roleLevelConfig.id]}"}, "method": "post", "dataType": "json", "messages": {"success": "删除成功！"}}}], "id": "u:96d5b5297574"}], "bulkActions": [], "itemActions": [], "features": ["update", "create"], "id": "u:337afccf35ff", "headerToolbar": ["bulkActions"]}], "className": "m b-info"}