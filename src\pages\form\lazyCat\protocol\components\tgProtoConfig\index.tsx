import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';

import { Button, Drawer, message, Popconfirm } from 'antd';
import { ProtoListToForm, TATable } from '../../utiles';
import FormRender from 'form-render/lib/antd';
import api from 'api';
const { fetchHash, postHash } = api;
const HASH_KEY = 'tgRiskContent';
export interface IProps {
  visible: boolean;
  data: TATable;
  onSubmit: (data: object) => void;
  onClose: () => void;
}
const TGProtoConfig: FC<IProps> = ({ visible, data, onSubmit, onClose }) => {
    const [formData, setData] = useState({});
  const [valid, setValid] = useState([]);

  const schema = {
    type: 'object',
    properties: {
      riskProtoName: {
        title: '风险揭示书名称',
        type: 'string',
      },
      riskProtoLink: {
        title: '风险揭示书链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$",
      },
      riskProtoContent: {
        title: '风险揭示书内容',
        type: 'string',
        description: `文案若需要加样式，可按如下例子：<p style="font-weight: bold;margin-bottom: 4.26667vw">你好</p><p>中国</p>`,
        format: "textarea",
      },
      serveProtoName: {
        title: '服务协议名称',
        type: 'string',
      },
      serveProtoLink: {
        title: '服务协议链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$"
      },
      ruleProtoName: {
        title: '业务规则名称',
        type: 'string',
      },
      ruleProtoLink: {
        title: '业务规则链接',
        type: 'string',
        pattern: "^http(|s)?://[^\n ，]*$"
      },
    },
   
  };
  useEffect(()=>{
    if(visible){
      // console.log(data)
      const _formData=ProtoListToForm(data?.share?.protos);
      setData(_formData)
      fetchHash({
        key: HASH_KEY,
        propName: data.taCode,
      }).then(res => {
        if (res.code === '0000') {
          if (res.data) {
            setData({
              ..._formData,
              riskProtoContent: res.data
            })
          }
        } else {
          message.error(`获取风险揭示书内容失败`)
        }
      })
    }
  },[visible,data])
  const handleSave=()=>{
    if (valid.length > 0) {
      message.error(`输入校验未通过，请检查`)
      return ;
    }
    postHash({
      key: HASH_KEY,
      propName: data.taCode,
      value: formData.riskProtoContent || '',
    })
      .then(res => {
        if (res.code !== '0000') {
          message.error(`保存获取风险揭示书内容失败`)
        }
      })
      .finally(() => {
        onSubmit(formData);
      });
    
  }
  return (
    <Drawer title={`投顾机构：${data?.taName||'--'}`} width={1000} visible={visible} onClose={onClose} >
      <div style={{ paddingTop: '40px' }}>
        <FormRender formData={formData} 
         onChange={setData}
         onValidate={setValid}
         showDescIcon={true}
        labelWidth={160} displayType={'row'} propsSchema={schema} />
        <div style={{display:'flex',justifyContent:'flex-end'}}>
        <Button style={{marginRight:'12px'}} onClick={onClose}>取消</Button>
        <Popconfirm title={'确定要提交吗？'} cancelText={'取消'} okText={'确定'} onConfirm={handleSave}>
        <Button type='primary' >提交</Button>
        </Popconfirm>
        </div>
      </div>
    </Drawer>
  );
};
export default TGProtoConfig;
