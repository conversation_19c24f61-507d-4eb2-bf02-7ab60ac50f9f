import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON> from 'form-render/lib/antd';
import api from 'api';
import { Button, Popconfirm, message, Tabs, Input} from 'antd';
import FORM_JSON from './form.json';

export default function () {
    const { fetchTemporaryIdeaSuggestion, postTemporaryIdeaSuggestion, fetchIdeaSuggestion, postIdeaSuggestion } = api;
    const [formData, setFormData] = useState({});
    const [valid, setValid] = useState([]);
    const changeFormData = (value: any) => {
        console.log(value)
    }
    /**
     * 更新表单
     */
    const updateForm = () => {
        console.log(formData)
        // valid 是校验判断的数组，valid 长度为 0 代表校验全部通过
        if (valid.length > 0) {
            alert(`校验未通过字段：${valid.toString()}`);
            return;
        }
        api.postSecondCategory({
            value: JSON.stringify(formData)
        }).then((res: any) => {
            if (res.code !== '0000') {
                message.error(res.msg);
            } else {
                message.success('提交成功！');
            }
        }).catch((e: Error) => {
            message.error(e.message);
        });
    };
    useEffect(() => {
        api.fetchSecondCategory().then((res: any) => {
            let { code, data } = res;
            if (code === '0000' && data) {
                setFormData(JSON.parse(data));
            }
        }).catch((e: Error) => {
            message.error(e.message);
        })
    }, [])
    return (
        <article> 
            <FormRender
                propsSchema={FORM_JSON.propsSchema}
                formData={formData}
                onChange={setFormData}
                onValidate={setValid}
                displayType="column"
                showDescIcon={true}
            />
            <Popconfirm
                placement="rightBottom"
                title={'你确认要更新么'}
                onConfirm={updateForm}
                okText="确认"
                cancelText="取消"
            >
                <Button
                    type="primary" 
                >
                    提交
                </Button>
            </Popconfirm>
        </article>
    )
}