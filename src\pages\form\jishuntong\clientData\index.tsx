/*
 * 机构客户数据维护
 * @Author: <PERSON><PERSON><PERSON><PERSON>@myhexin.com
 * @Date: 2023-02-16 15:56:07
 * @Last Modified by: z<PERSON><PERSON><PERSON>@myhexin.com
 * @Last Modified time: 2023-03-16 17:03:37
 */
import React, { useState, useEffect, FC } from 'react';
import classnames from 'classnames';

import {
  Button,
  Col,
  Collapse,
  DatePicker,
  message,
  Modal,
  Row,
  Select,
  Spin,
  Table,
  Card,
  Form,
  ConfigProvider,
  Pagination,
} from 'antd';
const { Panel } = Collapse;
const { Option } = Select;
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import api from 'api';
import ModifyIndicators from './modifyIndicators';
import moment from 'moment';
import { SummaryFilter } from '../components/filter';
import { fofProductProps, Result, showFiltersMap } from '../type';
import { safeValueShow } from '../../smsPlatform/fn';
import { generateManagerFeeInfoListInTable, generateSaleInfoListInTable } from '../util';
import AllTable from '../components/allTable';
import styles from '../index.less';
import zhCN from 'antd/es/locale/zh_CN';
import { ExportButton, ExportType } from '../components/exportButton';
import { FormComponentProps } from 'antd/lib/form';
const { postHash, fetchHashAll, postHashDel, getTopicFunds, fetchFofQueryList, fetchFofQueryResult } = api;

export interface IProps { }
export default Form.create()((props: FormComponentProps) => {
  const { getFieldDecorator } = props.form;
  const [isListLoading, setIsListLoading] = useState<boolean>(false); // 列表加载loading
  const [dataSource, setDataSource] = useState<any[]>();
  const [investNameOption, setInvestNameOption] = useState<any[]>();
  const [investAccountNameOption, setInvestAccountName] = useState<any[]>();
  const [modalShow, setModalShow] = useState(false);
  const [modalAllShow, setModalAllShow] = useState(false);
  const [result, setResult] = useState<Result>({})
  const [dataColumns, setDataColumns] = useState<any[]>([]);
  const [nowEditInfo, setNowEditInfo] = useState<any>({});
  const [searchItem, setSearchItem] = useState<any>({
    strategyCode:'',
    saleId:"",
    account:'',
    investor:'',
    tradeChannel:'',
    accountType:'',
    startDate:'',
    endDate:''
  });
  const [pageNum, setPageNum] = useState(1);
  const [tableTotal, setTableTotal] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const updateInfo = record => {
    // `1`;
  };
  const defaultColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 50,
    },
    {
      title: '投管人名称',
      dataIndex: 'investorName',
      key: 'investorName',
      width: 100,
    },
    {
      title: '投资账户名称',
      dataIndex: 'accountName',
      key: 'accountName',
      width: 100,
      render: (text: unknown, record: any) => {
        return <span onClick={() => { setNowEditInfo(record); setModalAllShow(true) }}>{text}</span>
      }
    },
  ];
  // 初始化数据
  useEffect(() => {
    fetchFofQueryList({}, '')
      .then((res: any) => {
        if (res.status_code === 0) {
          let resultSingleData = res.data as Partial<fofProductProps>;
          setDataSource((resultSingleData.productInfoList || []).map((item, index) => ({ ...item, index: index })))
          if (resultSingleData?.count) setTableTotal(resultSingleData?.count)
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
      fetchFofQueryResult({}, '')
      .then((res: any) => {
        if (res.status_code === 0) {
          setResult(res.data)
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
  }, []);

  // 用户名称修改事件
  const handlePageNum = (current: number) => {
    setPageNum(current)
  }
  useEffect(() => {
    const pageObj = { ...searchItem, pageNum: pageNum, pageSize: pageSize }
    !(pageNum === 1 && pageSize === 10) && fetchFofQueryList({}, getUrlParmFromObject(pageObj))
      .then((res: any) => {
        if (res.status_code === 0) {
          let resultSingleData = res.data as Partial<fofProductProps>;
          console.log('resultSingleData.productInfoList', resultSingleData.productInfoList?.length)

          setDataSource((resultSingleData.productInfoList || []).map((item, index) => ({ ...item, index: index })))
        } else {
          message.warn(res.status_msg);
        }
      })
      .catch((err: unknown) => {
        console.log(err);
      });
  }, [pageSize, pageNum])
  const handleOk = () => { };
  const handleIndicatorOk = (valArr) => {
    let column = valArr.map(item => {
      let berforeObj: any = {
        title: item.label,
        dataIndex: item.value,
        key: item.value,
        width: 100,
      }
      console.log('accountColumn', item)
      if (item.value === 'saleInfoList') {
        berforeObj = {
          ...berforeObj,
          render: (text: unknown, record: any) => {
            return <span>{generateSaleInfoListInTable(text)}</span>
          }
        }
      }
      if (item.value === 'managerFeeInfoList') {
        berforeObj = {
          ...berforeObj,
          render: (text: unknown, record: any) => {
            return <span>{generateManagerFeeInfoListInTable(text)}</span>
          }
        }
      }
      if (item.value === 'investment') {
        berforeObj = {
          ...berforeObj,
          render: (text: unknown, record: any) => {
            return <span>{item.value ? '是' : '否'}</span>
          }
        }
      }
      if (item.type === 'returnRatio') {
        berforeObj = {
          ...berforeObj,
          render: (text: unknown, record: any) => {
            return <span>{`${Number(text) * 100}%`}</span>
          }
        }
      }
      return berforeObj
    });
    setDataColumns([...defaultColumns, ...column])
    // console.log(column,'what is the')
  };
  const getUrlParmFromObject = obj => {
    let url = '?';
    Object.keys(obj || {})?.forEach((key, index) => {
      if (obj[key]) {
        url += key + '=' + obj[key] + (index !== Object.keys(obj || {}).length - 1 ? '&' : '');
      }
    });
    return url;
  };
  const handlePageSize = (pageNum: number, pageSize: number) => {
    console.log('页容量控制器', pageNum, pageSize);
    setPageSize(pageSize);
  };
  return (
    <div>
      <Form layout="inline">
        <Form.Item>
          <SummaryFilter
            submitSearch={param => {
              setSearchItem(param)
              fetchFofQueryList({}, getUrlParmFromObject(param))
                .then((res: any) => {
                  if (res.status_code === 0) {
                    let resultSingleData = res.data as Partial<fofProductProps>;
                    setDataSource((resultSingleData?.productInfoList || []).map((item, index) => ({ ...item, index: index })))
                    if (resultSingleData?.count) setTableTotal(resultSingleData?.count)
                  } else {
                    message.warn(res.status_msg);
                  }
                })
              fetchFofQueryResult({}, getUrlParmFromObject(param))
                .then((res: any) => {
                  if (res.status_code === 0) {
                    setResult(res.data)
                  } else {
                    message.warn(res.status_msg);
                  }
                })
                .catch((err: unknown) => {
                  console.log(err);
                });
            }}
            showArr={showFiltersMap}
          />
          <ExportButton exportType={ExportType.CLIENT_DATA_TYPE} data={searchItem}></ExportButton>
        </Form.Item>
      </Form>
      <Card style={{ marginTop: 50, marginBottom: 50 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
          <span style={{ marginRight: 50 }}>结果总览</span><div >{`投管人${safeValueShow(result.accountCount)}户`}</div>
          <div style={{ marginRight: 20 }}>{`投管人${safeValueShow(result.investorCount)}户`}</div>
          <div style={{ marginRight: 20 }}>{`投资账户${safeValueShow(result.accountCount)}户`}</div>
          <div style={{ marginRight: 20 }}>{`保有量${safeValueShow(result.holdings)}元`}</div>
          <div style={{ marginRight: 20 }}>{`非货保有量${safeValueShow(result.stockHoldings)}元`}</div>
          <div style={{ marginRight: 20 }}>{`货币保有量${safeValueShow(result.moneyHoldings)}元`}</div>
          <div style={{ marginRight: 20 }}>{`资管产品保有量${safeValueShow(result.financeHoldings)}元`}</div>
          <Button onClick={() => { setModalShow(true) }} type='primary'>调整指标</Button>
        </div>
      </Card>


      <ConfigProvider locale={zhCN}>
        <Table columns={dataColumns}
          style={{ marginTop: 50 }}
          dataSource={dataSource}
          rowKey={record => record.index}
          scroll={{ x: 1300 }}
          pagination={false}
        >
        </Table>
      </ConfigProvider>
      <div className={classnames(styles['m-table-pagination'], styles['m-top'])}>
        <ConfigProvider locale={zhCN}>
          <Pagination
            className={classnames(styles['m-pagination'])}
            current={pageNum}
            pageSize={pageSize}
            total={tableTotal}
            onChange={handlePageNum}
            showSizeChanger
            pageSizeOptions={['10', '20', '30', '50']}
            onShowSizeChange={handlePageSize}
            hideOnSinglePage={tableTotal < 10}
          />
        </ConfigProvider>
      </div>
      <div>
        <ModifyIndicators show={modalShow} onClose={() => { setModalShow(false) }} onCheck={(valArr) => { handleIndicatorOk(valArr) }}></ModifyIndicators>
      </div>
      <div>
        <AllTable nowEditInfo={nowEditInfo} onClose={() => { setModalAllShow(false) }} modalAllShow={modalAllShow} />
      </div>
    </div>
  );
});
