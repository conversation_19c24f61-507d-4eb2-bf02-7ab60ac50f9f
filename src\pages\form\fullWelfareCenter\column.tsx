import React from 'react';
import { ITableItem } from './types';
import { Button, Popconfirm } from 'antd';
import styles from './index.less';

export const getColumns = (
  handleEdit: (tableItem: ITableItem, index: number) => void,
  handleDelete: (tableItem: ITableItem, index: number) => void,
  handleCopyUrl: (index: number) => void,
  handleCopyConfig: (index: number) => void,
  handleAbort: (tableItem: ITableItem, index: number) => void,
  handleEnable: (tableItem: ITableItem, index: number) => void,
) => [
  {
    title: '策略名称',
    key: 'strategyName',
    render: (dataItem: ITableItem) => {
      return dataItem.strategyContent.name;
    },
  },
  {
    title: '创建人',
    dataIndex: 'createAuthor',
    key: 'createAuthor',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '最后编辑人',
    dataIndex: 'lastEditor',
    key: 'lastEditor',
  },
  {
    title: '最后编辑时间',
    dataIndex: 'lastEditTime',
    key: 'lastEditTime',
  },
  {
    title: '操作',
    key: 'action',
    render: (tableItem: ITableItem, _: ITableItem, index: number) => {
      return (
        <div>
          <Button
            type="primary"
            className={styles['button']}
            onClick={() => handleEdit(tableItem, index)}
          >
            编辑
          </Button>
          {index === 0 ? null : (
            <Popconfirm
              title="你确定要删除这条策略吗"
              onConfirm={() => handleDelete(tableItem, index)}
              okText="是"
              cancelText="否"
            >
              <Button type="primary" className={styles['button']}>
                删除
              </Button>
            </Popconfirm>
          )}
          <Button type="primary" className={styles['button']} onClick={() => handleCopyUrl(index)}>
            复制地址
          </Button>
          <Button
            type="primary"
            className={styles['button']}
            onClick={() => handleCopyConfig(index)}
          >
            复制配置
          </Button>
          {index == 0 ? null : (
            <span>
              {tableItem.isEnable ? (
                <Button
                  type="primary"
                  className={styles['button']}
                  onClick={() => handleAbort(tableItem, index)}
                >
                  中止
                </Button>
              ) : (
                <Button
                  type="primary"
                  className={styles['button']}
                  onClick={() => handleEnable(tableItem, index)}
                >
                  启用
                </Button>
              )}
            </span>
          )}
        </div>
      );
    },
  },
];
