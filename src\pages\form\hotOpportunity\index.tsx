import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Table, Button, Popconfirm, message, Modal, Form, Input, DatePicker } from 'antd';
import moment from 'moment';
import ModalContent from './components/ModalContent';
import AiGenerateModal from './components/AiGenerateModal';
import api from 'api';
import { PageStatus, statusMap, EditType } from './consts';
import { getOperator, extractAiParamsFromRecord, createSubmitDebounce } from './utils';
import { OpportunityItem, QueryParams, PaginationResponse, ApiResponse, AiGenerateParams } from './types';
import { env } from 'config';

const { getETFHotOpportunity, editETFHotOpportunity, deleteETFHotOpportunity, changeOnlineStatus } = api;

const HotOpportunity: React.FC = () => {
  const [dataSource, setDataSource] = useState<OpportunityItem[]>([]);
  const [edit, setEdit] = useState<number>(EditType.ADD); // 0-新增 1-编辑 2-复制
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [currentData, setCurrentData] = useState<OpportunityItem | {}>({});
  const [queryParams, setQueryParams] = useState<QueryParams>({});
  const [queryForm, setQueryForm] = useState<QueryParams>({});
  const [loading, setLoading] = useState<boolean>(false);
  
  // AI生成弹窗状态管理
  const [showAiDialog, setShowAiDialog] = useState<boolean>(false);
  
  // AI生成初始数据状态（用于参数回填）
  const [aiInitialData, setAiInitialData] = useState<any>(undefined);
  
  // 用于跟踪是否是初始化加载
  const isInitialLoad = useRef(true);
  
  // 分页状态管理
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => 
      `共 ${total} 条记录，显示第 ${range[0]}-${range[1]} 条`,
    pageSizeOptions: ['10', '20', '50', '100'],
  });

  // 创建防抖提交函数
  const debouncedSingleSubmit = useRef(
    createSubmitDebounce(async (formData: OpportunityItem) => {
      return await editETFHotOpportunity(formData);
    }, 1000)
  ).current;

  // 创建AI提交防抖函数
  const debouncedAiSubmit = useRef(
    createSubmitDebounce(async (data: AiGenerateParams) => {
      return await editETFHotOpportunity(data);
    }, 1000)
  ).current;

  // 创建删除操作防抖函数
  const debouncedDelete = useRef(
    createSubmitDebounce(async (configId: number) => {
      return await deleteETFHotOpportunity({}, configId);
    }, 800)
  ).current;

  // 创建状态变更防抖函数
  const debouncedStatusChange = useRef(
    createSubmitDebounce(async (params: { configId: number; status: number; publish: boolean; operator: string }) => {
      return await changeOnlineStatus(params);
    }, 800)
  ).current;

  // 处理页码变化
  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    setPagination(prevPagination => ({
      ...prevPagination,
      current: page,
      pageSize: pageSize || prevPagination.pageSize,
    }));
  }, []);

  // 处理每页条数变化
  const handlePageSizeChange = useCallback((current: number, size: number) => {
    setPagination(prevPagination => ({
      ...prevPagination,
      current: 1,
      pageSize: size,
    }));
  }, []);

  // 初始化数据
  useEffect(() => {
    getList();
    isInitialLoad.current = false;
  }, []);

  // 监听分页变化，重新获取数据（使用最新的查询参数）
  useEffect(() => {
    if (!isInitialLoad.current) {
      getList(queryParams, { pageNo: pagination.current, pageSize: pagination.pageSize });
    }
  }, [pagination.current, pagination.pageSize, queryParams]);

  const getList = async (searchParams?: QueryParams, paginationParams?: { pageNo?: number; pageSize?: number }) => {
    setLoading(true);
    const params = {
      group: '1',
      pageNo: paginationParams?.pageNo || pagination.current,
      pageSize: paginationParams?.pageSize || pagination.pageSize,
      ...searchParams,
    };
    try {
      const res: ApiResponse<PaginationResponse<OpportunityItem>> = await getETFHotOpportunity(params);
      if (res.status_code === 0) {
        setDataSource(res.data.list);
        setPagination(prevPagination => ({
          ...prevPagination,
          total: res.data.total,
        }));
      } else {
        message.error(res.status_msg);
      }
    } catch (error: any) {
      message.error(error?.message || '获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除操作
  const handleDelete = async (record: OpportunityItem) => {
    try {
      const result = await debouncedDelete.submit(record.configId!);
      
      if (result.success && result.data?.status_code === 0) {
        message.success('删除成功');
        // 删除后重新获取当前页数据
        getList(queryParams, { pageNo: pagination.current, pageSize: pagination.pageSize });
      } else {
        if (result.error !== '请勿重复提交') {
          const errorMsg = result.data?.status_msg || result.error?.message || '删除失败';
          message.error(errorMsg);
        }
      }
    } catch (error: any) {
      console.error('删除操作异常:', error);
      message.error(error?.message || '删除失败');
    }
  };

  // 编辑操作
  const handleEdit = (record: OpportunityItem, index: number) => {
    setCurrentData(record);
    setEdit(EditType.EDIT);
    setShowDialog(true);
  };

  // 新增操作
  const handleAdd = () => {
    setCurrentData({} as OpportunityItem);
    setEdit(EditType.ADD);
    setShowDialog(true);
  };

  // 处理表单提交
  const handleSingleData = async (formData: OpportunityItem) => {
    try {
      const newData: OpportunityItem = {
        ...formData,
      };
      
      const result = await debouncedSingleSubmit.submit(newData);

      if (result.success && result.data?.status_code === 0) {
        message.success('保存成功');
        // 保存后重新获取当前页数据
        getList(queryParams, { pageNo: pagination.current, pageSize: pagination.pageSize });
      } else {
        if (result.error !== '请勿重复提交') {
          const errorMsg = result.data?.status_msg || result.error?.message || '提交失败';
          message.error(errorMsg);
        }
      }
    } catch (error: any) {
      console.error('表单提交异常:', error);
      message.error(error?.message || '提交失败');
    }

    setShowDialog(false);
  };

  // 关闭弹窗
  const onEditClose = () => {
    setShowDialog(false);
  };

  // 查询操作
  const handleSearch = () => {
    const searchParams: QueryParams = {};
    
    // 处理日期参数，转换为yyyymmdd格式
    if (queryForm.date && queryForm.date.trim()) {
      const formattedDate = moment(queryForm.date).format('YYYYMMDD');
      searchParams.date = formattedDate;
    }
    
    // 处理页面名称参数
    if (queryForm.name && queryForm.name.trim()) {
      searchParams.name = queryForm.name.trim();
    }
    
    // 处理创建人参数
    if (queryForm.operator && queryForm.operator.trim()) {
      searchParams.operator = queryForm.operator.trim();
    }
    
    // 先更新查询参数
    setQueryParams(searchParams);
    
    // 然后重置分页到第一页，useEffect会自动触发数据获取
    setPagination(prevPagination => ({
      ...prevPagination,
      current: 1,
    }));
  };

  // 重置查询
  const handleReset = () => {
    setQueryForm({});
    
    // 先清空查询参数
    setQueryParams({});
    
    // 然后重置分页到初始状态，useEffect会自动触发数据获取
    setPagination(prevPagination => ({
      ...prevPagination,
      current: 1,
    }));
  };

  // 变更上下线状态
  const handleOnlineStatus = async (record: OpportunityItem, status: number) => {
    try {
      const params = {
        configId: record.configId!,
        status,
        publish: status === PageStatus.ONLINE ? true : false,
        operator: getOperator(),
      };
      
      const result = await debouncedStatusChange.submit(params);
      
      if (result.success && result.data?.status_code === 0) {
        message.success('变更成功');
        // 状态变更后重新获取当前页数据
        getList(queryParams, { pageNo: pagination.current, pageSize: pagination.pageSize });
      } else {
        if (result.error !== '请勿重复提交') {
          const errorMsg = result.data?.status_msg || result.error?.message || '状态变更失败';
          message.error(errorMsg);
        }
      }
    } catch (error: any) {
      console.error('状态变更异常:', error);
      message.error(error?.message || '状态变更失败');
    }
  };

  // 生成页面地址
  const generatePageUrl = (record: OpportunityItem, fundType: string): string => {
    // 当前使用configId作为标识符，实际地址规则需要根据业务需求确定
    if (record.configId) {
      return `https://${
        env === 'dev' ? 'test' : ''
      }fund.10jqka.com.cn/thsjj-jj-fe-hot-opportunity/index.html?type=${fundType}&id=${
        record.configId
      }`;
    }
    return '地址生成失败：缺少配置ID';
  };

  // 查看地址
  const handleView = (record: OpportunityItem) => {
    try {
      const etfUrl = generatePageUrl(record, 'etf');
      const fundUrl = generatePageUrl(record, 'fund');
      Modal.info({
        title: '页面地址',
        content: (
          <div>
            <p>页面名称：{record.name}</p>
            <p>ETF地址：</p>
            <div style={{ 
              background: '#f5f5f5', 
              padding: '8px 12px', 
              borderRadius: '4px',
              marginTop: '8px',
              wordBreak: 'break-all',
              userSelect: 'all'
            }}>
              {etfUrl}
            </div>
            <p>场外基金地址：</p>
            <div style={{ 
              background: '#f5f5f5', 
              padding: '8px 12px', 
              borderRadius: '4px',
              marginTop: '8px',
              wordBreak: 'break-all',
              userSelect: 'all'
            }}>
              {fundUrl}
            </div>
            <p style={{ marginTop: '8px', color: '#999', fontSize: '12px' }}>
              提示：可以选中地址进行复制
            </p>
          </div>
        ),
        width: 600,
        okText: '确定',
      });
    } catch (error) {
      console.error('查看地址失败:', error);
      message.error('查看地址失败，请重试');
    }
  };

  // 复制
  const handleCopy = (record: OpportunityItem, index: number) => {
    try {
      // 深拷贝原始数据，避免修改原始对象
      const copiedData = JSON.parse(JSON.stringify(record));
      
      // 移除configId，让后端将其作为新增处理
      delete copiedData.configId;
      
      // 更新操作人为当前用户
      copiedData.operator = getOperator();
      
      // 设置当前时间戳
      copiedData.timestamp = new Date().toISOString();
      
      // 为页面名称添加"_复制"后缀，便于区分
      copiedData.name = `${copiedData.name}_复制`;
      
      // 设置为复制模式并打开编辑弹窗
      setCurrentData(copiedData);
      setEdit(EditType.COPY);
      setShowDialog(true);
    } catch (error: any) {
      console.error('复制操作失败:', error);
      message.error(error?.message || '复制失败，请重试');
    }
  };

  // AI生成
  const handleAiGenerate = (record?: OpportunityItem) => {
    if (record) {
      // 重新生成：从失败记录中提取参数并回填
      const extractedParams = extractAiParamsFromRecord(record);
      setAiInitialData(extractedParams);
    } else {
      // 新增生成：清空初始数据
      setAiInitialData(undefined);
    }
    setShowAiDialog(true);
  };

  // 关闭AI生成弹窗
  const handleAiClose = () => {
    setShowAiDialog(false);
    setAiInitialData(undefined);
  };

  // AI生成提交处理
  const handleAiSubmit = async (data: AiGenerateParams) => {
    try {
      const result = await debouncedAiSubmit.submit(data);

      if (result.success && result.data?.status_code === 0) {
        message.success('AI生成请求已提交，请稍候查看结果');
        // AI生成后重新获取当前页数据
        getList(queryParams, { pageNo: pagination.current, pageSize: pagination.pageSize });
      } else {
        if (result.error !== '请勿重复提交') {
          const errorMsg = result.data?.status_msg || result.error?.message || 'AI生成提交失败';
          message.error(errorMsg);
        }
      }
    } catch (error: any) {
      console.error('AI生成提交异常:', error);
      message.error(error?.message || 'AI生成提交失败');
    }

    setShowAiDialog(false);
    setAiInitialData(undefined);
  };

  // 操作列渲染
  const renderOperate = (text: string, record: OpportunityItem, index: number) => {
    return (
      <div>
        {record.status !== PageStatus.AI_GENERATING && (
          <Button type="link" onClick={() => handleEdit(record, index)}>
            编辑
          </Button>
        )}
        {record.status === PageStatus.AI_FAILED && (
          <Button type="link" style={{ marginRight: 8 }} onClick={() => handleAiGenerate(record)}>
            重新生成
          </Button>
        )}
        {record.status !== PageStatus.AI_GENERATING && (
          <Popconfirm
            title="是否确认复制该配置？"
            okText="确认"
            cancelText="取消"
            onConfirm={() => handleCopy(record, index)}
          >
            <Button type="link">复制</Button>
          </Popconfirm>
        )}
        {record.status !== PageStatus.AI_GENERATING && (
          <Popconfirm
            title="是否确认删除？"
            okText="确认"
            cancelText="取消"
            onConfirm={() => handleDelete(record)}
          >
            <Button type="link" style={{ color: '#ff0000' }}>
              删除
            </Button>
          </Popconfirm>
        )}
        {(record.status === PageStatus.COMPLETE || record.status === PageStatus.OFFLINE) && (
          <Popconfirm
            title="是否确认上线？"
            okText="确认"
            cancelText="取消"
            onConfirm={() => handleOnlineStatus(record, PageStatus.ONLINE)}
          >
            <Button type="link">上线</Button>
          </Popconfirm>
        )}
        {record.status === PageStatus.ONLINE && (
          <Popconfirm
            title="是否确认下线？"
            okText="确认"
            cancelText="取消"
            onConfirm={() => handleOnlineStatus(record, PageStatus.OFFLINE)}
          >
            <Button type="link" style={{ color: '#ff0000' }}>
              下线
            </Button>
          </Popconfirm>
        )}
        {record.status === PageStatus.ONLINE && (
          <Button type="link" onClick={() => handleView(record)}>
            查看地址
          </Button>
        )}
      </div>
    );
  };

  // 状态渲染
  const renderStatus = (status: number) => {
    return statusMap[status] || '未知状态';
  };

  // 表格列配置
  const columns = [
    {
      title: '页面名称',
      dataIndex: 'name',
      key: 'name',
      width: '15%',
    },
    {
      title: '概念板块',
      dataIndex: 'data.conceptBlock.name',
      key: 'data.conceptBlock.name',
      width: '20%',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      render: renderStatus,
    },
    {
      title: '创建时间',
      dataIndex: 'ctime',
      key: 'ctime',
      width: '20%',
    },
    {
      title: '编辑时间',
      dataIndex: 'mtime',
      key: 'mtime',
      width: '20%',
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: '10%',
    },
    {
      title: '操作',
      key: 'operate',
      width: '10%',
      render: renderOperate,
    },
  ];

  return (
    <div>
      {/* 查询表单 */}
      <div
        style={{ marginBottom: 16, padding: '16px', background: '#fafafa', borderRadius: '6px' }}
      >
        <Form layout="inline">
          <Form.Item label="创建日期">
            <DatePicker
              placeholder="请选择创建日期"
              value={queryForm.date ? moment(queryForm.date) : undefined}
              onChange={(date, dateString) => {
                setQueryForm({
                  ...queryForm,
                  date: dateString || undefined,
                });
              }}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item label="页面名称">
            <Input
              placeholder="请输入页面名称"
              value={queryForm.name}
              onChange={e => setQueryForm({ ...queryForm, name: e.target.value })}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item label="创建人">
            <Input
              placeholder="请输入创建人"
              value={queryForm.operator}
              onChange={e => setQueryForm({ ...queryForm, operator: e.target.value })}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
            <Button onClick={handleReset} style={{ marginLeft: 8 }}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </div>

      <div style={{ marginBottom: 16 }}>
        热点日历地址： https://occ.10jqka.com.cn/scene/spotopm/calendar
      </div>

      <div style={{ marginBottom: 16 }}>
        <Button type="primary" style={{ marginRight: 8 }} onClick={handleAdd}>
          新增
        </Button>
        <Button type="primary" style={{ marginRight: 8 }} onClick={() => handleAiGenerate()}>
          AI生成
        </Button>
      </div>

      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey="configId"
        loading={loading}
        pagination={{
          ...pagination,
          onChange: handlePageChange,
          onShowSizeChange: handlePageSizeChange,
        }}
      />

      <ModalContent
        showDialog={showDialog}
        onEditClose={onEditClose}
        currentData={currentData}
        edit={edit}
        handleSingleData={handleSingleData}
      />

      <AiGenerateModal
        showDialog={showAiDialog}
        onClose={handleAiClose}
        onSubmit={handleAiSubmit}
        initialData={aiInitialData}
      />
    </div>
  );
};

export default HotOpportunity;
