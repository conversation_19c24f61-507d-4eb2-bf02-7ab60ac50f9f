import React from 'react';
import api from 'api';
import { autobind } from 'core-decorators';
import { Button, Input, Select, Table, Popconfirm, Modal, Form, message, Upload, DatePicker } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import { timeFormat } from 'utils/utils';
import styles from './index.less';

const { Option } = Select;
const { TextArea } = Input;
const { Search } = Input;
const { MonthPicker } = DatePicker;

const columns = [
    {
        title: '推荐时间',
        dataIndex: 'ctime',
        key: 'ctime',
    },
    {
        title: '基金代码',
        dataIndex: 'fundCode',
        key: 'fundCode',
    },
    {
        title: '基金名称',
        dataIndex: 'fundName',
        key: 'fundName',
    },
    {
        title: '推荐前一周销量',
        dataIndex: 'preSalesAmt7d',
        key: 'preSalesAmt7d',
    },
    {
        title: '推荐后一周销量',
        dataIndex: 'afterSalesAmt7d',
        key: 'afterSalesAmt7d',
    },
    {
        title: '推广后平均收益',
        dataIndex: 'average',
        key: 'average',
    },
];
const fundType: any = {
    qy: '权益类基金',
    cz: '纯债类基金',
    gs: '固收+',
    hy: '行业主题',
    fg: '风格',
    zs: '指数'
}
const recommendLevel = ['一星', '二星', '三星', '四星', '五星'];
const { fetchFundRecommend, operateFundRecommend, uploadFile, getFundSaleData } = api;
interface FundRecommendationProps extends FormComponentProps {
    [propsName: string]: any
}
interface dataFundFace {
    key: number,
    num: number,
    [propName: string]: any
}
@autobind
class FundRecommendation extends React.Component<FundRecommendationProps, any> {
    constructor(props: FundRecommendationProps) {
        super(props);
        this.state = {
            init: false,
            visible: false,
            dataFund: [],
            fundTime: '',
            editFund: {},
            searchText: '',
            modalStyle: true, // true 编辑按钮 false 新增
            selectType: '',
            selectDate: '',
            dataSource: [],
            visibleSales: false
        }
    }
    
    componentDidMount() {
        this.fetchFundRecommend('');
        this.getFundSaleData();
    }

    // 推荐基金查询
    async fetchFundRecommend(query: string) {
        try {
            const { status_code, data, status_msg } = await fetchFundRecommend(null, query);
            if (status_code === 0) {
                let arr: dataFundFace[] = [];
                data && data.forEach((item: dataFundFace, index: number) => {
                    let obj: dataFundFace = { ...item };
                    let type: string = obj.fundType;
                    obj.num = index + 1;
                    obj.key = index;
                    obj.type = fundType[type] || '';
                    obj.level = recommendLevel[obj.recommendLevel - 1] || '';
                    arr.push(obj);
                });
                let time: string = timeFormat(new Date());
                this.setState({
                    init: true,
                    dataFund: arr,
                    fundTime: time
                })
            } else {
                message.error(status_msg || '查询数据失败');
            }
        } catch(e) {
            message.error(e.message);
        }
    }

    // 操作推荐基金
    async operateFundRecommend(type: string, obj: any) {
        try {
            let value = {
                id: obj.id,
                fundCode: obj.fundCode,
                fundName: obj.fundName,
                fundType: obj.fundType,
                recommendReason: obj.recommendReason,
                recommendLevel: obj.recommendLevel,
                remark: obj.remark,
                researchReportUrl: obj.researchReportUrl,
            }
            let params = {
                type,
                value: JSON.stringify(value)
            }
            const { status_code, status_msg } = await operateFundRecommend(params);
            if (status_code === 0) {
                if (['add', 'update'].includes(type)) {
                    this.setState({
                        visible: false,
                    });
                    type === 'add' ?　message.success('新增成功') : message.success('保存成功');
                } else if(type === 'delete') {
                    message.success('删除成功');
                }
                if (type === 'add') {
                    this.setState({
                        searchText: ''
                    });
                    this.fetchFundRecommend('');
                } else {
                    this.fetchFundRecommend(this.state.searchText);
                }
            } else {
                if (['add', 'update'].includes(type)) {
                    type === 'add' ?　message.error(status_msg || '新增失败') : message.error(status_msg || '保存失败');
                } else if(type === 'delete') {
                    message.error(status_msg || '删除失败');
                }
            }
        } catch(e) {
            message.error(e.message);
        }
    }
    // 上传文件
    async customRequest(options: any, id: any) {
		try {
            const file = options.file;
            const formData = new FormData();
            formData.append('id', id);
            formData.append('file', file);
            const { status_code } = await uploadFile(formData);
            if (status_code === 0) {
                message.success('上传文件成功');
                this.fetchFundRecommend(this.state.searchText);
            } else {
                message.error('上传文件失败');
            }
        }catch(e) {
            message.error(e.message);
        }
    }
    // 获取推荐基金数据
    async getFundSaleData() {
        try {
            let { selectType, selectDate = "" } = this.state;
            selectDate = selectDate ? selectDate + '-01' : selectDate;
            const { status_code, data, status_msg } = await getFundSaleData(null, selectDate, selectType);
            if (status_code === 0) {
                data && data.forEach((item: any, index: number) => {
                    item.key = index;
                    item.average = (item.afterSalesAmt7d !== undefined && item.afterSalesAmt7d !== null) ? (item.afterSalesAmt7d / 7).toFixed(2) : '';
                });
                this.setState({
                    init: true,
                    dataSource: [...data]
                })
            } else {
                message.error(status_msg || '查询数据失败');
            }
        }catch(e) {
            message.error(e.message);
        }
    }
    // 下载
    downLoad(url: string, name: string) {
        if (!url) {
            return
        }
        let a = document.createElement("a");// 创建a标签
        if ('download' in a) {
            let str = url.substring(url.lastIndexOf('.') + 1);
            if (['doc', 'pdf', 'docx'].includes(str)) {
                a.download = name + '.' + str;// 设置下载文件的文件名
            }
        }
        (document.body || document.documentElement).appendChild(a);
        a.href = url;// downUrl为后台返回的下载地址
        a.target = '_parent';
        a.click();// 设置点击事件
        a.remove(); // 移除a标签
    }
    // 搜索
    onSearch(value: any) {
        this.setState({
            searchText: value
        });
        this.fetchFundRecommend(value);
    }
    searchChange(e: React.ChangeEvent<HTMLInputElement>) {
        this.setState({
            searchText: e && e.target && e.target.value
        })
    }
    handleDelete(record: any) {
        let obj = {
            id: record.id
        }
        this.operateFundRecommend('delete', {...obj});
    }
    // 增加单元格
    handleAdd() {
        const that = this;
        this.setState({
            modalStyle: false,
            editFund: {
                fundCode: '',
                fundName: '',
                fundType: '',
                recommendReason: '',
                recommendLevel: '',
                remark: ''
            },
            visible: true,
        })
    }
    // 编辑
    handleEdit(record: any) {
        record.average = (record.afterSalesAmt7d !== undefined && record.afterSalesAmt7d !== null) ? (record.afterSalesAmt7d / 7).toFixed(2) : '';
        this.setState({
            visible: true,
            modalStyle: true,
            editFund: {...record}
        });
    }
    handleSales(record: any) {
        record.average = (record.afterSalesAmt7d !== undefined && record.afterSalesAmt7d !== null) ? (record.afterSalesAmt7d / 7).toFixed(2) : '';
        this.setState({
            visibleSales: true,
            editFund: {...record}
        });
    }
    handleSalesCancel() {
        this.setState({
            visibleSales: false,
        });
    }
    handleSubmit() {
        let { modalStyle, editFund } = this.state;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                editFund = {...editFund, ...values};
                let type:string = modalStyle ? 'update' : 'add';
                this.operateFundRecommend(type, editFund);
            }
        });
    }
    handleCancel() {
        this.setState({
            editFund: {
                fundCode: '',
                fundName: '',
                fundType: '',
                recommendReason: '',
                recommendLevel: '',
                remark: ''
            },
            visible: false,
        });
    }
    handleTableChange(pagination: any, filters: any) {
        this.setState({
            searchText: ''
        })
        let str = '';
        if (filters.type) {
            if (filters.type.length > 0) {
                str = (filters.type).toString();
            }
        }
        this.fetchFundRecommend(str);
    };
    dateChange(date: any, dateString: string) {
        this.setState({
            selectDate: dateString
        }, () => {
            this.getFundSaleData();
        });
    }
    handleSelectChange(value: string) {
        this.setState({
            selectType: value
        }, () => {
            this.getFundSaleData();
        });
    }
    render () {
        const columnsFund = [
            {
                title: '序号',
                dataIndex: 'num',
                key: 'num',
            },
            {
                title: '基金代码',
                dataIndex: 'fundCode',
                key: 'fundCode',
            },
            {
                title: '基金名称',
                dataIndex: 'fundName',
                key: 'fundName',
            },
            {
                title: '基金类别',
                dataIndex: 'type',
                key: 'type',
                filters: [
                    { text: '权益类基金', value: '权益类基金' }, 
                    { text: '纯债类基金', value: '纯债类基金' },
                    { text: '固收+', value: '固收+' },
                    { text: '行业主题', value: '行业主题' },
                    { text: '风格', value: '风格' },
                    { text: '指数', value: '指数' },
                ],
                filterMultiple: false
            },
            {
                title: '推荐理由',
                dataIndex: 'recommendReason',
                key: 'recommendReason',
            },
            {
                title: '推荐评级',
                dataIndex: 'level',
                key: 'level',
            },
            {
                title: '备注',
                dataIndex: 'remark',
                key: 'remark',
            },
            {
                title: '研报上传',
                dataIndex: 'researchReportUrl',
                key: 'researchReportUrl',
                render: (text: any, record: any) => {
                    return <>
                        <div style={{display: 'inline-block', marginRight: 10}}>
                            <Upload
                                accept=".pdf,.doc,.docx"
                                customRequest={(options) => this.customRequest(options, record.id)}
                                showUploadList={false}
                            >
                                <Button type="primary" size="small">{record.researchReportUrl ? '重新上传' : '上传'}</Button> 
                            </Upload>
                        </div>
                        <Button type="primary" size="small" disabled={record.researchReportUrl ? false : true} onClick={() => this.downLoad(record.researchReportUrl, record.fundName)}>下载</Button>
                    </>
                }
            },
            {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                render: (text: any, record: any) => <>
                    <Button type="primary" style={{marginRight: 10}} size="small" onClick={() => this.handleEdit(record)}>编辑</Button>
                    <Popconfirm title="是否删除该基金？" onConfirm={() => this.handleDelete(record)}>
                      <Button type="primary" size="small" style={{marginRight: 10}}>删除</Button>
                    </Popconfirm>
                    <Button type="primary" size="small" onClick={() => this.handleSales(record)}>销量</Button>
                </>
            },
        ];
        const { getFieldDecorator } = this.props.form;
        const formItemLayout = {
            labelCol: { span: 6 },
            wrapperCol: { span: 16 },
        };
        const formItemLayout1 = {
            labelCol: { span: 10 },
            wrapperCol: { span: 12 },
        };
        const { visible, visibleSales, editFund, modalStyle, searchText, selectType, selectDate, dataSource } = this.state;
        if (!this.state.init) return '加载中';
        return (
            <div className={styles['fund-recommendation']}>
                <header className={styles['fund-header']}>
                    <div className={styles['fund-header-left']}>
                        <span>推荐评级：</span>
                        <span>五星=强烈推荐</span>
                        <span>四星=适当推荐</span>
                        <span>三星=谨慎推荐</span>
                        <span>二星，一星=不推荐</span>
                    </div>
                    <h1 className={styles['fund-header-middle']}>基金推荐</h1>
                </header>
                <div className={styles['f-center']} style={{marginBottom: '20px'}}>
                    <Search 
                        placeholder="基金代码，基金名称，基金类别" 
                        value={searchText} 
                        onSearch={this.onSearch}
                        onChange={this.searchChange}
                        enterButton="搜索" 
                        style={{width: 320}}
                        allowClear
                    />
                    <Button onClick={this.handleAdd} type="primary" >
                        增加
                    </Button>
                    <div>
                        最新更新时间 <span>{this.state.fundTime}</span>
                    </div>
                </div>
                <Table 
                    dataSource={this.state.dataFund} 
                    columns={columnsFund} 
                    style={{margin: '20px 0'}} 
                    onChange={this.handleTableChange}
                />
                <section>
                    <div className={styles['remark']}>
                        <span>备注</span>
                        <span>此处为推荐逻辑：</span>
                        <span>第一步 基金初始池</span>
                        <span>第二步 量化精选池</span>
                        <span>第三步 专家评级池</span>
                    </div>
                </section>
                <section className={styles['f-center']} style={{margin: '20px 0 10px 0'}}>
                    <Select value={selectType} style={{width: 120}} onChange={this.handleSelectChange}>
                        <Option value="">基金类型</Option>
                        <Option value="qy">权益类基金</Option>
                        <Option value="cz">纯债类基金</Option>
                        <Option value="gs">固收+</Option>
                        <Option value="hy">行业主题</Option>
                        <Option value="fg">风格</Option>
                        <Option value="zs">指数</Option>
                    </Select>
                    <h3>推荐结果</h3>
                    <div>
                        <MonthPicker onChange={this.dateChange}/>
                    </div>
                </section>
                <Table dataSource={dataSource} columns={columns}/>
                <Modal
                    title={modalStyle ? "编辑基金内容" : "新增基金"}
                    visible={visible}
                    footer={null}
                    onCancel={this.handleCancel}
                    wrapClassName="recommendation-wrapper"
                    destroyOnClose
                    >
                    <Form layout="horizontal" {...formItemLayout}>
                        <Form.Item label="基金代码" wrapperCol={{span: 5}}>
                            {getFieldDecorator("fundCode", {
                                initialValue: editFund.fundCode,
                                rules: [{ required: true, message: "请输入基金代码" }],
                            })(<Input />)}
                        </Form.Item>
                        <Form.Item label="基金名称" >
                            {getFieldDecorator("fundName", {
                                initialValue: editFund.fundName,
                                rules: [{ required: true, message: "请输入基金名称" }],
                            })(<Input />)}
                        </Form.Item>
                        <Form.Item label="基金类别" wrapperCol={{span: 6}}>
                            {getFieldDecorator("fundType", {
                                initialValue: editFund.fundType,
                            })(<Select >
                                <Option value="qy">权益类基金</Option>
                                <Option value="cz">纯债类基金</Option>
                                <Option value="gs">固收+</Option>
                                <Option value="hy">行业主题</Option>
                                <Option value="fg">风格</Option>
                                <Option value="zs">指数</Option>
                            </Select>)}
                        </Form.Item>
                        <Form.Item label="推荐理由">
                            {getFieldDecorator("recommendReason", {
                                initialValue: editFund.recommendReason,
                            })(<TextArea style={{height: 80}}></TextArea>)}
                        </Form.Item>
                        <Form.Item label="推荐评级" wrapperCol={{span: 4}}>
                            {getFieldDecorator("recommendLevel", {
                                initialValue: editFund.recommendLevel,
                            })(<Select>
                                <Option value={5}>五星</Option>
                                <Option value={4}>四星</Option>
                                <Option value={3}>三星</Option>
                                <Option value={2}>二星</Option>
                                <Option value={1}>一星</Option>
                              </Select>)}
                        </Form.Item>
                        <Form.Item label="备注">
                            {getFieldDecorator("remark", {
                                initialValue: editFund.remark,
                            })(<TextArea style={{height: 80}}></TextArea>)}
                        </Form.Item>
                        <Form.Item wrapperCol={{span: 24}} style={{textAlign: 'center'}}>
                            <Button onClick={this.handleCancel} style={{marginRight: 20}}>取消</Button>
                            <Popconfirm title={`是否${modalStyle ? "保存" : "新增"}该基金？`} onConfirm={() => this.handleSubmit()}>
                                <Button type="primary" htmlType="submit">{modalStyle ? "保存" : "新增"}</Button>
                            </Popconfirm>
                            
                        </Form.Item>
                    </Form>
                </Modal>
                <Modal
                    title={`${editFund.fundName}销量`}
                    visible={visibleSales}
                    footer={null}
                    onCancel={this.handleSalesCancel}
                    wrapClassName="recommendation-wrapper"
                    destroyOnClose
                    >
                    <Form layout="horizontal" {...formItemLayout1}>
                        <Form.Item label="推荐前一周销量" wrapperCol={{span: 8}}>
                            <Input readOnly value={editFund.preSalesAmt7d}/>
                        </Form.Item>
                        <Form.Item label="推荐后一周销量" wrapperCol={{span: 8}}>
                            <Input readOnly value={editFund.afterSalesAmt7d}/>
                        </Form.Item>
                        <Form.Item label="推广后平均收益" wrapperCol={{span: 8}}>
                            <Input readOnly value={editFund.average}/>
                        </Form.Item>
                    </Form>
                </Modal>
            </div>
        )
    }
    
}

export default Form.create({ name: 'fundRecommendation' })(FundRecommendation)