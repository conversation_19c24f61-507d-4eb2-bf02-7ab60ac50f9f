/*
*接口配置
* <AUTHOR>
* @time 2019.12
*/

import React, { Fragment } from 'react';
import {Button, Card, Row, message, Popconfirm, Collapse, DatePicker, Select, Input} from 'antd';
import api from 'api';
import { autobind } from 'core-decorators';
import ReactDOM from 'react-dom';
import FormRender from 'form-render/lib/antd';

const { fetchSpringFestival, fetchNewBoy, fetchNewBoyInit, postNewBoy, postSeniorUser, fetchSeniorUser, fetchSeniorUserDev, fetchLargeTransferDev, fetch818Number1, post818Number1, fetchFundAwardRegister } = api;
const schema = {
    "propsSchema": {
        "type": "object",
        "properties": {
            "name": {
                "title": "活动名称",
                "type": "string"
            },
            "ticketName": {
                "title": "副标题",
                "type": "string",
                "description": "例：百信月薪宝 2天体验金"
            },
            "prodId": {
                "title": "产品代码",
                "type": "string"
            },
            "prodName": {
                "title": "产品名称",
                "type": "string"
            },
            "awardShare": {
                "title": "奖励金额",
                "type": "number"
            },
            "awardRate": {
                "title": "奖励利率",
                "type": "number",
                "description": "单位：小数 如：2%为0.02"
            },
            "awardDate": {
                "title": "奖励天数",
                "type": "number"
            },
            "maxActivityAward": {
                "title": "活动最大奖励金额",
                "type": "number"
            },
            "startDate": {
                "title": "活动开始时间",
                "type": "string",
                "format": "dateTime"
            },
            "endDate": {
                "title": "活动结束时间",
                "type": "string",
                "format": "dateTime"
            },
            "activityState": {
                "title": "活动状态",
                "type": "number",
                "description": "0 - 正常"
            }
        }
    },
    "formData": {
        "name": "",
        "ticketName": "",
        "prodId": "",
        "prodName": "",
        "awardShare": "",
        "awardRate": "",
        "awardDate": "",
        "maxActivityAward": "",
        "startDate": "",
        "endDate": "",
        "activityState": ""
    },
    "seniorUserSchema": {
        "type": "object",
        "properties": {
            "aibProdId": {
              "title": "百信产品Id",
              "type": "string"
            },
            "aibProdName": {
              "title": "百信产品名称",
              "type": "string"
            },
            "aibOperator": {
              "title": "百信渠道标识",
              "type": "string"
            },
            "paProdId": {
              "title": "平安产品Id",
              "type": "string"
            },
            "paProdName": {
              "title": "平安产品名称",
              "type": "string"
            },
            "paOperator": {
              "title": "平安渠道标识",
              "type": "string"
            },
            "startDate": {
                "title": "活动开始时间",
                "type": "string",
                "format": "dateTime"
            },
            "endDate": {
                "title": "活动结束时间",
                "type": "string",
                "format": "dateTime"
            },
            "awardDate": {
              "title": "奖励天数",
              "type": "string"
            },
            "maxPersonAward": {
              "title": "活动最大个人奖励金额",
              "type": "string"
            },
            "maxActivityAward": {
              "title": "活动最大奖励金额",
              "type": "string"
            },
            "awardThreshold": {
                "title": "预值",
                "type": "string"
              },
            "activityState": {
              "title": "活动状态 0-正常 3-提前结束（活动总资金达到上限）",
              "type": "string"
            }
          }
    },
    "seniorUserData": {
        "aibProdId": "",
        "aibProdName": "",
        "aibOperator": "",
        "paProdId": "",
        "paProdName": "",
        "paOperator": "",
        "startDate": "",
        "endDate": "",
        "awardDate": "",
        "maxPersonAward": "",
        "maxActivityAward": "",
        "activityState": "",
        "awardThreshold": "",
    }
};


@autobind
class codeInterface extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            newBoyData: schema.formData || {},
            seniorUserData: schema.seniorUserData || {},
            largeTransferStartTime: '',
            largeTransferEndTime: '',
            activityStartTime: '',
            activityEndTime: '',
            activityId: '', //活动ID

            isShow818: false, //是否展示818
            name818: '', //818第一名名字
        };
    }

    downloadSpringFestival () {
        fetchSpringFestival({
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '春节加息活动' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    downloadNewBoy () {
        fetchNewBoy({
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '新手专区体验金' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    downloadSeniorUser () {
        fetchSeniorUserDev({
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '高净值用户拉新活动' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    downloadLargeTransfer () {
        if (!this.state.largeTransferStartTime || !this.state.largeTransferEndTime) {
            message.error('请填写时间');
            return;
        }
        fetchLargeTransferDev({
            startDate: this.state.largeTransferStartTime,
            endDate: this.state.largeTransferEndTime,
            responseType: 'blob',
        }).then(res => {
            let url = window.URL.createObjectURL(new Blob([res.data]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", '使用大额转账功能刮红包' + ".csv");

            document.body.appendChild(link);

            link.click();
        })
    }

    downLoadFundActivity () {
        if (!this.state.activityId) {
            message.error('请填写活动ID');
            return;
        }
        fetchFundAwardRegister({},
            this.state.activityId,
            `&startDate=${this.state.activityStartTime}&endDate=${this.state.activityEndTime}`,
            {
            responseType: 'blob'
        })
    }

    newBoyDataInit () {
        fetchNewBoyInit({
            type: 'query'
        }).then(data => {
            this.setState({newBoyData: data.data});
        })
    }

    newBoyUpload () {
        let _newBoyData = this.state.newBoyData;
        console.log(_newBoyData)
        postNewBoy ({
            type: 'init',
            value: JSON.stringify(_newBoyData)
        }).then(data => {
            if (data.code === '0000') {
                message.success('修改成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    onChange = newBoyData => {
        this.setState({ newBoyData }, () => {console.log(this.state)});
    }

    seniorUserDataInit () {
        fetchSeniorUser({
            type: 'query'
        }).then(data => {
            console.log(data)
            this.setState({seniorUserData: data.data});
        })
    }

    seniorUserUpload () {
        let _seniorUserData = this.state.seniorUserData;
        console.log(_seniorUserData)
        postSeniorUser ({
            type: 'init',
            value: JSON.stringify(_seniorUserData)
        }).then(data => {
            if (data.code === '0000') {
                message.success('修改成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    onchangeDownLoadTime (value, type = "normal") {
        // largeTransferStartTime
        function getTime(Date) {
            let _year = Date.getFullYear();
            let _month = Date.getMonth() + 1;
            _month = _month < 10 ? '0' + _month : _month;
            let _day = Date.getDate();
            _day = _day < 10 ? '0' + _day : _day;
            let _hour = Date.getHours();
            _hour = _hour < 10 ? '0' + _hour : _hour;
            let _minute = Date.getMinutes();
            _minute = _minute < 10 ? '0' + _minute : _minute;
            let _second = Date.getSeconds();
            _second = _second < 10 ? '0' + _second : _second;
            return `${_year}-${_month}-${_day} ${_hour}:${_minute}:${_second}`;
        }
        let _startTime = getTime(value[0]._d);
        
        let _endTime = getTime(value[1]._d);
        console.log(_startTime, _endTime)
        let _state = {}
        if (type === 'normal') {
            _state = {
                largeTransferStartTime: _startTime,
                largeTransferEndTime: _endTime
            }
        } else {
            _state = {
                activityStartTime: _startTime,
                activityEndTime: _endTime
            }
        }
        this.setState(_state, () => {console.log(this.state)})
    }

    onChangeSeniorUser = seniorUserData => {
        this.setState({ seniorUserData }, () => {console.log(this.state)});
    }

    init818() {
        fetch818Number1().then((res) => {
            let data = JSON.parse(res.data)
            this.setState({
                isShow818: data.isShow818,
                name818: data.name818
            })
            console.log(res)
        })
    }

    postInit818() {
        post818Number1({
            value: JSON.stringify({
                isShow818: this.state.isShow818,
                name818: this.state.name818
            })
        }).then((data) => {
            if (data.code === '0000') {
                message.success('修改成功');
                window.location.reload();
            }else {
                message.error(data.message);
            }
        })
    }

    componentDidMount () {
        this.init818()
        // this.newBoyDataInit();
    }

    render () {
        return (
            <section className="codeInterface">
                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-15563'}
                    extra={'上线时间：2020/01/20'}
                >
                    <Row>
                        <span
                        style={{fontSize: '18px'}}>春节加息活动 下载奖励列表</span>
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadSpringFestival}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-15232'}
                    extra={'上线时间：2020/02'}
                >
                    <Row>
                        <span
                        style={{fontSize: '18px'}}>新手专区体验金 下载奖励列表</span>
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadNewBoy}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-15232'}
                    extra={'上线时间：2020/02'}
                >
                    <Row>
                    <span
                        style={{fontSize: '18px'}}>高净值用户拉新活动 下载奖励列表</span>
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadSeniorUser}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FS-16461'}
                    extra={'上线时间：2020/04'}
                >
                    <Row>
                    <header>
                    <span
                        style={{fontSize: '18px'}}>使用大额转账功能刮红包 下载奖励列表</span>
                        </header>
                        <article style={{marginTop: '20px'}}>
                        <DatePicker.RangePicker showTime onChang={(value, dateString) => {console.log(value, dateString)}} onOk={(value) => {this.onchangeDownLoadTime(value)}}/>
                        </article>
                        
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downloadLargeTransfer}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Card
                    style={{width: '600px', marginTop: '10px'}}
                    title={'FW 838 基金奖励导出'}
                >
                    <Row>
                        <div style={{marginTop: '20px', marginBottom: '20px'}}>
                        <DatePicker.RangePicker showTime onChang={(value, dateString) => {console.log(value, dateString)}} onOk={(value) => {this.onchangeDownLoadTime(value, 'activity')}}/>
                        </div>
                        <div>
                            <Input 
                            addonBefore="活动Id"
                            value={this.state.activityId} 
                            onChange={(e) => {
                            this.setState({
                                activityId: e.target.value
                                }, () => {
                                    console.log(this.state.activityId)
                                    })
                                }} style={{width: 400}}></Input>
                        </div>
                        
                        <Button
                            type="primary" 
                            style={{ float: 'right' }}
                            onClick={this.downLoadFundActivity}
                        >
                            下载
                        </Button>
                    </Row>
                </Card>

                <Collapse onChange={this.newBoyDataInit}>
                    <Collapse.Panel 
                        header="FS-15232 新手专区 上线时间：2020/02" 
                        key="1">
                        <FormRender
                            propsSchema={schema.propsSchema}
                            formData={this.state.newBoyData}
                            onChange={this.onChange}
                        />

                        <Popconfirm
                            placement="leftBottom"
                            title={'你确定要提交么'}
                            onConfirm={this.newBoyUpload}
                            okText="确认"
                            cancelText="取消"
                            >

                            <Button
                                type="primary" 
                                style={{ float: 'right' }}
                            >
                                提交修改
                            </Button>

                        </Popconfirm>
                    </Collapse.Panel>
                </Collapse>

                <Collapse onChange={this.seniorUserDataInit}>
                    <Collapse.Panel 
                        header="FS-15805 2月高净值用户拉新活动 上线时间：2020/02" 
                        key="1">
                        <FormRender
                        propsSchema={schema.seniorUserSchema}
                        formData={this.state.seniorUserData}
                        onChange={this.onChangeSeniorUser}
                        />

                        <Popconfirm
                            placement="leftBottom"
                            title={'你确定要提交么'}
                            onConfirm={this.seniorUserUpload}
                            okText="确认"
                            cancelText="取消"
                        >
                            <Button
                                type="primary" 
                                style={{ float: 'right' }}
                            >
                                提交修改
                            </Button>
                        </Popconfirm>
                    </Collapse.Panel>
                </Collapse>

                <section>
                    <div>
                    是否显示818第一名：
                    <Select style={{width: 200}} value={this.state.isShow818} onChange={(value) => {
                        this.setState({isShow818: value}, () => {console.log(this.state.isShow818)})
                        }}>
                        <Select.Option value={true}>显示</Select.Option>
                        <Select.Option value={false}>不显示</Select.Option>
                    </Select>
                    </div>
                    <div>
                    第一名用户名：
                    <Input value={this.state.name818} onChange={(e) => {
                        this.setState({
                            name818: e.target.value
                            }, () => {
                                console.log(this.state.name818)
                                })
                            }} style={{width: 200}}></Input>
                    <Button
                        type="primary" 
                        onClick={this.postInit818}
                    >
                        上传
                    </Button>
                    </div>
                    
                </section>

            </section>
        )
    }
}

export default codeInterface;