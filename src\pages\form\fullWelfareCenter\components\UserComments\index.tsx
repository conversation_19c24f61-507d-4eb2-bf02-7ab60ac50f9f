import React from 'react';
import { Form, Input, Radio } from 'antd';
import { RadioChangeEvent } from 'antd/lib/radio';
import DynamicCommentForm from './DynamicCommentForm';

const UserCommentsForm = props => {
  const { getFieldDecorator, showComments, setShowComments, commentNum, setCommentNum } = props;
  const handleSelectChange = (e: RadioChangeEvent) => {
    if (e.target.value === '0') {
      setShowComments(false);
    } else {
      setShowComments(true);
    }
  };
  return (
    <>
      <Form.Item label="是否展示用户精评区">
        {getFieldDecorator('comments.isShow', {
          rules: [{ required: true, message: '请选择是否展示用户精评区' }],
        })(
          <Radio.Group onChange={handleSelectChange}>
            <Radio value="1">是</Radio>
            <Radio value="0">否</Radio>
          </Radio.Group>,
        )}
      </Form.Item>
      {showComments ? (
        <>
          <Form.Item label="用户精评名称">
            {getFieldDecorator('comments.commentName', {
              rules: [{ required: true, message: '请输入用户精评名称' }],
            })(<Input />)}
          </Form.Item>
          <DynamicCommentForm getFieldDecorator={getFieldDecorator} commentNum={commentNum} setCommentNum={setCommentNum}/>
        </>
      ) : null}
    </>
  );
};

export default UserCommentsForm;
