import React, { useEffect, useState } from 'react';
import { Button, message, Collapse } from 'antd';
import api from 'api';
import styles from '../index.less';
import MyCard from './myCard';
import ConfigSelect from '../component/selectModel';
import LocationModel from '../component/locationModel';

const { fetchAccountSuccess, fetchCBAS, fetchOLAS, fetchAccountUpload } = api;
const { Panel } = Collapse;
export default function () {

    const [init, setInit] = useState(true);
    const [originData, setData] = useState<any>([]);
    const [allData, setAllData] = useState<any>({})
    const [isModify, setModify] = useState(false)
    const [config, setConfig] = useState<{ utype: string[], platform: string[] }>({ utype: [], platform: [] })
    const [kycTag, setKycTag] = useState([]);
    const [olasTag, setOlasTag] = useState([]);
    const [fileFormData, setFileFormData] = useState<any>({});
    const [activeKey, setActiveKey] = useState(0);

    const handleChange = (data: any) => {
        console.log(data)
        setConfig(data)
    }
    useEffect(() => {
        fetchCBAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    setKycTag(data)
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])
    
    useEffect(() => {
        fetchOLAS().then((res: any) => {
            const { code, data } = res;
            if (code === '0000') {
                if (data) {
                    let _olasTag: any = [];
                    for (let prop in data) {
                        _olasTag.push({ "groupid": prop, "description": data[prop].description })
                        console.log(_olasTag)
                    }
                    setOlasTag(_olasTag)
                }
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    useEffect(() => {
        fetchAccountSuccess({
            type: 'query'
        }).then((res: any) => {
            console.log(res);
            const { code, data } = res;
            if (code === '0000') {
                setInit(true);
                let _data: any = [];
                data?.confs?.forEach((val: any) => {
                    let obj = {
                        formData: {
                            buttonName: val.buttonName,
                            jumpAction: val.jumpAction,
                            version: val.version,
                            startTime: val.startTime,
                            endTime: val.endTime,
                            accountFrom:val.accountFrom
                        },
                        configData: {
                            platform: val.platform || [],
                            utype: val.utype || [],
                        },
                        relationData: {
                            targetType: val.targetType,
                            kycLogic: val.kycLogic,
                            kycs: val.kycs,
                            olasId: val.olasId,
                            blackList: val.blackList,
                            whiteList: val.whiteList
                        },
                        other: {
                            sv: val.sv,
                            updateTime: val.updateTime
                        },
                        fileExist: {
                            blackListFile: val?.blackList?.length > 0,
                            whiteListFile: val?.whiteList?.length > 0,
                        }
                    }
                    _data.push(obj)
                })
                console.log(1,_data);
                setData(_data)
                setAllData(data ?? {})
            } else {
                message.error(res?.message || '系统繁忙');
            }
        }).catch((e: Error) => {
            message.error(e?.message || '系统繁忙');
        })
    }, [])

    function onSubmit() {
        let _value: any = [];
        for (let i = 0,len = originData?.length; i < len; i++) {
            let val = originData[i];
            let _data = { ...val.other, ...val.formData, ...val.configData, ...val.relationData }
            if (!_data.buttonName) {
                message.error(`请填写第${i+1}项按钮名称`)
                return
            }
            if (!_data.jumpAction) {
                message.error(`请填写第${i+1}项跳转链接`)
                return
            }
            if (_data.platform?.length === 0) {
                message.error(`请选择第${i+1}项适用平台`)
                return
            }
            if (_data.utype?.length === 0) {
                message.error(`请选择第${i+1}项用户类型`)
                return
            }
            let temp = fileFormData[i];
            if (temp?.has('whiteListFile')) {
                _data.whiteList = ['custList']
            }
            if (temp?.has('blackListFile')) {
                _data.blackList = ['custList']
            }
            _value.push(_data)
        }
        let promiseArr: any = [];
        Object.keys(fileFormData)?.forEach(num => {
            console.log(fileFormData)
            if (fileFormData[num]?.has('whiteListFile') || fileFormData[num]?.has('blackListFile')) {
                promiseArr.push(fetchAccountUpload(fileFormData[num]));
            }
        })
        console.log(promiseArr)
        Promise.all(promiseArr).then((resArr: any) => {
            let num = 0, len = resArr?.length;
            for (let i = 0; i < len; i++) {
                const { code } = resArr[i];
                if (code === '0000') {
                    num++;
                } else {
                    message.error(resArr[i]?.message);
                    return;
                }
            }
            if (num === len) {
                let _sendData = {
                    type: 'update',
                    value: JSON.stringify({
                        type: allData?.type,
                        fv: allData?.fv,
                        index: allData?.index,
                        fvUpdateTime: allData?.fvUpdateTime,
                        confs: _value,
                    }),
                    lastEditor: localStorage.name
                }
                console.log('send', _sendData)
                fetchAccountSuccess(
                    _sendData
                ).then((res: any) => {
                    if (res.code !== '0000') {
                        message.error(res.message);
                    } else {
                        message.success('提交成功！');
                        setTimeout(() => {
                            location.href = `#/form/fundAccountSystem`
                        }, 1000);
                    }
                }).catch((e: Error) => {
                    message.error(e?.message);
                })
            }
        }).catch((e: Error) => {
            message.error(e?.message);
        })   
    }
    function addItem() {
        let obj = {
            formData: {
                buttonName: '',
                jumpAction: '',
                version: '',
                startTime: '',
                endTime: '',
                accountFrom:'',
            },
            configData: {
                platform: config.platform,
                utype: config.utype,
            },
            relationData: {
                targetType: '',
                kycLogic: '',
                kycs: [],
                olasId: [],
                blackList: null,
                whiteList: null,
            },
            other: {
                // sv: null,
                updateTime: null,
                configIndex: null,
                
            },
            fileExist: {
                blackListFile: false,
                whiteListFile: false
            },
            isNew: true
        }
        let data = [...originData, obj];
        setData(data)
        setActiveKey(data.length - 1);
    }

    function handleUpdate(data: any, index: number, obj: any, isNew: boolean = false) {
        if (!isModify && !isNew) setModify(true)
        let _originData: any = [...originData];
        _originData[index] = data;
        setData(_originData);
        if (!isNew) {
            let _fileFormData: any = {...fileFormData};
            Object.keys(obj)?.forEach(num => {
                if (obj[num]?.has('type')) {
                    _fileFormData[num] = obj[num];
                    setFileFormData(_fileFormData);
                }
            })
        }
        
    }
    function handleDelete(index: number) {
        if (!isModify) setModify(true)
        let _originData = [].concat(originData)
        _originData.splice(index, 1)
        setData(_originData)
    }
    function handleSelect(item: any) {
        let tag = 0;
        if (item.configData.platform?.length === 0 && item.configData.utype?.length === 0) {
            tag = 1;
        } else if (config.utype?.length === 0) {
            for (let data of item.configData.platform) {
                if (config.platform.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        } else if (config.platform?.length === 0) {
            for (let data of item.configData.utype) {
                if (config.utype.indexOf(data) != -1) {
                    tag = 1;
                    break;
                }
            }
        } else {
            for (let data of item.configData.platform) {
                if (config.platform.indexOf(data) != -1) {
                    for (let data of item.configData.utype) {
                        if (config.utype.indexOf(data) != -1) {
                            tag = 1;
                            break;
                        }
                    }
                    if (tag === 1) {
                        break;
                    }
                }
            }
        }
        return !!tag
    }
    function handleActiveKey (key: any) {
        setActiveKey(key);
    }
    if (!init) return '加载中';
    return <div>
        <LocationModel location='开户成功页' />
        <ConfigSelect handleChange={handleChange} isHead={true} />
        <Button type="primary" onClick={onSubmit} disabled={!isModify}>保存</Button>
        <Collapse activeKey={activeKey} onChange={handleActiveKey}>
            {
                originData.map((item: any, index: number) => {
                    return handleSelect(item) ?
                <Panel header={(<span style={{height: 22, display: 'inline-block', verticalAlign: 'middle'}}>{index+1}    {item.formData?.buttonName}</span>)} key={index}>
                    <MyCard
                        data={item}
                        position={index}
                        kycTag={kycTag}
                        olasTag={olasTag}
                        handleDelete={handleDelete}
                        handleUpdate={handleUpdate}
                        key={index}></MyCard>
                </Panel>
                : null

                })
            }
        </Collapse>
        <Button onClick={addItem} type='primary' style={{ marginTop: '20px' }}>增加</Button>
    </div>

}