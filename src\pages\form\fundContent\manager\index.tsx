import React, { useState, useEffect } from "react";
import api from 'api';
import { Button, message, Popover, Table, Row, Col, Input, Select, Modal, Switch } from 'antd'
import classNames from "classnames";

const { Option } = Select;
const { TextArea } = Input;
const { fetchManagerContent, postManagerContent, refreshManagerContent } = api;

// const tableData=[
//     {
//         fundManagerId:'123',
//         fundManagerName:'1234',
//         url:'12533'
//     }
// ]
const pageSize = 10
// 

export default function () {
    const [tableData, setData] = useState([])
    const [page, setPage] = useState<any>({ current: 1, defaultPageSize: 15, total: 0 })
    const [type, setType] = useState('0') //右上角的筛选 0-全部，1-已关联个人，2-已开通经理说
    const [sortField, setSortField] = useState('')
    const [sortOrder, setSortOrder] = useState('')
    const [searchText, setSearchText] = useState('')
    const [visible, setVisible] = useState(false)
    const [modalData, setModalData] = useState<any>({})

    useEffect(() => {
        getInfo('0', '', 1, '', '')
    }, [])
    function getInfo(_type: string, _searchText: string, _current: number, _sortField: string, _sortOrder: string) {
        const stringToSend = `?type=${_type}&search=${_searchText}&pageSize=15&pageNum=${_current}&sortField=${_sortField}&sortOrder=${_sortOrder}`
        fetchManagerContent({}, stringToSend).then((res: any) => {
            if (res.code === '0000' && res.data) {
                if(res.data.records?.length === 0 ){
                    message.error(res.message)
                }
                let _page: any = { ...page }
                _page.total = res.data.total
                _page.current = _current
                setData(res.data.records)
                setPage(_page)
            } else {
                message.error(res.message)
            }
        })
    }
    function handleTableChange(pagination: any, filters: any, sorter: any) {
        console.log(pagination, filters, sorter)
        console.log(sorter, sortField, sortOrder)
        if (!sorter.field || (sorter.field === sortField && ((sorter.order === sortOrder) || (!sorter.order && sortOrder === '')))) {
            console.log('status1')
            //仅页码改变
            getInfo(type, searchText, pagination.current, sortField, sortOrder)

        } else {
            //排序改变
            console.log('status2')
            getInfo(type, searchText, 1, sorter.field || '', sorter.order || '')
            setSortOrder(sorter.order || '')
            setSortField(sorter.field)
        }

    };
    function handleSelect(value: string) {
        setType(value)
        getInfo(value, searchText, 1, sortField, sortOrder)
    }
    function getModalData(record: any) {
        setVisible(true)
        let _ext = record.relationList ? record.relationList.map((val: any) => val.fid).join(',') : ''
        const _data = {
            id: record.id,
            fundManagerId: record.fundManagerId,
            fundManagerName: record.fundManagerName,
            ext: _ext,
            articleFlag: record.relationList ? record.articleFlag : 1
        }
        setModalData(_data)
    }
    function handleModal() {

        console.log(modalData)
        postManagerContent({ fundManagerArticleDto: JSON.stringify(modalData) }).then((res: any) => {
            if (res.code === '0000') {
                message.info('提交成功')
                setVisible(false)
                getInfo(type,searchText,page.current,sortField,sortOrder)
            } else {
                message.error(res.message)
            }
        })
    }
    function refresh() {
        refreshManagerContent().then((res: any) => {
            if (res.code = '0000') {
                message.info('刷新成功')
                getInfo(type,searchText,page.current,sortField,sortOrder)
            }
        })
    }



    let columns = [
        { title: '基金经理ID', dataIndex: 'fundManagerId', key: 'fundManagerId', sorter: true, width:'15%' },
        { title: '基金经理名称', dataIndex: 'fundManagerName', key: 'fundManagerName', sorter: true, width:'15%' },
        {
            title: '关联同顺号', dataIndex: 'relationList', key: 'relationList', width:'50%', render: (text: any, record: any) => {
                return text ? text.map((val: any) => val.name + '(' + val.fid + ')\n') : '无'
            }
        },

        {
            title: '操作', dataIndex: 'options', key: 'options', width:'20%', render: (text: any, record: any) => {
                return <div className={'u-l-middle'}>
                    <Button onClick={() => getModalData(record)}>配置</Button>
                    <span className={classNames(record.articleFlag === 0 ? '' : 'z-hide', 'g-ml20')} >未开通</span>
                    <span className={classNames(record.articleFlag === 1 ? '' : 'z-hide', 'g-ml20')} style={{ color: '#b7eb8f' }}>已开通</span>
                </div>
            }
        }
    ]
    return <div>
        <Row className={'g-mb30'}>
            <Col span={20}>
                <Input
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    style={{ width: "200px" }}
                />
                <Button onClick={() => getInfo(type, searchText, 1, sortField, sortOrder)} className={'g-ml20'}> 搜索</Button>
                <Button onClick={refresh} className={'g-ml20'}> 刷新</Button>
            </Col>
            <Col span={4}>
                <Select value={type} style={{ width: 200 }} onChange={(value: any) => handleSelect(value)}>
                    <Option value="0">全部基金经理</Option>
                    <Option value="1">已关联个人同顺号</Option>
                    <Option value="2">已开通经理说</Option>
                </Select>
            </Col>
        </Row>
        <Table
            columns={columns as any}
            dataSource={tableData}
            pagination={page}
            onChange={handleTableChange}
        />
        <Modal
            title="关联同顺号"
            visible={visible}
            onOk={handleModal}
            onCancel={() => setVisible(false)}
            okText="确认"
            cancelText="取消"
        >
            <TextArea rows={4} value={modalData.ext} onChange={(e: any) => setModalData({ ...modalData, ext: e.target.value })} style={{ marginBottom: '20px' }} />
            <span>是否开通经理说: </span>
            <Switch
                checkedChildren="开"
                unCheckedChildren="关"
                checked={modalData.articleFlag ? true : false}
                // style={{marginTop: '20px'}}
                onClick={(checked) => setModalData({ ...modalData, articleFlag: checked ? 1 : 0 })} />
        </Modal>
    </div>
}