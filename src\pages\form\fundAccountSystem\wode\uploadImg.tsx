import React from 'react';
import ImgUpload from '../component/uploadImg/index.jsx';
interface dataProps {
    onChange: Function,
    imgUrl: string,
    disabled: boolean
}

export default function (props:dataProps) {

    return <div style={{width: 514}}>
                <ImgUpload 
                        handleChange={(value: any) => props.onChange(value)}
                        imageUrl={props.imgUrl}
                        isEdit={props.disabled}
                        title=''
                    />
            </div>

}