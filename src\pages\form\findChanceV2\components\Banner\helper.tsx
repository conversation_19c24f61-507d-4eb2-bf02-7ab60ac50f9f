import { Banner } from '../../type';
import { checkRequiredAndThrow } from '../../utils';
export const mapPropToName = {
  // open: false,
  img: '活动图片',
  name: '活动名称',
  link: '跳转链接',

  startTime: '开始时间',
  endTime: '结束时间',
};
export const bannerHelper = {
  addInit: function(): Banner {
    return {
      open: false,
      img: '',
      name: '',
      link: '',

      startTime: '',
      endTime: '',
    };
  },

  checkData: function(data) {
    try {
      if (data.open) {
        checkRequiredAndThrow(data.img, mapPropToName['img']);
        checkRequiredAndThrow(data.name, mapPropToName['name']);
        checkRequiredAndThrow(data.link, mapPropToName['link']);
      }
      if (data.startTime && data.endTime && data.startTime > data.endTime) {
        throw new Error('开始时间应小于结束时间');
      }
    } catch (e) {
      throw new Error(`首屏直播卡-${e.message}`);
    }
    return true;
  },
};
