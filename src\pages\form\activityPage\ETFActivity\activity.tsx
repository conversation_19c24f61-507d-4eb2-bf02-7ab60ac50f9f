import React, {useEffect, useState} from 'react'
import FormRender from 'form-render/lib/antd';
import {But<PERSON>, Popconfirm, message, Upload} from 'antd';
import { SCHEMA1, SCHEMA2 } from './config'
import api from 'api';

interface ETFActivityItem {
    "activityId": string
    "type": string
    "cardType": string
    "activityName": string
    "startDate": string
    "endDate": string
    "status": string
    "title": string
    "subTitle": string
    "tipTitle": string
    "tipContext": string
    "recommendTitle": string
    "recommendCode": string
    "recommendName": string
    "profitDescribe": string
    "productTitle": string
    "purchaseContext": string
    "windowText": string
    "activityRules": string | any
    "codeRate": string
    "cashVoucherList": any
}

const {
    fetchETFActivity,
    uploadETFActivityFile,
    fetchETFActivityAward
} = api

export default function({
    isAdd = false,
    isShowAdd,
    activities,
    activity,
    setActivity,
    setIsShowAdd = () => {},
    handleFetchETFActivity = () => {}
}: {
    isAdd?: boolean
    isShowAdd: boolean
    activities: any
    activity?: any
    setActivity: any
    setIsShowAdd: Function
    handleFetchETFActivity: Function
}) {

    // const [formData, setData] = useState({});
    const [valid, setValid] = useState([]);
    const [file, setFile] = useState<File | null>(null)
    const [list, setList] = useState([]) //名单

    useEffect(() => {
        if (!isShowAdd) {
            setFile(null)
            setList([])
        }
    }, [isShowAdd])

    /**
     * 提交
     */
    function postConfig() {
        if (activities.filter((item) => item.activityId === activity.activityId).length > (isAdd ? 0 : 1)) return message.error('activityId重复，需为唯一')
        if (valid.length > 0) return message.error(JSON.stringify(valid))
        // 兑换码
        if (activity.type === 'level2') {
            const {imgTitle, imgDescription, popTitle,awardDescription, guideDescription, exchangeOpen, exchangeChangeConfig } = activity.exchangeCodeConfig;
            switch (true) {
                case !imgTitle:
                    return message.error('请填写获奖图片第一行文案')
                case handleStringLength(imgTitle) > 10:
                    return message.error('获奖图片第一行文案不超过10个字符，汉字算两个');
                case !imgDescription:
                    return message.error('请填写获奖图片第二行文案')
                case handleStringLength(imgDescription) > 10:
                    return message.error('获奖图片第二行文案不超过10个字符，汉字算两个');
                case !popTitle:
                    return message.error('请填写弹窗标题')
                case handleStringLength(popTitle) > 16:
                    return message.error('弹窗标题不超过16个字符，汉字算两个');
                case !awardDescription:
                    return message.error('请填写弹窗获奖文案')
                case handleStringLength(awardDescription) > 28:
                    return message.error('弹窗获奖文案不超过28个字符，汉字算两个');
                case !guideDescription:
                    return message.error('请填写弹窗引导文案')
                case handleStringLength(guideDescription) > 58:
                    return message.error('弹窗引导文案不超过58个字符，汉字算两个');
                case isAdd && !file:
                    return message.error('请先上传文件')
                default:
                    break;
            }
            // 需要兑换码转换页面
            if ( exchangeOpen === '1' ) {
                const {conductDescription, awardDescription, activityRule, buttonDescription} = exchangeChangeConfig;
                switch (true) {
                    case !conductDescription:
                        return message.error('请填写宣传文案')
                    case handleStringLength(conductDescription) > 24:
                        return message.error('宣传文案不超过24个字符，汉字算两个');
                    case !awardDescription:
                        return message.error('请填写获奖文案')
                    case handleStringLength(awardDescription) > 28:
                        return message.error('宣传文案不超过28个字符，汉字算两个');
                    case handleStringLength(buttonDescription) > 28:
                        return message.error('按钮文案不超过28个字符，汉字算两个');
                    case !activityRule:
                        return message.error('请填写活动规则')
                    default:
                        break;
                }
            }
        }
        // 现金
        if (activity.type === 'cash') {
            if (activity.cashVoucherList.length === 0) return message.error('请检查红包金额')
            let _totalRate = 0,
                _isOk = true
            activity.cashVoucherList.forEach((item: any) => {
                if (!item.money || !item.rate) {
                    _isOk = false
                    message.error('请填写全金额和概率')
                }
                if (item.rate < 0) {
                    _isOk = false
                    message.error('红包概率不能小于0')
                } 
                if (item.rate > 100){
                    _isOk = false
                    return message.error('红包概率不能大于100')
                }
                _totalRate += item.rate
            })
            if (!_isOk) return
            if (_totalRate !== 100) return message.error('请检查红包概率和是否为100')
        }
        if (activity.activityRules.length === 0) return message.error('请填写规则')
        let _form: any = {...activity}
        _form.activityRules = JSON.stringify(_form.activityRules)
        _.fundLoading()
        fetchETFActivity({
            type: 'init',
            activityId: _form.activityId,
            value: JSON.stringify(_form)
        }).then((res: any) => {
            _.hideFundLoading()
            if (res.code === '0000') {
                setIsShowAdd(false)
                message.success(isAdd ? '添加完成' : '修改完成')
                if (file) uploadFile(() => {window.location.reload(true)})
                handleFetchETFActivity();
            }
        }).catch(() => {
            _.hideFundLoading()
        })
    }

    /**
     * 计算字符串长度，汉字算两个字符
     */
    function handleStringLength(str:String) {
        let entranceLength = 0;
        for (let i = 0;i < str.length;i++) {
            if (str.charCodeAt(i) > 255) {
                entranceLength+=2;
            } else {
                entranceLength+=1;
            }
        }
        return entranceLength;
    }

    function addFile(_file: File) {
        if (file) {
            message.error('只能上传一个文件，请先删除原先文件')
            return false
        }
        setFile(_file)
        // if (!isAdd) uploadFile(_file)
        return false
    }

    /**
     * 上传文件
     * @param callback 
     */
    function uploadFile(callback?: Function) {
        let _data = new FormData()
        _data.append('file', (file as File))
        _data.append('activityId', activity.activityId)
        uploadETFActivityFile(_data).then((res: any) => {
            if (res.code === '0000') {
                message.success('文件上传成功')
                setFile(null)
                if (callback) callback()
            }
        }).catch(e => {
            message.error('文件上传失败')
        })
    }

    /**
     * 获取剩余兑换码
     */
    function fetchCodes() {
        fetchETFActivity({
            type: 'codes',
            activityId: activity.activityId
        }).then((res: any) => {
            if (res.code === '0000') {
                setList(res.data)
                if (res.data.length === 0) message.info('获取列表结果为空')
            } else {
                message.error(res.message)
            }
        })
    }

    /**
     * 下载奖励
     */
    function downloadAward() {
        fetchETFActivityAward({},
            activity.activityId,
            null,
            {responseType: 'blob'}
        ).then((data: any) => {
            console.log(data)
            if (data.data && data.data.size === 0 && data.message !== 'OK' && data.message) return message.error('下载失败')
            if (data.code !== '0000' && data.message !== 'OK' && data.message) return message.error(data.message)
        }).catch((e: Error) => {
            message.error('下载失败')
        })
    }

    function clearList() {
        fetchETFActivity({
            type: 'codesClean',
            activityId: activity.activityId
        }).then((res: any) => {
            if (res.code === '0000') {
                message.success('清除列表成功')
            } else {
                message.error(res.message)
            }
        })
    }

    let isShowUploadFileButton = (isShowAdd && activity.type === 'level2'), //是否显示上传文件按钮.
        isShowAwardDownLoadButton = (!isAdd && activity.type === 'cash'), //是否显示奖励名单下载按钮
        isShowGetListButton = (!isAdd && activity.type === 'level2') //是否显示获取名单按钮

    return (
        <div>
            <FormRender
                propsSchema={isAdd ? SCHEMA1 : SCHEMA2}
                uiSchema={{}}
                onValidate={setValid}
                formData={activity}
                onChange={setActivity}
                displayType="row"
                showDescIcon={true}
            />
            <div>
                {
                    isShowUploadFileButton
                    ?
                    <Upload 
                    beforeUpload={addFile}
                    onRemove={() => {setFile(null)}}
                    >
                        <Button type="primary" disabled={file}>上传文件(一行一个兑换码的txt文件)</Button>
                    </Upload>
                    :
                    null
                }
            </div>
            <div style={{display: isShowAwardDownLoadButton ? 'block' : 'none'}}>
                <Button type="primary" onClick={downloadAward} style={{marginTop: 10}}>奖励下载</Button>
            </div>
            <div className="u-l-middle" style={{display: isShowGetListButton ? 'block' : 'none'}}>
                <Button type="primary" onClick={fetchCodes} style={{marginTop: 10, marginRight: 20}}>获取剩余名单</Button>
                {/* <Popconfirm
                    placement="rightBottom"
                    title={'你确定要清空所有兑换码么'}
                    onConfirm={clearList}
                    okText="确认"
                    cancelText="取消"
                > */}
                    <Button type="danger" disabled={true}>清空兑换码</Button>
                {/* </Popconfirm> */}
            </div>
            <div style={{display: isShowGetListButton ? 'block' : 'none'}}>
                {
                    list.map((item: string, index: number) => (<span key={index} style={{marginRight: 20}}>{item}</span>))
                }
            </div>
            <div className="u-r-middle" style={{margin: 20}}>
                <Button type="primary" onClick={() => {setIsShowAdd(false)}} style={{marginRight: 20}}>取消</Button>
                <Popconfirm
                    placement="rightBottom"
                    title={'你确定要提交么'}
                    onConfirm={postConfig}
                    okText="确认"
                    cancelText="取消"
                >
                    <Button
                        type="danger" 
                    >
                        提交
                    </Button>
                </Popconfirm>
            </div>
        </div>
    )
}