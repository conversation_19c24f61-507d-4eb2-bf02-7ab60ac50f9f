import React, { Component } from 'react';
import { Row, Col, Radio, Input, DatePicker, Popconfirm, message, Button, Alert, Spin } from 'antd';
import api from 'api';
import styles from './style.less';
import { DraggableArea, DraggableAreasGroup} from '../../../../components/DragTags'

const {fetchKycTopV,fetchKycTop,postKycTop} = api
const group = new DraggableAreasGroup();
const DraggableArea1 = group.addArea();
const DraggableArea2 = group.addArea();
const { RangePicker } = DatePicker;
const { Search } = Input;

export default class CrossArea extends Component {
  constructor() {
    super();
    this.state = {
      initRight:[], //接口原始数据，关闭人工干预时返回
      status:'true',
      leftTags: [],  //最终展示数据
      rightTags: [],
      left: [],  //搜索框数据保存
      right: [],
      searchWord:'',
      date:[],
      loading:true
    }
  }

  componentDidMount() {

    fetchKycTopV().then((res) =>{
      this.setState({
        loading:false
      })
        if(res.code === '0000'){
          let data = JSON.parse(res.data);
          let _data = data.sortedNews;
          let _user = data.userInfoMap;
          _data.forEach((val) =>{
            if(!val.id){
              let user = _user[val.uid]
              if(user !== undefined) {
                val.id = val.pid;
                val.username = user.nickname
                val.programeName = '大V内容'
                val.title = ''
                val.avatar = user.avatar
                val.mobileUserHomePage = user.mobileUserHomePage
              }

            }
          })
          this.setState({
            initRight:_data,
            rightTags:_data,
            right:_data
          })
        }else{
         
          message.error(res.status_msg)
        }
    })
      .catch(e => console.log(e))
      

  }

  handleClickDelete(tag) {
    const rightTags = this.state.rightTags.filter(t => tag.id !== t.id);
    const right = this.state.right.filter(t => tag.id !== t.id);
    this.setState({
      rightTags,
      right
    });
  }
  handleClickUp(tag) {
    let _left = [].concat(this.state.left)
    let _right = [].concat(this.state.right)
    let _leftTags = [].concat(this.state.leftTags)
    let _rightTags = [].concat(this.state.rightTags)
    let upElement = _rightTags.find(t => tag.id === t.id);
    const rightTags = _rightTags.filter(t => tag.id !== t.id);
    const right = _right.filter(t => tag.id !== t.id);
    _leftTags.unshift(upElement);
    _left.unshift(upElement);
    this.setState({
      leftTags:_leftTags,
      left:_left,
      rightTags:rightTags,
      right:right
    })
  }
  handleClickDown(tag) {
    let _left = [].concat(this.state.left)
    let _right = [].concat(this.state.right)
    let _leftTags = [].concat(this.state.leftTags)
    let _rightTags = [].concat(this.state.rightTags)
    let downElement = _leftTags.find(t => tag.id === t.id);
    const leftTags = _leftTags.filter(t => tag.id !== t.id);
    const left = _left.filter(t => tag.id !== t.id);
    _rightTags.push(downElement);
    _right.push(downElement);
    this.setState({
      leftTags:leftTags,
      left:left,
      rightTags:_rightTags,
      right:_right
    })


  }

  changeStatus= (e) => {
    const {status} = this.state
    this.setState({
      status: e.target.value
    })
  }
  handleDate = (e) => {
    this.setState({
      date:e
    })
    console.log(e)
  }
  handeleSearch = (val) =>{
    let _left =  [].concat(this.state.left)
    let _right =  [].concat(this.state.right)

    if(val && val.length > 0){

      _left = _left.filter((item)=>{
        console.log(val,item.username,item.username.includes(val))
          return item.username.includes(val) || item.title.includes(val)
        }
      )
      _right = _right.filter((item)=>{
        console.log(val,item.username,item.username.includes(val))
          return item.username.includes(val) || item.title.includes(val)
        }
      )
    }
    console.log(_left,_right)
    this.setState({
      leftTags:_left,
      rightTags:_right,
      searchWord: val
    })
  }
  handleSave = () =>{
    //判断是否在搜索状态保存
    if(this.state.searchWord !== ''){
      message.error('请不要在搜索状态下进行保存操作，可能导致排序错误')
      return;
    }
    let len = 0;
    let _data = []
    //判断是否开启人工干预
    if(this.state.status === 'false'){
      _data = [].concat(this.state.initRight)
    }else {
      _data = [].concat(this.state.leftTags,this.state.rightTags)
    }
    let status =this.state.status
    let date = this.state.date
    // _data.date = this.state.date;

    console.log(_data)
    if(_data.length % 10 === 0){
      len = parseInt(_data.length/10)
    }else {
      len = parseInt(_data.length/10)+1
    }
    console.log('len',len)
    for(let i=0 ; i<len; i++){
      let dataSend = {}
      dataSend.status = status;
      dataSend.date = date;
      if(i<len){
        dataSend.data = _data.slice(i*10,(i+1)*10)
      }else{
        dataSend.data = _data.slice(i*10)
      }
      console.log('l='+i,dataSend)
      postKycTop(
        {value:JSON.stringify(dataSend)},
        `${i+1}`
      ).then((res)=>{
        try {
          if (res.code !== '0000') {
            message.error(res.message);
          }else  if(i === len-1){
            message.info('保存成功')
          }
        } catch (e) {
          message.error(e.message);
        }
      })
    }

  }


  render() {
    return (
      <div>
        <Row>
          <Col span={3}>
            <p>是否开启人工干预</p>
          </Col>
          <Col span={3}>
            <Radio.Group onChange={this.changeStatus} value={this.state.status}>
              <Radio value='true'>是</Radio>
              <Radio value='false'>否</Radio>
            </Radio.Group>
          </Col>
          <Col span={8}>
            <RangePicker showTime onOk={this.handleDate}/>
          </Col>
          <Col span={8}>
            <Row>
              <Search
                placeholder="支持作者标题模糊搜索"
                enterButton="Search"
                size="large"
                onSearch={this.handeleSearch}
              />
            </Row>
            <Row>
              <p>在搜索状态下，仅支持删除、置顶、下架操作。请不要在搜索状态下进行排序工作，取消搜索后可能会导致排序混乱</p>
            </Row>

          </Col>
          <Col span={2}>
            <Button type="primary" onClick={this.handleSave}>保存</Button>
          </Col>
        </Row>
        <Row className={styles['rowStyle']}>
          <Col span={1}>序号</Col>
          <Col span={4}>类型</Col>
          <Col span={4}>作者</Col>
          <Col span={4}>标题</Col>
          <Col span={6}>链接/内容</Col>
          <Col span={5}>操作</Col>
        </Row>
        {this.state.status === 'true' ? <div className={styles['crossArea']}>
          <div className={styles['left']}>
            <div>置顶内容</div>
            <DraggableArea1
              isList
              tags={this.state.leftTags}
              render={({tag, index}) => (
                <div className={styles["tag"]}>

                  <Row>
                    <Col span={1}>{index}</Col>
                    <Col span={4}>{tag.programeName}</Col>
                    <Col span={4}>{tag.username}</Col>
                    <Col span={4}>{tag.title}</Col>
                    <Col span={6}>{tag.content}</Col>
                    <Col span={2}>
                      <Popconfirm
                        title="确定下架?"
                        onConfirm={() => this.handleClickDown(tag)}
                        // onCancel={cancel}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button type='primary'>下架</Button>
                      </Popconfirm>

                    </Col>
                  </Row>
                </div>
              )}
              onChange={leftTags => {
                console.log('leftTags',leftTags)
                this.setState({leftTags});
              }}
            />
            <Spin spinning={this.state.loading}/>
          </div>
          <div className={styles['right']}>
            <div>非置顶内容</div>
            <DraggableArea2
              isList
              tags={this.state.rightTags}
              render={({tag,index}) => (
                <div className={styles["tag"]}>

                  <Row>
                    <Col span={1}>{index}</Col>
                    <Col span={4}>{tag.programeName}</Col>
                    <Col span={4}>{tag.username}</Col>
                    <Col span={4}>{tag.title}</Col>
                    <Col span={6}>{tag.content}</Col>
                    <Col offset={1} span={2}>
                      <Popconfirm
                        title="确定删除?"
                        onConfirm={() => this.handleClickDelete(tag)}
                        // onCancel={cancel}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button type="danger">删除</Button>
                      </Popconfirm>
                    </Col>
                    <Col span={2}>
                      <Popconfirm
                        title="确定置顶?"
                        onConfirm={() => this.handleClickUp(tag)}
                        // onCancel={cancel}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button type='primary'>置顶</Button>
                      </Popconfirm>
                    </Col>
                  </Row>
                </div>
              )}
              onChange={rightTags => {
                console.log('22',rightTags)
                this.setState({rightTags});
              }}
            />
            <Spin spinning={this.state.loading}/>
          </div>
        </div>: null}

      </div>
    );
  }
}
