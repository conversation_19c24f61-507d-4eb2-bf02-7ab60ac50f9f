import * as React from 'react';
import classNames from 'classnames';
import { Button, message } from 'antd';
import api from 'api';
import PacketTempForm from './packetTempForm';

const { fetchPacketTemp, addPacketTemp } = api;
const { useState, useEffect } = React;

function fetchPacketList() {
  const dataToSend = { type: 'query' };
  return new Promise((resolve, reject) => {
    fetchPacketTemp(dataToSend).then((data: any) => {
      if (data.code === '0000') {
        resolve(Object.values(data.data));
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}

function addOrEditPacket(value: any) {
  if (!value) message.info('缺少必要信息');
  const dataToSend = { type: 'insert', value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    addPacketTemp(dataToSend).then((data: any) => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}

function deletePacket(value: any) {
  if (!value) message.info('缺少必要信息');
  const dataToSend = { type: 'delete', value: JSON.stringify(value) };
  return new Promise((resolve, reject) => {
    addPacketTemp(dataToSend).then((data: any) => {
      if (data.code === '0000') {
        resolve(data);
      } else {
        message.info(data.message);
        reject();
      }
    }).catch((e: any) => {
      message.info(e.message);
    });
  });
}

interface iProps {
  className?: string
}

export default function({ className }: iProps) {
  const [activityList, setActivityList] = useState([]);

  useEffect(() => {
    fetchPacketList().then((data: any) => {
      setActivityList(data);
    });
  }, []);

  function addActivityListItem() {
    const _activityList = JSON.parse(JSON.stringify(activityList));
    _activityList.push({});
    setActivityList(_activityList);
  }

  function submitPacketForm(data: any) {
    addOrEditPacket(data).then(() => {
      message.info('编辑成功');
    });
  }

  function deletePacketForm(data: any, index: number) {
    deletePacket(data).then(() => {
      fetchPacketList().then((data: any) => {
        setActivityList(data);
      });
      message.info('删除成功');
    });
  }

  return (
    <section className={classNames(className)}>
      <div className={'g-mb20'}>
        {
          activityList.length ? activityList.map((formData, index) => <PacketTempForm  className={'g-mb20'} doDelete={(data) => {deletePacketForm(data, index);}} submit={submitPacketForm} data={formData} key={formData.activityId}/>) :
            <p className={'f-tc'}>暂无红包活动~</p>
        }
      </div>
      < Button type={'primary'} onClick={addActivityListItem}>新增</Button>
    </section>
  );
}
