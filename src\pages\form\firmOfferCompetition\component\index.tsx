import React, { useCallback, useEffect, useState } from 'react';
import { Button, Upload, Icon, message, Modal } from 'antd';
import { RcCustomRequestOptions, UploadFile } from '_antd@3.26.20@antd/lib/upload/interface';
import api from 'api';
import styles from './index.less';

const { uploadImg } = api;
interface UploadUrl {
  code: string;
  data: { thurl: unknown; url: string };
  message: string;
  statusCode: number;
  success: boolean;
}
export function UploadImg(props: any) {
  // 预览图片
  const [imageUrl, setImageUrl] = useState<string>();
  // 文件列表
  const [fileList, setFileList] = useState<UploadFile[] | undefined>([]);
  // 是否预览
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  useEffect(() => {
    // 保存预览图片url
    setImageUrl(props.value);
    // 重置文件列表，使图片列表最多只有一项
    setFileList([
      {
        uid: '-1',
        name: '',
        status: 'done',
        url: props.value,
        size: 1,
        type: '',
      },
    ]);
  }, [props.value]);
  // 自定义上传实现
  const customUpload = (info: RcCustomRequestOptions) => {
    const reader = new FileReader();
    const img = new Image();

    reader.readAsDataURL(info.file);
    reader.onload = (e: ProgressEvent<FileReader>) => {
      if (typeof e.target?.result === 'string') {
        img.src = e.target?.result;
        img.onload = () => {
          if (img.width !== props.width) {
            return message.error(`您所上传的图片宽度需为${props.width}px，请重新上传图片`);
          }
          if (img.height !== props.height) {
            return message.error(`您所上传的图片宽度需为${props.height}px，请重新上传图片`);
          }
          typeof e.target?.result === 'string' && _uploadImg(e.target?.result);
        };
      }
      // else:设置listType="picture-card"的upload组件可自动对上传错误格式的文件进行错误判断
    };
  };
  // 上传图片
  const _uploadImg = (imageUrl: string) => {
    uploadImg({
      base64str: imageUrl,
    })
      .then((res: UploadUrl) => {
        if (res.code === '0000') {
          if (res.data.url) {
            // 对图片进行二次包装，修复正式环境返回http协议图片错误
            res.data.url.substring(0, 5) === 'https'
              ? void 0
              : (res.data.url = res.data.url.replace(/http/, 'https'));
            // 将url传递给formItem
            props.ifFormItem
              ? props.onChange(res.data.url)
              : props.onChange(props.name, res.data.url);
          } else {
            message.error('上传图片接口返回值错误');
          }
        } else {
          message.warn(res.message);
        }
      })
      .catch((err: unknown) => {
        console.warn(err);
        message.warn('网络请求错误，请稍后再试');
      });
  };
  // 点击预览图标时的回调
  const handlePreview = useCallback(() => {
    setPreviewVisible(true);
  }, []);
  // 点击移除文件图标时的回调
  const handleRemove = useCallback(() => {
    // 预览图片url重置
    setImageUrl('');
    // 文件列表清空
    setFileList([]);
    // 表单数据清空
    props.ifFormItem ? props.onChange('') : props.onChange(props.name, '');
  }, []);
  return (
    <section className={styles['m-image-upload']}>
      <Modal
        visible={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={props.width + 200}
      >
        <img alt="error" src={imageUrl} />
      </Modal>
      <Upload
        customRequest={customUpload}
        listType="picture-card"
        fileList={fileList}
        onPreview={handlePreview}
        onRemove={handleRemove}
      >
        <Button>
          <Icon type="upload" /> 选择文件
        </Button>
        <span style={{ color: 'red' }}>{`请上传${props.width}px * ${props.height}px的图片`}</span>
      </Upload>
    </section>
  );
}
