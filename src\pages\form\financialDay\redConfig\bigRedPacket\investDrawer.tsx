import {
    Form,
    Input,
    But<PERSON>,
    Popconfirm,
    Select
  } from 'antd';
import { FormComponentProps } from 'antd/es/form';
import React from 'react';
import { iBigRed } from './redPacket';
const { Option } = Select;

interface bigDrawerProps extends FormComponentProps {
    onEditClose: () => void;
    currentData: iBigRed;
    handleData: (data: iBigRed) => void;
}

class bigDrawer extends React.Component<bigDrawerProps, any> {
    constructor(props: bigDrawerProps) {
        super(props);
    }

    formItemLayout = {
        labelCol: {
            span: 4
        },
        wrapperCol: {
            span: 19
        },
    };
    handleSubmit = (e: any) => { 
        e.preventDefault();
        const { currentData } = this.props;
        this.props.form.validateFields((err, values) => {
            if (!err) {
                values = { 
                    ...currentData,
                    ...values,
                };
                console.log(values);
                this.props.handleData(values);
                this.props.onEditClose();
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;
        const { onEditClose } = this.props;
        return (
            <>
                <Form {...this.formItemLayout}>
                    <Form.Item label="红包名称" wrapperCol={{span: 6}}>
                        {getFieldDecorator('name', {
                            initialValue: this.props.currentData?.name,
                            rules: [{ required: true, message: '请输入红包名称' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="活动ID" wrapperCol={{span: 6}}>
                        {getFieldDecorator('activityId', {
                            initialValue: this.props.currentData?.activityId,
                            rules: [{ required: true, message: '请输入活动ID' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="红包ID" wrapperCol={{span: 6}}>
                        {getFieldDecorator('redPaperId', {
                            initialValue: this.props.currentData?.redPaperId,
                            rules: [{ required: true, message: '请输入红包ID' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="门槛" wrapperCol={{span: 6}}>
                        {getFieldDecorator('threshold', {
                            initialValue: this.props.currentData?.threshold,
                            rules: [{ required: true, message: '请输入活动门槛' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="满减金额" wrapperCol={{span: 6}}>
                        {getFieldDecorator('minusMoney', {
                            initialValue: this.props.currentData?.minusMoney,
                            rules: [{ required: true, message: '请输入活动满减金额' }],
                        })(
                            <Input />
                        )}
                    </Form.Item>
                    <Form.Item label="红包状态" wrapperCol={{span: 6}}>
                        {getFieldDecorator('status', {
                            initialValue: this.props.currentData?.status,
                            rules: [{ required: true, message: '请选择红包状态' }],
                        })(
                            <Select style={{ width: 120 }}>
                                <Option key={'0'} value={'0'}>可领取</Option>
                                <Option key={'2'} value={'2'}>已抢完</Option>
                            </Select>
                        )}
                    </Form.Item>
                    <Form.Item style={{textAlign: 'left'}}>
                        <Button style={{marginRight: 20, marginLeft: 200}} onClick={onEditClose}>
                            取消
                        </Button>
                        <Popconfirm placement="left" title="确定要提交吗?" onConfirm={this.handleSubmit}>
                            <Button type="primary">
                                确定
                            </Button>
                        </Popconfirm>
                    </Form.Item>
                </Form>
            </>
        )
    }
}
const WrappedBigDrawer = Form.create<bigDrawerProps>({ name: 'bigDrawer' })(bigDrawer);
export default WrappedBigDrawer