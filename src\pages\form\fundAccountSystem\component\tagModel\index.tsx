import React, { useState, useEffect } from 'react';
import { Radio, Select, Input, Button, Popconfirm, message, Upload, Row, Col } from 'antd';
import api from 'api';
const { Option } = Select;
import styles from '../selectModel/index.less'
import classNames from 'classnames';

interface iProps {
    isEdit: boolean,
    kycTag: Array<any>,
    olasTag: Array<any>,
    data: any,
    handleChange: (data: any) => void,
    fileType?: string,
    saveFile: (file: any, type?: string) => void,
    downloadFile: () => void,//下载已上传文件
    downloadModel: () => void, //下载模板文件
    isExist?: boolean  //文件是否存在
}
export default function (props: any) {
    const { isEdit, kycTag, olasTag, data, handleChange, saveFile, downloadFile, fileType, downloadModel, isExist } = props
    const [radio, setRadio] = useState('')
    const [kycLogic, setKycLogic] = useState('')
    const [kycs, setKycs] = useState<Array<any>>([])
    const [olasId, setOlasId] = useState<string[]>([])
    const [fileList, setFileList] = useState<any>([]);

    useEffect(() => {
        if (data) {
            //判断kyc标签是否在场景内
            console.log('data', data)
            data.kycs && data.kycs.map((val: any) => {
                let target = kycTag.find((item: any) => item.label_code === val.label)
                val.available = target && target.app_info && target.app_info.includes('fund0005')
            })
            setRadio(data.targetType)
            setKycLogic(data.kycLogic)
            setKycs(data.kycs)
            setOlasId(data.olasId)
        }

    }, [data])
    //提交时返回给父组件的回调
    const onSubmit = (radio: any, kycLogic: any, kycs: any, olasId: any) => {
        let _data = {
            targetType: radio,
            kycLogic: kycLogic,
            olasId: olasId,
            kycs: kycs
        }
        handleChange(_data)
    }
    const onRadioChange = (value: any) => {
        setRadio(value)
        onSubmit(value, kycLogic, kycs, olasId)
    }
    const onKycLogicChange = (val: any) => {
        setKycLogic(val)
        onSubmit(radio, val, kycs, olasId)
    }
    const addTag = () => {
        let obj = {
            label: kycTag[0].label_code,
            logic: "equal",
            value: "",
            available: kycTag[0].app_info && kycTag[0].app_info.includes('fund0005')
        }
        let data = [...kycs, obj];
        setKycs(data)
        onSubmit(radio, kycLogic, data, olasId)
    }
    const onLabelChange = (val: any, index: any, other: any) => {

        let available = kycTag[other.key].app_info && kycTag[other.key].app_info.includes('fund0005')
        // console.log(val, available)

        let _kyc: any = [...kycs]
        _kyc[index].label = val
        _kyc[index].available = available
        setKycs(_kyc)
        onSubmit(radio, kycLogic, _kyc, olasId)
    }
    const onLogicChange = (val: any, index: any) => {
        let _kyc: any = [...kycs]
        _kyc[index].logic = val
        setKycs(_kyc)
        onSubmit(radio, kycLogic, _kyc, olasId)
    }
    const onInput = (e: any, index: any) => {
        let _kyc = [...kycs]
        _kyc[index].value = e.target.value
        setKycs(_kyc)
        onSubmit(radio, kycLogic, _kyc, olasId)
    }
    const onOlasChange = (val: string) => {
        let arr: string[] = [val];
        setOlasId(arr)
        onSubmit(radio, kycLogic, kycs, arr)
    }
    const handleDelete = (index: number) => {
        let _kycs = kycs.filter((item, idx) => idx !== index)
        setKycs(_kycs)
        onSubmit(radio, kycLogic, _kycs, olasId)
    }
    const readWorkbookFromLocalFile = (file: any) => {
        let fileArr = [file];
        setFileList(fileArr);

        saveFile(file, fileType);
        return false
    }
    const download = () => {
        downloadFile()
    }
    const removeFile = (file: any) => {
        setFileList([]);
        saveFile(null, fileType);
    }
    return (
        <section>
            <p className={styles['m-card-label']}>指定用户:</p>
            <Radio.Group onChange={(e) => onRadioChange(e.target.value)} value={radio} disabled={!isEdit}>
                <Radio value={'kyc'} key='kyc'>通过KYC标签</Radio>
                <Radio value={'olas'} key='olas'>通过OLAS平台推送</Radio>
                <Radio value={'file'} key='file'>手动上传用户名单</Radio>
            </Radio.Group>
            <Button onClick={() => { onRadioChange('') }} disabled={!isEdit}>清除指定用户</Button>
            <br />
            <div key='kyc' className={radio !== 'kyc' ? 'z-hide' : ''}>

                <div className={styles['m-tagModel-row']}>
                    <p className={styles['m-card-label']}>标签逻辑关系:</p>
                    <Select value={kycLogic} style={{ width: 120 }} onChange={onKycLogicChange} disabled={!isEdit}>
                        <Option value="and">与</Option>
                        <Option value="or">或</Option>
                    </Select>
                </div>
                <br />
                <div className={styles['m-tagModel-row']}>
                    <p className={styles['m-card-label']}>标签值:</p>
                    <Button type='primary' onClick={addTag} disabled={!isEdit} size='small' className={styles['m-tagModel-button']}>添加</Button>
                </div>
                {kycs?.map((item: any, index) => {
                    return (
                        <div key={index} className={'g-mb10 g-ml100'}>
                            <Select
                                showSearch
                                style={{ width: 260, marginRight: "10px" }}
                                optionFilterProp="children"
                                onChange={(val: any, other: any) => onLabelChange(val, index, other)}
                                maxTagCount={5}
                                filterOption={(input, option: any) =>
                                    option.props.children.indexOf(input) >= 0
                                }
                                value={item.label}
                                disabled={!isEdit}
                            >
                                {kycTag.map((val: any, cnt: number) => {
                                    return <Option value={val.label_code} key={cnt}>{val.label_name}</Option>
                                })}
                            </Select>
                            <Select
                                style={{ width: 260, marginRight: "10px" }}
                                onChange={(val: any) => onLogicChange(val, index)}
                                value={item.logic}
                                disabled={!isEdit}
                            >
                                <Option value="equal">等于</Option>
                                <Option value="notEqual">不等于</Option>
                                <Option value="in">字符串包含</Option>
                                <Option value="notIn">字符串不包含</Option>
                                <Option value="isSet">有值</Option>
                                <Option value="notSet">没值</Option>
                                <Option value="greater">大于</Option>
                                <Option value="less">小于</Option>
                                <Option value="greaterEqual">大于等于</Option>
                                <Option value="lessEqual">小于等于</Option>
                            </Select>
                            {item.logic === 'isSet' || item.logic === 'notSet' ? null :
                                <Input
                                    style={{ width: 260 }}
                                    onChange={(e) => onInput(e, index)}
                                    value={item.value}
                                    disabled={!isEdit}
                                />
                            }
                            <span className={'g-ml20'}>
                                <Popconfirm
                                    title="确定删除？"
                                    onConfirm={() => handleDelete(index)}
                                    okText="确定"
                                    cancelText="取消"
                                    disabled={!isEdit}
                                >
                                    <Button type='danger' ghost className={'g-ml10'} disabled={!isEdit}>删除</Button>
                                </Popconfirm>

                            </span>
                            {
                                !item.available ? <p style={{ color: "red" }}>此标签未添加进基金的应用场景fund0005，请联系叶志飞（<EMAIL>）手动添加，15:30前添加，T+1生效</p> : null
                            }

                        </div>
                    )
                })}
            </div>
            <div key='olas' className={classNames(styles['m-tagModel-row'], radio !== 'olas' ? 'z-hide' : '')}>
                <span className={styles['m-card-label']}>用户群体:</span>
                <Select
                    showSearch
                    style={{ width: 260 }}
                    optionFilterProp="children"
                    onChange={onOlasChange}
                    value={olasId && olasId[0]}
                    filterOption={(input, option: any) =>
                        option.props.children.indexOf(input) >= 0
                    }
                    disabled={!isEdit}
                >
                    {olasTag?.map((item: any, index: number) => {
                        return <Option value={item.groupid} key={index}>{item.description}</Option>
                    })}
                </Select>
            </div>
            <div key='file' className={classNames(styles['m-tagModel-row'], radio !== 'file' ? 'z-hide' : '', 'g-mb20')}>
                <Row>
                    <Col span={3}><p className={styles['m-card-label']}>上传文件:</p></Col>
                    <Col span={2} className={isExist ? '' : 'z-hide'}>
                        <a onClick={download} className={'f-unl'}>用户白名单</a>
                    </Col>
                    <Col span={2} offset={1} >
                        <Upload
                            accept=".xls,.xlsx"
                            beforeUpload={readWorkbookFromLocalFile}
                            onRemove={removeFile}
                            fileList={fileList}
                        >
                            <Button disabled={!isEdit}>上传文件</Button>
                        </Upload>
                    </Col>
                    <Col span={2}>
                        <Button disabled={!isEdit} onClick={downloadModel}>下载模板</Button>
                    </Col>
                    <Col span={7}>
                        <p style={{ color: "red" }}>请上传xlsx文档，每行填入一个基金客户号（cust_id），第一行不要填数据</p>
                    </Col>
                </Row>

            </div>


        </section>
    )
}
