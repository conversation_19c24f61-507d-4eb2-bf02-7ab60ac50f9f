import React, { useState, useEffect } from 'react';
import { Input, Button, Popconfirm, message, Table, Modal, Row, Col, Drawer, Radio } from 'antd';
import api from 'api';
import FormRender from 'form-render/lib/antd';
import FROM_JSON from './form.json';
import Upload from '../components/uploadFile';
import styles from './index.less';

const { fetchBuySectorIndex, postBuySectorIndex } = api;

const ImgUpload = ({ value, onChange, name }) => {
  const uploadCallback = (fileName: string, size: number, url: string) => {
    onChange(name, url);
  };
  return (
    <>
      {value && <img width={'500px'} src={value} />}
      <span className={styles['upload-logo']}>
        <Upload text="选择文件" callback={uploadCallback} />
      </span>
    </>
  );
};
export default function() {
  const [formData, setFormData] = useState<any>({});
  const [notValid, setNotValid] = useState([]);
  useEffect(() => {
    fetchBuySectorIndex().then((res: any) => {
      if (res.code === '0000' && res.data) {
        let _data = JSON.parse(res.data);
        setFormData(_data);
      }
    });
  }, []);
  function commit() {
    if (!formData.receiveText) {
      message.error('请填写红包领取前展示文案');
      return;
    }
    if (!formData.receiveBtn) {
      message.error('请填写红包领取按钮文案');
      return;
    }
    if (!formData.unReceiveAlert) {
      message.error('请填写未领取用户挽留文案');
      return;
    }
    if (!formData.unUseAlert) {
      message.error('请填写未使用用户挽留文案');
      return;
    }
    if (notValid.length) {
      message.error(`交验未通过字段 ${notValid.toString()}`);
      return;
    }
    let sendData = JSON.stringify(formData);
    postBuySectorIndex({ value: sendData }).then((res: any) => {
      if (res.code === '0000') {
        message.info('保存成功');
      } else {
        message.error('保存失败');
      }
    });
  }
  return (
    <section>
      <Button className="g-mb20" onClick={commit}>
        保存
      </Button>
      <FormRender
        propsSchema={FROM_JSON}
        formData={formData}
        onChange={setFormData}
        widgets={{
          imgUpload: ImgUpload,
        }}
        onValidate={setNotValid}
      />
    </section>
  );
}
